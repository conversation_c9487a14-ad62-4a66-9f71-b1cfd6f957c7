// import { i18n } from "@/main.js";
import {
  ReconciliationStatusOptions,
  ReconciliationStatus,
  ReconciliationStatusText
} from './constant'
import { ColumnComponent as Component } from './columnComponent'
import { codeNameColumn } from '@/utils/utils'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 对账通知 表格数据
export const formatNotifyTableColumnData = (args) => {
  const { data } = args
  const colData = []
  data.forEach((col) => {
    const defaultCol = {
      ...col,
      field: col.fieldCode,
      headerText: col.fieldName,
      width: 'auto'
    }
    if (col.fieldCode === 'thePrimaryKey') {
      // 行主键
      defaultCol.visible = false
      defaultCol.isIdentity = true // 加上这个，新增一行的时候，主键不会变成可输入
      defaultCol.isPrimaryKey = true
      defaultCol.allowEditing = false
    } else if (col.fieldCode === 'checkBox') {
      // checkBox
      defaultCol.type = 'checkbox'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '50'
      // 编辑时显示
      // defaultCol.editTemplate = Component.empty;
    } else if (col.fieldCode === 'serialNumber') {
      // 序号
      defaultCol.width = '80'
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.ignore = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'companyCode') {
      // 公司编码 可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = true
      defaultCol.allowFiltering = false
      defaultCol.ignore = false
      defaultCol.template = codeNameColumn({
        firstKey: 'companyCode',
        secondKey: 'companyName'
      })
      defaultCol.editTemplate = Component.companySelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany,
        placeholder: i18n.t('公司')
      }
    } else if (col.fieldCode === 'supplierCode') {
      // 供应商/客户编号 可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = true
      defaultCol.allowFiltering = false
      defaultCol.ignore = false
      defaultCol.template = codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      })
      defaultCol.editTemplate = Component.supplierSelect
      // 主数据选择器
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplierAll,
        placeholder: i18n.t('供应商/客户')
      }
    } else if (col.fieldCode === 'reconciliationStatus') {
      // 允许本月对账 可编辑
      defaultCol.width = '150'
      defaultCol.allowEditing = true
      ;(defaultCol.searchOptions = {
        elementType: 'select',
        dataSource: ReconciliationStatusOptions,
        fields: { text: 'text', value: 'value' }
      }),
        (defaultCol.template = Component.text({
          dataKey: col.fieldCode,
          valueConverter: {
            type: 'map',
            map: ReconciliationStatusOptions
          }
        }))
      defaultCol.editTemplate = Component.switch({
        dataKey: col.fieldCode,
        onLabel: ReconciliationStatusText[ReconciliationStatus.allow],
        offLabel: ReconciliationStatusText[ReconciliationStatus.notAllowed],
        activeValue: ReconciliationStatus.allow,
        inactiveValue: ReconciliationStatus.notAllowed
      })
    } else if (col.fieldCode === 'reconciliationPerson') {
      // 对账人 可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = true
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
      defaultCol.editTemplate = Component.reconciliationPersonSelect
    } else if (col.fieldCode === 'reconciliationPersonEmail') {
      // 对账人邮箱 不可编辑
      defaultCol.headerTemplate = Component.requiredHeader({
        headerText: defaultCol.headerText
      })
      defaultCol.width = '350'
      defaultCol.allowEditing = false
      defaultCol.template = Component.text({
        dataKey: col.fieldCode
      })
    } else if (col.fieldCode === 'updateUserName') {
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '150'
    } else if (col.fieldCode === 'updateTime') {
      defaultCol.allowFiltering = false
      defaultCol.allowEditing = false
      defaultCol.showInColumnChooser = false
      defaultCol.width = '150'
      defaultCol.ignore = true
      defaultCol.searchOptions = MasterDataSelect.dateRange
    }
    colData.push(defaultCol)
  })

  return colData
}

// 对账通知 序列化
export const serializeNotifyList = (list) => {
  if (list.length > 0) {
    list.forEach((item, index) => {
      item.serialNumber = index + 1 // 序号
      item.thePrimaryKey = item.id // 主键
    })
  }
  return list
}
