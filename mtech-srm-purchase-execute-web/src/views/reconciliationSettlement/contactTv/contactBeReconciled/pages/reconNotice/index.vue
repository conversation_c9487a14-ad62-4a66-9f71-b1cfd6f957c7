<template>
  <!-- 往来对账-待对账-采方 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="template-height"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    />
    <!-- 通知配置导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { formatNotifyTableColumnData, serializeNotifyList } from './config/index'
import {
  EditSettings,
  NotifyToolbar,
  NotifyColumnData,
  RequestType,
  ActionType,
  NewRowData
} from './config/constant'
import { rowDataTemp } from './config/variable'
import { cloneDeep } from 'lodash'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    UploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      apiWaitingQuantity: 0, // 调用的api正在等待数
      actionType: {
        createStatement: 1 // 明细创建对账单
      },
      isEditing: false, // 正在编辑数据
      downTemplateParams: {
        pageFlag: false
      }, // 通知配置导入下载模板参数
      uploadParams: {}, // 导入通知配置文件参数
      // 通知配置导入请求接口配置
      requestUrls: {
        templateUrlPre: 'reconciliationSettlement',
        templateUrl: 'postTransactionReconciliationSupplierConfigDownload', // 下载模板接口方法名
        uploadUrl: 'postTransactionReconciliationSupplierConfigUpload' // 上传接口方法名
      },
      componentConfig: []
    }
  },
  mounted() {
    let _config = [
      {
        useToolTemplate: false, // 不使用预置(新增、编辑、删除)
        useBaseConfig: true, // 不使用组件中的toolbar配置
        toolbar: NotifyToolbar,
        gridId: this.$tableUUID.reconciliationSettlement.contactBeReconciled.reconNoticeTab,
        grid: {
          editSettings: EditSettings,
          allowPaging: true, // 分页
          columnData: formatNotifyTableColumnData({
            data: NotifyColumnData
          }),
          dataSource: [],
          // 往来对账供应商配置-查询往来对账配置列表
          asyncConfig: {
            url: `${BASE_TENANT}/transaction_reconciliation_supplier_config/query`,
            defaultRules: [],
            serializeList: serializeNotifyList
          }
          // frozenColumns: 1,
        }
      }
    ]
    this.componentConfig = _config
  },
  methods: {
    // ToolBar
    handleClickToolBar(args) {
      const { gridRef, toolbar } = args
      const selectRows = gridRef.getMtechGridRecords()
      const commonToolbar = [
        'SendNotify',
        'AddNotify',
        'UpdateNotify',
        'ImportNotify',
        'ExportNotify',
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal',
        'excelExport'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'AddNotify') {
        // 对账通知-新增
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'UpdateNotify') {
        // 对账通知-更新 结束编辑
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'DeleteNotify') {
        // 对账通知-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleDeleteNotify({ selectRows, selectedIds })
          }
        })
      } else if (toolbar.id === 'SendNotify') {
        // 对账通知-发送通知
        this.postTransactionReconciliationSupplierConfigSendNotice({
          ids: selectedIds
        })
      } else if (toolbar.id === 'ImportNotify') {
        // 对账通知-导入
        this.showUploadExcel(true)
      } else if (toolbar.id === 'ExportNotify') {
        // 对账通知-导出
        this.postTransactionReconciliationSupplierConfigDownload()
      }
    },
    // 对账通知-删除
    handleDeleteNotify(args) {
      const { selectedIds } = args
      // 租户级-往来对账通知配置-删除往来对账配置
      this.postTransactionReconciliationSupplierConfigDelete(selectedIds)
    },
    // 刷新当前 Grid
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // actionBegin 表格编辑生命周期
    actionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    // actionComplete 表格编辑生命周期
    actionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          // 租户级-往来对账通知配置-保存往来对账配置
          this.postTransactionReconciliationSupplierConfigSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API
          // 租户级-往来对账通知配置-保存往来对账配置
          this.postTransactionReconciliationSupplierConfigSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 校验数据
    isValidSaveData(data) {
      const { companyName, supplierName, reconciliationPerson } = data
      let valid = true
      if (!companyName) {
        this.$toast({ content: this.$t('请选择公司'), type: 'warning' })
        valid = false
      } else if (!supplierName) {
        this.$toast({
          content: this.$t('请选择供应商/客户编号'),
          type: 'warning'
        })
        valid = false
      } else if (!reconciliationPerson) {
        this.$toast({ content: this.$t('请选择对账人'), type: 'warning' })
        valid = false
      }

      return valid
    },
    // 租户级-往来对账通知配置-删除往来对账配置
    postTransactionReconciliationSupplierConfigDelete(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigDelete(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账通知配置-保存往来对账配置
    postTransactionReconciliationSupplierConfigSave(args) {
      const { rowData, rowIndex } = args
      const params = [
        {
          ...rowData,
          thePrimaryKey: undefined
        }
      ]
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigSave(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            // 刷新当前 Grid
            this.refreshColumns()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    // 租户级-往来对账通知配置-发送往来对账通知
    postTransactionReconciliationSupplierConfigSendNotice(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigSendNotice(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 租户级-往来对账通知配置-往来对账配置下载
    postTransactionReconciliationSupplierConfigDownload() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .postTransactionReconciliationSupplierConfigDownload(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 显示隐藏上传弹框
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 上传成功后
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.$toast({
        content: this.$t('导入成功'),
        type: 'success'
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 导出
    handleExport() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement.reconciliationTransactionDownload(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
