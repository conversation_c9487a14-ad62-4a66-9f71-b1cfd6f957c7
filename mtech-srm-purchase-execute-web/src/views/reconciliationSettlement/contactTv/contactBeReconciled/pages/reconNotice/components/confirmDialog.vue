<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    size="small"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" v-show="!modalData.isExtra">
        <mt-form-item prop="autoInitialBalance" :label="$t('是否自动结转：')" label-style="right">
          <mt-switch
            style="margin-left: 15px"
            v-model="autoInitialBalance"
            :on-label="$t('是')"
            :off-label="$t('否')"
          ></mt-switch>
        </mt-form-item>
      </mt-form>
      <p style="font-size: 14px; font-weight: bold">{{ modalData.message }}</p>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      autoInitialBalance: null, //是否自动结转
      //配置方式
      radioData: [
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$emit('confirm-function', this.autoInitialBalance ? 1 : 0)
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
