import { i18n } from '@/main.js'

export const statusOptions = [
  { value: 1, text: i18n.t('关联供应商'), cssClass: 'col-abnormal' },
  { value: 0, text: i18n.t('非关联供应商'), cssClass: 'col-active' }
]

// 往来待对账明细 表格列数据
export const columnData = [
  {
    field: 'companyCode', // 公司
    headerText: i18n.t('公司代码')
  },
  {
    field: 'companyName', // 公司
    headerText: i18n.t('公司名称')
  },
  {
    field: 'currencyCode', // 币种
    headerText: i18n.t('币种编码')
    // 币种编码 currencyCode
  },
  {
    field: 'currencyName', // 币种
    headerText: i18n.t('币种')
    // 币种编码 currencyCode
  },
  {
    field: 'supplierCode', // 供应商
    headerText: i18n.t('供应商代码')
  },
  {
    field: 'supplierName', // 供应商
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'isAssociateSupplier', // 往来类别 往来类别 0-对账；1-索赔；2-返利
    headerText: i18n.t('关联状态'),
    valueConverter: {
      type: 'map',
      map: statusOptions
    }
  },
  {
    field: 'amountSum', // 金额
    headerText: i18n.t('金额汇总')
  }
]
