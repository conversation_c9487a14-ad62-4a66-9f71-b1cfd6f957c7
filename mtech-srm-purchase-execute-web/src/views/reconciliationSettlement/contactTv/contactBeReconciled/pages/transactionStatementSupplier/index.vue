<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="companyCodes" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCodes"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                multiple
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCodes" :label="$t('供应商编码')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCodes"
                url="/masterDataManagement/tenant/supplier/paged-query"
                multiple
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="isAssociateSupplier" :label="$t('关联状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.isAssociateSupplier"
                :data-source="statusOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { columnData, statusOptions } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      statusOptions,
      pageConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [
            // {
            //   id: "CreateStatementDetails",
            //   icon: "icon_solid_Createorder",
            //   title: this.$t("创建对账单"),
            //   permission: ["O_02_0408"],
            // },
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          grid: {
            // lineSelection: 0,
            lineIndex: 0,
            columnData,
            dataSource: [],
            // 往来待对账-往来待对账明细信息
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionWait/payableSum`,
              defaultRules: []
              // 序列化
              // serializeList: (list) => {
              //   const afterSerialization = addTheOperationDateToList(list)
              //   return afterSerialization
              // }
            }
            // frozenColumns: 1
          }
        }
      ]
    }
  },
  created() {},
  methods: {
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id == 'excelExport') {
        this.handleExport()
      }
    },
    // 导出
    handleExport() {
      let params = {
        ...this.searchFormModel,
        page: { current: 1, size: 10000 }
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .reconciliationTransactionDownloadSupplierTv(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style></style>
