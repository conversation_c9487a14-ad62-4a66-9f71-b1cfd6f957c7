<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <!-- <p style="font-size: 14px; font-weight: bold; padding: 20px 0">{{ modalData.message }}</p> -->
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="rules">
        <mt-form-item prop="companyCode" :label="$t('公司')" label-style="left">
          <RemoteAutocomplete
            style="flex: 1"
            v-model="searchFormModel.companyCode"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            :placeholder="$t('请选择公司')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="left">
          <!-- <mt-input
            style="flex: 1"
            v-model="searchFormModel.currencyCode"
            :show-clear-button="true"
            :placeholder="$t('请输入币种编码')"
          /> -->
          <mt-select
            :allow-filtering="true"
            v-model="searchFormModel.currencyCode"
            :data-source="currencyOptions"
            :fields="{ text: 'label', value: 'value' }"
            :show-clear-button="true"
            :filtering="getCurrency"
            :placeholder="$t('请选择币种')"
          />
        </mt-form-item>
        <mt-form-item prop="reconciliationPersonCode" :label="$t('对账人编码')" label-style="left">
          <!-- <mt-input
            style="flex: 1"
            v-model="searchFormModel.reconciliationPersonCode"
            :show-clear-button="true"
            :placeholder="$t('请输入对账人编码')"
          /> -->
          <debounce-filter-select
            :open-dispatch-change="false"
            v-model="searchFormModel.reconciliationPersonCode"
            :request="getUser"
            :data-source="buyerUserOptions"
            :fields="{ text: 'text', value: 'code' }"
            :show-clear-button="true"
            :placeholder="$t('请选择采购员')"
          ></debounce-filter-select>
        </mt-form-item>
        <mt-form-item prop="date" :label="$t('截止年月')" label-style="left">
          <mt-date-picker
            style="flex: 1"
            start="Year"
            depth="Year"
            format="y MMMM"
            :allow-edit="false"
            v-model="searchFormModel.date"
            :placeholder="$t('选择年月')"
          ></mt-date-picker>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  components: {
    DebounceFilterSelect: () => import('@/components/debounceFilterSelect')
  },
  data() {
    return {
      currencyOptions: [], //订单币种
      buyerUserOptions: [], //采购员
      searchFormModel: {},
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('创建') }
        }
      ],
      rules: {
        companyCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        currencyCode: [{ required: true, message: this.$t('请输入币种编码'), trigger: 'blur' }],
        reconciliationPersonCode: [
          { required: true, message: this.$t('请输入对账人编码'), trigger: 'blur' }
        ],
        date: [{ required: true, message: this.$t('请选择年月'), trigger: 'blur' }]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.getCurrency()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    getCurrency(val, currencyCode) {
      let params = { fuzzyParam: val?.text || '' }
      this.$API.masterData.getCurrencyByFilter(params).then((res) => {
        let list = res.data || []
        list.forEach((item) => {
          item.label = `${item.currencyCode}-${item.currencyName}`
          item.text = item.currencyName
          item.value = item.currencyCode
        })
        this.currencyOptions = list
        if (currencyCode) {
          this.topInfo.currency = currencyCode
        }
        if (val?.updateData) {
          this.$nextTick(() => {
            val.updateData(this.currencyOptions)
          })
        }
      })
    },
    // 获取 用户列表
    getUser(e) {
      const { text: fuzzyName } = e
      this.buyerUserOptions = []
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = []
        res.data?.forEach((item) => {
          tmp.push({
            ...item,
            text: `${item.employeeCode}-${item.employeeName}`,
            code: item.employeeCode,
            name: item.employeeName
          })
        })
        this.buyerUserOptions = tmp
      })
    },
    //点击确认
    confirm() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          // 往来待对账-创建对账单
          const params = {
            ...this.searchFormModel,
            year: this.searchFormModel.date.getFullYear(),
            month: this.searchFormModel.date.getMonth() + 1
          }
          this.$API.reconciliationSettlement
            .reconciliationTransactionBatchCreateTv(params)
            .then((res) => {
              if (res?.code == 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$emit('confirm-function')
              }
            })
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
