<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabSource"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <reconNoticeTV v-show="tabIndex == 0" />
      <reconDetailNew v-show="tabIndex == 1" />
      <reconSummary v-show="tabIndex == 2" />
      <contactBeGenerateSupplier v-show="tabIndex == 3" />
      <!-- <toBeRecon v-if="tabIndex == 3" />
      <reconNotice v-if="tabIndex == 4" />
      <transactionStatementSupplier v-if="tabIndex == 5" /> -->
    </div>
  </div>
</template>

<script>
export default {
  components: {
    reconNoticeTV: () => import('./pages/reconNoticeTV/index.vue'),
    reconDetailNew: () => import('./pages/reconDetailNew/index.vue'),
    reconSummary: () => import('./pages/reconSummary/index.vue'),
    contactBeGenerateSupplier: () =>
      import('@/views/reconciliationSettlement/contactTv/contactBeGenerateSupplier/index.vue')
    // reconDetail: require('./pages/reconDetail/index.vue').default, // 往来对账明细(旧)
    // toBeRecon: require('./pages/toBeRecon/index.vue').default, // 往来待对账
    // reconNotice: require('./pages/reconNotice/index.vue').default, // 对账通知
    // transactionStatementSupplier: require('./pages/transactionStatementSupplier/index.vue').default // 可生成往来对账单供应商（旧）
  },
  data() {
    const purTabList = [
      {
        title: this.$t('往来对账通知')
      },
      {
        title: this.$t('往来对账明细')
      },
      {
        title: this.$t('往来对账汇总')
      },
      {
        title: this.$t('可生成往来对账单供应商')
      }
    ]
    return {
      tabIndex: this.$route.query.currentIndex ?? 0,
      selectedItem: this.$route.query.currentIndex ?? 0,
      tabSource: purTabList
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
