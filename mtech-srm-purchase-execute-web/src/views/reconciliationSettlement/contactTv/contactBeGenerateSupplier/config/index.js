import { i18n } from '@/main.js'

const statusOptions = [
  { text: i18n.t('非关联供应商'), value: '0' },
  { text: i18n.t('关联供应商'), value: '1' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'companyCode',
    title: i18n.t('公司编码')
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称'),
    minWidth: 160
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 160
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种编码')
  },
  {
    field: 'currencyName',
    title: i18n.t('币种名称')
  },
  {
    field: 'totalAmount',
    title: i18n.t('金额汇总')
  },
  {
    field: 'acctGroupCode',
    title: i18n.t('关联状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  }
]
