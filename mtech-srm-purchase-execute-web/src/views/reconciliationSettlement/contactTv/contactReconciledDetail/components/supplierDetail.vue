<template>
  <div>
    <mt-template-page
      ref="supplierDetailsRef"
      class="template-height"
      :template-config="supplierComponentConfig"
      @handleClickToolBar="supplierDetailClickToolBar"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <!-- <mt-form-item prop="type" :label="$t('往来类别')" label-style="top">
              <mt-select
                v-model="searchFormModel.type"
                :data-source="ContactsTypeOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择往来类别')"
              />
            </mt-form-item> -->
            <mt-form-item prop="code" :label="$t('对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.code"
                :show-clear-button="true"
                :placeholder="$t('请输入对账单号')"
              />
            </mt-form-item>
            <mt-form-item prop="invoiceNo" :label="$t('入账发票号')" label-style="top">
              <mt-input
                v-model="searchFormModel.invoiceNo"
                :show-clear-button="true"
                :placeholder="$t('请输入入账发票号')"
              />
            </mt-form-item>
            <mt-form-item prop="amount" :label="$t('金额')" label-style="top">
              <mt-input
                v-model="searchFormModel.amount"
                :show-clear-button="true"
                :placeholder="$t('请输入金额')"
              />
            </mt-form-item>
            <mt-form-item prop="currencyCode" :label="$t('币种')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="currencyCode" :label="$t('币种编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.currencyCode"
                :show-clear-button="true"
                :placeholder="$t('请输入币种编码')"
              />
            </mt-form-item> -->
            <!-- <mt-form-item prop="claimDemageNo" :label="$t('索赔单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.claimDemageNo"
                :show-clear-button="true"
                :placeholder="$t('请输入索赔单号')"
              />
            </mt-form-item> -->
            <!-- <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                :show-clear-button="true"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item> -->
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { SupplierDetailsColumnData, ContactsTypeOptions } from '../config/constant'
import { formatDetailsTableColumnData } from '../config/index'
import { BASE_TENANT } from '@/utils/constant'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'SupplierDetail',
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    const statementId = this.$route?.query?.id // 从URL获取对账单id
    // const code = this.$route?.query?.code // 从URL获取对账单code
    let supplierDetailToolbar = [
      {
        id: 'SupplierDetailExport',
        icon: 'icon_solid_Import',
        title: this.$t('导出')
      }
    ]
    return {
      searchFormModel: {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      ContactsTypeOptions,
      // 供方-往来明细
      supplierComponentConfig: [
        {
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          isUseCustomEditor: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: supplierDetailToolbar, // 供方-往来明细 操作按钮
          gridId:
            this.$tableUUID.reconciliationSettlement.contactReconciledDetail.supplierDetailsTab,
          grid: {
            editSettings: {
              allowEditing: false
            },
            allowPaging: true, // 分页
            lineIndex: 1,
            columnData: formatDetailsTableColumnData({
              data: SupplierDetailsColumnData
            }),
            dataSource: [],
            // 分页查询特定对账单的明细
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionItem/page/query`,
              params: {
                belong: 1, // 数据归属，0-采方,1-供方
                headerId: statementId
              }
              // defaultRules: []
            }
          }
        }
      ]
    }
  },
  methods: {
    //供方往来明细 ToolBar
    supplierDetailClickToolBar(args) {
      if (args.toolbar.id === 'SupplierDetailExport') {
        // 供方往来明细-导出
        this.supplierDetailExport()
      }
    },
    // 供方-导出
    supplierDetailExport() {
      const params = {
        page: { current: 1, size: 10000 },
        headerId: this.$route?.query?.id,
        isDownLoad: true
      } // 筛选条件
      this.apiStartLoading()
      this.$API.reconciliationCollaboration
        .PuPuchaseDetailSupplierExportTv(params)
        .then((res) => {
          this.apiEndLoading()
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
