<template>
  <div>
    <mt-template-page
      ref="supplierAdjustRef"
      class="template-height"
      :template-config="supplierAdjustConfig"
    >
      <!-- <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="adjustType" :label="$t('类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.adjustType"
                :data-source="AdjustTypeOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择类型')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="adjustDescription" :label="$t('调整项摘要')" label-style="top">
              <mt-input
                v-model="searchFormModel.adjustDescription"
                :show-clear-button="true"
                :placeholder="$t('请输入调整项摘要')"
              />
            </mt-form-item>
            <mt-form-item prop="amount" :label="$t('调整项金额')" label-style="top">
              <mt-input
                v-model="searchFormModel.amount"
                :show-clear-button="true"
                :placeholder="$t('请输入调整项金额')"
              />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                :show-clear-button="true"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template> -->
    </mt-template-page>
  </div>
</template>

<script>
import { AdjustColumnData, AdjustTypeOptions } from '../config/constant'
import {
  // formatLogTableColumnData,
  formatAdjustTableColumnData
} from '../config/index'
import { BASE_TENANT, maxPageSize } from '@/utils/constant'
export default {
  name: 'SupplierAdjust',
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    tableType: {
      type: String,
      default: 'plus'
    }
  },
  data() {
    const statementId = this.$route?.query?.id // 从URL获取对账单id
    // const code = this.$route?.query?.code // 从URL获取对账单code
    return {
      AdjustTypeOptions,
      searchFormModel: {},
      supplierAdjustConfig: [
        {
          // isUseCustomSearch: true,
          // isCustomSearchRules: true,
          // isUseCustomEditor: true,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: {
            useBaseConfig: false,
            tools: [[], ['Refresh', 'Setting']]
          }, // 供方-差异调整 操作按钮
          grid: {
            height: '300', // 固定高度
            editSettings: {
              allowEditing: false // 不可编辑供方差异调整
            },
            allowPaging: false, // 不分页
            columnData: formatAdjustTableColumnData({
              data: AdjustColumnData.filter(
                (item) => item.fieldCode !== 'checkBox' && item.fieldCode !== 'adjustType'
              )
            }),
            dataSource: [],
            // 租户级-往来对账单差异调整项接口-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionAdjust/criteria/query`,
              params: {
                headerId: statementId,
                belong: 1,
                adjustType: this.tableType === 'plus' ? 1 : -1,
                page: {
                  current: 1,
                  size: maxPageSize
                }
              },
              recordsPosition: 'data',
              // defaultRules: [
              //   {
              //     field: 'belong', // 数据归属，0-采方,1-供方
              //     operator: 'equal',
              //     value: 1
              //   }
              // ],
              serializeList: this.serializeAdjustList
            }
          }
        }
      ]
    }
  },
  methods: {
    // 差异调整项 数据序列化
    serializeAdjustList(list) {
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.serialNumber = index + 1 // 序号
          item.thePrimaryKey = item.id // 主键
        })
      }
      return list
    }
  }
}
</script>

<style lang="scss" scoped></style>
