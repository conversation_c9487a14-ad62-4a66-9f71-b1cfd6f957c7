<template>
  <div>
    <mt-template-page
      ref="purchaseAdjustRef"
      class="template-height"
      :template-config="purchaseAdjustConfig"
      @handleClickToolBar="purchaseAdjustClickToolBar"
      @actionBegin="purchaseAdjustActionBegin"
      @actionComplete="purchaseAdjustActionComplete"
    >
      <!-- <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="adjustType" :label="$t('类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.adjustType"
                :data-source="AdjustTypeOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择类型')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="adjustDescription" :label="$t('调整项摘要')" label-style="top">
              <mt-input
                v-model="searchFormModel.adjustDescription"
                :show-clear-button="true"
                :placeholder="$t('请输入调整项摘要')"
              />
            </mt-form-item>
            <mt-form-item prop="amount" :label="$t('调整项金额')" label-style="top">
              <mt-input
                v-model="searchFormModel.amount"
                :show-clear-button="true"
                :placeholder="$t('请输入调整项金额')"
              />
            </mt-form-item>
            <mt-form-item prop="remark" :label="$t('备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                :show-clear-button="true"
                :placeholder="$t('请输入备注')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template> -->
    </mt-template-page>
  </div>
</template>

<script>
import {
  EntryType,
  // LogColumnData,
  AdjustColumnData,
  EditSettings,
  RequestType,
  ActionType,
  PurchaseAdjustNewRowData,
  AdjustTypeOptions
} from '../config/constant'
import {
  // formatLogTableColumnData,
  formatAdjustTableColumnData
} from '../config/index'
import { rowDataTemp } from '../config/variable'
// import bigDecimal from 'js-big-decimal'
import { BASE_TENANT, maxPageSize } from '@/utils/constant'
import { cloneDeep } from 'lodash'
export default {
  name: 'PurchaseAdjust',
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  props: {
    tableType: {
      type: String,
      default: 'plus'
    }
  },
  data() {
    const entryType = this.$route?.query?.type // 从URL获取页面状态
    const statementId = this.$route?.query?.id // 从URL获取对账单id
    // const code = this.$route?.query?.code // 从URL获取对账单code
    let purchaseAdjustToolbar = {
      useBaseConfig: false,
      tools: [[], ['Refresh', 'Setting']]
    } // 采方-差异调整 操作按钮
    if (entryType == EntryType.edit) {
      // 页面显示类型 = 编辑
      purchaseAdjustToolbar = {
        useBaseConfig: false,
        tools: [
          [
            {
              id: 'PurchaseAdjustAdd',
              icon: 'icon_table_new',
              title: this.$t('新增')
            },
            {
              id: 'PurchaseAdjustDelete',
              icon: 'icon_table_delete',
              title: this.$t('删除')
            },
            {
              id: 'PurchaseAdjustUpdate',
              icon: 'icon_table_save',
              title: this.$t('更新')
            }
          ],
          ['Refresh', 'Setting']
        ]
      }
    }
    return {
      AdjustTypeOptions,
      searchFormModel: {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      purchaseAdjustConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          // isUseCustomSearch: true,
          // isCustomSearchRules: true,
          // isUseCustomEditor: true,
          toolbar: purchaseAdjustToolbar, // 采方-差异调整 操作按钮
          grid: {
            height: '300', // 固定高度
            editSettings: {
              ...EditSettings,
              allowEditing: entryType == EntryType.edit
            },
            allowPaging: false, // 不分页
            columnData: formatAdjustTableColumnData({
              data: AdjustColumnData.filter((item) => item.fieldCode !== 'adjustType'),
              tableType: this.tableType
            }),
            dataSource: [],
            // 租户级-往来对账单差异调整项接口-分页查询
            asyncConfig: {
              url: `${BASE_TENANT}/tv/transactionAdjust/criteria/query`,
              params: {
                headerId: statementId,
                belong: 0,
                adjustType: this.tableType === 'plus' ? 1 : -1,
                page: {
                  current: 1,
                  size: maxPageSize
                }
              },
              recordsPosition: 'data',
              // defaultRules: [
              //   {
              //     field: 'belong', // 数据归属，0-采方,1-供方
              //     operator: 'equal',
              //     value: 0
              //   }
              // ],
              serializeList: this.serializeAdjustList
            }
          }
        }
      ]
    }
  },
  methods: {
    // 差异调整项 数据序列化
    serializeAdjustList(list) {
      if (list.length > 0) {
        list.forEach((item, index) => {
          item.serialNumber = index + 1 // 序号
          item.thePrimaryKey = item.id // 主键
        })
      }
      return list
    },
    // 采方差异调整项 actionBegin 表格编辑生命周期
    purchaseAdjustActionBegin(args) {
      const { requestType, action, rowData } = args
      // console.log(`Begin,\nrequest: ${requestType}\naction: ${action}`, args);
      if (requestType === RequestType.add) {
        // 开始行添加
        this.isEditing = true
        // 即将添加一行，赋值新增行的初始数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(PurchaseAdjustNewRowData)
        newRowData.headerId = this.statementId // 主单id
        rowDataTemp.push(newRowData)
        args.rowData = newRowData
        args.data = newRowData
      } else if (requestType === RequestType.save && action === ActionType.add) {
        // 即将保存新增时，将新增中编辑的数据付给组件
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === RequestType.save && action === ActionType.edit) {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === RequestType.beginEdit) {
        // 开始行编辑
        this.isEditing = true
        // 即将编辑行，赋值当前行的数据
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },

    // 采方差异调整项 ToolBar
    purchaseAdjustClickToolBar(args) {
      const { grid, toolbar } = args
      const selectRows = grid.getSelectedRecords()
      const commonToolbar = [
        'PurchaseAdjustAdd',
        'PurchaseAdjustUpdate',
        'more-option-btn',
        'Filter',
        'Refresh',
        'Setting',
        'refreshDataByLocal',
        'filterDataByLocal'
      ]
      if (selectRows.length === 0 && !commonToolbar.includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      const selectedIds = []
      selectRows.forEach((item) => {
        selectedIds.push(item.id)
      })

      if (toolbar.id === 'PurchaseAdjustAdd') {
        // 采方差异调整项-新增
        this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      } else if (toolbar.id === 'PurchaseAdjustUpdate') {
        // 采方差异调整项-更新 结束编辑
        this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
      } else if (toolbar.id === 'PurchaseAdjustDelete') {
        // 采方差异调整项-删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            // 采方差异调整项-删除
            this.handlePurchaseAdjustDelete({ selectRows, selectedIds })
          }
        })
      }
    },
    // 采方差异调整项-删除
    handlePurchaseAdjustDelete(args) {
      const { selectedIds } = args
      // 租户级-往来对账单差异调整项接口-批量删除
      this.deleteTransactionReconciliationAdjustBatchDelete({
        ids: selectedIds
      })
    },
    // 租户级-往来对账单差异调整项接口-批量删除
    deleteTransactionReconciliationAdjustBatchDelete(params) {
      this.apiStartLoading()
      this.$API.reconciliationSettlement
        .deleteTransactionReconciliationAdjustBatchDeleteTv(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || {}
            // 通过响应更新差异调整项
            this.upDateTotalDiffByResponse(data)

            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            // 刷新 Grid
            this.$refs.purchaseAdjustRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    // 采方差异调整项 actionComplete 表格编辑生命周期
    purchaseAdjustActionComplete(args) {
      const { requestType, action, rowIndex, index } = args
      const rowData = rowDataTemp[rowDataTemp.length - 1]
      // console.log(
      //   `Complete,\nrequest: ${requestType}\naction: ${action}`,
      //   args
      // );
      if (requestType === RequestType.save && action == ActionType.edit) {
        // 完成编辑行
        this.isEditing = false
        // rowData, rowIndex
        if (!this.isValidAdjustSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账单差异调整项接口-新增或修改
          this.postTransactionReconciliationAdjustBatchSave({
            rowData,
            rowIndex
          })
        }
      } else if (requestType === RequestType.save && action == ActionType.add) {
        // 完成新增行
        this.isEditing = false
        if (!this.isValidAdjustSaveData(rowData)) {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(index)
          this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        } else {
          // 调用API 租户级-往来对账单差异调整项接口-新增或修改
          this.postTransactionReconciliationAdjustBatchSave({
            rowData,
            rowIndex: index
          })
        }
      }
    },
    // 校验 差异调整项 数据
    isValidAdjustSaveData(data) {
      const { adjustType, amount } = data
      let valid = true
      if (!adjustType) {
        // this.$toast({ content: this.$t('请选择类型'), type: 'warning' })
        // valid = false
      } else if (!amount) {
        this.$toast({ content: this.$t('请填写调整金额'), type: 'warning' })
        valid = false
      }

      return valid
    },
    // 租户级-往来对账单差异调整项接口-新增或修改
    postTransactionReconciliationAdjustBatchSave(args) {
      const { rowData, rowIndex } = args
      const params = {
        ...rowData,
        id: rowData.id || undefined, // 没有时即为新增
        adjustType: this.tableType === 'plus' ? 1 : -1,
        thePrimaryKey: undefined,
        headerId: this.$route?.query?.id
      }
      this.apiStartLoading()
      this.getSaveApi(params.id)(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code == 200) {
            const data = res?.data || {}
            // 通过响应更新差异调整项
            this.upDateTotalDiffByResponse(data.header)

            this.$toast({ content: this.$t('操作成功'), type: 'success' })

            // 刷新 Grid
            this.$refs.purchaseAdjustRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.purchaseAdjustRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    },
    getSaveApi(id) {
      if (id) {
        return this.$API.reconciliationSettlement.postTransactionReconciliationAdjustUpdate
      }
      return this.$API.reconciliationSettlement.postTransactionReconciliationAdjustAdd
    },
    // 通过响应更新差异调整项
    upDateTotalDiffByResponse() {
      // 计算对账总差异
      // this.headerInfo.closingBalance = data?.closingBalance // 采方期末金额
      // this.headerInfo.supplierClosingBalance = data?.supplierClosingBalance // 供方期末金额
      // const closingBalance = this.headerInfo.closingBalance || 0 // 采方期末金额
      // const supplierClosingBalance = this.headerInfo.supplierClosingBalance || 0 // 供方期末金额
      // // 对账总差异 = 采方期末金额 + 供方期末金额
      // this.totalDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
      this.$emit('upDateTotalDiffByResponse')
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped></style>
