<template>
  <!-- 往来对账单列表-供方 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      class="template-height"
      :template-config="componentConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
      @handleClickToolBar="handleClickToolBar"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="code" :label="$t('往来对账单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.code"
                :show-clear-button="true"
                :placeholder="$t('请输入往来对账单号')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-select
                style="flex: 1"
                v-model="searchFormModel.status"
                :data-source="ReconciledStatusOptions"
                :show-clear-button="true"
                :placeholder="$t('请选择')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('客户公司名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入客户公司名称')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="openingBalance" :label="$t('期初余额')" label-style="top">
              <mt-input
                v-model="searchFormModel.openingBalance"
                :show-clear-button="true"
                :placeholder="$t('请输入期初余额')"
              />
            </mt-form-item>
            <mt-form-item prop="closingBalance" :label="$t('期末余额')" label-style="top">
              <mt-input
                v-model="searchFormModel.closingBalance"
                :show-clear-button="true"
                :placeholder="$t('请输入期末余额')"
              />
            </mt-form-item> -->
            <!-- <mt-form-item prop="remark" :label="$t('采方备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.remark"
                :show-clear-button="true"
                :placeholder="$t('请输入采方备注')"
              />
            </mt-form-item> -->
            <!-- <mt-form-item prop="supRemark" :label="$t('供方备注')" label-style="top">
              <mt-input
                v-model="searchFormModel.supRemark"
                :show-clear-button="true"
                :placeholder="$t('请输入供方备注')"
              />
            </mt-form-item> -->
          </mt-form>
        </div>
      </template></mt-template-page
    >
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import {
  addTheReconDiffToList,
  columnData,
  EntryType,
  ReconciledStatusOptions
} from './config/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  components: {},
  data() {
    return {
      ReconciledStatusOptions,
      searchFormModel: {},
      apiWaitingQuantity: 0, // 调用的api正在等待数
      actionType: {
        createStatement: 1 // 明细创建对账单
      },
      componentConfig: [
        {
          // tab: { title: this.$t("往来对账单列表") },
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: [
            {
              id: 'print',
              icon: 'icon_list_print',
              title: this.$t('打印')
            },
            {
              id: 'excelExport',
              icon: 'icon_solid_export',
              title: this.$t('导出')
            }
          ],
          grid: {
            // lineSelection: 0,
            // lineIndex: 0,
            columnData,
            dataSource: [],
            asyncConfig: {
              // 租户级-往来对账单供应商控制器-分页查询
              url: `${BASE_TENANT}/tv/transaction/supplier/pageQuery`,
              // defaultRules: [
              //   {
              //     field: 'status',
              //     operator: 'in',
              //     value: [1, 6]
              //   }
              // ],
              // 往来对账单列表 序列化
              serializeList: (list) => {
                let afterSerialization = addTheReconDiffToList(list)
                return afterSerialization
              }
            }
            // frozenColumns: 1,
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id == 'excelExport') {
        this.handleExport()
      }
      if (e.toolbar.id == 'print') {
        const _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length !== 1) {
          this.$toast({ content: this.$t('请仅且选择一行打印'), type: 'warning' })
          return
        }
        this.handlePrint({ id: _selectRows[0]['id'] })
      }
    },
    // CellTool TODO
    handleClickCellTool(args) {
      const { tool, data } = args
      if (tool.id === 'FeedbackStatement' || tool.id === 'uploadAttach') {
        // 反馈
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-供方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(0))
        // 跳转 往来对账单详情-供方
        this.$router.push({
          name: 'contact-detail-supplier-tv',
          query: {
            id: data.id,
            code: data.code,
            type: tool.id === 'uploadAttach' ? EntryType.upload : EntryType.edit // 上传附件
          }
        })
      }
    },
    // CellTitle
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'code') {
        // 点击对账单号
        const id = data.id
        const code = data.code
        // 将 lastTabIndex 放到 localStorage 往来对账单详情-供方 读
        localStorage.setItem('lastTabIndex', JSON.stringify(0))
        // 跳转 往来对账单详情-供方
        this.$router.push({
          name: 'contact-detail-supplier-tv',
          query: {
            id,
            code,
            type: data.status === 1 ? EntryType.edit : EntryType.view
          }
        })
      }
    },
    //打印对账单
    handlePrint(params) {
      const _this = this
      this.$API.reconciliationCollaboration.PuPuchaseDetailPrintTv(params).then((res) => {
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = () => {
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            _this.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = this.pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    // 导出
    handleExport() {
      let params = {
        ...this.searchFormModel,
        page: { current: 1, size: 10000 }
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationCollaboration
        .reconciliationTransactionExportTv(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  }
}
</script>

<style lang="scss" scoped>
// 对账差异 theReconDiffTemplate
/deep/ .the-recon-diff-blue {
  color: #6386c1; // 这是 UI 给的颜色
}
/deep/ .the-recon-diff-red {
  color: #ed5633; // 这是 UI 给的颜色
}
</style>
