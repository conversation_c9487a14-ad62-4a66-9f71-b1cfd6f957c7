import { i18n } from '@/main.js'
import bigDecimal from 'js-big-decimal'
// import { ReconciledStatus } from './../../contactReconciledList/pages/reconDetail/config/index'
import { theReconDiffTemplate } from './columnComponent'

// 页面显示类型
export const EntryType = {
  view: 1, // 查看
  edit: 2, // 继续发布时就行编辑
  upload: 3 // 上传附件
}

// 添加 对账差异 字段到列表中
export const addTheReconDiffToList = (list) => {
  if (list.length > 0) {
    // 往来对账单 对账差异 前端定义、计算 = 采方期末+供方期末
    list.forEach((item, index) => {
      const closingBalance = item.closingBalance || 0 // 采方期末
      const supplierClosingBalance = item.supplierClosingBalance || 0 // 供方期末
      // 对账差异
      item.lineNo = index + 1
      item.theReconDiff = bigDecimal.add(closingBalance, supplierClosingBalance)
    })
  }
  return list
}

// 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；6-财务已确认
export const ReconciledStatus = {
  new: 0, // 新建
  pending: 1, // 待反馈
  normal: 2, // 反馈正常
  abnormal: 3, // 反馈异常
  confirmed: 4, // 采购已确认
  close: 5, // 关闭
  finConfirmed: 6, // 财务已确认
  finished: 7 // 已完结
}

// 往来类别 text
export const ReconciledStatusText = {
  [ReconciledStatus.new]: i18n.t('新建'),
  [ReconciledStatus.pending]: i18n.t('待反馈'),
  [ReconciledStatus.normal]: i18n.t('反馈正常'),
  [ReconciledStatus.abnormal]: i18n.t('反馈异常'),
  [ReconciledStatus.confirmed]: i18n.t('采购已确认'),
  [ReconciledStatus.close]: i18n.t('关闭'),
  [ReconciledStatus.finConfirmed]: i18n.t('财务已确认'),
  [ReconciledStatus.finished]: i18n.t('已完结')
}

// 往来类别 对应的 css class
export const ReconciledStatusCssClass = {
  [ReconciledStatus.new]: 'col-active',
  [ReconciledStatus.pending]: 'col-active',
  [ReconciledStatus.normal]: 'col-active',
  [ReconciledStatus.abnormal]: 'col-active',
  [ReconciledStatus.confirmed]: 'col-inactive',
  [ReconciledStatus.close]: 'col-inactive',
  [ReconciledStatus.finConfirmed]: 'col-inactive',
  [ReconciledStatus.finished]: 'col-inactive'
}

// 往来类别 对应的 Options
export const ReconciledStatusOptions = [
  {
    // 新建
    value: ReconciledStatus.new,
    text: ReconciledStatusText[ReconciledStatus.new],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.new]
  },
  {
    // 待反馈
    value: ReconciledStatus.pending,
    text: ReconciledStatusText[ReconciledStatus.pending],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.pending]
  },
  {
    // 反馈正常
    value: ReconciledStatus.normal,
    text: ReconciledStatusText[ReconciledStatus.normal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.normal]
  },
  {
    // 反馈异常
    value: ReconciledStatus.abnormal,
    text: ReconciledStatusText[ReconciledStatus.abnormal],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.abnormal]
  },
  {
    // 采购已确认
    value: ReconciledStatus.confirmed,
    text: ReconciledStatusText[ReconciledStatus.confirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.confirmed]
  },
  {
    // 关闭
    value: ReconciledStatus.close,
    text: ReconciledStatusText[ReconciledStatus.close],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.close]
  },
  {
    // 财务已确认
    value: ReconciledStatus.finConfirmed,
    text: ReconciledStatusText[ReconciledStatus.finConfirmed],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finConfirmed]
  },
  {
    // 已完结
    value: ReconciledStatus.finished,
    text: ReconciledStatusText[ReconciledStatus.finished],
    cssClass: ReconciledStatusCssClass[ReconciledStatus.finished]
  }
]

// 单元格操作按钮
export const CellTools = [
  {
    id: 'FeedbackStatement',
    icon: '',
    title: i18n.t('反馈'),
    // permission: ['O_02_1322'],
    visibleCondition: (data) => data.status == ReconciledStatus.pending // 待反馈
  },
  {
    id: 'uploadAttach',
    icon: '',
    title: i18n.t('上传附件'),
    // permission: ["O_02_1322"],
    visibleCondition: (data) => data.status == ReconciledStatus.finConfirmed // 财务已确认
  }
]

// 往来对账单列表 表格列数据
export const columnData = [
  {
    type: 'checkbox',
    allowFiltering: false,
    allowEditing: false,
    showInColumnChooser: false,
    width: '50'
  },
  {
    field: 'lineNo', // 公司名称
    headerText: i18n.t('序号'),
    width: '70'
  },
  {
    field: 'code', // 往来对账单号
    headerText: i18n.t('往来对账单号'),
    cellTools: [],
    width: '150'
  },
  {
    field: 'status', // 状态 状态 0-新建；1-待反馈；2-反馈正常；3-反馈异常；4-采购已确认；5-关闭；
    headerText: i18n.t('状态'),
    width: '150',
    valueConverter: {
      type: 'map',
      map: ReconciledStatusOptions
    },
    cellTools: CellTools
  },
  {
    field: 'companyName', // 公司名称
    headerText: i18n.t('客户公司名称'),
    width: '150'
  },
  {
    field: 'openingBalance', // 期初余额
    headerText: i18n.t('期初余额'),
    width: '150'
  },
  {
    field: 'closingBalance', // 期末余额 采方期末余额
    headerText: i18n.t('期末余额'),
    width: '150'
  },
  {
    field: 'theReconDiff', // 对账差异 前端定义、计算 = 采方期末余额-供方期末余额
    headerText: i18n.t('对账差异'),
    width: '150',
    template: theReconDiffTemplate
    // supplierClosingBalance	供方期末余额
  },
  {
    field: 'currencyName', // 币种
    headerText: i18n.t('币种'),
    width: '150'
  },
  {
    field: 'remark', // 采方备注
    headerText: i18n.t('采方备注'),
    width: '150'
  },
  {
    field: 'supRemark', // 供方备注
    headerText: i18n.t('供方备注'),
    width: '150'
  }
]
