import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('已通知'), value: 1 },
  { text: i18n.t('已提交'), value: 2 },
  { text: i18n.t('已生成'), value: 3 },
  { text: i18n.t('已确认'), value: 4 },
  { text: i18n.t('已退回'), value: 5 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'recoNo',
    title: i18n.t('往来对账单号'),
    minWidth: 120,
    slots: {
      default: 'recoNoDetault'
    }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('公司'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商'),
    minWidth: 160,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : ''
    }
  },
  {
    field: 'deadlineDate',
    title: i18n.t('截止日期'),
    minWidth: 160
  },
  {
    field: 'createTime',
    title: i18n.t('创建日期'),
    minWidth: 160
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  }
]
