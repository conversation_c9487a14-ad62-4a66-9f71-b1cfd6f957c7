<!-- 对账明细 -->
<template>
  <div class="vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item v-if="!isSup" prop="factoryCodeList" :label="$t('工厂编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item v-if="isSup" prop="factoryCodeList" :label="$t('工厂编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCodeList"
            url="/masterDataManagement/auth/site/auth-fuzzy"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
            params-key="fuzzyParam"
            records-position="data"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择物料编码')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item v-if="isSup" prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemAuthUrl"
            :placeholder="$t('请选择物料编码')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierCode" :label="$t('供应商编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <sc-table
        ref="supplierDetailSctableRef"
        :grid-id="gridId"
        :loading="loading"
        :is-show-refresh-bth="true"
        :columns="columns"
        :table-data="tableData"
        @refresh="handleSearch"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
    </div>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  name: 'SupplierDetail',
  props: {
    isSup: {
      type: Boolean,
      default: false
    }
  },
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      loading: false,
      toolbar: [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      tableData: []
    }
  },
  computed: {
    currentIndex() {
      return this.$route.query?.currentIndex ?? 0
    },
    tableRef() {
      return this.$refs.supplierDetailSctableRef.$refs.xGrid
    },
    columns() {
      let column = [
        {
          field: 'factoryCode',
          title: this.$t('工厂编码')
        },
        {
          field: 'supplierCode',
          title: this.$t('供应商编码')
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          minWidth: 160
        },
        {
          field: 'sourceOrderCode',
          title: this.$t('采购订单号'),
          minWidth: 120
        },
        {
          field: 'itemCode',
          title: this.$t('物料编码')
        },
        {
          field: 'itemName',
          title: this.$t('物料名称')
        },
        {
          field: 'materialVoucherItemNo',
          title: this.$t('行项目')
        },
        {
          field: 'location',
          title: this.$t('库位')
        },
        {
          field: 'materialVoucherDate',
          title: this.$t('入库时间')
        },
        {
          field: 'qty',
          title: this.$t('入库数量')
        },
        {
          field: 'currencyCode',
          title: this.$t('币种'),
          minWidth: 80
        },
        {
          field: 'unitPriceUntaxed',
          title: this.$t('原单价')
        },
        {
          field: 'executionUnitPriceUntaxed',
          title: this.$t('应执行单价')
        },
        {
          field: 'totalAmtUntaxed',
          title: this.$t('原金额'),
          minWidth: 80
        },
        {
          field: 'executionTotalPriceUntaxed',
          title: this.$t('应执行金额')
        },
        {
          field: 'deductionAmtUntaxed',
          title: this.$t('扣款金额')
        },
        {
          field: 'receiveCode',
          title: this.$t('入库单号')
        },
        {
          field: 'materialVoucherItemNo',
          title: this.$t('入库单行号')
        },
        {
          field: 'deliveryCode',
          title: this.$t('送货单号')
        },
        {
          field: 'recordDate',
          title: this.$t('记录日期')
        },
        {
          field: 'supplierRemark',
          title: this.$t('供应商备注')
        },
        {
          field: 'buyerHistoryRemark',
          title: this.$t('采购方历史备注'),
          minWidth: 140
        },
        {
          field: 'buyerRemark',
          title: this.$t('采购方备注'),
          minWidth: 120
        }
      ]
      if (this.currentIndex === 1) {
        column = [
          {
            field: 'factoryCode',
            title: this.$t('工厂代码')
          },
          {
            field: 'supplierCode',
            title: this.$t('供应商编码')
          },
          {
            field: 'supplierName',
            title: this.$t('供应商名称'),
            minWidth: 160
          },
          {
            field: 'itemCode',
            title: this.$t('物料编码')
          },
          {
            field: 'itemName',
            title: this.$t('物料名称'),
            minWidth: 120
          },
          {
            field: 'materialVoucherItemNo',
            title: this.$t('行项目')
          },
          {
            field: 'receiveCode',
            title: this.$t('出库单号')
          },
          {
            field: 'location',
            title: this.$t('库位')
          },
          {
            field: 'materialVoucherDate',
            title: this.$t('出库日期')
          },
          {
            field: 'qty',
            title: this.$t('出库数量')
          },
          {
            field: 'currencyCode',
            title: this.$t('币种')
          },
          {
            field: 'unitPriceUntaxed',
            title: this.$t('原单价')
          },
          {
            field: 'executionUnitPriceUntaxed',
            title: this.$t('应执行单价')
          },
          {
            field: 'totalAmtUntaxed',
            title: this.$t('原金额')
          },
          {
            field: 'executionTotalPriceUntaxed',
            title: this.$t('应执行金额')
          },
          {
            field: 'deductionAmtUntaxed',
            title: this.$t('扣款金额')
          },
          {
            field: 'initialStageInv',
            title: this.$t('期初库存')
          },
          {
            field: 'curMonthInstockQty',
            title: this.$t('本月入库')
          },
          {
            field: 'curMonthOutstockQty',
            title: this.$t('本月出库')
          },
          {
            field: 'curMonthAllotQty',
            title: this.$t('本月移库')
          },
          {
            field: 'curMonthBalanceQty',
            title: this.$t('本月结余')
          },
          {
            field: 'recordDate',
            title: this.$t('记录日期')
          },
          {
            field: 'supplierRemark',
            title: this.$t('供应商备注')
          },
          {
            field: 'buyerHistoryRemark',
            title: this.$t('采购方历史备注'),
            minWidth: 140
          },
          {
            field: 'buyerRemark',
            title: this.$t('采购方备注'),
            minWidth: 120
          }
        ]
      }
      return column
    },
    gridId() {
      let id = 'fc80f877-3a8e-470b-ba83-ca7365f12466'
      if (this.currentIndex === 1) {
        id = 'ea422e96-cadf-4ec3-80e5-2dfa5560f0c9'
      }
      return id
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    recoTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['startTime'] = dayjs(e.startDate).format('YYYYMM')
        this.searchFormModel['endTime'] = dayjs(e.endDate).format('YYYYMM')
      } else {
        this.searchFormModel['startTime'] = null
        this.searchFormModel['endTime'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        headerId: this.$route?.query?.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.reconciliationSettlement.pageVietnamReconDetailStandardApi
      if (this.currentIndex === 1) {
        api = this.$API.reconciliationSettlement.pageVietnamReconDetailConsignmentApi
      }

      this.loading = true
      const res = await api(params).catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        headerId: this.$route?.query?.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      let api = this.$API.reconciliationSettlement.exportVietnamReconDetailStandardApi
      if (this.currentIndex === 1) {
        api = this.$API.reconciliationSettlement.exportVietnamReconDetailConsignmentApi
      }

      api(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    }
  }
}
</script>
