import Vue from 'vue'
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { timeDate } from '@/views/reconciliationSettlement/purchaseRecon/beRecon/beReconciled/config/columnComponent'

// 同步状态
export const SyncStatus = {
  synced: 1, // 是
  notSynced: 0 // 否
}

export const formatTableColumnData = (config) => {
  const { data, tabCode } = config
  const colData = [
    {
      width: '150',
      headerText: i18n.t('序号'),
      field: 'index',
      valueConverter: {
        type: 'function',
        filter: (e, row) => {
          return Number(+row.index + 1)
        }
      },
      ignore: true
    }
  ]

  // 待对账明细
  data.forEach((col) => {
    const defaultCol = {
      field: col.code,
      headerText: col.name,
      width: data.length > 10 ? '150' : 'auto',
      ignore: tabCode === 'RTC006' // 标准页签中的字段默认都不作为快捷查询字段出现，只有个别字段需要
    }
    if (defaultCol.field === 'companyCode') {
      defaultCol.ignore = false
      defaultCol.searchOptions = {
        ...MasterDataSelect.businessCompany
      }
    }
    if (defaultCol.field === 'supplierCode') {
      defaultCol.ignore = false
      defaultCol.searchOptions = {
        ...MasterDataSelect.supplierAll
      }
    }
    if (defaultCol.field === 'advanceInvoicing') {
      // 提前开票
      defaultCol.valueConverter = {
        type: 'map',
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    } else if (defaultCol.field === 'syncStatus') {
      // 同步状态
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: SyncStatus.notSynced,
            text: i18n.t('未同步'),
            cssClass: 'col-notSynced'
          },
          {
            value: SyncStatus.synced,
            text: i18n.t('已同步'),
            cssClass: 'col-synced'
          }
        ]
      }
    } else if (defaultCol.field === 'receiveTime') {
      // 收货时间
      defaultCol.ignore = false // 标准页签的设定
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'priceDateRangeStartDate') {
      // 价格有效期开始
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.ignore = true
      defaultCol.searchOptions = MasterDataSelect.timeOnly
    } else if (defaultCol.field === 'priceDateRangeEndDate') {
      // 价格有效期结束
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.ignore = true
      defaultCol.searchOptions = MasterDataSelect.timeOnly
    } else if (defaultCol.field === 'fileBaseInfoList') {
      // 文件信息
      defaultCol.template = () => {
        return {
          template: Vue.component('fileBaseInfoList', {
            template: `<div @click="showFileBaseInfo" class="cell-operable-title">{{data.fileBaseInfoList | listNumFormat}}</div>`,
            data: function () {
              return { data: {} }
            },
            filters: {
              listNumFormat(value) {
                if (value && value.length > 0) {
                  return value.length
                } else {
                  return ''
                }
              }
            },
            methods: {
              showFileBaseInfo() {
                this.$parent.$emit('showFileBaseInfo', {
                  index: this.data.index,
                  value: this.data.fileBaseInfoList
                })
              }
            }
          })
        }
      }
    } else if (defaultCol.field === 'sourceType') {
      // 来源类型（枚举） 0: 上游流入1:第三方接口
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('上游流入'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('第三方接口'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'status') {
      // 单据状态 0:待对账 1:已创建对账单
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('已创建对账单'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'prePayStatus') {
      // 是否预付 0-否；1-是
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('否'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('是'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'itemVoucherDate') {
      // 物料凭证日期
      defaultCol.ignore = false // 标准页签的设定
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'provisionalEstimateStatus') {
      if (defaultCol.headerText == '是否执行价') {
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('是'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('否'),
              cssClass: ''
            }
          ]
        }
      } else {
        defaultCol.valueConverter = {
          type: 'map',
          map: [
            {
              value: 0,
              text: i18n.t('否'),
              cssClass: ''
            },
            {
              value: 1,
              text: i18n.t('是'),
              cssClass: ''
            }
          ]
        }
      }
    } else if (defaultCol.field === 'createTime') {
      // 创建时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'updateTime') {
      // 最后修改时间
      defaultCol.template = timeDate({
        dataKey: defaultCol.field,
        hasTime: true
      })
      defaultCol.searchOptions = MasterDataSelect.dateRange
    } else if (defaultCol.field === 'type') {
      // 待对账类型:0-采购待对账；1-销售待对账
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购待对账'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('销售待对账'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'realPriceStatus') {
      // 正式价标识 0-无正式价；1-有正式价
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('无正式价'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('有正式价'),
            cssClass: ''
          }
        ]
      }
    } else if (defaultCol.field === 'inOutType') {
      // 出入库类型 0-采购入库；1-采购出库；2-销售出库；3-销售退回
      defaultCol.valueConverter = {
        type: 'map',
        map: [
          {
            value: 0,
            text: i18n.t('采购入库'),
            cssClass: ''
          },
          {
            value: 1,
            text: i18n.t('采购出库'),
            cssClass: ''
          },
          {
            value: 2,
            text: i18n.t('销售出库'),
            cssClass: ''
          },
          {
            value: 3,
            text: i18n.t('销售退回'),
            cssClass: ''
          }
        ]
      }
    }
    if (defaultCol.field != 'syncStatus') {
      colData.push(defaultCol)
    }
  })

  if (tabCode === 'RTC006') {
    colData.push({
      width: '150',
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      template: timeDate({
        dataKey: 'createTime',
        hasTime: true
      }),
      searchOptions: MasterDataSelect.dateRange
    })
  }

  return colData
}
