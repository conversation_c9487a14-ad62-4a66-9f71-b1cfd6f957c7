<template>
  <!-- 采方-对账历史查询 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="componentConfig"
      :current-tab="currentTab"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { formatTableColumnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      componentConfig: [],
      currentTab: 0,
      asyncOriginTabs: []
    }
  },

  mounted() {
    this.getReconTabs()
  },

  methods: {
    getReconTabs() {
      this.$API.reconciliationSettlement.getTypeTab().then((res) => {
        this.asyncOriginTabs = res.data
        this.setAsyncTabs()
      })
    },

    setAsyncTabs() {
      let _asyncTabs = []
      this.asyncOriginTabs.forEach((item) => {
        let _cols = formatTableColumnData({
          data: item.fields,
          tabCode: item.code
        })
        const toolbar = [
          {
            id: 'download',
            icon: 'icon_solid_export',
            title: this.$t('导出')
          }
        ]
        let _oneTab = {
          title: item.name,
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          toolbar: item.code === 'RTC006' ? toolbar : [],
          gridId: this.$md5(
            this.$tableUUID.reconciliationSettlement.reconciliationHistory.list + item.code
          ),
          grid: {
            columnData: _cols,
            dataSource: [],
            // 对账单主单接口-查询-对账历史信息
            asyncConfig: {
              url: `${BASE_TENANT}/reconciliationItem/queryBuilderHistory`,
              defaultRules: [
                {
                  label: this.$t('对账类型编码'),
                  field: 'reconciliationTypeCode',
                  type: 'string',
                  operator: 'equal',
                  value: item.code
                },
                {
                  label: this.$t('业务类型编码'),
                  field: 'businessTypeCode',
                  type: 'string',
                  operator: 'equal',
                  value: item.businessTypeCode
                }
              ]
            }
          }
        }
        _asyncTabs.push(_oneTab)
      })
      this.componentConfig = _asyncTabs
    },
    handleClickToolBar(e) {
      if (e.toolbar.id == 'download') {
        this.handleDownload()
      }
    },
    // 导出
    handleDownload() {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10000 },
        pageFlag: true,
        rules: rule.rules || [],
        defaultRules: [
          {
            label: this.$t('对账类型编码'),
            field: 'reconciliationTypeCode',
            type: 'string',
            operator: 'equal',
            value: 'RTC006'
          },
          {
            label: this.$t('业务类型编码'),
            field: 'businessTypeCode',
            type: 'string',
            operator: 'equal',
            value: 'BTTCL004'
          }
        ]
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .exoprtHistoryStander(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .catch(() => {
          this.$toast({
            content: this.$t('导出异常，请联系管理员'),
            type: 'error'
          })
        })
    }
  }
}
</script>

<style></style>
