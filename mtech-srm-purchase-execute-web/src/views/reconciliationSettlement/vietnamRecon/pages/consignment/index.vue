<!-- 寄售 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="companyCodes" :label="$t('公司')">
          <RemoteAutocomplete
            v-if="isSup"
            v-model="searchFormModel.companyCodes"
            url="/masterDataManagement/auth/company/auth-fuzzy"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
            records-position="data"
          />
          <RemoteAutocomplete
            v-else
            v-model="searchFormModel.companyCodes"
            url="/masterDataManagement/tenant/organization/specified-level-paged-query"
            multiple
            :placeholder="$t('请选择')"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :params="{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="materialVoucherYear" :label="$t('对账日期')">
          <mt-date-range-picker
            style="flex: 1"
            v-model="searchFormModel.materialVoucherYear"
            @change="(e) => handleDateTimeChange(e, 'materialVoucherYear')"
            :placeholder="$t('请选择对账日期')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否月结对账单')" prop="dataFlag">
          <mt-select
            v-model="searchFormModel.dataFlag"
            :data-source="dataFlagOptions"
            :fields="{ text: 'text', value: 'value' }"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="reconciliationCode" :label="$t('对账单号')">
          <mt-input
            v-model="searchFormModel.reconciliationCode"
            :show-clear-button="true"
            :placeholder="$t('请输入对账单号')"
          />
        </mt-form-item>
        <mt-form-item prop="factoryCodes" :label="$t('工厂')">
          <RemoteAutocomplete
            v-if="isSup"
            v-model="searchFormModel.factoryCodes"
            url="/masterDataManagement/auth/site/auth-fuzzy"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
          <RemoteAutocomplete
            v-else
            v-model="searchFormModel.factoryCodes"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择物料')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierCodes" :label="$t('供应商编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="recoTypeCodes" :label="$t('对账类型')">
          <mt-multi-select
            v-model="searchFormModel.recoTypeCodes"
            :data-source="recoTypeOptions"
            :show-clear-button="false"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="477d9abe-749a-4bf8-8aa5-32e1a594b302"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #recoCodeDetault="{ row }">
        <div :class="['able-click-field']" @click="recoCodeClick(row)">
          {{ row.recoCode }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, recoTypeOptions, dataFlagOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import Vue from 'vue'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    const startDate = dayjs().subtract(3, 'month').startOf('month')
    return {
      searchFormModel: {
        materialVoucherYear: [new Date(startDate), new Date()],
        materialVoucherYearEnd: dayjs(new Date()).format('YYYYMMDD'),
        materialVoucherYearStart: dayjs(new Date(startDate)).format('YYYYMMDD'),
        recoTypeCodes: ['B', 'D'],
        dataFlag: 0
      },
      searchFormRules: {},
      tableData: [],
      loading: false,
      columns: columnData,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },

      statusOptions,
      recoTypeOptions,
      dataFlagOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    isSup() {
      return this.$route.name === 'vietnam-recon-sup'
    },
    toolbar() {
      let btns = [
        { code: 'rePublish', name: this.$t('重新发布'), status: 'info', loading: false },
        { code: 'cancel', name: this.$t('作废'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
        { code: 'print', name: this.$t('打印'), status: 'info', loading: false }
      ]
      if (this.isSup) {
        btns = [
          { code: 'confirm', name: this.$t('确认'), status: 'info', loading: false },
          { code: 'return', name: this.$t('退回'), status: 'info', loading: false },
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
          { code: 'print', name: this.$t('打印'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    if (this.$store.state.vnReconConsignmentSearch) {
      this.searchFormModel = this.$store.state.vnReconConsignmentSearch
    }
    this.getTableData()
  },
  beforeDestroy() {
    this.$store.commit('setVnReconConsignmentSearch', null)
  },
  methods: {
    recoCodeClick(row) {
      if (this.isSup) {
        this.$router.push({
          name: 'sup-recon-detail-vn',
          query: {
            id: row.id,
            code: row.recoCode,
            currentIndex: 1,
            timeStamp: new Date().getTime()
          }
        })
      } else {
        this.$router.push({
          name: 'pur-recon-detail-vn',
          query: {
            id: row.id,
            code: row.recoCode,
            type: 'vietnam-recon',
            currentIndex: 1,
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = dayjs(e.startDate).format('YYYYMMDD')
        this.searchFormModel[field + 'End'] = dayjs(e.endDate).format('YYYYMMDD')
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.searchFormModel.dataFlag = 0
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      this.$store.commit('setVnReconConsignmentSearch', this.searchFormModel)
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      if (this.searchFormModel.materialVoucherYear) {
        params.materialVoucherYearStart =
          this.searchFormModel.dataFlag === 1
            ? dayjs(this.searchFormModel.materialVoucherYear[0]).format('YYYYMM')
            : dayjs(this.searchFormModel.materialVoucherYear[0]).format('YYYYMMDD')
        params.materialVoucherYearEnd =
          this.searchFormModel.dataFlag === 1
            ? dayjs(this.searchFormModel.materialVoucherYear[1]).format('YYYYMM')
            : dayjs(this.searchFormModel.materialVoucherYear[1]).format('YYYYMMDD')
      }
      this.loading = true
      const res = await this.$API.reconciliationSettlement
        .pageVietnamReconConsignmentApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['rePublish', 'cancel', 'delete', 'print', 'confirm', 'return']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.code === 'rePublish' && selectedRecords.some((v) => v.status !== 3)) {
        this.$toast({
          content: this.$t('已退回状态的数据才能重新发布'),
          type: 'warning'
        })
        return
      }
      if (e.code === 'cancel' && selectedRecords.some((v) => [2, 4].includes(v.status))) {
        this.$toast({
          content: this.$t('已确认、已作废状态的数据不能作废'),
          type: 'warning'
        })
        return
      }
      if (e.code === 'confirm' && selectedRecords.some((v) => v.status !== 1)) {
        this.$toast({
          content: this.$t('未确认状态的数据才能确认'),
          type: 'warning'
        })
        return
      }
      if (e.code === 'return' && selectedRecords.some((v) => v.status !== 1)) {
        this.$toast({
          content: this.$t('未确认状态的数据才能退回'),
          type: 'warning'
        })
        return
      }
      if (e.code === 'print' && selectedRecords.length !== 1) {
        this.$toast({
          content: this.$t('仅支持单行打印'),
          type: 'warning'
        })
        return
      }
      switch (e.code) {
        case 'rePublish':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认重新发布？')
            },
            success: () => {
              this.handleRePublish(selectedRecords)
            }
          })
          break
        case 'cancel':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认作废？')
            },
            success: () => {
              this.handleCancel(selectedRecords)
            }
          })
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'confirm':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('是否确认？')
            },
            success: () => {
              this.handleConfirm(selectedRecords)
            }
          })
          break
        case 'return':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认退回？')
            },
            success: () => {
              this.handleReturn(selectedRecords)
            }
          })
          break
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'print':
          e.loading = true
          this.handlePrint(selectedRecords, e)
          break
        default:
          break
      }
    },
    handleRePublish(selectedRecords) {
      let idList = selectedRecords.map((v) => v.id)
      this.$API.reconciliationSettlement.rePublishVietnamReconApi({ idList }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('发布成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleCancel(selectedRecords) {
      let idList = selectedRecords.map((v) => v.id)
      this.$API.reconciliationSettlement.discardVietnamReconApi({ idList }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('作废成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleDelete(selectedRecords) {
      let idList = selectedRecords.map((v) => v.id)
      this.$API.reconciliationSettlement.deleteVietnamReconApi({ idList }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleConfirm(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.reconciliationSettlement
        .updateStatusVietnamReconApi({ ids, status: 2 })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('确认成功'), type: 'success' })
            this.handleSearch()
          }
        })
    },
    handleReturn(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.reconciliationSettlement
        .updateStatusVietnamReconApi({ ids, status: 3 })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('退回成功'), type: 'success' })
            this.handleSearch()
          }
        })
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .exportVietnamReconConsignmentApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handlePrint(selectedRecords, e) {
      let row = selectedRecords[0]
      let params = {
        headerId: row.id
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .queryFactoryCodesVietnamReconApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code === 200) {
            if (row.companyCode === '0602') {
              this.batchPrint(row.id, res.data, row.recoTypeCode)
            } else {
              for (let i = 0; i < res.data.length; i++) {
                this.batchPrint(row.id, res.data[i], row.recoTypeCode)
              }
            }
          }
        })
        .finally(() => {
          e.loading = false
          this.$store.commit('endLoading')
        })
    },
    batchPrint(headerId, factoryCode, recoTypeCode) {
      this.$store.commit('startLoading')
      this.getPrintApi(recoTypeCode)({ headerId, factoryCode })
        .then((res) => {
          this.$store.commit('endLoading')
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          window.open(this.pdfUrl)
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    getPrintApi(recoTypeCode) {
      if (recoTypeCode === 'C') {
        return this.$API.reconciliationSettlement.printVietnamReconConsignRetrospectApi
      }
      return this.$API.reconciliationSettlement.printVietnamReconConsignmentApi
    }
  }
}
</script>
