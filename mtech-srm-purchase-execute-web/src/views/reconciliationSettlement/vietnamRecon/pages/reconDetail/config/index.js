import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('是否创建对账单'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂编码')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 160
  },
  {
    field: 'sourceOrderCode',
    title: i18n.t('采购订单号'),
    minWidth: 120
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'materialVoucherItemNo',
    title: i18n.t('行项目')
  },
  {
    field: 'location',
    title: i18n.t('库位')
  },
  {
    field: 'materialVoucherDate',
    title: i18n.t('入库时间')
  },
  {
    field: 'qty',
    title: i18n.t('入库数量')
  },
  {
    field: 'currencyCode',
    title: i18n.t('币种'),
    minWidth: 80
  },
  {
    field: 'unitPriceUntaxed',
    title: i18n.t('原单价')
  },
  {
    field: 'executionUnitPriceUntaxed',
    title: i18n.t('应执行单价')
  },
  {
    field: 'totalAmtUntaxed',
    title: i18n.t('原金额'),
    minWidth: 80
  },
  {
    field: 'executionTotalPriceUntaxed',
    title: i18n.t('应执行金额')
  },
  {
    field: 'deductionAmtUntaxed',
    title: i18n.t('扣款金额')
  },
  {
    field: 'receiveCode',
    title: i18n.t('入库单号')
  },
  {
    field: 'materialVoucherItemNo',
    title: i18n.t('入库单行号')
  },
  {
    field: 'deliveryCode',
    title: i18n.t('送货单号')
  },
  {
    field: 'recordDate',
    title: i18n.t('记录日期')
  },
  {
    field: 'supplierRemark',
    title: i18n.t('供应商备注')
  },
  {
    field: 'buyerHistoryRemark',
    title: i18n.t('采购方历史备注'),
    minWidth: 140
  },
  {
    field: 'buyerRemark',
    title: i18n.t('采购方备注'),
    minWidth: 120
  }
]
