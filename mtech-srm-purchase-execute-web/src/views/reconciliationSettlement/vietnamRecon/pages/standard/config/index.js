import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('未确认'), value: 1, cssClass: 'col-abnormal' },
  { text: i18n.t('已确认'), value: 2, cssClass: 'col-active' },
  { text: i18n.t('已退回'), value: 3, cssClass: 'col-normal' },
  { text: i18n.t('已作废'), value: 4, cssClass: 'col-published' }
]

export const recoTypeOptions = [
  { text: i18n.t('标准对账单'), value: 'A' },
  { text: i18n.t('标准追溯对账单'), value: 'C' }
]

export const dataFlagOptions = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    },
    className: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.cssClass : ''
    }
  },
  {
    field: 'companyName',
    title: i18n.t('公司名称'),
    minWidth: 140
  },
  {
    field: 'recoCode',
    title: i18n.t('对账单号'),
    minWidth: 140,
    slots: {
      default: 'recoCodeDetault'
    }
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'recoTypeCode',
    title: i18n.t('对账类型'),
    formatter: ({ cellValue }) => {
      let item = recoTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'materialVoucherYear',
    title: i18n.t('对账日期')
  },
  {
    field: 'dataFlag',
    title: i18n.t('是否月结对账单'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = dataFlagOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 120
  }
]
