<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :width="500"
    :height="400"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <RemoteAutocomplete
            v-model="formObject.supplierCode"
            url="/masterDataManagement/tenant/supplier/paged-query"
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="year" :label="$t('年')">
          <mt-input-number
            v-model="formObject.year"
            :placeholder="$t('请输入')"
            :show-spin-button="false"
            :min="1"
          ></mt-input-number>
        </mt-form-item>
        <mt-form-item prop="month" :label="$t('月')">
          <mt-select
            v-model="formObject.month"
            :data-source="monthOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        supplierCode: null,
        year: null,
        month: null
      },
      //必填项
      formRules: {
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商'),
            trigger: 'blur'
          }
        ],
        year: [
          {
            required: true,
            message: this.$t('请输入年份'),
            trigger: 'blur'
          }
        ],
        month: [
          {
            required: true,
            message: this.$t('请选择月份'),
            trigger: 'blur'
          }
        ]
      },
      monthOptions: [
        { text: '1', value: 1 },
        { text: '2', value: 2 },
        { text: '3', value: 3 },
        { text: '4', value: 4 },
        { text: '5', value: 5 },
        { text: '6', value: 6 },
        { text: '7', value: 7 },
        { text: '8', value: 8 },
        { text: '9', value: 9 },
        { text: '10', value: 10 },
        { text: '11', value: 11 },
        { text: '12', value: 12 }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 20px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
