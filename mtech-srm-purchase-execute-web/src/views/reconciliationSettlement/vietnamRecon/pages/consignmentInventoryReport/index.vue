<!-- 寄售进出存报表 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      :default-expand="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item prop="factoryCodes" :label="$t('工厂')">
          <RemoteAutocomplete
            v-if="isSup"
            v-model="searchFormModel.factoryCodes"
            url="/masterDataManagement/auth/site/auth-fuzzy"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          />
          <RemoteAutocomplete
            v-else
            v-model="searchFormModel.factoryCodes"
            :url="$API.masterData.getSiteListUrl"
            multiple
            :placeholder="$t('请选择工厂')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            :search-fields="['siteName', 'siteCode']"
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('物料编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.itemCode"
            :url="$API.masterData.getItemUrl"
            :placeholder="$t('请选择物料')"
            :fields="{ text: 'itemName', value: 'itemCode' }"
            :search-fields="['itemName', 'itemCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item prop="materialVoucherYear" :label="$t('年度')">
          <mt-select
            v-model="searchFormModel.materialVoucherYear"
            :data-source="yearList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择年度')"
          />
        </mt-form-item>
        <mt-form-item prop="materialVoucherMonth" :label="$t('月份')">
          <mt-select
            v-model="searchFormModel.materialVoucherMonth"
            :data-source="monthList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择月份')"
          />
        </mt-form-item>
        <mt-form-item v-if="!isSup" prop="supplierCodes" :label="$t('供应商编码')">
          <RemoteAutocomplete
            v-model="searchFormModel.supplierCodes"
            url="/masterDataManagement/tenant/supplier/paged-query"
            multiple
            :placeholder="$t('请选择供应商')"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :search-fields="['supplierName', 'supplierCode']"
          ></RemoteAutocomplete>
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="a16c4dbf-1c97-4709-b536-1def004b6e34"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, yearList, monthList } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
import Vue from 'vue'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    const lastMonth = dayjs().subtract(1, 'month').format('YYYY-MM')
    return {
      searchFormModel: {
        materialVoucherYear: lastMonth.split('-')[0],
        materialVoucherMonth: lastMonth.split('-')[1]
      },
      searchFormRules: {
        materialVoucherYear: [{ required: true, message: this.$t('请选择年度'), trigger: 'blur' }],
        materialVoucherMonth: [{ required: true, message: this.$t('请选择月份'), trigger: 'blur' }],
        supplierCodes: [{ required: true, message: this.$t('请选择供应商'), trigger: 'blur' }]
      },
      tableData: [],
      loading: false,
      columns: columnData,
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },

      yearList,
      monthList
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    isSup() {
      return this.$route.name === 'vietnam-recon-sup'
    },
    toolbar() {
      let btns = [{ code: 'export', name: this.$t('导出'), status: 'info', loading: false }]
      if (this.isSup) {
        btns = [
          { code: 'export', name: this.$t('导出'), status: 'info', loading: false },
          { code: 'print', name: this.$t('打印'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  methods: {
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.reconciliationSettlement
        .pageVietnamReconReportApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['print']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.code === 'print') {
        if (!this.searchFormModel.materialVoucherYear) {
          this.$toast({ content: this.$t('请选择年度'), type: 'warning' })
          return
        }
        if (!this.searchFormModel.materialVoucherMonth) {
          this.$toast({ content: this.$t('请选择月份'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'export':
          e.loading = true
          this.handleExport(e)
          break
        case 'print':
          e.loading = true
          this.handlePrint(e)
          break
        default:
          break
      }
    },
    handleExport(e) {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .exportVietnamReconReportApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handlePrint(e) {
      let params = {
        materialVoucherYear: this.searchFormModel.materialVoucherYear,
        materialVoucherMonth: this.searchFormModel.materialVoucherMonth
      }
      this.$store.commit('startLoading')
      this.$API.reconciliationSettlement
        .headerIdsVietnamReconReportApi(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res.code === 200) {
            for (let i = 0; i < res.data.length; i++) {
              this.batchPrint(res.data[i])
            }
          }
        })
        .finally(() => {
          e.loading = false
          this.$store.commit('endLoading')
        })
    },
    batchPrint(headerId) {
      this.$store.commit('startLoading')
      let params = {
        materialVoucherYear: this.searchFormModel.materialVoucherYear,
        materialVoucherMonth: this.searchFormModel.materialVoucherMonth,
        headerId
      }
      this.getPrintApi()(params)
        .then((res) => {
          this.$store.commit('endLoading')
          if (res?.data?.type === 'application/json') {
            const reader = new FileReader()
            reader.readAsText(res?.data, 'utf-8')
            reader.onload = () => {
              const readerRes = reader.result
              const resObj = JSON.parse(readerRes)
              Vue.prototype.$toast({
                content: resObj.msg,
                type: 'error'
              })
            }

            return
          }
          const content = res.data
          this.pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))
          window.open(this.pdfUrl)
        })
        .catch(() => {
          this.$store.commit('endLoading')
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    getPrintApi() {
      return this.$API.reconciliationSettlement.printVietnamReconReportApi
    }
  }
}
</script>
