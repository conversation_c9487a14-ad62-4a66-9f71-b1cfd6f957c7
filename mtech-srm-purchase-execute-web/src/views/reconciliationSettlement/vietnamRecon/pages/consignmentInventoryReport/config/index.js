import { i18n } from '@/main.js'

export const monthList = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']

const currentYear = new Date().getFullYear()
const years = []
for (let i = 0; i < 19; i++) {
  const year = currentYear + 2 - i
  years.push(String(year))
}
export const yearList = years

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'materialVoucherYear',
    title: i18n.t('对账日期')
  },
  {
    field: 'factoryCode',
    title: i18n.t('工厂代码')
  },
  {
    field: 'supplierCode',
    title: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    minWidth: 140
  },
  {
    field: 'itemCode',
    title: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    title: i18n.t('物料名称')
  },
  {
    field: 'initialStageInv',
    title: i18n.t('期初库存')
  },
  {
    field: 'curMonthInstockQty',
    title: i18n.t('本月入库')
  },
  {
    field: 'curMonthOutstockQty',
    title: i18n.t('本月出库')
  },
  {
    field: 'curMonthAllotQty',
    title: i18n.t('本月移库')
  },
  {
    field: 'curMonthBalanceQty',
    title: i18n.t('本月结余')
  }
]
