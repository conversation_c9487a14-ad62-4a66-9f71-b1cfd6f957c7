<!-- 越南日结对账 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div class="toggle-content">
      <standard v-show="tabIndex === 0" />
      <consignment v-show="tabIndex === 1" />
      <reconDetail v-show="!isSup && tabIndex === 2" />
      <consignmentInventoryReport v-show="tabIndex === (isSup ? 2 : 3)" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    standard: () => import('./pages/standard/index.vue'),
    consignment: () => import('./pages/consignment/index.vue'),
    reconDetail: () => import('./pages/reconDetail/index.vue'),
    consignmentInventoryReport: () => import('./pages/consignmentInventoryReport/index.vue')
  },
  data() {
    return {
      selectedItem: this.$route.query.currentIndex ?? 0,
      tabIndex: this.$route.query.currentIndex ?? 0
    }
  },
  computed: {
    isSup() {
      return this.$route.name === 'vietnam-recon-sup'
    },
    tabList() {
      let list = [
        { title: this.$t('标准') },
        { title: this.$t('寄售') },
        { title: this.$t('待对账明细') },
        { title: this.$t('寄售进出存报表') }
      ]
      if (this.isSup) {
        list = [
          { title: this.$t('标准') },
          { title: this.$t('寄售') },
          { title: this.$t('寄售进出存报表') }
        ]
      }
      return list
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
