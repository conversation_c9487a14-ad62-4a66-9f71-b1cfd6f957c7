<template>
  <div class="workbench-kanban">
    <div class="header">
      <button ref="btn" class="header-logo" />
      <span class="title">{{ title }}</span>
      <span class="date">{{ dateParam }}</span>
    </div>
    <div class="query-box header-right">
      <span class="query-item" style="margin-right: 10px; text-align: right">
        <i
          ref="scrRef"
          class="mt-icons mt-icon-a-icon_Fullscreen full-screen"
          @click="toggleScreen"
        ></i>
      </span>
    </div>
    <div class="content">
      <div class="left">
        <div class="top">
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('项目管理') }}</span>
            </div>
            <div class="card-content">
              <div class="card-content-item" v-for="(item, key) in projectList" :key="key">
                <div @click="handleClick(item.url, 'plm')">{{ item.title }}</div>
              </div>
            </div>
          </div>
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('模具台账管理') }}</span>
            </div>
            <div class="card-content">
              <div class="card-content-item" v-for="(item, key) in menuList" :key="key">
                <div @click="handleClick(item.url)">{{ item.title }}</div>
              </div>
            </div>
          </div>
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('模具使用管理') }}</span>
            </div>
            <div class="card-content">
              <div class="card-content-item" v-for="(item, key) in useList" :key="key">
                <div @click="handleClick(item.url, 'mes')">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="middle">
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('模具总数') }}</span>
            </div>
            <div class="card-content" style="height: calc(100% - 32px)">
              <div class="card-content-item">
                <span class="title">{{ $t('模具总数量') }}</span>
                <span class="number">{{ total }}</span>
                <span class="unit">{{ $t('套') }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('模具全景') }}</span>
            </div>
            <div class="card-content">
              <div class="card-content-item" v-for="(item, key) in statusList" :key="key">
                <div class="left">
                  <div class="icon" :class="'icon_' + (key + 1)"></div>
                </div>
                <div class="right">
                  <div class="title">{{ item.title }}</div>
                  <div class="number">
                    {{ item.number }}<span class="unit">{{ $t('台') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="top">
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('待办提醒') }}</span>
            </div>
            <div class="card-content" style="height: calc(100% - 32px)">
              <div class="card-content-item" v-for="(item, key) in agencyList" :key="key">
                <div @click="todoClick(item.urlRef)" class="title">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="middle">
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('业务管理') }}</span>
            </div>
            <div class="card-content" style="height: calc(100% - 32px)">
              <div class="card-content-item" v-for="(item, key) in businessList" :key="key">
                <div class="left">
                  <div class="bg" :class="'bg_' + (key + 1)"></div>
                </div>
                <div class="right">
                  <div class="title">{{ item.title }}</div>
                  <div class="number">
                    {{ item.number }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="card-item">
            <div class="sub-title">
              <span class="part-icon">{{ $t('通知') }}</span>
            </div>
            <div class="card-content" style="height: calc(100% - 32px)">
              <div class="card-content-item" v-for="(item, key) in noticeList" :key="key">
                <div class="seq">{{ key + 1 }}</div>
                <div class="title">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      title: '空调模具工作台',
      timestamp: null,
      projectList: [
        {
          title: this.$t('开模申请'),
          url: 'https://ktplm.tcl.com/tcl/moldManagement/application/list?origin=menu'
        },
        // { title: this.$t('模具图纸'), url: '' },
        {
          title: this.$t('立项申请'),
          url: 'https://ktplm.tcl.com/tcl/index'
        },
        {
          title: this.$t('改模申请'),
          url: 'https://ktplm.tcl.com/tcl/moldManagement/application/list?origin=menu'
        }
      ],
      menuList: [
        { title: this.$t('模具台账'), url: 'mould-page' },
        // { title: this.$t('模具图纸'), url: '' },
        { title: this.$t('模具履历'), url: 'mold-history-record' },
        { title: this.$t('交货数统计'), url: 'delivery-quantity-statistics' },
        { title: this.$t('审厂管理'), url: 'factory-audit-management' }
      ],
      useList: [
        {
          title: this.$t('调拨管理'),
          url: 'https://wx-mes-web.tcl.com/#/tcl/mes/mouldlifecyclemanage/mouldallocate'
        },
        {
          title: this.$t('保养管理'),
          url: 'https://wx-mes-web.tcl.com/#/tcl/mes/mouldlifecyclemanage/maintenancerecord'
        },
        {
          title: this.$t('维修管理'),
          url: 'https://wx-mes-web.tcl.com/#/tcl/mes/mouldlifecyclemanage/mouldmaintain'
        },
        {
          title: this.$t('状况评估'),
          url: 'https://wx-mes-web.tcl.com/#/tcl/mes/reports/moldbeyondlifecyclereport'
        }
      ],
      total: 10203,
      statusList: [
        { title: this.$t('开模申请'), number: 120 },
        { title: this.$t('设计中'), number: 1180 },
        { title: this.$t('制造中'), number: 1200 },
        { title: this.$t('试模中'), number: 510 },
        { title: this.$t('验收中'), number: 350 },
        { title: this.$t('保养中'), number: 2200 },
        { title: this.$t('改模中'), number: 700 },
        { title: this.$t('修模中'), number: 3120 },
        { title: this.$t('批产中'), number: 200 },
        { title: this.$t('调拨中'), number: 120 }
      ],
      agencyList: [
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' },
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' },
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' },
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' },
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' },
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' },
        { title: this.$t('您有6条 【审厂待确认】的任务，请及时处理'), urlRef: '' }
      ],
      businessList: [
        { title: this.$t('审厂管理'), number: 120 },
        { title: this.$t('验收申请'), number: 50 },
        { title: this.$t('其他'), number: 50 }
      ],
      noticeList: [
        { title: this.$t('审厂功能已上线，具体操作请查看操作手册') },
        { title: this.$t('审厂功能已上线，具体操作请查看操作手册') },
        { title: this.$t('审厂功能已上线，具体操作请查看操作手册') },
        { title: this.$t('审厂功能已上线，具体操作请查看操作手册') }
      ]
    }
  },
  computed: {
    dateParam() {
      return '周' + this.dateFormatter(this.timestamp, 'day \xa0 YYYY年MM月DD日')
    }
  },
  created() {
    this.getData()
    this.getTodoList()
  },
  mounted() {
    window.onresize = () => {
      this.setScrollHeight()
    }
    this.setScrollHeight()
    this.timestamp = +new Date()
    this.timeInterval && clearInterval(this.timeInterval)
    this.timeInterval = setInterval(() => {
      this.timestamp += 1000
    }, 1000)
  },
  methods: {
    getData() {
      this.$API.moldManagement.queryWorkbenchMouldCountApi().then((res) => {
        if (res.code === 200) {
          this.total = res.data.totalCount
          let field = [
            'mouldOpeningApply',
            'design',
            'manufacture',
            'trialMould',
            'acceptanceCheck',
            'maintain',
            'changeMould',
            'repairMould',
            'batchProduction',
            'allocation'
          ]
          for (let i = 0; i < 10; i++) {
            this.statusList[i].number = res.data[field[i]]
          }
        }
      })
    },
    getTodoList() {
      this.$API.moldManagement.queryMouldListApi().then((res) => {
        if (res.code === 200) {
          this.agencyList = res.data
        }
      })
    },
    handleClick(url, type) {
      if (['plm', 'mes'].includes(type)) {
        window.open(url)
      } else {
        this.$router.push({
          path: url
        })
      }
    },
    todoClick(url) {
      const linkArr = url.split('/#')
      if (linkArr[1]) {
        this.$router.push({
          path: linkArr[1]
        })
      } else {
        this.$router.push({
          path: 'factory-audit-management'
        })
      }
    },
    dateFormatter(date, format = 'YYYY-MM-DD hh:mm:ss') {
      if (typeof date === 'string') {
        date = /^\d+$/.test(date) ? Number(date) : date.replace(/-/g, '/')
      }
      const dt = new Date(date)
      if (!dt.getTime()) {
        return ''
      }
      const fill = (str, num = 2) => str.toString().padStart(num, '0')
      const days = ['日', '一', '二', '三', '四', '五', '六']
      const map = {
        YYYY: dt.getFullYear(),
        MM: fill(dt.getMonth() + 1),
        DD: fill(dt.getDate()),
        hh: fill(dt.getHours()),
        mm: fill(dt.getMinutes()),
        ss: fill(dt.getSeconds()),
        ms: fill(dt.getMilliseconds(), 3),
        day: dt.getDay(),
        ts: dt.getTime()
      }
      return Object.keys(map).reduce((acc, key) => {
        const reg = new RegExp(key, 'g')
        return acc.replace(reg, () => {
          return key === 'day' ? days[map[key]] : map[key]
        })
      }, format)
    },
    toggleScreen() {
      this.setScreenful()
    },
    setScreenful() {
      this.$nextTick(() => {
        const app = document.querySelector('.workbench-kanban')
        if (app.mozRequestFullScreen) {
          app.mozRequestFullScreen()
        } else if (app.webkitRequestFullscreen) {
          app.webkitRequestFullscreen()
        } else if (app.msRequestFullscreen) {
          app.msRequestFullscreen()
        }
      })
    },
    setScrollHeight() {
      this.$nextTick(() => {
        const tableDom = document.getElementsByClassName('auto-table')[0]
        if (tableDom) {
          this.scrollHeight = tableDom.offsetHeight - 40
        }
      })
    }
  }
}
</script>

<style lang="scss">
.workbench-kanban {
  width: 100%;
  height: 100% !important;
  background-color: #142478 !important;
  .header {
    display: flex;
    justify-content: space-between;
    height: 80px;
    text-align: center;
    line-height: 80px;
    background: url('~@/assets/images/kanban/header.png') center center no-repeat;
    background-size: 100% 100%;
    .header-logo {
      border: none;
      outline: none;
      margin-left: 1rem;
      background: url('~@/assets/images/kanban/logo.png') left center no-repeat;
      background-size: 100%;
      height: 100%;
      width: 210px;
    }
    .title {
      flex: 1;
      text-align: center;
      line-height: 40px;
      height: 40px;
      font-size: 2.4rem;
      font-weight: bold;
      margin-top: 18px;
      background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .date {
      text-align: right;
      padding-right: 24px;
      /* stylelint-disable */
      font-family: PingFangSC-Medium;
      /* stylelint-enable */
      font-weight: 500;
      font-size: 0.9rem;
      color: #abc8ff;
      letter-spacing: 0;
      min-width: 210px;
    }
  }
  .query-box {
    position: absolute;
    top: 58px;
    color: #ffffff80;
    left: 146px;
    display: flex;
    width: 300px;
    align-items: center;
    .query-item {
      flex: 1;
      cursor: pointer;
      color: #ffffff80;
      text-align: center;
    }
    .actived-item {
      color: #fff;
      span {
        color: #fff;
      }
    }
  }
  .header-left {
    right: unset;
  }
  .header-right {
    left: unset;
    right: 30px;
    top: 58px;
    width: 400px;
  }
  .content {
    height: calc(100% - 80px);
    display: flex;
    gap: 10px;
    padding: 10px 24px 16px !important;
    .left {
      width: 65%;
      display: flex;
      flex-direction: column;
      gap: 10px;
      .top {
        flex: 0.5;
        display: flex;
        .card-item {
          flex: 1;
          background-color: #172d8c;
          padding: 0 16px;
          margin-left: 10px;
          &:first-child {
            margin-left: 0;
          }
          .card-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            padding-top: 10px;
            /* stylelint-disable */
            font-family: PingFang SC;
            /* stylelint-enable */
            font-size: 15px;
            font-weight: 500;
            letter-spacing: 0px;
            color: #9fe4ff;
            .card-content-item {
              height: 30px;
              line-height: 30px;
              position: relative;
              padding-left: 20px;
              cursor: pointer;
            }
            .card-content-item::before {
              content: '';
              width: 15px;
              height: 15px;
              position: absolute;
              left: 0;
              top: 50%;
              background: url('~@/assets/images/kanban/icon.png');
              background-size: 100% 100%;
              transform: translateY(-50%);
            }
          }
        }
      }
      .middle {
        flex: 1;
        background-color: #172d8c;
        padding: 0 16px;
        .card-item {
          height: 100%;
          .card-content {
            background: url('~@/assets/images/kanban/background.png');
            background-size: 100% 100%;
            .card-content-item {
              height: 100%;
              display: flex;
              align-items: center;
              padding: 0 20%;
              .title {
                opacity: 1;
                font-family: Alimama ShuHeiTi;
                font-size: 40px;
                font-weight: bold;
                line-height: normal;
                text-align: center;
                letter-spacing: 0px;

                font-variation-settings: 'opsz' auto;
                background: linear-gradient(
                    360deg,
                    rgba(0, 67, 255, 0.43) -676%,
                    rgba(0, 208, 255, 0.43) -247%,
                    rgba(237, 251, 255, 0.43) 84%
                  ),
                  linear-gradient(0deg, #0263c6 0%, #b9dbff 63%, #f4f5ff 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
              .number {
                padding-left: 20px;
                opacity: 1;
                font-family: Alimama ShuHeiTi;
                font-size: 56px;
                font-weight: bold;
                line-height: normal;
                text-align: center;
                letter-spacing: 0px;

                font-variation-settings: 'opsz' auto;
                background: linear-gradient(0deg, #d0a32f 0%, #fff4d6 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
              .unit {
                padding-left: 10px;
                opacity: 1;
                font-family: Alimama ShuHeiTi;
                font-size: 28px;
                font-weight: bold;
                line-height: normal;
                text-align: center;
                letter-spacing: 0px;

                font-variation-settings: 'opsz' auto;
                background: linear-gradient(0deg, #d0a32f 0%, #fff4d6 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
          }
        }
      }
      .bottom {
        max-height: 40%;
        background-color: #172d8c;
        padding: 0 16px;
        .card-item {
          height: 100%;
          .card-content {
            height: calc(100% - 32px);
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            padding-top: 10px;
            .card-content-item {
              display: flex;
              align-items: center;
              gap: 10px;
              padding: 10px;
              .left {
                flex: 1;
                .icon {
                  width: 88px;
                  height: 88px;
                }
                .icon_1 {
                  background: url('~@/assets/images/kanban/icon-1.png');
                  background-size: 100% 100%;
                }
                .icon_2 {
                  background: url('~@/assets/images/kanban/icon-2.png');
                  background-size: 100% 100%;
                }
                .icon_3 {
                  background: url('~@/assets/images/kanban/icon-3.png');
                  background-size: 100% 100%;
                }
                .icon_4 {
                  background: url('~@/assets/images/kanban/icon-4.png');
                  background-size: 100% 100%;
                }
                .icon_5 {
                  background: url('~@/assets/images/kanban/icon-5.png');
                  background-size: 100% 100%;
                }
                .icon_6 {
                  background: url('~@/assets/images/kanban/icon-6.png');
                  background-size: 100% 100%;
                }
                .icon_7 {
                  background: url('~@/assets/images/kanban/icon-7.png');
                  background-size: 100% 100%;
                }
                .icon_8 {
                  background: url('~@/assets/images/kanban/icon-8.png');
                  background-size: 100% 100%;
                }
                .icon_9 {
                  background: url('~@/assets/images/kanban/icon-9.png');
                  background-size: 100% 100%;
                }
                .icon_10 {
                  background: url('~@/assets/images/kanban/icon-10.png');
                  background-size: 100% 100%;
                }
              }
              .right {
                flex: 1;
                display: flex;
                justify-content: center;
                .title {
                  opacity: 1;
                  font-family: PingFang SC;
                  font-size: 20px;
                  font-weight: 600;
                  line-height: normal;
                  letter-spacing: 0px;
                  color: #9fe4ff;
                }
                .number {
                  opacity: 1;
                  font-family: Arial;
                  font-size: 22px;
                  font-weight: bold;
                  line-height: normal;
                  letter-spacing: 0px;
                  background: linear-gradient(180deg, #95e2ee 30%, #29f2f8 82%), #ffffff;
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                }
                .unit {
                  padding-left: 10px;
                  font-family: Arial;
                  font-weight: 700;
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }
  .right {
    width: 35%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: hidden;
    .top {
      flex: 1;
      background-color: #172d8c;
      padding: 0 16px;
      overflow: hidden;
      max-height: 32.5%;
      .card-item {
        height: 100%;
        .card-content {
          opacity: 1;
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: 500;
          line-height: normal;
          letter-spacing: 0px;
          color: #fff;
          .card-content-item {
            padding: 10px 10px 10px 20px;
          }
          .title {
            position: relative;
            cursor: pointer;
          }
          .title::before {
            content: '';
            width: 6px;
            height: 6px;
            position: absolute;
            top: 8px;
            left: -18px;
            background: #ff3434;
            border-radius: 50%;
          }
        }
      }
    }
    .middle {
      flex: 1;
      background-color: #172d8c;
      padding: 0 16px;
      .card-item {
        height: 100%;
        .card-content {
          height: calc(100% - 32px);
          display: grid;
          grid-template-columns: 1fr 1fr;
          padding-top: 10px;
          .card-content-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            .left {
              flex: 1;
              .bg {
                width: 128px;
                height: 128px;
              }
              .bg_1 {
                background: url('~@/assets/images/kanban/bg-1.png');
                background-size: 100% 100%;
              }
              .bg_2 {
                background: url('~@/assets/images/kanban/bg-2.png');
                background-size: 100% 100%;
              }
              .bg_3 {
                background: url('~@/assets/images/kanban/bg-3.png');
                background-size: 100% 100%;
              }
            }
            .right {
              flex: 1;
              display: flex;
              justify-content: center;
              .title {
                opacity: 1;
                font-family: PingFang SC;
                font-size: 20px;
                line-height: normal;
                letter-spacing: 0px;
                color: #9fe4ff;
              }
              .number {
                opacity: 1;
                font-family: Arial;
                font-size: 22px;
                font-weight: bold;
                line-height: normal;
                letter-spacing: 0px;
                background: linear-gradient(180deg, #95e2ee 30%, #29f2f8 82%), #ffffff;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
              }
            }
          }
        }
      }
    }
    .bottom {
      flex: 1;
      background-color: #172d8c;
      padding: 0 16px;
      overflow: hidden;
      max-height: 32.5%;
      .card-item {
        height: 100%;
        .card-content {
          opacity: 1;
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: 500;
          line-height: normal;
          letter-spacing: 0px;
          color: #fff;
          .card-content-item {
            padding: 10px 10px 10px 0;
            display: flex;
            align-items: center;
          }
          .seq {
            background: rgba(0, 133, 255, 0.6);
            width: 16px;
            height: 16px;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            margin-right: 10px;
          }
          .title {
            position: relative;
          }
          // .title::before {
          //   content: '';
          //   width: 6px;
          //   height: 6px;
          //   position: absolute;
          //   top: 8px;
          //   left: -18px;
          //   background: #ff3434;
          //   border-radius: 50%;
          // }
        }
      }
    }
  }
}
.sub-title {
  height: 32px;
  line-height: 32px;
  overflow: hidden;
  .part-icon {
    position: relative;
    /* stylelint-disable */
    font-family: PingFangSC-Medium;
    /* stylelint-enable */
    font-size: 16px;
    font-weight: 700;
    background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .part-icon::after {
    content: '';
    width: 200px;
    height: 20px;
    position: absolute;
    top: 0;
    right: -146px;
    background: url('~@/assets/images/kanban/titleicon.png');
    background-size: 100% 100%;
  }
  .part-sub-title {
    position: relative;
    /* stylelint-disable */
    font-family: PingFangSC-SNaNpxibold;
    /* stylelint-enable */
    font-weight: 600;
    font-size: 15px;
    color: #fff;
    letter-spacing: 0;
    background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .part-sub-title::before {
    content: '';
    width: 6px;
    height: 6px;
    position: absolute;
    top: 8px;
    left: -12px;
    background: #6393ab;
    border-radius: 50%;
  }
}
@media screen and (max-width: 1280px) {
  .workbench-kanban {
    .content {
      height: calc(100% - 50px);
    }
    .header {
      height: 50px !important;
      .title {
        margin-top: 4px;
      }
    }
  }
}
</style>
