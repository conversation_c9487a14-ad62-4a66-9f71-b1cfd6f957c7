<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="mouldCode" :label="$t('模具编码')" label-style="left">
          <mt-select
            v-model="formObject.mouldCode"
            :data-source="mouldOptions"
            :fields="{ text: 'mouldCode', value: 'mouldCode' }"
            :show-clear-button="true"
            :allow-filtering="true"
            :filtering="getMouldOptions"
            :placeholder="$t('请选择')"
            @change="mouldChange"
          />
        </mt-form-item>
        <mt-form-item prop="mouldName" :label="$t('模具名称')" label-style="left">
          <div>{{ formObject.mouldName }}</div>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        mouldCode: ''
      },
      //必填项
      formRules: {
        mouldCode: [
          {
            required: true,
            message: this.$t('请选择模具编码'),
            trigger: 'blur'
          }
        ]
      },
      mouldOptions: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    headStates() {
      return this.modalData.headStates
    },
    salesData() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.getMouldOptions = utils.debounce(this.getMouldOptions, 500)
    this.getMouldOptions()
  },
  methods: {
    mouldChange(e) {
      this.formObject.mouldName = e.itemData?.mouldName
      this.formObject.mouldClassify = e.itemData?.mouldClassify
      this.formObject.mouldType = e.itemData?.mouldType
      this.formObject.productIdentification = e.itemData?.productIdentification
    },
    getMouldOptions(e = { text: '' }) {
      this.$API.moldManagement.queryMouldDetailByMouldCodeApi({ mouldCode: e.text }).then((res) => {
        if (res.code === 200) {
          this.mouldOptions = res.data
        }
      })
    },
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          this.$emit('confirm-function', this.formObject)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
