<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="modelForm" :rules="formRules">
        <mt-form-item prop="managementState" :label="$t('管理状态')">
          <mt-radio v-model="modelForm.managementState" :data-source="statusOptions" />
        </mt-form-item>
        <mt-form-item prop="reasonChange" :label="$t('更改原因')">
          <mt-input
            type="text"
            :multiline="true"
            :rows="3"
            v-model="modelForm.reasonChange"
            :placeholder="$t('请输入更改原因')"
            maxlength="300"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="file" :label="$t('附件')">
          <mt-button @click="handleUpload">{{ $t('上传文件') }}</mt-button>
          <input type="file" ref="fileRef" style="display: none" @change="chooseFiles" />
          <div class="file-list">
            <div class="file-item" v-for="(item, index) in fileList" :key="index">
              <a @click="handlePreview(item)">
                {{ item.fileName }}
                <i class="vxe-icon-delete" @click.stop="handleDelete(item)" />
                <!-- <i class="vxe-icon-download" @click.stop="handleDownload(item)" /> -->
              </a>
            </div>
          </div>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { statusOptions } from '../config/index'
import { download } from '@/utils/utils'
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      modelForm: {
        managementState: null, // 管理状态
        reasonChange: '', // 更改原因
        file: null
      },
      //必填项
      formRules: {
        managementState: [
          {
            required: true,
            message: this.$t('请选择管理状态'),
            trigger: 'blur'
          }
        ],
        reasonChange: [
          {
            required: true,
            message: this.$t('请输入更改原因'),
            trigger: 'blur'
          }
        ],
        file: [
          {
            required: true,
            message: this.$t('请上传附件'),
            trigger: 'blur'
          }
        ]
      },

      statusOptions,

      fileList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    currentRow() {
      return this.modalData.row
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleUpload() {
      this.$refs['fileRef'].click()
    },
    chooseFiles(data) {
      const fileList = Object.values(data?.target?.files)
      if (fileList.length < 1) {
        return
      }
      if (fileList.length > 5) {
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      // 判断当个文件是否过大s
      let isOutOfRange = false
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ content: params.msg })
        return
      }
      fileList.forEach((item) => {
        const _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data, 'fileList')
      })
    },
    async uploadFile(fileData, key) {
      this.$loading()
      const res = await this.$API.fileService.uploadPublicFile(fileData)
      this.$hloading()
      if (res.code === 200) {
        if (key === 'fileList') {
          const dataList = [...this.fileList]
          dataList.push({
            docType: '12',
            fileName: res.data.fileName,
            mainId: this.currentRow.id,
            sysFileId: res.data.id,
            url: res.data.url
          })
          this.fileList = dataList
          this.modelForm.file = this.fileList.length !== 0 ? true : null
        }
      }
    },
    // 预览
    handlePreview(file) {
      let params = {
        id: file?.sysFileId,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    // 删除
    handleDelete(file) {
      this.fileList = this.fileList.filter((item) => item.sysFileId !== file.sysFileId)
      this.modelForm.file = this.fileList.length !== 0 ? true : null
    },
    // 下载
    handleDownload(file) {
      this.$API.fileService
        .downloadPublicFile({
          id: file.sysFileId
        })
        .then((res) => {
          download({
            fileName: file.fileName,
            blob: res.data
          })
        })
    },
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = {
            ...this.currentRow,
            managementState: this.modelForm.managementState,
            reasonChange: this.modelForm.reasonChange,
            resumeDocumentList: this.fileList
          }
          this.$emit('confirm-function', params)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
.file-list {
  margin: 10px 0;
  .file-item {
    padding: 2px 0;
    a {
      color: #409eff;
      i {
        margin-left: 5px;
        font-weight: bold;
      }
    }
  }
}
</style>
