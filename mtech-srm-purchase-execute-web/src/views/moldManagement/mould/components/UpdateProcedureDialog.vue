<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    height="320"
    width="400"
  >
    <div class="dialog-content">
      <div style="display: flex; align-items: center; justify-content: space-between">
        <span>{{ $t('确认有') }}</span>
        <mt-input-number
          v-model="modelForm.updateProcedure"
          :min="1"
          :max="10"
          style="width: 75%"
        ></mt-input-number>
        <span>{{ $t('工序') }}</span>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      modelForm: {
        updateProcedure: 1
      },
      //必填项
      formRules: {}
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    //点击确认
    confirm() {
      if (this.modelForm.updateProcedure) {
        this.$emit('confirm-function', this.modelForm.updateProcedure)
      } else {
        this.$toast({ content: this.$t('请输入工序'), type: 'warning' })
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
