<!-- 批量编辑弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :height="1000"
    :width="1200"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form ref="dialogRef" :model="modelForm" :rules="formRules">
        <div class="subTitle">{{ $t('基本信息') }}</div>
        <div>
          <mt-row :gutter="24">
            <mt-col :span="6">
              <mt-form-item :label="$t('模具规格型号')" prop="moldSpecificationNumber">
                <mt-input
                  v-model="modelForm.moldSpecificationNumber"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('型腔数')" prop="cavityNumber">
                <mt-input
                  type="number"
                  v-model="modelForm.cavityNumber"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('设备吨位')" prop="equipmentTonnage">
                <mt-input
                  type="number"
                  v-model="modelForm.equipmentTonnage"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="6">
              <mt-form-item :label="$t('模架材料')" prop="moldMaterial">
                <mt-input
                  v-model="modelForm.moldMaterial"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('模腔材料')" prop="cavityMaterial">
                <mt-input
                  v-model="modelForm.cavityMaterial"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('模芯材料')" prop="coreMaterial">
                <mt-input
                  v-model="modelForm.coreMaterial"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <div class="subTitle">{{ $t('厂家与状态信息') }}</div>
        <div>
          <mt-row :gutter="24">
            <mt-col :span="6">
              <mt-form-item :label="$t('模具开制供应商')" prop="supplierCode">
                <RemoteAutocomplete
                  v-model="modelForm.supplierCode"
                  url="/masterDataManagement/tenant/supplier/paged-query"
                  :placeholder="$t('请选择')"
                  :fields="{ text: 'supplierName', value: 'supplierCode' }"
                  :search-fields="['supplierName', 'supplierCode']"
                  @change="supplierChange"
                ></RemoteAutocomplete>
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('模具开制供应商地址')" prop="supplierAddress">
                <mt-input v-model="modelForm.supplierAddress" disabled />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('入厂日期')" prop="dateEntry">
                <mt-date-picker
                  v-model="modelForm.dateEntry"
                  :placeholder="$t('请选择')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="6">
              <mt-form-item :label="$t('责任人')" prop="personCharge">
                <mt-input
                  v-model="modelForm.personCharge"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('使用寿命-万次')" prop="serviceLife">
                <mt-input
                  type="number"
                  v-model="modelForm.serviceLife"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col>
              <mt-form-item prop="managementState" :label="$t('管理状态')">
                <mt-radio v-model="modelForm.managementState" :data-source="statusOptions" />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col :span="18">
              <mt-form-item prop="reasonChange" :label="$t('更改原因')">
                <mt-input
                  type="text"
                  :multiline="true"
                  :rows="3"
                  v-model="modelForm.reasonChange"
                  :placeholder="$t('请输入更改原因')"
                  maxlength="300"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>
          <mt-row :gutter="24">
            <mt-col>
              <mt-form-item prop="file" :label="$t('附件')">
                <mt-button @click="handleUpload">{{ $t('上传文件') }}</mt-button>
                <input type="file" ref="fileRef" style="display: none" @change="chooseFiles" />
                <div class="file-list">
                  <div class="file-item" v-for="(item, index) in fileList" :key="index">
                    <a @click="handlePreview(item)">
                      {{ item.fileName }}
                      <i class="vxe-icon-delete" @click.stop="handleDelete(item)" />
                    </a>
                  </div>
                </div>
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
        <div class="subTitle">{{ $t('财务信息') }}</div>
        <div>
          <mt-row :gutter="24">
            <mt-col :span="6">
              <mt-form-item :label="$t('财务资产编码')" prop="financialCoding">
                <mt-input
                  v-model="modelForm.financialCoding"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('合同编号')" prop="contractNumber">
                <mt-input
                  v-model="modelForm.contractNumber"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
            <mt-col :span="6">
              <mt-form-item :label="$t('价格')" prop="price">
                <mt-input
                  type="number"
                  v-model="modelForm.price"
                  :placeholder="$t('请输入')"
                  :show-clear-button="true"
                />
              </mt-form-item>
            </mt-col>
          </mt-row>
          <div>
            <mt-row :gutter="24">
              <mt-col :span="12">
                <mt-form-item>
                  <div>
                    <sc-table
                      ref="paymentTableRef"
                      grid-id="97f666eb-c876-46e0-a9e8-b7b192255f2b"
                      :fix-height="180"
                      :loading="loading"
                      :columns="paymentColumns"
                      :table-data="paymentTableData"
                      :edit-config="editConfig"
                      :edit-rules="paymentEditRules"
                      keep-source
                      :sortable="false"
                      :is-show-right-btn="false"
                      :is-show-refresh-bth="false"
                      @edit-closed="paymentEditComplete"
                    >
                      <template slot="custom-tools">
                        <vxe-button
                          v-for="item in toolbar"
                          :key="item.code"
                          :status="item.status"
                          :icon="item.icon"
                          :loading="item.loading"
                          size="small"
                          @click="handleClickToolBar(item)"
                        >
                          {{ item.name }}
                        </vxe-button>
                      </template>
                    </sc-table>
                  </div>
                </mt-form-item>
              </mt-col>
            </mt-row>
          </div>
          <mt-row :gutter="24">
            <mt-col :span="18">
              <mt-form-item :label="$t('备注')" prop="remark">
                <mt-input v-model="modelForm.remark" />
              </mt-form-item>
            </mt-col>
          </mt-row>
        </div>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { statusOptions } from '../config/index'
import { download } from '@/utils/utils'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      modelForm: {
        supplierCode: null, // 供应商
        managementState: null, // 管理状态
        reasonChange: '', // 更改原因
        file: null
      },
      //必填项
      formRules: {
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择模具开制供应商'),
            trigger: 'blur'
          }
        ],
        managementState: [
          {
            required: true,
            message: this.$t('请选择管理状态'),
            trigger: 'blur'
          }
        ],
        reasonChange: [
          {
            required: true,
            message: this.$t('请输入更改原因'),
            trigger: 'blur'
          }
        ],
        file: [
          {
            required: true,
            message: this.$t('请上传附件'),
            trigger: 'blur'
          }
        ],
        contractNumber: [
          {
            required: true,
            message: this.$t('请输入合同编码'),
            trigger: 'blur'
          }
        ]
      },

      statusOptions,

      fileList: [],

      loading: false,
      paymentTableData: [],
      paymentEditRules: {
        paymentMilestones: [{ required: true, message: this.$t('必填') }],
        paymentDate: [{ required: true, message: this.$t('必填') }],
        paymentAmount: [{ required: true, message: this.$t('必填') }]
      },
      hasPerm: false // 是否有查看金额权限
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    ids() {
      return this.modalData.ids
    },
    paymentTableRef() {
      return this.$refs.paymentTableRef.$refs.xGrid
    },
    paymentColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'paymentMilestones',
          title: this.$t('付款里程碑'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.paymentMilestones}
                  placeholder={this.$t('请选择')}
                  options={this.paymentMilestonesOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.paymentMilestonesOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'paymentDate',
          title: this.$t('付款时间'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.paymentDate}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                />
              ]
            }
          }
        },
        {
          field: 'paymentAmount',
          title: this.$t('付款金额'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <span>{row.isShow ? row.paymentAmount : '***'}</span>
                  {this.hasPerm && this.type === 'check' ? (
                    <i
                      class={row.isShow ? 'vxe-icon-eye-fill' : 'vxe-icon-eye-fill-close'}
                      style='margin-left: 5px;cursor: pointer'
                      onClick={() => this.showAmount(row)}
                    />
                  ) : null}
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  {this.hasPerm ? (
                    <vxe-input
                      type='number'
                      v-model={row.paymentAmount}
                      placeholder={this.$t('请输入')}
                      transfer
                      clearable
                    />
                  ) : null}
                </div>
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.hasPerm,
        trigger: 'click',
        mode: 'row',
        showStatus: this.hasPerm
      }
    },
    toolbar() {
      let btns = []
      if (this.hasPerm) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    this.getPermission()
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    getPermission() {
      this.$API.moldManagement.verifyPermissionApi().then((res) => {
        if (res.code === 200) {
          this.hasPerm = res.data
        }
      })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.paymentTableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.paymentAdd()
          break
        case 'delete':
          this.paymentTableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    paymentAdd() {
      const item = { isShow: true }
      this.paymentTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.paymentTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.paymentTableRef.setEditRow(currentViewRecords[0])
      })
    },
    paymentEditComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.paymentTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    supplierChange(e) {
      this.modelForm.supplierName = e.itemData?.supplierName
      if (e?.itemData) {
        this.getDetailAddressBySupplierTenantId(e.itemData.supplierTenantId)
      }
    },
    getDetailAddressBySupplierTenantId(supplierTenantId) {
      let params = {
        supplierTenantId
      }
      this.$API.moldManagement
        .getDetailAddressBySupplierTenantIdApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.modelForm, 'supplierAddress', res.data)
          } else {
            this.modelForm.supplierAddress = ''
          }
        })
        .catch(() => {
          this.modelForm.supplierAddress = ''
        })
    },
    handleUpload() {
      this.$refs['fileRef'].click()
    },
    chooseFiles(data) {
      const fileList = Object.values(data?.target?.files)
      if (fileList.length < 1) {
        return
      }
      if (fileList.length > 5) {
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      // 判断当个文件是否过大s
      let isOutOfRange = false
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ content: params.msg })
        return
      }
      fileList.forEach((item) => {
        const _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data, 'fileList')
      })
    },
    async uploadFile(fileData, key) {
      this.$loading()
      const res = await this.$API.fileService.uploadPublicFile(fileData)
      this.$hloading()
      if (res.code === 200) {
        if (key === 'fileList') {
          const dataList = [...this.fileList]
          dataList.push({
            docType: '12',
            fileName: res.data.fileName,
            sysFileId: res.data.id,
            url: res.data.url
          })
          this.fileList = dataList
          this.modelForm.file = this.fileList.length !== 0 ? true : null
        }
      }
    },
    // 预览
    handlePreview(file) {
      let params = {
        id: file?.sysFileId,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    // 删除
    handleDelete(file) {
      this.fileList = this.fileList.filter((item) => item.sysFileId !== file.sysFileId)
      this.modelForm.file = this.fileList.length !== 0 ? true : null
    },
    // 下载
    handleDownload(file) {
      this.$API.fileService
        .downloadPublicFile({
          id: file.sysFileId
        })
        .then((res) => {
          download({
            fileName: file.fileName,
            blob: res.data
          })
        })
    },
    //点击确认
    confirm() {
      this.$refs.dialogRef.validate((valid) => {
        if (valid) {
          let params = {
            ...this.modelForm,
            ids: this.ids
          }
          let data = this.paymentTableRef.getTableData().visibleData.map((item) => {
            return {
              paymentMilestones: item.paymentMilestones,
              paymentDate: item.paymentDate,
              paymentAmount: item.paymentAmount
            }
          })
          params.paymentMilestone = JSON.stringify(data)
          params.documentList = this.fileList
          params.dateEntry = dayjs(dayjs(params.dateEntry).format('YYYY-MM-DD')).valueOf()
          this.$emit('confirm-function', params)
        }
      })
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px;
      // .mt-form {
      //   display: flex;
      //   justify-content: space-between;
      //   align-items: center;
      //   flex-wrap: wrap;
      //   .mt-form-item {
      //     width: 100%;
      //   }
      // }
    }
  }
  .subTitle {
    padding: 12px 0;
    font-size: 18px;
    font-weight: 600;
  }
}
.file-list {
  margin: 10px 0;
  .file-item {
    padding: 2px 0;
    a {
      color: #409eff;
      i {
        margin-left: 5px;
        font-weight: bold;
      }
    }
  }
}
</style>
