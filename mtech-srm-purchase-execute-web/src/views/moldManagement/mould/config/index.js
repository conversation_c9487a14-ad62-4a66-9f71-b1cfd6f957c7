import { i18n } from '@/main.js'

export const mouldTypeOptions = [
  { text: i18n.t('五金模'), value: '1' },
  { text: i18n.t('注塑模'), value: '2' },
  { text: i18n.t('泡沫及其它模'), value: '3' }
]

export const mouldClassifyOptions = [
  { text: i18n.t('新增模'), label: i18n.t('新增模'), value: '1' },
  { text: i18n.t('复制模'), label: i18n.t('复制模'), value: '2' },
  { text: i18n.t('批产前-改模'), label: i18n.t('批产前-改模'), value: '3' },
  { text: i18n.t('修模'), label: i18n.t('修模'), value: '4' },
  { text: i18n.t('批产后-改模'), label: i18n.t('批产后-改模'), value: '5' }
]

export const statusOptions = [
  { text: i18n.t('设计'), label: i18n.t('设计'), value: '1' },
  { text: i18n.t('制造'), label: i18n.t('制造'), value: '2' },
  { text: i18n.t('批产前改模'), label: i18n.t('批产前改模'), value: '3' },
  { text: i18n.t('修模'), label: i18n.t('修模'), value: '4' },
  { text: i18n.t('批产后改模'), label: i18n.t('批产后改模'), value: '5' },
  { text: i18n.t('批产'), label: i18n.t('批产'), value: '6' },
  { text: i18n.t('试模'), label: i18n.t('试模'), value: '7' },
  { text: i18n.t('验收'), label: i18n.t('验收'), value: '8' },
  // { text: i18n.t('保养'), label: i18n.t('保养'), value: '9' },
  // { text: i18n.t('调拨'), label: i18n.t('调拨'), value: '10' }
  { text: i18n.t('已报废'), label: i18n.t('已报废'), value: '11' }
]

export const productLibraryOptions = [
  { text: i18n.t('商用'), label: i18n.t('商用'), value: '1' },
  { text: i18n.t('家用'), label: i18n.t('家用'), value: '2' }
]

export const operationTypeOptions = [
  { text: i18n.t('连续模'), label: i18n.t('连续模'), value: '1' },
  { text: i18n.t('工序模'), label: i18n.t('工序模'), value: '2' }
]

export const processingTypeOptions = [
  { text: i18n.t('自制'), label: i18n.t('自制'), value: '1' },
  { text: i18n.t('委外'), label: i18n.t('委外'), value: '2' },
  { text: i18n.t('客供'), label: i18n.t('客供'), value: '3' }
]

export const paymentMilestonesOptions = [
  { text: i18n.t('首期款'), label: i18n.t('首期款'), value: '1' },
  { text: i18n.t('二期款'), label: i18n.t('二期款'), value: '2' },
  { text: i18n.t('三期款'), label: i18n.t('三期款'), value: '3' },
  { text: i18n.t('尾款'), label: i18n.t('尾款'), value: '4' },
  { text: i18n.t('一次性付清'), label: i18n.t('一次性付清'), value: '5' }
]

export const historyFlagOptions = [
  { text: i18n.t('PLM新增'), label: i18n.t('PLM新增'), value: 0 },
  { text: i18n.t('导入'), label: i18n.t('导入'), value: 1 }
]

export const ruleTypeOptions = [
  { text: i18n.t('新规则'), label: i18n.t('新规则'), value: '1' },
  { text: i18n.t('旧规则'), label: i18n.t('旧规则'), value: '2' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'mouldCode',
    title: i18n.t('模具编码')
  },
  {
    field: 'mouldName',
    title: i18n.t('模具名称')
  },
  {
    field: 'mouldType',
    title: i18n.t('模具类型'),
    formatter: ({ cellValue }) => {
      let item = mouldTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'mouldClassify',
    title: i18n.t('开模类型'),
    formatter: ({ cellValue }) => {
      let item = mouldClassifyOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'operationType',
    title: i18n.t('工序类型'),
    formatter: ({ cellValue }) => {
      let item = operationTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'useStatusMes',
    title: i18n.t('使用状态-MES'),
    minWidth: 120
  },
  {
    field: 'managementState',
    title: i18n.t('管理状态-PLM'),
    minWidth: 120,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => Number(item.value) === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'moldHistory',
    title: i18n.t('模具履历'),
    slots: {
      default: 'moldHistoryDefault'
    }
  },
  {
    field: 'maintenanceTimesMes',
    title: i18n.t('维修次数-MES'),
    minWidth: 120
  },
  {
    field: 'allotFrequencyMes',
    title: i18n.t('调拨次数-MES'),
    minWidth: 120
  },
  {
    field: 'prenatalDosePlm',
    title: i18n.t('批产前改模次数-PLM'),
    minWidth: 120
  },
  {
    field: 'postpartumMassPlm',
    title: i18n.t('批产后改模次数-PLM'),
    minWidth: 120
  },
  {
    field: 'upkeepFrequencyMes',
    title: i18n.t('保养次数-MES'),
    minWidth: 120
  },
  {
    field: 'mouldCount',
    title: i18n.t('模具生产数量累计'),
    minWidth: 120
  },
  {
    field: 'supplierCode',
    title: i18n.t('模具开制供应商'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.supplierName : row.supplierName
    },
    minWidth: 120
  },
  {
    field: 'supplierAddress',
    title: i18n.t('模具开制供应商地址'),
    minWidth: 120
  },
  {
    field: 'currentSupplierCode',
    title: i18n.t('当前使用供应商'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.currentSupplierName : row.currentSupplierName
    },
    minWidth: 120
  },
  {
    field: 'currentMouldAddress',
    title: i18n.t('当前模具地址'),
    minWidth: 120
  },
  {
    field: 'lastSupplierCode',
    title: i18n.t('上一次使用供应商'),
    minWidth: 120,
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.lastSupplierName : row.lastSupplierName
    }
  },
  {
    field: 'lastSupplierAddress',
    title: i18n.t('上一次使用供应商地址'),
    minWidth: 120
  },
  {
    field: 'sourceAssets',
    title: i18n.t('资产来源')
  },
  {
    field: 'projectSource',
    title: i18n.t('项目来源')
  },
  {
    field: 'mouldCycleStartTime',
    title: i18n.t('开模周期起始时间')
  },
  {
    field: 'batchProductionReviewTime',
    title: i18n.t('转批产评审时间')
  },
  {
    field: 'annualSalesVolume',
    title: i18n.t('年预计销量')
  },
  {
    field: 'mouldAccountCode',
    title: i18n.t('模具账套编码')
  },
  {
    field: 'cavityNumber',
    title: i18n.t('型腔数')
  },
  {
    field: 'moldSpecificationNumber',
    title: i18n.t('规格型号')
  },
  {
    field: 'dateEntry',
    title: i18n.t('入厂日期')
  },
  {
    field: 'financialCoding',
    title: i18n.t('财务资产编码'),
    minWidth: 120
  },
  {
    field: 'contractNumber',
    title: i18n.t('合同编号')
  },
  {
    field: 'serviceLife',
    title: i18n.t('使用寿命-万次'),
    minWidth: 120
  },
  {
    field: 'mouldSize',
    title: i18n.t('模具尺寸')
  },
  {
    field: 'mouldWeight',
    title: i18n.t('模具重量')
  },
  {
    field: 'equipmentTonnage',
    title: i18n.t('设备吨位')
  },
  {
    field: 'developmentDepartment',
    title: i18n.t('开发部门')
  },
  {
    field: 'projectCode',
    title: i18n.t('项目编码')
  },
  {
    field: 'projectName',
    title: i18n.t('项目名称')
  },
  {
    field: 'projectManager',
    title: i18n.t('项目经理')
  },
  {
    field: 'productIdentification',
    title: i18n.t('产品标识')
  },
  {
    field: 'seriesName',
    title: i18n.t('系列名称')
  },
  {
    field: 'corporateBook',
    title: i18n.t('法人帐套')
  },
  {
    field: 'costCenter',
    title: i18n.t('成本中心')
  },
  {
    field: 'fileType',
    title: i18n.t('建档类型')
  },
  {
    field: 'mapNumber',
    title: i18n.t('图号')
  },
  {
    field: 'drawingTitle',
    title: i18n.t('图纸名称')
  },
  {
    field: 'estimatedPrice',
    title: i18n.t('预算价格'),
    slots: {
      default: 'estimatedPriceDefault'
    }
  },
  {
    field: 'moldMaterial',
    title: i18n.t('模架材料')
  },
  {
    field: 'cavityMaterial',
    title: i18n.t('模腔材料')
  },
  {
    field: 'coreMaterial',
    title: i18n.t('模芯材料')
  },
  {
    field: 'updateTime',
    title: i18n.t('最近更新时间'),
    minWidth: 160
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    fixed: 'right',
    slots: {
      default: 'operateDefault'
    }
  }
]
