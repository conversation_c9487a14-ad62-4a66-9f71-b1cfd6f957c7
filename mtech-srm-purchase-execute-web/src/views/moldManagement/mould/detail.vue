<!-- 模具台账 - 新增/编辑 -->
<template>
  <div class="mould-detail">
    <div class="top">
      <mt-button v-if="type === 'check'" @click="handleCancel">{{ $t('返回') }}</mt-button>
      <mt-button v-if="type !== 'check'" @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button v-if="type !== 'check'" @click="beforeSave">{{ $t('保存') }}</mt-button>
    </div>
    <mt-form ref="formRef" :model="modelForm" :rules="formRules">
      <div class="subTitle">{{ $t('基本信息') }}</div>
      <div>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('开模类型')" prop="mouldClassify">
              <mt-select
                v-model="modelForm.mouldClassify"
                :data-source="mouldClassifyOptions"
                :fields="{ text: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模具类型')" prop="mouldType">
              <mt-select
                v-model="modelForm.mouldType"
                :data-source="mouldTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模具编码')" prop="mouldCode">
              <mt-input v-model="modelForm.mouldCode" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item>
              <mt-button v-if="showUpdate" @click="handleUpdate">{{
                $t('更新工序编码')
              }}</mt-button>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('模具名称')" prop="mouldName">
              <mt-input v-model="modelForm.mouldName" :disabled="type === 'check'" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('产品库')" prop="productLibrary">
              <mt-input v-model="modelForm.productLibrary" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('工序类型')" prop="operationType">
              <mt-select
                v-model="modelForm.operationType"
                :data-source="operationTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('加工类型')" prop="processingType">
              <mt-select
                v-model="modelForm.processingType"
                :data-source="processingTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('模具规格型号')" prop="moldSpecificationNumber">
              <mt-input
                v-model="modelForm.moldSpecificationNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('型腔数')" prop="cavityNumber">
              <mt-input
                type="number"
                v-model="modelForm.cavityNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('设备吨位')" prop="equipmentTonnage">
              <mt-input
                type="number"
                v-model="modelForm.equipmentTonnage"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('使用寿命-万次')" prop="serviceLife">
              <mt-input
                type="number"
                v-model="modelForm.serviceLife"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('模架材料')" prop="moldMaterial">
              <mt-input
                v-model="modelForm.moldMaterial"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模腔材料')" prop="cavityMaterial">
              <mt-input
                v-model="modelForm.cavityMaterial"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模芯材料')" prop="coreMaterial">
              <mt-input
                v-model="modelForm.coreMaterial"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模具账套编码')" prop="mouldAccountCode">
              <mt-input v-model="modelForm.mouldAccountCode" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('项目编码')" prop="projectCode">
              <mt-input v-model="modelForm.projectCode" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('项目名称')" prop="projectName">
              <mt-input v-model="modelForm.projectName" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('项目经理')" prop="projectManager">
              <mt-input v-model="modelForm.projectManager" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('系列名称')" prop="seriesName">
              <mt-input v-model="modelForm.seriesName" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('模具尺寸')" prop="mouldSize">
              <mt-input
                v-model="modelForm.mouldSize"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模具重量')" prop="mouldWeight">
              <mt-input
                v-model="modelForm.mouldWeight"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('产品平台')" prop="productPlatform">
              <mt-input v-model="modelForm.productPlatform" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('产品标识')" prop="productIdentification">
              <mt-input v-model="modelForm.productIdentification" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('项目来源')" prop="projectSource">
              <mt-input v-model="modelForm.projectSource" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('开模周期起始时间')" prop="mouldCycleStartTime">
              <mt-input v-model="modelForm.mouldCycleStartTime" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('转批产评审时间')" prop="batchProductionReviewTime">
              <mt-input v-model="modelForm.batchProductionReviewTime" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('年预计销量')" prop="annualSalesVolume">
              <mt-input v-model="modelForm.annualSalesVolume" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('开模申请单号')" prop="applicationForm">
              <mt-input v-model="modelForm.applicationForm" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
      <div class="subTitle">{{ $t('厂家与状态信息') }}</div>
      <div>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('模具开制供应商')" prop="supplierCode">
              <RemoteAutocomplete
                v-model="modelForm.supplierCode"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :placeholder="$t('请选择')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                :disabled="type === 'check'"
                @change="supplierChange"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('模具开制供应商地址')" prop="supplierAddress">
              <mt-input v-model="modelForm.supplierAddress" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('入厂日期')" prop="dateEntry">
              <mt-date-picker
                v-model="modelForm.dateEntry"
                :placeholder="$t('请选择')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('费用类型')" prop="expenseType">
              <mt-input v-model="modelForm.expenseType" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item :label="$t('管理状态-PLM')" prop="managementState">
              <mt-radio v-model="modelForm.managementState" :data-source="statusOptions" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('历史数据标识')" prop="historyFlag">
              <mt-select
                v-model="modelForm.historyFlag"
                :data-source="historyFlagOptions"
                :fields="{ text: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('规则类型')" prop="ruleType">
              <mt-select
                v-model="modelForm.ruleType"
                :data-source="ruleTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                disabled
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('开发部门')" prop="developmentDepartment">
              <mt-input v-model="modelForm.developmentDepartment" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('责任人')" prop="personCharge">
              <mt-input
                v-model="modelForm.personCharge"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
      <div>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item>
              <div>
                <sc-table
                  ref="sctableRef"
                  grid-id="5705e605-9bd1-4ed4-9090-d83980643822"
                  :fix-height="180"
                  :loading="loading"
                  :columns="columns"
                  :table-data="tableData"
                  keep-source
                  :sortable="false"
                  :is-show-right-btn="false"
                  :is-show-refresh-bth="false"
                >
                  <template slot="custom-tools">
                    <div style="font-size: 18px; font-weight: 600">{{ $t('零部件物料明细') }}</div>
                  </template>
                </sc-table>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
      <div class="subTitle">{{ $t('相关附件') }}</div>
      <div>
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-form-item>
              <mt-button v-if="type !== 'check'" @click="handleUpload">{{
                $t('上传附件')
              }}</mt-button>
              <input type="file" ref="fileRef" style="display: none" @change="chooseFiles" />
              <div class="file-list">
                <div class="file-item" v-for="(item, index) in fileList" :key="index">
                  <a @click="handlePreview(item)">
                    {{ item.fileName }}
                    <i
                      v-if="type !== 'check'"
                      class="vxe-icon-delete"
                      @click.stop="handleDelete(item)"
                    />
                    <!-- <i class="vxe-icon-download" @click.stop="handleDownload(item)" /> -->
                  </a>
                </div>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
      <div class="subTitle">{{ $t('财务信息') }}</div>
      <div>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('法人帐套')" prop="corporateBook">
              <mt-input v-model="modelForm.corporateBook" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('成本中心')" prop="costCenter">
              <mt-input v-model="modelForm.costCenter" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('费用承担部门')" prop="expenseDepartment">
              <mt-input v-model="modelForm.expenseDepartment" disabled />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('费用承担组织')" prop="bearingOrganization">
              <mt-input v-model="modelForm.bearingOrganization" disabled />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('财务资产编码')" prop="financialCoding">
              <mt-input
                v-model="modelForm.financialCoding"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('合同编号')" prop="contractNumber">
              <mt-input
                v-model="modelForm.contractNumber"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('预算价格')" prop="estimatedPrice">
              <div
                style="
                  padding: 0 2px 0 0;
                  background-color: #fafafa;
                  height: 22px;
                  line-height: 22px;
                  color: rgba(0, 0, 0, 0.38);
                "
              >
                <span style="font-size: 12px">{{
                  hasPerm ? modelForm.estimatedPrice : '***'
                }}</span>
              </div>
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('价格')" prop="price">
              <mt-input
                v-if="hasPerm"
                type="number"
                v-model="modelForm.price"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
              <div
                v-else
                style="
                  padding: 0 2px 0 0;
                  background-color: #fafafa;
                  height: 22px;
                  line-height: 22px;
                  color: rgba(0, 0, 0, 0.38);
                "
              >
                <span style="font-size: 12px">{{ hasPerm ? modelForm.price : '***' }}</span>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item>
              <div>
                <sc-table
                  ref="paymentTableRef"
                  grid-id="5e6ab7d8-ccaa-4e15-959d-d77e3b73bdab"
                  :fix-height="180"
                  :loading="loading"
                  :columns="paymentColumns"
                  :table-data="paymentTableData"
                  :edit-config="editConfig"
                  :edit-rules="paymentEditRules"
                  keep-source
                  :sortable="false"
                  :is-show-right-btn="false"
                  :is-show-refresh-bth="false"
                  @edit-closed="paymentEditComplete"
                >
                  <template slot="custom-tools">
                    <vxe-button
                      v-for="item in toolbar"
                      :key="item.code"
                      :status="item.status"
                      :icon="item.icon"
                      :loading="item.loading"
                      size="small"
                      @click="handleClickToolBar(item)"
                    >
                      {{ item.name }}
                    </vxe-button>
                  </template>
                </sc-table>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item :label="$t('备注')" prop="remark">
              <mt-input v-model="modelForm.remark" :disabled="type === 'check'" />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
    </mt-form>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  mouldTypeOptions,
  mouldClassifyOptions,
  statusOptions,
  operationTypeOptions,
  processingTypeOptions,
  paymentMilestonesOptions,
  historyFlagOptions,
  ruleTypeOptions
} from './config'
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
export default {
  components: { ScTable },

  data() {
    return {
      modelForm: {},
      formRules: {
        supplierCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择供应商')
          }
        ],
        contractNumber: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入合同编号')
          }
        ]
      },

      mouldTypeOptions,
      mouldClassifyOptions,
      statusOptions,
      operationTypeOptions,
      processingTypeOptions,
      paymentMilestonesOptions,
      historyFlagOptions,
      ruleTypeOptions,

      loading: false,
      tableData: [],

      paymentTableData: [],
      paymentEditRules: {
        paymentMilestones: [{ required: true, message: this.$t('必填') }],
        paymentDate: [{ required: true, message: this.$t('必填') }],
        paymentAmount: [{ required: true, message: this.$t('必填') }]
      },

      fileList: [],
      hasPerm: false // 是否有查看金额权限
    }
  },
  computed: {
    type() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'correspondingMaterialCode',
          title: this.$t('对应物料编码')
        },
        {
          field: 'materialName',
          title: this.$t('物料名称')
        },
        {
          field: 'materialDesc',
          title: this.$t('物料描述')
        }
      ]
    },
    paymentTableRef() {
      return this.$refs.paymentTableRef.$refs.xGrid
    },
    paymentColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'paymentMilestones',
          title: this.$t('付款里程碑'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.paymentMilestones}
                  placeholder={this.$t('请选择')}
                  options={this.paymentMilestonesOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.paymentMilestonesOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'paymentDate',
          title: this.$t('付款时间'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.paymentDate}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                />
              ]
            }
          }
        },
        {
          field: 'paymentAmount',
          title: this.$t('付款金额'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <span>{row.isShow ? row.paymentAmount : '***'}</span>
                  {this.hasPerm && this.type === 'check' ? (
                    <i
                      class={row.isShow ? 'vxe-icon-eye-fill' : 'vxe-icon-eye-fill-close'}
                      style='margin-left: 5px;cursor: pointer'
                      onClick={() => this.showAmount(row)}
                    />
                  ) : null}
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div>
                  {this.hasPerm ? (
                    <vxe-input
                      type='number'
                      v-model={row.paymentAmount}
                      placeholder={this.$t('请输入')}
                      transfer
                      clearable
                    />
                  ) : null}
                </div>
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.type !== 'check' && this.hasPerm,
        trigger: 'click',
        mode: 'row',
        showStatus: this.type !== 'check' && this.hasPerm
      }
    },
    toolbar() {
      let btns = []
      if (this.type !== 'check' && this.hasPerm) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    },
    showUpdate() {
      return (
        this.type !== 'check' &&
        this.modelForm.mouldType === '1' &&
        this.modelForm.operationType === '2'
      )
    }
  },
  created() {
    this.getPermission()
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getPermission() {
      this.$API.moldManagement.verifyPermissionApi().then((res) => {
        if (res.code === 200) {
          this.hasPerm = res.data
        }
      })
    },
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.detailMouldMainApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = res.data
          this.modelForm.managementState = String(res.data.managementState)
          this.tableData = res.data.detailList
          this.fileList = res.data.documentList || []
          this.paymentTableData = this.getAmount(res.data.paymentMilestoneList)
        }
      })
    },
    getAmount(arr) {
      let data = []

      if (this.type !== 'check') {
        let promises = arr.map((item) =>
          this.$API.moldManagement
            .getAmountByCodeApi(item.encryptMap.paymentAmount)
            .then((res) => res.data)
        )
        Promise.all(promises).then((resp) => {
          resp.forEach((amout, i) => {
            data.push({
              ...arr[i],
              paymentAmount: amout,
              isShow: this.hasPerm
            })
          })
        })
      } else {
        data = arr.map((item) => {
          return {
            isShow: false,
            ...item
          }
        })
      }

      return data
    },
    showAmount(row) {
      if (!row.isShow) {
        let key = row.encryptMap.paymentAmount
        this.$API.moldManagement.getAmountByCodeApi(key).then((res) => {
          if (res.code === 200) {
            row.paymentAmount = res.data
            row.isShow = !row.isShow
          }
        })
      } else {
        row.isShow = !row.isShow
      }
    },
    handleUpload() {
      this.$refs['fileRef'].click()
    },
    chooseFiles(data) {
      const fileList = Object.values(data?.target?.files)
      if (fileList.length < 1) {
        return
      }
      if (fileList.length > 5) {
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      // 判断当个文件是否过大
      let isOutOfRange = false
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ content: params.msg })
        return
      }
      fileList.forEach((item) => {
        const _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data, 'fileList')
      })
    },
    async uploadFile(fileData, key) {
      this.$loading()
      const res = await this.$API.fileService.uploadPublicFile(fileData)
      this.$hloading()
      if (res.code === 200) {
        if (key === 'fileList') {
          const dataList = [...this.fileList]
          dataList.push({
            docType: '12',
            fileName: res.data.fileName,
            mainId: this.$route.query.id,
            sysFileId: res.data.id,
            url: res.data.url
          })
          this.fileList = dataList
        }
      }
    },
    // 预览
    handlePreview(file) {
      let params = {
        id: file?.sysFileId,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    // 删除
    handleDelete(file) {
      this.fileList = this.fileList.filter((item) => item.sysFileId !== file.sysFileId)
    },
    // 下载
    handleDownload(file) {
      this.$API.fileService
        .downloadPublicFile({
          id: file.sysFileId
        })
        .then((res) => {
          download({
            fileName: file.fileName,
            blob: res.data
          })
        })
    },
    handleClickToolBar(e) {
      const selectedRecords = this.paymentTableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.paymentAdd()
          break
        case 'delete':
          this.paymentTableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    paymentAdd() {
      const item = { isShow: true }
      this.paymentTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.paymentTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.paymentTableRef.setEditRow(currentViewRecords[0])
      })
    },
    paymentEditComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.paymentTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    supplierChange(e) {
      this.modelForm.supplierName = e.itemData?.supplierName
      if (e?.itemData) {
        this.getDetailAddressBySupplierTenantId(e.itemData.supplierTenantId)
      }
    },
    getDetailAddressBySupplierTenantId(supplierTenantId) {
      let params = {
        supplierTenantId
      }
      this.$API.moldManagement
        .getDetailAddressBySupplierTenantIdApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$set(this.modelForm, 'supplierAddress', res.data)
          } else {
            this.modelForm.supplierAddress = ''
          }
        })
        .catch(() => {
          this.modelForm.supplierAddress = ''
        })
    },
    handleUpdate() {
      this.$dialog({
        data: {
          title: this.$t('更新工序编码'),
          message: this.$t('请确认已填写模具信息，才能更新工序')
        },
        success: () => {
          this.updateProcedure()
          // let params = this.getParams()
          // // 当前供应商赋值？
          // this.$API.moldManagement.saveMouldMainApi(params).then((res) => {
          //   if (res.code === 200) {
          //     this.updateProcedure()
          //   }
          // })
        }
      })
    },
    updateProcedure() {
      this.$dialog({
        data: {
          title: this.$t('更新工序编码')
        },
        modal: () => import('./components/UpdateProcedureDialog.vue'),
        success: (updateProcedure) => {
          let params = this.getParams()
          params.updateProcedure = updateProcedure
          this.$API.moldManagement.updateProcedureMouldMainApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
            }
          })
        }
      })
    },
    beforeSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.handleSave()
        } else {
          this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
        }
      })
    },
    handleSave() {
      let params = this.getParams()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认保存？')
        },
        success: () => {
          this.$API.moldManagement.saveMouldMainApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$router.push({
                name: 'mould-page',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = {
        ...this.modelForm
      }
      let data = this.paymentTableRef.getTableData().visibleData.map((item) => {
        return {
          paymentMilestones: item.paymentMilestones,
          paymentDate: item.paymentDate,
          paymentAmount: item.paymentAmount
        }
      })
      params.paymentMilestone = JSON.stringify(data)
      params.documentList = this.fileList
      params.dateEntry = dayjs(dayjs(params.dateEntry).format('YYYY-MM-DD')).valueOf()
      return params
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.mould-detail {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  overflow-y: scroll;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
  .subTitle {
    padding: 12px 0;
    font-size: 18px;
    font-weight: 600;
  }
}
.file-list {
  margin: 10px 0;
  .file-item {
    padding: 2px 0;
    a {
      color: #409eff;
      i {
        margin-left: 5px;
        font-weight: bold;
      }
    }
  }
}
</style>
