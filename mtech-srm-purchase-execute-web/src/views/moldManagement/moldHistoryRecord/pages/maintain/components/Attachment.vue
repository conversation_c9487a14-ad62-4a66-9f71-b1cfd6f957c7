<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :fix-height="450"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils'
export default {
  props: {
    currentId: {
      type: String,
      default: ''
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      toolbar: [{ code: 'download', name: this.$t('下载'), status: 'info', loading: false }]
    }
  },
  watch: {
    currentId: {
      handler() {
        this.getTableData()
      },
      immediate: true
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'fileName',
          title: this.$t('文件名称')
        },
        {
          field: 'fileType',
          title: this.$t('文件类型')
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['download']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && ['download'].includes(e.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'download':
          this.handleDownload(selectedRecords[0])
          break
        default:
          break
      }
    },
    handleDownload(row) {
      this.$loading()
      this.$API.fileService.downloadPublicFile({ id: row?.id }).then((res) => {
        this.$hloading()
        download({
          fileName: row.fileName,
          blob: new Blob([res.data])
        })
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        fixId: this.currentId,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .attachmentInfoMoldApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
