<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
    height="750"
    width="1710"
  >
    <div class="dialog-content">
      <mt-tabs
        class="toggle-tab"
        :e-tab="false"
        :data-source="tabList"
        :selected-item="selectedItem"
        @handleSelectTab="handleSelectTab"
      />
      <div class="toggle-content">
        <Detail ref="repairRef" v-show="tabIndex == 0" :current-id="currentId" />
        <Attachment ref="repairRef" v-show="tabIndex == 1" :current-id="currentId" />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import Detail from './Detail.vue'
import Attachment from './Attachment.vue'
export default {
  components: { Detail, Attachment },
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],

      tabList: [{ title: this.$t('项目明细') }, { title: this.$t('附件') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    currentId() {
      return this.modalData.id
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    // padding: 0;
    .dialog-content {
      padding: 20px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
