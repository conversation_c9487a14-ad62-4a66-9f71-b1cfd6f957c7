import { i18n } from '@/main.js'

export const maintenanceStatusOptions = [
  { text: i18n.t('待保养'), value: '0' },
  { text: i18n.t('已完成'), value: '1' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'maintenanceNo',
    title: i18n.t('保养单号')
  },
  {
    field: 'mouldNo',
    title: i18n.t('模具编号')
  },
  {
    field: 'mouldName',
    title: i18n.t('模具名称'),
    minWidth: 120
  },
  {
    field: 'depositAddr',
    title: i18n.t('存放地点')
  },
  {
    field: 'assignFactoryCode',
    title: i18n.t('指派工厂'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.assignFactoryName : ''
    }
  },
  {
    field: 'maintenanceStandardId',
    title: i18n.t('模具保养标准')
  },
  {
    field: 'mouldType',
    title: i18n.t('模具类型')
  },
  {
    field: 'fixType',
    title: i18n.t('保养类型')
  },
  {
    field: 'levelType',
    title: i18n.t('保养级别')
  },
  {
    field: 'maintenanceStatus',
    title: i18n.t('保养状态'),
    formatter: ({ cellValue }) => {
      let item = maintenanceStatusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'planFinsihTime',
    title: i18n.t('计划完成时间'),
    minWidth: 120
  },
  {
    field: 'maintainBy',
    title: i18n.t('保养人')
  },
  {
    field: 'maintainTime',
    title: i18n.t('保养时间'),
    minWidth: 120
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  },
  {
    field: 'createdAccountName',
    title: i18n.t('创建人')
  },
  {
    field: 'creationDate',
    title: i18n.t('创建时间'),
    minWidth: 120
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 120
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    fixed: 'right',
    slots: {
      default: 'operateDefault'
    }
  }
]
