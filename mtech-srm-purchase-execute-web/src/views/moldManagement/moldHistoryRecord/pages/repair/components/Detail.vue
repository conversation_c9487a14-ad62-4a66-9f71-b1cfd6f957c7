<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :fix-height="450"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
    >
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    currentId: {
      type: String,
      default: ''
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  watch: {
    currentId: {
      handler() {
        this.getTableData()
      },
      immediate: true
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'defectCode',
          title: this.$t('故障代码')
        },
        {
          field: 'defectCate',
          title: this.$t('故障分类')
        },
        {
          field: 'defectDescription',
          title: this.$t('故障代码描述')
        },
        {
          field: 'faultDescription',
          title: this.$t('故障描述')
        },
        {
          field: 'reasonAnalysis',
          title: this.$t('原因分析')
        },
        {
          field: 'fixMethod',
          title: this.$t('维修方法')
        },
        {
          field: 'detailRemark',
          title: this.$t('备注')
        },
        {
          field: 'createUserName',
          title: this.$t('创建人')
        },
        {
          field: 'createTime',
          title: this.$t('创建时间')
        },
        {
          field: 'updateUserName',
          title: this.$t('更新人')
        },
        {
          field: 'updateTime',
          title: this.$t('更新时间')
        }
      ]
    }
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        fixId: this.currentId,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .detailFixMoldApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
