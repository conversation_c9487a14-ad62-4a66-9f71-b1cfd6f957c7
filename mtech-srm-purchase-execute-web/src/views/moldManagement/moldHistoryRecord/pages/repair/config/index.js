import { i18n } from '@/main.js'

export const sourceTypeOptions = [
  { text: i18n.t('点检报修'), value: '0' },
  { text: i18n.t('保养报修'), value: '1' },
  { text: i18n.t('生产报修'), value: '2' },
  { text: i18n.t('手动报修'), value: '3' }
]
export const fixTypeOptions = [
  { text: i18n.t('it维修'), value: '0' },
  { text: i18n.t('设备内部'), value: '1' },
  { text: i18n.t('设备外部'), value: '2' }
]
export const urgencyDegreeOptions = [
  { text: i18n.t('一般'), value: '0' },
  { text: i18n.t('紧急'), value: '1' },
  { text: i18n.t('非常紧急'), value: '2' }
]
export const fixStateOptions = [
  { text: i18n.t('待维修'), value: '0' },
  { text: i18n.t('已完成'), value: '1' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'fixNo',
    title: i18n.t('维修单号')
  },
  {
    field: 'sourceType',
    title: i18n.t('单据来源'),
    formatter: ({ cellValue }) => {
      let item = sourceTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'moldFactoryCode',
    title: i18n.t('厂家编码')
  },
  {
    field: 'moldNo',
    title: i18n.t('模具编号')
  },
  {
    field: 'moldCode',
    title: i18n.t('模具编码'),
    minWidth: 120
  },
  {
    field: 'moldName',
    title: i18n.t('模具名称'),
    minWidth: 120
  },
  {
    field: 'depositAddr',
    title: i18n.t('存放地点')
  },
  {
    field: 'fixType',
    title: i18n.t('维修类型'),
    formatter: ({ cellValue }) => {
      let item = fixTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'urgencyDegree',
    title: i18n.t('紧急程度'),
    formatter: ({ cellValue }) => {
      let item = urgencyDegreeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'requiredFinishDate',
    title: i18n.t('预计完成时间'),
    minWidth: 120
  },
  {
    field: 'expectedReturnDate',
    title: i18n.t('预计返厂时间'),
    minWidth: 120
  },
  {
    field: 'startTime',
    title: i18n.t('维修开始时间'),
    minWidth: 120
  },
  {
    field: 'fixFinsihDate',
    title: i18n.t('维修完成时间'),
    minWidth: 120
  },
  {
    field: 'maintenanceDuration',
    title: i18n.t('维修时长')
  },
  {
    field: 'factoryCode',
    title: i18n.t('维修工厂')
  },
  {
    field: 'fixPersonName',
    title: i18n.t('维修人')
  },
  {
    field: 'fixState',
    title: i18n.t('维修状态'),
    formatter: ({ cellValue }) => {
      let item = fixStateOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'qualifiedStatus',
    title: i18n.t('合格状态')
  },
  {
    field: 'remark',
    title: i18n.t('备注')
  },
  {
    field: 'sendFactoryCode',
    title: i18n.t('送修工厂'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.sendFactoryName : ''
    }
  },
  {
    field: 'sendPersonName',
    title: i18n.t('送修人')
  },
  {
    field: 'sendDate',
    title: i18n.t('送修时间')
  },
  {
    field: 'createdAccountName',
    title: i18n.t('创建人')
  },
  {
    field: 'creationDate',
    title: i18n.t('创建时间'),
    minWidth: 120
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 120
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    fixed: 'right',
    slots: {
      default: 'operateDefault'
    }
  }
]
