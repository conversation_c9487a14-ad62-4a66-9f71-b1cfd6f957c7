<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :fix-height="450"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
    >
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    currentId: {
      type: String,
      default: ''
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  watch: {
    currentId: {
      handler() {
        this.getTableData()
      },
      immediate: true
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'oaNo',
          title: this.$t('OA审批单号')
        },
        {
          field: 'approveNode',
          title: this.$t('签批节点')
        },
        {
          field: 'userName',
          title: this.$t('人员信息')
        },
        {
          field: 'approveTime',
          title: this.$t('签批时间')
        },
        {
          field: 'approveDesc',
          title: this.$t('签批意见')
        }
      ]
    }
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        businessId: this.currentId,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .logAllotMoldApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    }
  }
}
</script>
