<!-- 模具履历记录 -->
<template>
  <div>
    <mt-tabs
      class="toggle-tab"
      :e-tab="false"
      :data-source="tabList"
      :selected-item="selectedItem"
      @handleSelectTab="handleSelectTab"
    />
    <div class="toggle-content">
      <Repair ref="repairRef" v-show="tabIndex == 0" />
      <Maintain ref="maintainRef" v-show="tabIndex == 1" />
      <Allocation ref="allocationRef" v-show="tabIndex == 2" />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    Repair: () => import('./pages/repair/index.vue'),
    Maintain: () => import('./pages/maintain/index.vue'),
    Allocation: () => import('./pages/allocation/index.vue')
  },
  data() {
    return {
      tabList: [{ title: this.$t('维修') }, { title: this.$t('保养') }, { title: this.$t('调拨') }],
      selectedItem: 0,
      tabIndex: 0
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
    }
  }
}
</script>
