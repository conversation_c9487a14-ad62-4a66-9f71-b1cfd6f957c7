<!-- 审厂管理 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('审厂单号')" prop="examineCode">
          <mt-input
            v-model="searchFormModel.examineCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('审厂主题名称')" prop="examineName">
          <mt-input
            v-model="searchFormModel.examineName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('审厂类型')" prop="examineTypeList">
          <mt-multi-select
            v-model="searchFormModel.examineTypeList"
            :data-source="examineTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('审厂方式')" prop="examineMode">
          <mt-select
            v-model="searchFormModel.examineMode"
            :data-source="examineModeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <!-- <mt-form-item :label="$t('工厂类型')" prop="factoryType">
          <mt-select
            v-model="searchFormModel.factoryType"
            :data-source="factoryTypeOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item> -->
        <mt-form-item :label="$t('考核厂')" prop="factoryCode">
          <RemoteAutocomplete
            v-model="searchFormModel.factoryCode"
            :url="$API.masterData.getSiteAuthFuzzyUrl"
            :placeholder="$t('请选择')"
            :fields="{ text: 'siteName', value: 'siteCode' }"
            params-key="fuzzyParam"
            records-position="data"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item :label="$t('考核厂地点')" prop="factoryAddress">
          <mt-input
            v-model="searchFormModel.factoryAddress"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方联系人')" prop="supplierContactName">
          <mt-input
            v-model="searchFormModel.supplierContactName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方联系电话')" prop="supplierContactPhone">
          <mt-input
            v-model="searchFormModel.supplierContactPhone"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="planExamineDate" :label="$t('计划审厂日期')">
          <mt-date-range-picker
            v-model="searchFormModel.planExamineDate"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'planExamineDate')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否关联模具')" prop="relationMouldFlag">
          <mt-select
            v-model="searchFormModel.relationMouldFlag"
            :data-source="options"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否需要各部门线上确认')" prop="onlineConfirmFlag">
          <mt-select
            v-model="searchFormModel.onlineConfirmFlag"
            :data-source="options"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('确认时长/天')" prop="confirmDuration">
          <mt-input
            v-model="searchFormModel.confirmDuration"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('是否允许供方申诉')" prop="allowAppealFlag">
          <mt-select
            v-model="searchFormModel.allowAppealFlag"
            :data-source="options"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="appealEndTime" :label="$t('供方计划申诉结束时间')">
          <mt-date-range-picker
            v-model="searchFormModel.appealEndTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'appealEndTime')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('供方申诉时长/天')" prop="appealDuration">
          <mt-input
            v-model="searchFormModel.appealDuration"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('考核频率')" prop="examineFrequencyList">
          <mt-multi-select
            v-model="searchFormModel.examineFrequencyList"
            :data-source="examineFrequencyOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item prop="remindTime" :label="$t('审核提醒周期')">
          <mt-date-range-picker
            v-model="searchFormModel.remindTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="remindTimeChange"
          />
        </mt-form-item>
        <mt-form-item :label="$t('使用组织')" prop="orgCode">
          <mt-input
            v-model="searchFormModel.orgCode"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('超期供方是否自动确认')" prop="autoConfirmFlag">
          <mt-select
            v-model="searchFormModel.autoConfirmFlag"
            :data-source="options"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('创建人')" prop="createUserName">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'createTime')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('更新人')" prop="updateUserName">
          <mt-input
            v-model="searchFormModel.updateUserName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')">
          <mt-date-range-picker
            v-model="searchFormModel.updateTime"
            :placeholder="$t('请选择日期')"
            :show-clear-button="true"
            :change="(e) => dateTimeChange(e, 'updateTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="83614778-115f-4d6a-b053-5c7fec564cdb"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #examineCodeDefault="{ row }">
        <div>
          <span style="cursor: pointer; color: #2783fe" @click="handleCheck(row)">
            {{ row.examineCode }}
          </span>
        </div>
      </template>
      <template #operateDefault="{ row }">
        <div>
          <span
            v-if="row.showDeptConfirmBtn"
            style="cursor: pointer; color: #2783fe"
            @click="handleConfirm(row)"
          >
            {{ $t('部门确认') }}
          </span>
          <span
            v-if="row.showExecuteScoreBtn"
            style="cursor: pointer; color: #2783fe"
            @click="handleScore(row)"
          >
            {{ $t('执行打分') }}
          </span>
          <span
            v-if="row.showScoreApprovalBtn"
            style="cursor: pointer; color: #2783fe"
            @click="handlePass(row)"
          >
            {{ $t('通过') }}
          </span>
          <span
            v-if="row.showScoreApprovalBtn"
            style="cursor: pointer; color: #2783fe"
            @click="handleReject(row)"
          >
            {{ $t('驳回') }}
          </span>
          <span
            v-if="row.showRectificationConfirmBtn"
            style="cursor: pointer; color: #2783fe"
            @click="handleRecConfirm(row)"
          >
            {{ $t('整改确认') }}
          </span>
          <span
            v-if="row.showRectificationApprovalBtn"
            style="cursor: pointer; color: #2783fe"
            @click="handleReview(row)"
          >
            {{ $t('整改审核') }}
          </span>
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import {
  columnData,
  statusOptions,
  examineTypeOptions,
  examineModeOptions,
  factoryTypeOptions,
  options,
  examineFrequencyOptions
} from './config'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info',
          loading: false,
          permission: ['O_02_1776']
        },
        {
          code: 'edit',
          name: this.$t('编辑'),
          status: 'info',
          loading: false,
          permission: ['O_02_1777']
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info',
          loading: false,
          permission: ['O_02_1778']
        },
        {
          code: 'resultRecord',
          name: this.$t('审厂结果记录'),
          status: 'info',
          loading: false,
          permission: ['O_02_1779']
        },
        {
          code: 'record',
          name: this.$t('内容变更记录'),
          status: 'info',
          loading: false,
          permission: ['O_02_1780']
        },
        {
          code: 'export',
          name: this.$t('导出'),
          status: 'info',
          loading: false,
          permission: ['O_02_1781']
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      examineTypeOptions,
      examineModeOptions,
      factoryTypeOptions,
      options,
      examineFrequencyOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    remindTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['remindStartTime'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel['remindEndTime'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel['remindStartTime'] = null
        this.searchFormModel['remindEndTime'] = null
      }
    },
    dateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'Start'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.searchFormModel[field + 'End'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.searchFormModel[field + 'Start'] = null
        this.searchFormModel[field + 'End'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .pageFactoryAuditManagementApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const ids = selectedRecords.map((v) => v.id)
      const commonToolbar = ['edit', 'delete', 'record']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && ['edit', 'record'].includes(e.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }
      if (selectedRecords.some((v) => ![0, 1, 2].includes(v.status)) && e.code === 'edit') {
        this.$toast({ content: this.$t('【待供方确认】前才可编辑'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleEdit(selectedRecords[0])
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'export':
          e.loading = true
          this.handleExport(e, ids)
          break
        case 'record':
          this.handleRecord(selectedRecords[0])
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$router.push({
        name: 'factory-audit-management-detail',
        query: {
          type: 'add',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleEdit(row) {
      this.$router.push({
        name: 'factory-audit-management-detail',
        query: {
          type: 'edit',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.moldManagement.deleteFactoryAuditManagementApi({ ids }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleExport(e, ids = []) {
      const params = {
        idList: ids,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$API.moldManagement
        .exportFactoryAuditManagementApi(params)
        .then((res) => {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          e.loading = false
        })
    },
    handleRecord(row) {
      this.$dialog({
        data: {
          title: this.$t('内容变更记录'),
          id: row.id
        },
        modal: () => import('./components/LogDialog.vue'),
        success: () => {}
      })
    },
    handleConfirm(row) {
      let _this = this
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('部门确认？')
        },
        success: () => {
          let params = { id: row.id }
          _this.$API.moldManagement.deptConfirmApi(params).then((res) => {
            if (res.code === 200) {
              _this.$toast({ content: this.$t('确认成功'), type: 'success' })
              _this.handleSearch()
            }
          })
        }
      })
    },
    handleScore(row) {
      this.$router.push({
        name: 'factory-audit-management-score',
        query: {
          type: 'score',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handlePass(row) {
      let _this = this
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认通过？')
        },
        success: () => {
          let params = { id: row.id, agreeFlag: true }
          _this.$API.moldManagement.scoreApprovalApi(params).then((res) => {
            if (res.code === 200) {
              _this.$toast({ content: this.$t('操作成功'), type: 'success' })
              _this.handleSearch()
            }
          })
        }
      })
    },
    handleReject(row) {
      let _this = this
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认驳回？')
        },
        success: () => {
          let params = { id: row.id, agreeFlag: false }
          _this.$API.moldManagement.scoreApprovalApi(params).then((res) => {
            if (res.code === 200) {
              _this.$toast({ content: this.$t('操作成功'), type: 'success' })
              _this.handleSearch()
            }
          })
        }
      })
    },
    handleRecConfirm(row) {
      this.$router.push({
        name: 'factory-audit-management-appeal-confirm',
        query: {
          type: 'appeal',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleReview(row) {
      let _this = this
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认整改审核通过？')
        },
        success: () => {
          let params = { id: row.id }
          _this.$API.moldManagement.rectificationApprovalApi(params).then((res) => {
            if (res.code === 200) {
              _this.$toast({ content: this.$t('操作成功'), type: 'success' })
              _this.handleSearch()
            }
          })
        }
      })
    },
    handleCheck(row) {
      this.$router.push({
        name: 'factory-audit-management-oa',
        query: {
          id: row.id,
          type: 'check',
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>
