<template>
  <div class="factory-audit-score">
    <div class="top">
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button @click="handleSubmit">{{ $t('提交') }}</mt-button>
    </div>

    <div>
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-tabs
            :e-tab="false"
            :data-source="tabList"
            :selected-item="tabIndex"
            @handleSelectTab="handleSelectTab"
          />
        </mt-col>
      </mt-row>
      <div v-if="tabIndex === 0">
        <Summary v-if="summaryFormData" :form-data="summaryFormData" />
      </div>
      <div v-if="tabIndex === 1">
        <ImprovementSummary :summary-data="summaryData" :can-edit="true" @save="summarySave" />
      </div>
      <div v-if="tabIndex === 2">
        <ImprovementDetail :detail-data="detailData" @save="detailSave" />
      </div>
    </div>
  </div>
</template>

<script>
import Summary from './components/Summary.vue'
import ImprovementSummary from './components/ImprovementSummary.vue'
import ImprovementDetail from './components/ImprovementDetail.vue'
export default {
  components: { Summary, ImprovementSummary, ImprovementDetail },
  data() {
    return {
      tabList: [
        { title: this.$t('汇总') },
        { title: this.$t('待改善要求汇总') },
        { title: this.$t('待改善要求明细') }
      ],
      tabIndex: 0,

      modelForm: {},

      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      summaryFormData: null,
      summaryData: [],
      detailData: []
    }
  },
  created() {
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.buyerQueryResultAppealApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = res.data
          this.summaryFormData = res.data.summary
          this.summaryData = res.data.improveSummary || []
          this.detailData = res.data.improveDetail
        }
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    summarySave(data) {
      this.summaryData = data
    },
    detailSave(data) {
      this.detailData.forEach((item) => {
        item.detailList = data[item.module]
      })
    },
    handleSubmit() {
      let params = this.getParams()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交？')
        },
        success: () => {
          this.$API.moldManagement.buyerSubmitResultAppealApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$router.push({
                name: 'factory-audit-management',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = {
        examineId: this.modelForm.examineId,
        noAgreeFlag: Number(this.modelForm.noAgreeFlag)
      }
      params.summary = this.summaryFormData
      params.improveSummary = this.summaryData
      params.improveDetail = this.detailData

      return params
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.factory-audit-score {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
</style>
