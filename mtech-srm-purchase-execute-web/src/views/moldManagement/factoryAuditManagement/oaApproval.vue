<template>
  <div class="factory-audit-score">
    <div v-if="type === 'check'" class="top">
      <mt-button @click="handleCancel">{{ $t('返回') }}</mt-button>
    </div>
    <div style="font-weight: 600; margin-bottom: 10px">{{ $t('审厂单号') }}：{{ examineCode }}</div>
    <div>
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-tabs
            :e-tab="false"
            :data-source="tabList"
            :selected-item="tabIndex"
            @handleSelectTab="handleSelectTab"
          />
        </mt-col>
      </mt-row>
      <div v-if="tabIndex === 0">
        <Summary v-if="summaryFormData" :form-data="summaryFormData" />
      </div>
      <div v-if="tabIndex === 1">
        <ImprovementSummary :summary-data="summaryData" />
      </div>
      <div v-if="tabIndex === 2">
        <ImprovementDetail :detail-data="detailData" />
      </div>
    </div>
  </div>
</template>

<script>
import Summary from './components/Summary.vue'
import ImprovementSummary from './components/ImprovementSummary.vue'
import ImprovementDetail from './components/ImprovementDetail.vue'
export default {
  components: { Summary, ImprovementSummary, ImprovementDetail },
  data() {
    return {
      tabList: [
        { title: this.$t('汇总') },
        { title: this.$t('待改善要求汇总') },
        { title: this.$t('待改善要求明细') }
      ],
      tabIndex: 0,

      modelForm: {},

      summaryFormData: null,
      summaryData: [],
      detailData: [],
      examineCode: null
    }
  },
  created() {
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  computed: {
    type() {
      return this.$route.query?.type
    }
  },
  methods: {
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.queryOaViewApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = res.data
          this.summaryFormData = res.data.summary
          this.summaryData = res.data.improveSummary || []
          this.detailData = res.data.improveDetail
          this.examineCode = res.data.examineCode
        }
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.factory-audit-score {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
</style>
