<template>
  <div class="factory-audit-score">
    <div class="top">
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button v-if="showDetailTable" @click="handleSubmit">{{ $t('提交') }}</mt-button>
    </div>
    <div>
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-tabs
            :e-tab="false"
            :data-source="tabList"
            :selected-item="tabIndex"
            @handleSelectTab="handleSelectTab"
          />
        </mt-col>
      </mt-row>
      <mt-row v-if="showDetailTable" :gutter="24">
        <mt-col :span="24">
          <sc-table
            ref="detailTableRef"
            :fix-height="480"
            :columns="detailColumns"
            :table-data="currentDetailTableData"
            :edit-config="editConfig"
            :edit-rules="detailEditRules"
            keep-source
            :sortable="false"
            :is-show-right-btn="false"
            :is-show-refresh-bth="false"
          >
          </sc-table>
        </mt-col>
      </mt-row>
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      tabList: [],
      tabIndex: 0,
      tabName: null,

      currentDetailColumns: [], // 当前显示明细列
      // detailColumns: {}, // 所有模块明细列
      currentDetailTableData: [], // 当前显示明细
      detailTableData: {}, // 所有模块明细
      detailEditRules: {
        score: [{ required: true, message: this.$t('必填') }],
        explain: [{ required: true, message: this.$t('必填') }]
      },

      modelForm: {},
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  computed: {
    detailTableRef() {
      return this.$refs.detailTableRef.$refs.xGrid
    },
    showDetailTable() {
      return this.tabList.length > 0
    },
    fixedDetailColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'scoreType',
          title: this.$t('类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.scoreType}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'examineType',
          title: this.$t('考核类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.examineType}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'ratingContent',
          title: this.$t('评分内容'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.ratingContent}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'problemLevel',
          title: this.$t('问题等级'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.problemLevel}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'marking',
          title: this.$t('评分'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.marking}
                  placeholder={this.$t('请输入')}
                  min='0'
                  max='100'
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    detailColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'examineType',
          title: this.$t('考核类型')
        },
        {
          field: 'scoreContent',
          title: this.$t('评分内容')
        },
        {
          field: 'problemLevel',
          title: this.$t('问题等级')
        },
        {
          field: 'standardScore',
          title: this.$t('标分')
        },
        {
          field: 'score',
          title: this.$t('得分'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.score}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max={row.standardScore}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'explain',
          title: this.$t('稽查发现/得分说明'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.explain}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    }
  },
  created() {
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.getScoreDetailApi(params).then((res) => {
        if (res.code === 200) {
          this.tabList = res.data.scoreModuleList.map((item) => {
            return {
              title: item.module
            }
          })
          this.modelForm = res.data
          this.setDetail()
        }
      })
    },
    setDetail() {
      this.tabList.forEach((item) => {
        this.detailTableData[item.title] = this.modelForm.scoreModuleList.find(
          (v) => v.module === item.title
        )?.scoreItemList
      })
      this.tabName = this.tabList[this.tabIndex]?.title
      let currentDetailTableData = this.detailTableData[this.tabName] || []
      this.currentDetailTableData = currentDetailTableData
    },
    handleSelectTab(e) {
      this.tabIndex = e
      this.tabName = this.tabList[e]?.title
      let currentDetailTableData = this.detailTableData[this.tabName] || []
      this.currentDetailTableData = currentDetailTableData
    },
    validateTableData() {
      let valid = false
      let arr = Object.values(this.detailTableData)
      for (let i = 0; i < arr.length; i++) {
        valid = arr[i].some((v) => !v.score || !v.explain)
        if (valid) {
          this.tabIndex = i
          break
        }
      }
      return valid
    },
    handleSubmit() {
      if (this.validateTableData()) {
        this.$toast({ content: this.$t('请完成必填项后进行提交操作'), type: 'warning' })
        return
      }
      let params = this.getParams()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认提交？')
        },
        success: () => {
          this.$API.moldManagement.saveScoreApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('提交成功'), type: 'success' })
              this.$router.push({
                name: 'factory-audit-management',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = { ...this.modelForm }
      params.scoreModuleList.forEach((item) => {
        item.scoreItemList = this.detailTableData[item.module]
      })
      return params
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.factory-audit-score {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
</style>
