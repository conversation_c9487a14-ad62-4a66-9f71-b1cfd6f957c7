<!-- 审厂管理 - 新增/编辑 -->
<template>
  <div class="factory-audit-detail">
    <div v-if="isNextStep" class="top">
      <mt-button type="primary" @click="handleBack">{{ $t('上一步') }}</mt-button>
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button @click="beforeSave">{{ $t('保存') }}</mt-button>
    </div>
    <div v-if="!isNextStep" class="top">
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button type="primary" @click="handleNext">{{ $t('下一步') }}</mt-button>
    </div>
    <mt-form ref="formRef" :model="modelForm" :rules="formRules">
      <div v-show="!isNextStep">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('审厂主题名称')" prop="examineName">
              <mt-input
                v-model="modelForm.examineName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('审厂类型')" prop="examineType">
              <mt-select
                v-model="modelForm.examineType"
                :data-source="examineTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('审厂方式')" prop="examineMode">
              <mt-radio v-model="modelForm.examineMode" :data-source="examineModeOptions" />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('考核频率')" prop="examineFrequency">
              <mt-radio
                v-model="modelForm.examineFrequency"
                :data-source="examineFrequencyOptions"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('审核提醒周期')" prop="remindTime">
              <mt-date-range-picker
                v-model="modelForm.remindTime"
                :placeholder="$t('请选择日期')"
                :show-clear-button="true"
                :change="remindTimeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('使用组织')" prop="orgCode">
              <mt-select
                v-model="modelForm.orgCode"
                :data-source="orgCodeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
                @change="orgChange"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <!-- <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('工厂类型')" prop="factoryType">
              <mt-select
                v-model="modelForm.factoryType"
                :data-source="factoryTypeOptions"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row> -->
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-form-item>
              <div>
                <sc-table
                  ref="sctableRef"
                  grid-id="e6d04053-9ab6-4c4b-8d68-162ed584cf55"
                  :loading="loading"
                  :columns="columns"
                  :table-data="tableData"
                  :edit-config="editConfig"
                  :edit-rules="editRules"
                  keep-source
                  :sortable="false"
                  :is-show-right-btn="false"
                  :is-show-refresh-bth="false"
                  @edit-closed="editComplete"
                >
                  <template slot="custom-tools">
                    <vxe-button
                      v-for="item in toolbar"
                      :key="item.code"
                      :status="item.status"
                      :icon="item.icon"
                      :loading="item.loading"
                      size="small"
                      @click="handleClickToolBar(item)"
                    >
                      {{ item.name }}
                    </vxe-button>
                  </template>
                </sc-table>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
      <div v-if="isNextStep">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('选择模板')" prop="templateCode">
              <mt-select
                v-model="modelForm.templateCode"
                :data-source="tempOptions"
                :fields="{ text: 'templateName', value: 'templateCode' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
                @change="templateChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('是否需要各部门线上确认')" prop="onlineConfirmFlag">
              <mt-radio v-model="modelForm.onlineConfirmFlag" :data-source="options" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('确认时长/天')" prop="confirmDuration">
              <mt-input-number
                v-model="modelForm.confirmDuration"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :show-spin-button="false"
                :min="1"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24" style="margin-bottom: 20px">
          <mt-col :span="18">
            <div>
              <sc-table
                ref="userTableRef"
                grid-id="327ebcec-fba2-4edc-820f-fd620e03358b"
                :fix-height="180"
                :loading="loading"
                :columns="userColumns"
                :table-data="userTableData"
                :edit-config="editConfig"
                :edit-rules="userEditRules"
                keep-source
                :sortable="false"
                :is-show-right-btn="false"
                :is-show-refresh-bth="false"
              >
                <template slot="custom-tools">
                  <vxe-button
                    v-for="item in userToolbar"
                    :key="item.code"
                    :status="item.status"
                    :icon="item.icon"
                    :loading="item.loading"
                    size="small"
                    @click="userClickToolBar(item)"
                  >
                    {{ item.name }}
                  </vxe-button>
                </template>
                <template #userEdit="{ row }">
                  <SelectUser
                    :value="row.userName"
                    :model-form="row"
                    :value-mapping="{ userName: 'employeeName', userCode: 'value' }"
                    @selectUser="(item) => userSelect(row, item)"
                  />
                </template>
              </sc-table>
            </div>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('供方审厂结果是否走OA会签')" prop="oaCountersignFlag">
              <mt-radio v-model="modelForm.oaCountersignFlag" :data-source="options" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('是否通知供方考核')" prop="noticeSupplierExamineFlag">
              <mt-radio v-model="modelForm.noticeSupplierExamineFlag" :data-source="options" />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item
              :label="$t('通知供方审厂需要提前准备资料（内容将通过站内信、邮件通知）')"
              prop="supplierPrepareInfo"
            >
              <mt-input
                type="text"
                v-model="modelForm.supplierPrepareInfo"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :multiline="true"
                :rows="5"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
    </mt-form>
    <BatchUpdateDialog ref="batchUpdateDialogRef" @confirm="batchUpdateConfirm" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  examineTypeOptions,
  examineModeOptions,
  examineFrequencyOptions,
  factoryTypeOptions,
  options
} from './config'
import ScTable from '@/components/ScTable/src/index'
import SelectUser from './components/SelectUser.vue'
export default {
  components: {
    ScTable,
    SelectUser,
    BatchUpdateDialog: () => import('./components/BatchUpdateDialog')
  },
  data() {
    return {
      isNextStep: false,
      modelForm: {
        orgCode: 'KT',
        orgName: this.$t('空调'),
        onlineConfirmFlag: '0',
        oaCountersignFlag: '0',
        noticeSupplierExamineFlag: '0'
      },

      examineTypeOptions,
      examineModeOptions,
      examineFrequencyOptions,
      factoryTypeOptions,
      options,

      orgCodeOptions: [{ text: this.$t('空调'), value: 'KT' }],
      tempOptions: [],

      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      loading: false,
      tableData: [],
      toolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
        { code: 'batchUpdate', name: this.$t('批量编辑'), status: 'info', loading: false }
      ],
      userToolbar: [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
      ],
      factoryList: [],

      userTableData: [],
      userEditRules: {
        userCode: [{ required: true, message: this.$t('必填') }]
      },

      firstInit: false
    }
  },
  computed: {
    type() {
      return this.$route.query?.type
    },
    formRules() {
      return {
        examineName: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入审厂主题名称')
          }
        ],
        examineType: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择审厂类型')
          }
        ],
        examineMode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择审厂方式')
          }
        ],
        examineFrequency: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择考核频率')
          }
        ],
        remindTime: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择审核提醒周期')
          }
        ],
        orgCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择使用组织')
          }
        ],
        templateCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择模板')
          }
        ],
        onlineConfirmFlag: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择')
          }
        ],
        confirmDuration: [
          {
            required: this.modelForm?.onlineConfirmFlag === '1',
            trigger: 'blur',
            message: this.$t('请输入确认时长/天')
          }
        ],
        oaCountersignFlag: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择')
          }
        ],
        noticeSupplierExamineFlag: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择')
          }
        ],
        supplierPrepareInfo: [
          {
            required: this.modelForm?.noticeSupplierExamineFlag === '1',
            trigger: 'blur',
            message: this.$t('请输入准备资料')
          }
        ]
      }
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        // {
        //   field: 'factoryType',
        //   title: this.$t('工厂类型'),
        //   editRender: {},
        //   slots: {
        //     edit: ({ row }) => {
        //       return [
        //         <vxe-select
        //           v-model={row.factoryType}
        //           placeholder={this.$t('请选择')}
        //           options={this.factoryTypeOptions}
        //           transfer
        //           clearable
        //           onChange={() => {
        //             row.factoryCode = null
        //             row.factoryName = null
        //             row.factoryAddress = null
        //             row.supplierContactName = null
        //             row.supplierContactPhone = null
        //             this.factoryTypeChange(row)
        //           }}
        //         />
        //       ]
        //     }
        //   },
        //   formatter: ({ cellValue }) => {
        //     let item = this.factoryTypeOptions.find((item) => item.value === cellValue)
        //     return item ? item.label : ''
        //   }
        // },
        {
          field: 'factoryCode',
          title: this.$t('工厂'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.factoryCode}
                  placeholder={this.$t('请选择')}
                  options={this.factoryOptions}
                  option-props={{ label: 'label', value: 'factoryCode' }}
                  transfer
                  clearable
                  filterable
                  onChange={(e) => {
                    let item = this.factoryOptions.find((v) => v.factoryCode === e.value)
                    row.factoryName = item?.factoryName
                    row.factoryAddress = item?.factoryAddress
                    row.supplierContactName = item?.supplierContactName
                    row.supplierContactPhone = item?.supplierContactPhone
                  }}
                />
              ]
            }
          },
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.factoryName : ''
          }
        },
        {
          field: 'factoryAddress',
          title: this.$t('工厂地点'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.factoryAddress}
                  placeholder={this.$t('请选择工厂')}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'supplierContactName',
          title: this.$t('供方联系人'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.supplierContactName}
                  placeholder={this.$t('请选择工厂')}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'supplierContactPhone',
          title: this.$t('供方联系电话'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.supplierContactPhone}
                  placeholder={this.$t('请选择工厂')}
                  transfer
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'planExamineDate',
          title: this.$t('计划审厂日期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const planExamineDateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.planExamineDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={planExamineDateDisabled}
                  onChange={() => {
                    row.appealEndTime = dayjs(row.planExamineDate)
                      .add(row.appealDuration, 'day')
                      .format('YYYY-MM-DD')
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'relationMouldFlag',
          title: this.$t('是否关联模具'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.relationMouldFlag}
                  placeholder={this.$t('请选择')}
                  options={this.options}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.options.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'allowAppealFlag',
          title: this.$t('是否允许供方申诉'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.allowAppealFlag}
                  placeholder={this.$t('请选择')}
                  options={this.options}
                  transfer
                  clearable
                  onChange={() => {
                    row.appealDuration = null
                    row.appealEndTime = null
                    if (row.allowAppealFlag === '0') {
                      row.autoConfirmFlag = '1'
                    }
                  }}
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.options.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        },
        {
          field: 'appealDuration',
          title: this.$t('供方申诉时长/天'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.appealDuration}
                  placeholder={this.$t('请输入')}
                  min='1'
                  transfer
                  clearable
                  disabled={row.allowAppealFlag === '0'}
                  onChange={() => {
                    row.appealEndTime = dayjs(row.planExamineDate)
                      .add(row.appealDuration, 'day')
                      .format('YYYY-MM-DD')
                  }}
                />
              ]
            }
          }
        },
        {
          field: 'appealEndTime',
          title: this.$t('供方计划申诉结束时间'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.appealEndTime}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled
                />
              ]
            }
          }
        },
        {
          field: 'autoConfirmFlag',
          title: this.$t('超期供方是否自动确认'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.autoConfirmFlag}
                  placeholder={this.$t('请选择')}
                  options={this.options}
                  transfer
                  clearable
                  disabled={row.allowAppealFlag === '0'}
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.options.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        }
      ]
    },
    editRules() {
      return {
        factoryType: [{ required: true, message: this.$t('必填') }],
        factoryCode: [{ required: true, message: this.$t('必填') }],
        planExamineDate: [{ required: true, message: this.$t('必填') }],
        relationMouldFlag: [{ required: true, message: this.$t('必填') }],
        allowAppealFlag: [{ required: true, message: this.$t('必填') }],
        // appealDuration: [{ required: true, message: this.$t('必填') }],
        // appealEndTime: [{ required: true, message: this.$t('必填') }],
        autoConfirmFlag: [{ required: true, message: this.$t('必填') }]
      }
    },
    userTableRef() {
      return this.$refs.userTableRef.$refs.xGrid
    },
    userColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'module',
          title: this.$t('模块')
        },
        {
          field: 'departmentCode',
          title: this.$t('部门'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.departmentCode} transfer disabled />]
            }
          },
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.departmentName : ''
          }
        },
        {
          field: 'userCode',
          title: this.$t('员工'),
          editRender: {},
          slots: {
            edit: 'userEdit'
          },
          formatter: ({ cellValue, row }) => {
            return cellValue ? cellValue + '-' + row.userName : ''
          }
        },
        {
          field: 'email',
          title: this.$t('邮箱'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.email} transfer clearable />]
            }
          }
        }
      ]
    }
  },
  created() {
    this.getFactoryList()
    this.getTemplateList()
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      this.firstInit = true
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.detailFactoryAuditManagementApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = {
            id: res.data.id,
            examineCode: res.data.examineCode,
            examineName: res.data.examineName,
            examineType: res.data.examineType,
            examineMode: String(res.data.examineMode),
            examineFrequency: String(res.data.examineFrequency),
            orgCode: res.data.orgCode,
            orgName: res.data.orgName,
            templateId: res.data.templateId,
            templateCode: res.data.templateCode,
            templateName: res.data.templateName,
            onlineConfirmFlag: String(res.data.onlineConfirmFlag),
            confirmDuration: res.data.confirmDuration,
            oaCountersignFlag: String(res.data.oaCountersignFlag),
            noticeSupplierExamineFlag: String(res.data.noticeSupplierExamineFlag),
            supplierPrepareInfo: String(res.data.supplierPrepareInfo)
          }
          this.modelForm.remindTime = [
            new Date(dayjs(Number(res.data.remindStartTime))),
            new Date(dayjs(Number(res.data.remindEndTime)))
          ]
          this.tableData = res.data.factoryList.map((item) => {
            return {
              factoryType: item.factoryType,
              factoryCode: item.factoryCode,
              factoryName: item.factoryName,
              factoryAddress: item.factoryAddress,
              supplierTenantId: item.supplierTenantId,
              supplierContactName: item.supplierContactName,
              supplierContactPhone: item.supplierContactPhone,
              planExamineDate: dayjs(Number(item.planExamineDate)).format('YYYY-MM-DD'),
              relationMouldFlag: String(item.relationMouldFlag),
              allowAppealFlag: String(item.allowAppealFlag),
              appealDuration: item.appealDuration !== 0 ? item.appealDuration : null,
              appealEndTime:
                item.appealEndTime !== '0'
                  ? dayjs(Number(item.appealEndTime)).format('YYYY-MM-DD')
                  : null,
              autoConfirmFlag: String(item.autoConfirmFlag)
            }
          })
          this.userTableData = res.data.personList
        }
      })
    },
    orgChange(e) {
      this.modelForm.orgName = e.itemData?.text
    },
    templateChange(e) {
      this.modelForm.templateId = e.itemData?.id
      this.modelForm.templateName = e.itemData?.templateName
      if (this.modelForm.templateId && !this.firstInit) {
        this.getDeptByTempId(this.modelForm.templateId)
      }
      this.firstInit = false
    },
    getDeptByTempId(templateId) {
      let params = {
        templateId
      }
      this.$API.moldManagement.queryExamineDepartmentListApi(params).then((res) => {
        if (res.code === 200) {
          this.userTableData = res.data.map((item) => {
            return {
              module: item.module,
              ...item
            }
          })
        }
      })
    },
    getTemplateList() {
      this.$API.moldManagement.queryTemplateListApi().then((res) => {
        if (res.code === 200) {
          this.tempOptions = res.data
        }
      })
    },
    factoryTypeChange(row) {
      let data = this.factoryList.filter((item) => item.factoryType === row.factoryType)
      this.$set(row, 'factoryOptions', data)
    },
    getFactoryList() {
      this.$API.moldManagement.queryListApi().then((res) => {
        if (res.code === 200) {
          this.factoryOptions = res.data.map((item) => {
            return {
              label: item.factoryCode + '-' + item.factoryName,
              ...item
            }
          })
        }
      })
    },
    userSelect(row, item) {
      row.userCode = item.accountName
      row.userName = item.employeeName
      row.departmentCode = item.departmentOrgCode
      row.departmentName = item.departmentOrgName
      row.email = item.email
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete', 'batchUpdate']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.tableRef.removeCheckboxRow()
          break
        case 'batchUpdate':
          this.handleBatchUpdate(selectedRecords)
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {
        relationMouldFlag: '0',
        allowAppealFlag: '1',
        autoConfirmFlag: '0'
      }
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    remindTimeChange(e) {
      if (e.startDate) {
        this.modelForm['remindStartTime'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        )
        this.modelForm['remindEndTime'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        )
      } else {
        this.modelForm['remindStartTime'] = null
        this.modelForm['remindEndTime'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleBatchUpdate(selectedRecords) {
      this.$refs.batchUpdateDialogRef.dialogInit({
        title: this.$t('批量修改'),
        selectedRecords
      })
    },
    batchUpdateConfirm(args) {
      const { ids, form } = args
      ids.forEach((id) => {
        let item = this.tableData.find((v) => v.id === id)
        if (item) {
          item.planExamineDate = form?.planExamineDate
            ? dayjs(form?.planExamineDate).format('YYYY-MM-DD')
            : item.planExamineDate
          item.relationMouldFlag = form?.relationMouldFlag
          item.allowAppealFlag = form?.allowAppealFlag
          item.appealDuration = form?.appealDuration || item.appealDuration
          item.autoConfirmFlag = form?.autoConfirmFlag
          item.appealEndTime = item.appealDuration
            ? dayjs(item.planExamineDate).add(item.appealDuration, 'day').format('YYYY-MM-DD')
            : item.appealEndTime
        }
      })
    },
    userClickToolBar(e) {
      const selectedRecords = this.userTableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.userAdd()
          break
        case 'delete':
          this.userTableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    userAdd() {
      const item = {}
      this.userTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.userTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.userTableRef.setEditRow(currentViewRecords[0])
      })
    },
    userEditComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.userTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    validateUserTableData() {
      let valid = false
      let arr = this.userTableRef.getTableData()?.visibleData
      valid = arr.some((v) => !v.userCode)
      return valid
    },
    beforeSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.validateUserTableData()) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
          this.handleSave()
        }
      })
    },
    handleSave() {
      let params = this.getParams()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认保存？')
        },
        success: () => {
          this.$API.moldManagement.saveFactoryAuditManagementApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$router.push({
                name: 'factory-audit-management',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = { ...this.modelForm }
      params.examineMode = Number(params.examineMode)
      params.examineFrequency = Number(params.examineFrequency)
      params.onlineConfirmFlag = Number(params.onlineConfirmFlag)
      params.oaCountersignFlag = Number(params.oaCountersignFlag)
      params.noticeSupplierExamineFlag = Number(params.noticeSupplierExamineFlag)
      params.factoryList = this.tableRef.getTableData()?.visibleData?.map((item) => {
        return {
          id: item.id?.includes('row_') ? null : item.id,
          factoryType: item.factoryType,
          factoryCode: item.factoryCode,
          factoryName: item.factoryName,
          factoryAddress: item.factoryAddress,
          supplierContactName: item.supplierContactName,
          supplierContactPhone: item.supplierContactPhone,
          planExamineDate: dayjs(item.planExamineDate).valueOf(),
          relationMouldFlag: Number(item.relationMouldFlag),
          allowAppealFlag: Number(item.allowAppealFlag),
          appealDuration: item.appealDuration,
          appealEndTime: dayjs(item.appealEndTime).valueOf(),
          autoConfirmFlag: Number(item.autoConfirmFlag)
        }
      })
      params.personList = this.userTableRef.getTableData()?.visibleData?.map((item) => {
        return {
          id: item.id?.includes('row_') ? null : item.id,
          module: item.module,
          departmentCode: item.departmentCode,
          departmentName: item.departmentName,
          userCode: item.userCode,
          userName: item.userName,
          email: item.email
        }
      })
      return params
    },
    handleCancel() {
      this.tableRef.clearEdit()
      if (this.isNextStep) {
        this.userTableRef.clearEdit()
      }
      this.$router.go(-1)
    },
    handleNext() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.validateTableData()) {
            this.$toast({ content: this.$t('允许供方申诉时，供方申诉时长必填'), type: 'warning' })
            return
          } else {
            this.isNextStep = true
          }
        }
      })
    },
    validateTableData() {
      let valid = false
      let arr = this.tableRef.getTableData()?.visibleData
      valid = arr.some((v) => v.allowAppealFlag === '1' && v.appealDuration <= 0)
      return valid
    },
    handleBack() {
      this.isNextStep = false
    }
  }
}
</script>

<style lang="scss" scoped>
.factory-audit-detail {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
</style>
