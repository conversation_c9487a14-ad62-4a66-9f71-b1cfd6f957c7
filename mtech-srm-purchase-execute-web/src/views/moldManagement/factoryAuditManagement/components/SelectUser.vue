<!--下拉选人-->
<template>
  <vxe-pulldown ref="pullDown" transfer v-model="showPull" style="width: 100%">
    <template #default>
      <vxe-input
        v-if="showInput"
        size="small"
        v-model="newValue"
        :placeholder="$t('请选择')"
        readonly
        @click="showDropDown"
        @focus="() => (showPull = false)"
      />
    </template>

    <template #dropdown>
      <vxe-input
        v-model="searchValue"
        size="small"
        prefix-icon="vxe-icon-search"
        :placeholder="$t('搜索')"
        @input="handleInputPerson"
        style="width: 100%"
      />

      <vxe-list class="user-vxe-dropdown" :data="userOptions" auto-resize>
        <template #default="{ items }">
          <div
            v-show="userOptions.length"
            class="user-vxe-list-item"
            v-for="item in items"
            :key="item.value"
            @click="() => handleSelected(item)"
          >
            <span>{{ item.label }}</span>
          </div>
          <div v-show="!userOptions.length" class="user-vxe-list-item">
            <span>{{ $t('暂无数据') }}</span>
          </div>
        </template>
      </vxe-list>
    </template>
  </vxe-pulldown>
</template>

<script>
import { utils } from '@mtech-common/utils'

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    // 映射form表单赋值 key：表单字段 value: 列表数据字段
    valueMapping: {
      type: Object,
      default: () => ({})
    },
    // 绑定的对象
    modelForm: {
      type: Object,
      default: () => ({})
    },
    // 是否在初始化时后就加载数据
    initLoad: {
      type: Boolean,
      default: true
    },
    showInput: {
      type: Boolean,
      default: true
    }
  },

  computed: {
    newValue: {
      get: function () {
        return this.value
      },
      set: function (value) {
        this.$emit('input', value)
      }
    }
  },
  created() {
    this.initLoad && this.initOptions()
    this.filterOptions = utils.debounce(this.initOptions, 1000)
  },
  data() {
    return {
      label: '',
      loading: false,
      showPull: false,
      searchValue: null,
      userOptions: [],
      filterOptions: () => {}
    }
  },
  methods: {
    // 显示面板
    showDropDown() {
      this.searchValue = null
      this.showPull = true
      !this.initLoad && this.initOptions()
    },
    handleInputPerson() {
      this.filterOptions()
    },
    handleSelected(item) {
      if (item) {
        if (this.modelForm) {
          for (let i in this.valueMapping) {
            this.$set(this.modelForm, i, item[this.valueMapping[i]])
          }
        }
      }
      this.$emit('selectUser', item)
      this.label = item.label ?? null
      this.showPull = false
    },
    initOptions() {
      if (this.loading) return
      this.loading = true
      let params = {
        fuzzyName: this.searchValue ?? ''
      }
      this.$API.masterData.getCurrentTenantEmployees(params).then((res) => {
        if (res.code === 200) {
          this.userOptions = res.data.map((item) => {
            return {
              label: item.accountName + '-' + item.employeeName,
              ...item
            }
          })
        }
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>
.user-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.user-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.user-vxe-list-item:hover {
  background-color: #f5f7fa;
}
</style>
