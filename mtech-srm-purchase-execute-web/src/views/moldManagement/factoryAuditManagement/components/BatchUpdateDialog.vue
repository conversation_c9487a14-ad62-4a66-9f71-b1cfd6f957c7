<!-- 批量修改弹框 -->
<template>
  <mt-dialog
    ref="dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="650px"
    height="450px"
  >
    <div style="padding-top: 1rem">
      <mt-form ref="modelForm" :model="modelForm" :rules="rules">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="planExamineDate" :label="$t('计划审厂日期')">
              <mt-date-picker
                v-model="modelForm.planExamineDate"
                :open-on-focus="true"
                :allow-edit="false"
                :time-stamp="true"
                :min="new Date()"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="relationMouldFlag" :label="$t('是否关联模具')">
              <mt-select
                v-model="modelForm.relationMouldFlag"
                :data-source="options"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="allowAppealFlag" :label="$t('是否允许供方申诉')">
              <mt-select
                v-model="modelForm.allowAppealFlag"
                :data-source="options"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
                @change="allowAppealFlagChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item prop="appealDuration" :label="$t('供方申诉时长/天')">
              <mt-input-number
                v-model="modelForm.appealDuration"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :show-spin-button="false"
                min="1"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item prop="autoConfirmFlag" :label="$t('超期供方是否自动确认')">
              <mt-select
                v-model="modelForm.autoConfirmFlag"
                :data-source="options"
                :fields="{ text: 'text', value: 'value' }"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { options } from '../config'
export default {
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      modelForm: {},
      rules: {},

      options,

      ids: []
    }
  },
  methods: {
    dialogInit(args) {
      const { title, selectedRecords } = args
      this.dialogTitle = title
      this.modelForm = {
        relationMouldFlag: '0',
        allowAppealFlag: '1',
        autoConfirmFlag: '0'
      }
      let ids = selectedRecords.map((v) => v.id)
      this.ids = ids
      this.$refs.dialog.ejsRef.show()
    },
    allowAppealFlagChange(e) {
      if (e.value === '0') {
        this.modelForm.appealDuration = null
        this.modelForm.autoConfirmFlag = '1'
      }
    },
    onOpen(args) {
      args.preventFocus = true
    },
    confirm() {
      let params = {
        ids: this.ids,
        form: this.modelForm
      }
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', params)
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    apiEndLoading() {
      this.$store.commit('endLoading')
    },
    apiStartLoading() {
      this.$store.commit('startLoading')
    }
  }
}
</script>
