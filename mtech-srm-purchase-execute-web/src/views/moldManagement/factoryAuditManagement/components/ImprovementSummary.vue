<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template #fileSlot="{ row }">
        <div>
          <div>
            <span style="color: #409eff; cursor: pointer" @click="handlePreview(row)">
              {{ row.fileName }}
            </span>
          </div>
        </div>
      </template>
    </sc-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    summaryData: {
      type: Array,
      default: () => {
        return []
      }
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        approvalOperate: [{ required: true, message: this.$t('必填') }]
      },
      operateOptions: [
        { label: this.$t('通过加分'), value: '1' },
        { label: this.$t('通过不加分'), value: '2' },
        { label: this.$t('驳回'), value: '3' }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      let columns = [
        {
          title: this.$t('供应商审厂不符合项改善要求书'),
          children: [
            {
              type: 'seq',
              title: this.$t('序号'),
              width: 50,
              fixed: 'left',
              align: 'center'
            },
            {
              title: this.$t('不符合项内容描述/审核发现'),
              children: [
                {
                  field: 'module',
                  title: this.$t('所属模块')
                },
                {
                  field: 'examineType',
                  title: this.$t('考核类型')
                },
                {
                  field: 'scoreContent',
                  title: this.$t('评分内容')
                },
                {
                  field: 'problemLevel',
                  title: this.$t('问题等级')
                },
                {
                  field: 'standardScore',
                  title: this.$t('标分')
                },
                {
                  field: 'score',
                  title: this.$t('得分')
                },
                {
                  field: 'explain',
                  title: this.$t('稽查发现/得分说明')
                }
              ]
            },
            {
              field: 'problemSeriousLevel',
              title: this.$t('问题严重度（必改项/建议项)')
            },
            {
              title: this.$t('供应商回复'),
              children: [
                {
                  field: 'improveIdea',
                  title: this.$t('改善对策')
                },
                {
                  field: 'responsiblePerson',
                  title: this.$t('责任人')
                },
                {
                  field: 'fileId',
                  title: this.$t('改善佐证'),
                  editRender: {},
                  slots: {
                    default: 'fileSlot',
                    edit: 'fileSlot'
                  }
                },
                {
                  field: 'finishTime',
                  title: this.$t('预计完成时间'),
                  formatter: ({ cellValue }) => {
                    let text = ''
                    if (cellValue && cellValue !== '0') {
                      text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
                    }
                    return text
                  }
                }
              ]
            },
            {
              field: 'approvalOperate',
              title: this.$t('操作'),
              editRender: {},
              slots: {
                edit: ({ row }) => {
                  return [
                    <vxe-select
                      v-model={row.approvalOperate}
                      placeholder={this.$t('请选择')}
                      options={this.operateOptions}
                      transfer
                      clearable
                    />
                  ]
                }
              },
              formatter: ({ cellValue }) => {
                let item = this.operateOptions.find((item) => item.value === cellValue)
                return item ? item.label : ''
              }
            }
          ]
        }
      ]
      if (!this.canEdit) {
        columns[0].children.pop()
      }
      return columns
    },
    editConfig() {
      return {
        enabled: this.canEdit,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  watch: {
    summaryData: {
      handler(value) {
        this.tableData = value
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handlePreview(row) {
      let params = {
        id: row?.fileId,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行提交操作'), type: 'warning' })
            return
          }

          let data = this.tableRef.getTableData().visibleData
          this.$emit('save', data)
        })
      }
    }
  }
}
</script>
