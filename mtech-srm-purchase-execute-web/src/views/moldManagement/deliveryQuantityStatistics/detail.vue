<!-- 交货数统计 - 新增/编辑 -->
<template>
  <div class="delivery-quantity-statistics-detail">
    <div class="top">
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button v-if="type !== 'check'" @click="handleSave">{{ $t('保存') }}</mt-button>
    </div>
    <mt-form ref="formRef" :model="modelForm" :rules="formRules">
      <mt-row :gutter="24">
        <mt-col :span="5">
          <mt-form-item :label="$t('模具编码')" prop="mouldCode">
            <mt-select
              v-model="modelForm.mouldCode"
              :data-source="mouldOptions"
              :fields="{ text: 'mouldCode', value: 'mouldCode' }"
              :show-clear-button="true"
              :allow-filtering="true"
              filter-type="Contains"
              :placeholder="$t('请选择')"
              :disabled="type !== 'add'"
              @change="mouldChange"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="5">
          <mt-form-item :label="$t('模具名称')" prop="mouldName">
            <mt-input v-model="modelForm.mouldName" disabled />
          </mt-form-item>
        </mt-col>
        <mt-col :span="5">
          <mt-form-item :label="$t('开模类型')" prop="mouldClassify">
            <mt-select
              v-model="modelForm.mouldClassify"
              :data-source="mouldClassifyOptions"
              :fields="{ text: 'text', value: 'value' }"
              disabled
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="5">
          <mt-form-item :label="$t('模具类型')" prop="mouldType">
            <mt-select
              v-model="modelForm.mouldType"
              :data-source="mouldTypeOptions"
              :fields="{ text: 'text', value: 'value' }"
              disabled
            />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="5">
          <mt-form-item :label="$t('产品标识')" prop="productIdentification">
            <mt-input v-model="modelForm.productIdentification" disabled />
          </mt-form-item>
        </mt-col>
        <mt-col :span="5">
          <mt-form-item :label="$t('使用厂家')" prop="companyCode">
            <mt-input v-model="modelForm.companyName" disabled />
            <!-- <RemoteAutocomplete
              v-model="modelForm.companyCode"
              url="/masterDataManagement/auth/company/auth-fuzzy"
              :params="{
                organizationLevelCodes: ['ORG02', 'ORG01'],
                orgType: 'ORG001PRO',
                includeItself: true
              }"
              :placeholder="$t('请选择')"
              :fields="{ text: 'orgName', value: 'orgCode' }"
              records-position="data"
              :disabled="type !== 'add'"
              @change="companyChange"
            /> -->
          </mt-form-item>
        </mt-col>
        <mt-col :span="5">
          <mt-form-item :label="$t('使用物料编码')" prop="materialCode">
            <RemoteAutocomplete
              v-if="type === 'add'"
              v-model="modelForm.materialCode"
              :url="$API.masterData.getItemUrl"
              :placeholder="$t('请选择')"
              :fields="{ text: 'itemCode', value: 'itemCode' }"
              :search-fields="['itemName', 'itemCode']"
              :is-generate="false"
              @change="materialChange"
            />
            <mt-input v-else v-model="modelForm.materialCode" disabled />
          </mt-form-item>
        </mt-col>
        <mt-col :span="5">
          <mt-form-item :label="$t('物料描述')" prop="materialName">
            <mt-input v-model="modelForm.materialName" disabled />
          </mt-form-item>
        </mt-col>
      </mt-row>
      <mt-row :gutter="24">
        <mt-col :span="15">
          <div>
            <sc-table
              ref="sctableRef"
              grid-id="a0a4ee2f-8dc5-47ea-80a1-513fab4b9a15"
              :loading="loading"
              :columns="columns"
              :table-data="tableData"
              :edit-config="editConfig"
              :edit-rules="editRules"
              keep-source
              :sortable="false"
              :is-show-right-btn="false"
              :is-show-refresh-bth="false"
              @edit-closed="editComplete"
            >
              <template slot="custom-tools">
                <vxe-button
                  v-for="item in toolbar"
                  :key="item.code"
                  :status="item.status"
                  :icon="item.icon"
                  :loading="item.loading"
                  size="small"
                  @click="handleClickToolBar(item)"
                >
                  {{ item.name }}
                </vxe-button>
              </template>
            </sc-table>
          </div>
        </mt-col>
      </mt-row>
    </mt-form>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { mouldTypeOptions, mouldClassifyOptions } from './config'
export default {
  components: { ScTable },
  data() {
    return {
      modelForm: {},
      formRules: {
        mouldCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择模具编码')
          }
        ],
        companyCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择厂家')
          }
        ],
        materialCode: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择物料')
          }
        ]
      },
      editRules: {
        periodDate: [{ required: true, message: this.$t('必填') }],
        prodtQty: [{ required: true, message: this.$t('必填') }]
      },
      loading: false,
      tableData: [],

      mouldTypeOptions,
      mouldClassifyOptions,
      mouldOptions: [],

      material: null
    }
  },
  computed: {
    type() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'periodDate',
          title: this.$t('日期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              const dateDisabled = (params) => {
                const { date } = params
                return date < new Date()
              }
              return [
                <vxe-input
                  type={'date'}
                  v-model={row.periodDate}
                  placeholder={this.$t('请选择')}
                  transfer
                  clearable
                  label-format='yyyy-MM-dd'
                  value-format='yyyy-MM-dd'
                  disabled-method={dateDisabled}
                />
              ]
            }
          }
        },
        {
          field: 'prodtQty',
          title: this.$t('生产数'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.prodtQty}
                  placeholder={this.$t('请输入')}
                  min='1'
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'prodtOrderNo',
          title: this.$t('生产订单号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.prodtOrderNo}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.type !== 'check',
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (['add', 'edit'].includes(this.type)) {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    }
  },
  created() {
    this.getMouldOptions()
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  beforeDestroy() {
    this.$store.commit('setDeliveryQuantityStatisticsRow', null)
  },
  methods: {
    getDetail() {
      let row = this.$store.state.deliveryQuantityStatisticsRow
      this.modelForm = {
        id: row.id,
        mouldCode: row.mouldCode,
        mouldName: row.mouldName,
        mouldClassify: row.mouldClassify,
        mouldType: row.mouldType,
        productIdentification: row.productIdentification,
        companyCode: row.companyCode,
        companyName: row.companyName,
        materialCode: row.materialCode,
        materialName: row.materialName
      }
      this.material = row.materialCode + '-' + row.materialName
      this.tableData = row.detailList
    },
    mouldChange(e) {
      this.modelForm.mouldName = e.itemData?.mouldName
      this.modelForm.mouldClassify = e.itemData?.mouldClassify
      this.modelForm.mouldType = e.itemData?.mouldType
      this.modelForm.productIdentification = e.itemData?.productIdentification
    },
    getMouldOptions() {
      this.$API.moldManagement.queryMouldDetailByMouldCodeApi({ mouldCode: '' }).then((res) => {
        if (res.code === 200) {
          this.mouldOptions = res.data
        }
      })
    },
    companyChange(e) {
      this.modelForm.companyName = e.itemData?.orgName || null
    },
    materialChange(e) {
      this.modelForm.materialName = e.itemData?.itemName || null
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'delete':
          this.tableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    handleAdd() {
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.tableRef.getTableData().visibleData.length === 0) {
            this.$toast({ content: this.$t('请新增'), type: 'warning' })
            return
          }
          let params = this.getParams()
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认保存？')
            },
            success: () => {
              this.$API.moldManagement.saveDeliveryQuantityStatisticsApi(params).then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('保存成功'), type: 'success' })
                  this.$router.push({
                    name: 'delivery-quantity-statistics',
                    query: {
                      timeStamp: new Date().getTime()
                    }
                  })
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = { ...this.modelForm }
      params.detailList = this.tableRef.getTableData().visibleData.map((item) => {
        return {
          id: item.id?.includes('row_') ? null : item.id,
          periodDate: item.periodDate,
          prodtQty: item.prodtQty,
          prodtOrderNo: item.prodtOrderNo
        }
      })
      return params
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.delivery-quantity-statistics-detail {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
</style>
