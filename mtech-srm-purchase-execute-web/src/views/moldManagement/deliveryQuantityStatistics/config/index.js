import { i18n } from '@/main.js'

export const mouldTypeOptions = [
  { text: i18n.t('五金模'), value: '1' },
  { text: i18n.t('注塑模'), value: '2' },
  { text: i18n.t('泡沫及其它模'), value: '3' }
]

export const mouldClassifyOptions = [
  { text: i18n.t('新增模'), value: '1' },
  { text: i18n.t('复制模'), value: '2' },
  { text: i18n.t('批产前-改模'), value: '3' },
  { text: i18n.t('修模'), value: '4' },
  { text: i18n.t('批产后-改模'), value: '5' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'mouldCode',
    title: i18n.t('模具编码')
  },
  {
    field: 'mouldName',
    title: i18n.t('模具名称')
  },
  {
    field: 'mouldType',
    title: i18n.t('模具类型'),
    formatter: ({ cellValue }) => {
      let item = mouldTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'mouldClassify',
    title: i18n.t('开模类型'),
    formatter: ({ cellValue }) => {
      let item = mouldClassifyOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'companyCode',
    title: i18n.t('使用厂家'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.companyName : ''
    }
  },
  {
    field: 'prodtQtyTotal',
    title: i18n.t('生产数量累计')
  },
  {
    field: 'productIdentification',
    title: i18n.t('产品标识')
  },
  {
    field: 'materialCode',
    title: i18n.t('使用物料编码')
  },
  {
    field: 'materialName',
    title: i18n.t('物料名称')
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    fixed: 'right',
    slots: {
      default: 'operateDefault'
    }
  }
]
