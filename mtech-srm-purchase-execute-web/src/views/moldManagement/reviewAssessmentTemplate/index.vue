<!-- 审厂考核模板 -->
<template>
  <div>
    <collapse-search
      class="toggle-container"
      :is-grid-display="true"
      @reset="handleReset"
      @search="handleSearch"
    >
      <mt-form ref="searchFormRef" :model="searchFormModel" :rules="searchFormRules">
        <mt-form-item :label="$t('模板名称')" prop="templateName">
          <mt-input
            v-model="searchFormModel.templateName"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item :label="$t('有无汇总表')" prop="thereIsSumTable">
          <mt-select
            v-model="searchFormModel.thereIsSumTable"
            :data-source="thereIsSumTableOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <sc-table
      ref="sctableRef"
      grid-id="1c280f97-a815-4726-bd08-d0584eae66b2"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      keep-source
      :is-show-refresh-bth="true"
      @refresh="handleSearch"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :loading="item.loading"
          v-permission="item.permission"
          size="small"
          @click="handleClickToolBar(item)"
        >
          {{ item.name }}
        </vxe-button>
      </template>
      <template #operateDefault="{ row }">
        <div
          v-permission="['O_02_1775']"
          style="cursor: pointer; color: #2783fe"
          @click="handleCopy(row)"
        >
          {{ $t('复制模板') }}
        </div>
      </template>
    </sc-table>
    <mt-page
      ref="pageRef"
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, statusOptions, thereIsSumTableOptions } from './config'

export default {
  components: { CollapseSearch, ScTable },
  data() {
    return {
      searchFormModel: {},
      searchFormRules: {},
      toolbar: [
        {
          code: 'add',
          name: this.$t('新增'),
          status: 'info',
          loading: false,
          permission: ['O_02_1768']
        },
        {
          code: 'edit',
          name: this.$t('编辑'),
          status: 'info',
          loading: false,
          permission: ['O_02_1769']
        },
        {
          code: 'delete',
          name: this.$t('删除'),
          status: 'info',
          loading: false,
          permission: ['O_02_1770']
        },
        {
          code: 'enable',
          name: this.$t('生效'),
          status: 'info',
          loading: false,
          permission: ['O_02_1771']
        },
        {
          code: 'disable',
          name: this.$t('失效'),
          status: 'info',
          loading: false,
          permission: ['O_02_1772']
        },
        {
          code: 'record',
          name: this.$t('模板变更记录'),
          status: 'info',
          loading: false,
          permission: ['O_02_1773']
        },
        {
          code: 'check',
          name: this.$t('查看'),
          status: 'info',
          loading: false,
          permission: ['O_02_1774']
        }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      },
      columns: columnData,
      loading: false,
      tableData: [],

      statusOptions,
      thereIsSumTableOptions
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.$refs.searchFormRef.validate((valid) => {
        if (valid) {
          this.currentPage = 1
          this.getTableData()
        } else {
          this.$toast({ content: this.$t('请完善查询条件'), type: 'warning' })
        }
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .pageReviewAssessmentTemplateApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records.map((item) => {
          return {
            supplierLevel: item?.scoringStandard.length,
            ...item
          }
        })
      }
    },
    handleClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['edit', 'delete', 'enable', 'disable', 'record', 'check']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (selectedRecords.length > 1 && ['edit', 'record', 'check'].includes(e.code)) {
        this.$toast({ content: this.$t('只能选择一行进行操作'), type: 'warning' })
        return
      }
      if (e.code === 'edit' && selectedRecords.some((v) => v.status === 1)) {
        this.$toast({ content: this.$t('【生效】状态数据不可编辑'), type: 'warning' })
        return
      }
      if (e.code === 'delete' && selectedRecords.some((v) => v.status === 1)) {
        this.$toast({ content: this.$t('【生效】状态数据不可删除'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.handleAdd()
          break
        case 'edit':
          this.handleEdit(selectedRecords[0])
          break
        case 'delete':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认删除？')
            },
            success: () => {
              this.handleDelete(selectedRecords)
            }
          })
          break
        case 'enable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认生效？')
            },
            success: () => {
              this.handleEnable(selectedRecords)
            }
          })
          break
        case 'disable':
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认失效？')
            },
            success: () => {
              this.handleDisable(selectedRecords)
            }
          })
          break
        case 'record':
          this.handleRecord(selectedRecords[0])
          break
        case 'check':
          this.handleCheck(selectedRecords[0])
          break
        default:
          break
      }
    },
    handleAdd() {
      this.$router.push({
        name: 'review-assessment-template-detail',
        query: {
          type: 'add',
          timeStamp: new Date().getTime()
        }
      })
    },
    handleEdit(row) {
      this.$router.push({
        name: 'review-assessment-template-detail',
        query: {
          type: 'edit',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleCheck(row) {
      this.$router.push({
        name: 'review-assessment-template-detail',
        query: {
          type: 'check',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    },
    handleDelete(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.moldManagement.deleteReviewAssessmentTemplateApi(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('删除成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleEnable(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.moldManagement.enableReviewAssessmentTemplateApi(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('生效成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleDisable(selectedRecords) {
      let ids = selectedRecords.map((v) => v.id)
      this.$API.moldManagement.disableReviewAssessmentTemplateApi(ids).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('失效成功'), type: 'success' })
          this.handleSearch()
        }
      })
    },
    handleRecord(row) {
      this.$dialog({
        data: {
          title: this.$t('变更记录'),
          id: row.id
        },
        modal: () => import('./components/RecordDialog.vue'),
        success: () => {}
      })
    },
    handleCopy(row) {
      this.$router.push({
        name: 'review-assessment-template-detail',
        query: {
          type: 'copy',
          id: row.id,
          timeStamp: new Date().getTime()
        }
      })
    }
  }
}
</script>
