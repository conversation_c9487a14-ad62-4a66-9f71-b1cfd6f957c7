<!-- 变更记录 -->
<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    :height="750"
    :width="1100"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <sc-table
        ref="sctableRef"
        grid-id="a0ab1600-3829-4b0c-80e1-7a51449ffe26"
        :loading="loading"
        :columns="columns"
        :table-data="tableData"
        keep-source
        :sortable="false"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
      </sc-table>
      <mt-page
        ref="pageRef"
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      />
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],

      loading: false,
      tableData: [],
      toolbar: [
        // { code: 'export', name: this.$t('导出'), status: 'info', loading: false }
      ],
      currentPage: 1,
      pageSettings: {
        pageCount: 5,
        totalPages: 0,
        totalRecordsCount: 0,
        pageSize: 50,
        pageSizes: [20, 50, 200, 500, 1000]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'operatorWay',
          title: this.$t('操作方式')
        },
        {
          field: 'operatorName',
          title: this.$t('操作人')
        },
        {
          field: 'operatorTime',
          title: this.$t('操作时间')
        },
        {
          field: 'changeContent',
          title: this.$t('变更内容')
        },
        {
          field: 'beforeChange',
          title: this.$t('变更前')
        },
        {
          field: 'afterChange',
          title: this.$t('变更后')
        },
        {
          field: 'remark',
          title: this.$t('备注')
        }
      ]
    }
  },
  mounted() {
    this.getTableData()
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChange(pageSize) {
      this.currentPage = 1
      this.pageSettings.pageSize = pageSize
      this.getTableData()
    },
    async getTableData() {
      const params = {
        templateId: this.modalData.id,
        page: {
          current: this.currentPage,
          size: this.pageSettings.pageSize
        }
      }
      this.loading = true
      const res = await this.$API.moldManagement
        .getTemplateLogApi(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const total = res?.data?.total || 0
        this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
        this.pageSettings.totalRecordsCount = Number(total)

        const records = res.data?.records || []
        this.tableData = records
      }
    },
    handleClickToolBar(e) {
      switch (e.code) {
        case 'export':
          this.handleExport(e)
          break
        default:
          break
      }
    },
    handleExport() {},
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    .dialog-content {
      padding: 10px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 100%;
        }
      }
    }
  }
}
</style>
