import { i18n } from '@/main.js'

export const statusOptions = [
  { text: i18n.t('生效'), value: 1 },
  { text: i18n.t('失效'), value: 0 }
]

export const thereIsSumTableOptions = [
  { text: i18n.t('有'), label: i18n.t('有'), value: '1' },
  { text: i18n.t('无'), label: i18n.t('无'), value: '0' }
]

export const thereIsCrDefectOptions = [
  { text: i18n.t('是'), label: i18n.t('是'), value: '1' },
  { text: i18n.t('否'), label: i18n.t('否'), value: '0' }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'templateName',
    title: i18n.t('模板名称'),
    minWidth: 120
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'supplierLevel',
    title: i18n.t('供应商等级')
  },
  {
    field: 'thereIsSumTable',
    title: i18n.t('有无汇总表'),
    formatter: ({ cellValue }) => {
      let item = thereIsSumTableOptions.find((item) => Number(item.value) === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'thereIsCrDefect',
    title: i18n.t('是否启用CRI缺陷'),
    formatter: ({ cellValue }) => {
      let item = thereIsCrDefectOptions.find((item) => Number(item.value) === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    slots: {
      default: 'operateDefault'
    }
  }
]
