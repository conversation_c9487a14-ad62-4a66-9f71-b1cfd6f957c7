<!-- 审厂考核模板 - 新增/编辑 -->
<template>
  <div class="review-template-detail">
    <div v-if="isNextStep" class="top">
      <mt-button type="primary" @click="handleBack">{{ $t('上一步') }}</mt-button>
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button v-if="type !== 'check'" @click="handleSave">{{ $t('保存') }}</mt-button>
    </div>
    <div v-if="!isNextStep" class="top">
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button type="primary" @click="handleNext">{{ $t('下一步') }}</mt-button>
    </div>
    <mt-form ref="formRef" :model="modelForm" :rules="formRules">
      <div v-show="!isNextStep">
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item :label="$t('模板名称')" prop="templateName">
              <mt-input
                v-model="modelForm.templateName"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="6">
            <mt-form-item :label="$t('有无汇总表')" prop="thereIsSumTable">
              <mt-radio
                v-model="modelForm.thereIsSumTable"
                :data-source="thereIsSumTableOptions"
                disabled
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item :label="$t('评分标准')" required>
              <div>
                <sc-table
                  ref="sctableRef"
                  grid-id="de862315-7d8c-4cff-9af4-50a5d0ce5f90"
                  :fix-height="180"
                  :loading="loading"
                  :columns="columns"
                  :table-data="tableData"
                  :edit-config="editConfig"
                  :edit-rules="editRules"
                  keep-source
                  :sortable="false"
                  :is-show-right-btn="false"
                  :is-show-refresh-bth="false"
                  @edit-closed="editComplete"
                >
                  <template slot="custom-tools">
                    <vxe-button
                      v-for="item in toolbar"
                      :key="item.code"
                      :status="item.status"
                      :icon="item.icon"
                      :loading="item.loading"
                      size="small"
                      @click="standardClickToolBar(item)"
                    >
                      {{ item.name }}
                    </vxe-button>
                  </template>
                </sc-table>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item :label="$t('问题等级说明')" required>
              <div>
                <sc-table
                  ref="levelTableRef"
                  grid-id="cb670903-94d7-41a6-a235-ffc143238867"
                  :fix-height="180"
                  :columns="levelColumns"
                  :table-data="levelTableData"
                  :edit-config="editConfig"
                  :edit-rules="levelEditRules"
                  keep-source
                  :sortable="false"
                  :is-show-right-btn="false"
                  :is-show-refresh-bth="false"
                  @edit-closed="levelEditComplete"
                >
                  <template slot="custom-tools">
                    <vxe-button
                      v-for="item in toolbar"
                      :key="item.code"
                      :status="item.status"
                      :icon="item.icon"
                      :loading="item.loading"
                      size="small"
                      @click="levelClickToolBar(item)"
                    >
                      {{ item.name }}
                    </vxe-button>
                  </template>
                </sc-table>
              </div>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item :label="$t('评分评审说明')" prop="gradingSpecification">
              <mt-input
                type="text"
                v-model="modelForm.gradingSpecification"
                :placeholder="$t('请输入')"
                :show-clear-button="true"
                :multiline="true"
                :rows="5"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item
              :label="$t('是否启用有CRI缺陷（启用时总分打八折）')"
              prop="thereIsCrDefect"
            >
              <mt-radio
                v-model="modelForm.thereIsCrDefect"
                :data-source="thereIsCrDefectOptions"
                :disabled="type === 'check'"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
      </div>
      <div v-show="isNextStep">
        <mt-row :gutter="24">
          <mt-col :span="12">
            <mt-form-item :label="$t('考核模块及分值')" required>
              <sc-table
                ref="moduleTableRef"
                grid-id="3c165d45-2a3f-46ce-9dc2-1f02f6fc2ac4"
                :fix-height="180"
                :columns="moduleColumns"
                :table-data="moduleTableData"
                :edit-config="editConfig"
                :edit-rules="moduleEditRules"
                keep-source
                :sortable="false"
                :is-show-right-btn="false"
                :is-show-refresh-bth="false"
                @edit-closed="moduleEditComplete"
              >
                <template slot="custom-tools">
                  <vxe-button
                    v-for="item in toolbar"
                    :key="item.code"
                    :status="item.status"
                    :icon="item.icon"
                    :loading="item.loading"
                    size="small"
                    @click="moduleClickToolBar(item)"
                  >
                    {{ item.name }}
                  </vxe-button>
                </template>
                <template #operateDefault="{ row }">
                  <div style="cursor: pointer; color: #2783fe">
                    <span @click="moduleDelete(row)">
                      {{ $t('删除') }}
                    </span>
                  </div>
                </template>
              </sc-table>
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="24">
            <mt-tabs
              :e-tab="false"
              :data-source="tabList"
              :selected-item="tabIndex"
              @handleSelectTab="handleSelectTab"
            />
          </mt-col>
        </mt-row>
        <mt-row v-if="showDetailTable" :gutter="24">
          <mt-col :span="24">
            <sc-table
              ref="detailTableRef"
              :fix-height="480"
              :columns="currentDetailColumns"
              :table-data="currentDetailTableData"
              :edit-config="editConfig"
              :edit-rules="detailEditRules"
              keep-source
              :sortable="false"
              :is-show-right-btn="false"
              :is-show-refresh-bth="false"
              @edit-closed="detailEditComplete"
            >
              <template slot="custom-tools">
                <vxe-button
                  v-for="item in detailToolbar"
                  :key="item.code"
                  :status="item.status"
                  :icon="item.icon"
                  :loading="item.loading"
                  size="small"
                  @click="detailClickToolBar(item)"
                >
                  {{ item.name }}
                </vxe-button>
              </template>
            </sc-table>
          </mt-col>
        </mt-row>
      </div>
    </mt-form>
  </div>
</template>

<script>
import { thereIsSumTableOptions, thereIsCrDefectOptions } from './config'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: { ScTable },
  data() {
    return {
      isNextStep: false,
      modelForm: {
        thereIsSumTable: '1'
      },
      formRules: {
        templateName: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入模板名称')
          }
        ],
        thereIsSumTable: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择')
          }
        ],
        gradingSpecification: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请输入评分评审说明')
          }
        ],
        thereIsCrDefect: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('请选择')
          }
        ]
      },
      thereIsSumTableOptions,
      thereIsCrDefectOptions,

      loading: false,
      tableData: [],
      editRules: {
        max: [{ required: true, message: this.$t('必填') }],
        lv: [{ required: true, message: this.$t('必填') }]
      },
      levelOptions: [
        { label: this.$t('A类供应商'), value: '1' },
        { label: this.$t('B类供应商'), value: '2' },
        { label: this.$t('C类供应商'), value: '3' },
        { label: this.$t('D类供应商'), value: '4' }
      ],

      levelTableData: [],
      identificationOptions: [
        { label: this.$t('重大问题项'), value: 'critical' },
        { label: this.$t('主要问题项'), value: 'major' },
        { label: this.$t('一般问题项'), value: 'minor' },
        { label: this.$t('观察建议项'), value: 'observation' }
      ],
      levelEditRules: {
        identification: [{ required: true, message: this.$t('必填') }],
        definition: [{ required: true, message: this.$t('必填') }],
        rectificationDate: [{ required: true, message: this.$t('必填') }]
      },

      moduleTableData: [],
      moduleEditRules: {
        module: [{ required: true, message: this.$t('必填') }],
        score: [{ required: true, message: this.$t('必填') }]
      },

      currentDetailColumns: [], // 当前显示明细列
      detailColumns: {}, // 所有模块明细列
      currentDetailTableData: [], // 当前显示明细
      detailTableData: {}, // 所有模块明细
      detailEditRules: {
        scoreType: [{ required: true, message: this.$t('必填') }],
        examineType: [{ required: true, message: this.$t('必填') }],
        ratingContent: [{ required: true, message: this.$t('必填') }],
        problemLevel: [{ required: true, message: this.$t('必填') }],
        marking: [{ required: true, message: this.$t('必填') }]
      },

      tabList: [],
      tabIndex: 0,
      tabId: null
    }
  },
  computed: {
    type() {
      return this.$route.query?.type
    },
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    editConfig() {
      return {
        enabled: this.type !== 'check',
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    toolbar() {
      let btns = []
      if (this.type !== 'check') {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
        ]
      }
      return btns
    },
    detailToolbar() {
      let btns = []
      if (this.type !== 'check') {
        btns = [
          { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
          { code: 'delete', name: this.$t('删除'), status: 'info', loading: false },
          { code: 'colAdd', name: this.$t('新增列'), status: 'info', loading: false }
        ]
      }
      return btns
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'max',
          title: this.$t('得分范围'),
          minWidth: 135,
          editRender: {},
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  {row.min}~{row.max}
                </div>
              ]
            },
            edit: ({ row }) => {
              return [
                <div style='display: flex'>
                  <vxe-input
                    type='number'
                    v-model={row.min}
                    placeholder={this.$t('请输入')}
                    min='0'
                    max='99'
                    transfer
                    clearable
                    style='width: 50%'
                  />
                  ~
                  <vxe-input
                    type='number'
                    v-model={row.max}
                    placeholder={this.$t('请输入')}
                    min={Number(row.min) + 1}
                    max='100'
                    transfer
                    clearable
                    style='width: 50%'
                  />
                </div>
              ]
            }
          }
        },
        {
          field: 'lv',
          title: this.$t('等级'),
          minWidth: 135,
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.lv}
                  placeholder={this.$t('请选择')}
                  options={this.levelOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.levelOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        }
      ]
    },
    levelTableRef() {
      return this.$refs.levelTableRef.$refs.xGrid
    },
    levelColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'identification',
          title: this.$t('问题等级'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.identification}
                  placeholder={this.$t('请选择')}
                  options={this.identificationOptions}
                  transfer
                  clearable
                  onChange={() => {
                    row.flag = row.identification
                  }}
                />
              ]
            }
          },
          formatter: ({ row }) => {
            let item = this.identificationOptions.find((item) => item.value === row.identification)
            return item ? item.label : ''
          }
        },
        {
          field: 'flag',
          title: this.$t('标识符号')
        },
        {
          field: 'definition',
          title: this.$t('定义'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.definition}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'rectificationDate',
          title: this.$t('整改期限'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                // <vxe-input
                //   type={'date'}
                //   v-model={row.rectificationDate}
                //   placeholder={this.$t('请选择')}
                //   transfer
                //   clearable
                //   label-format='yyyy-MM-dd'
                //   value-format='yyyy-MM-dd'
                // />
                <vxe-input
                  type='number'
                  v-model={row.rectificationDate}
                  placeholder={this.$t('请输入')}
                  min='1'
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    },
    moduleTableRef() {
      return this.$refs.moduleTableRef.$refs.xGrid
    },
    moduleColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'module',
          title: this.$t('模块'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.module}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'score',
          title: this.$t('分值占比（%）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.score}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max='100'
                  transfer
                  clearable
                />
              ]
            }
          }
        }
        // {
        //   field: 'operate',
        //   title: this.$t('操作'),
        //   editRender: {},
        //   slots: {
        //     default: 'operateDefault'
        //   }
        // }
      ]
    },
    detailTableRef() {
      return this.$refs.detailTableRef.$refs.xGrid
    },
    showDetailTable() {
      return this.tabList.length > 0
    },
    fixedDetailColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'scoreType',
          title: this.$t('类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.scoreType}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'examineType',
          title: this.$t('考核类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.examineType}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'ratingContent',
          title: this.$t('评分内容'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.ratingContent}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        },
        {
          field: 'problemLevel',
          title: this.$t('问题等级'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.problemLevel}
                  placeholder={this.$t('请选择')}
                  options={this.identificationOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ row }) => {
            let item = this.identificationOptions.find((item) => item.value === row.problemLevel)
            return item ? item.label : ''
          }
        },
        {
          field: 'marking',
          title: this.$t('标分'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  type='number'
                  v-model={row.marking}
                  placeholder={this.$t('请输入')}
                  min='1'
                  max='100'
                  transfer
                  clearable
                />
              ]
            }
          }
        }
      ]
    }
  },
  created() {
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.detailReviewAssessmentTemplateApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = {
            id: res.data.id,
            templateName: res.data.templateName,
            templateCode: res.data.templateCode,
            thereIsCrDefect: String(res.data.thereIsCrDefect),
            thereIsSumTable: String(res.data.thereIsSumTable),
            gradingSpecification: res.data.gradingSpecification,
            status: res.data.status
          }
          if (this.type === 'copy') {
            this.modelForm.id = null
            this.modelForm.templateName = null
          }
          this.tableData = res.data.scoringStandard
          this.levelTableData = res.data.problemLevelDetail.map((v) => {
            return {
              ...v,
              flag: v.identification
            }
          })
          this.moduleTableData = res.data.examinelList
          this.tabList = []
          this.moduleTableData.forEach((item) => {
            this.tabList.unshift({
              title: item.module,
              id: item.id
            })
          })
          this.setDetail(res.data.detailList)
        }
      })
    },
    // 明细数据处理
    setDetail(arr) {
      this.tabList.forEach((item) => {
        this.detailColumns[item.id] = this.setDetailColumn(item.title)
        this.detailTableData[item.id] = arr.filter((e) => e.moduleHeader === item.title)
      })
      this.tabId = this.tabList[this.tabIndex]?.id
      let currentDetailColumns = this.detailColumns[this.tabId] || []
      this.currentDetailColumns = currentDetailColumns
      let currentDetailTableData = this.detailTableData[this.tabId] || []
      this.currentDetailTableData = currentDetailTableData
    },
    setDetailColumn(title) {
      let column = []
      let detailHeader = this.moduleTableData.find((v) => v.module === title)?.detailHeader
      detailHeader.forEach((e) => {
        let field = Object.keys(e)[0]
        let title = Object.values(e)[0]
        column.push({
          field,
          title,
          editRender: {},
          slots: {
            header: ({ columnIndex }) => {
              return this.type !== 'check'
                ? [
                    <div style='display: inline-block'>
                      <span style='margin-right: 10px'>{title}</span>
                      <vxe-icon
                        name='error-circle-fill'
                        style='cursor: pointer'
                        onClick={() => {
                          this.colDelete(columnIndex)
                        }}
                      />
                    </div>
                  ]
                : [
                    <div style='display: inline-block'>
                      <span style='margin-right: 10px'>{title}</span>
                    </div>
                  ]
            },
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row[field]}
                  placeholder={this.$t('请输入')}
                  transfer
                  clearable
                />
              ]
            }
          }
        })
      })
      return this.fixedDetailColumns.concat(column)
    },
    handleSelectTab(e) {
      this.tabIndex = e
      this.tabId = this.tabList[e]?.id
      let currentDetailColumns = this.detailColumns[this.tabId] || []
      this.currentDetailColumns = currentDetailColumns
      let currentDetailTableData = this.detailTableData[this.tabId] || []
      this.currentDetailTableData = currentDetailTableData
    },
    standardClickToolBar(e) {
      const selectedRecords = this.tableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.standardAdd()
          break
        case 'delete':
          this.tableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    standardAdd() {
      const item = {}
      this.tableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.tableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.tableRef.setEditRow(currentViewRecords[0])
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    levelClickToolBar(e) {
      const selectedRecords = this.levelTableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.levelAdd()
          break
        case 'delete':
          this.levelTableRef.removeCheckboxRow()
          break
        default:
          break
      }
    },
    levelAdd() {
      const item = {}
      this.levelTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.levelTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.levelTableRef.setEditRow(currentViewRecords[0])
      })
    },
    levelEditComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.levelTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }
        })
      }
    },
    moduleClickToolBar(e) {
      const selectedRecords = this.moduleTableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.moduleAdd()
          break
        case 'delete':
          this.moduleDelete(selectedRecords)
          break
        default:
          break
      }
    },
    moduleAdd() {
      const item = {}
      this.moduleTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.moduleTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.moduleTableRef.setEditRow(currentViewRecords[0])
      })
    },
    moduleDelete(selectedRecords) {
      selectedRecords.forEach((item) => {
        delete this.detailColumns[item.id]
        delete this.detailTableData[item.id]
      })
      this.moduleTableRef.removeCheckboxRow()
      this.setTabList()
    },
    moduleEditComplete(args) {
      const { row } = args
      console.log('moduleEditComplete', row)
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.moduleTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }

          if (!this.tabList.some((v) => v.id === row.id)) {
            this.detailColumnAdd()
          } else {
            this.tabList = []
            this.moduleTableRef.getTableData().visibleData.forEach((item) => {
              this.tabList.unshift({
                title: item.module,
                id: item.id
              })
            })
            let _index = this.tabList.findIndex((e) => e.id === row.id)
            this.tabIndex = _index
            this.handleSelectTab(this.tabIndex)
          }
        })
      }
    },
    detailColumnAdd() {
      this.setTabList()
      let tabId = this.tabList[this.tabIndex].id
      let fixedDetailColumns = [...this.fixedDetailColumns]
      this.currentDetailColumns = fixedDetailColumns
      this.detailColumns[tabId] = fixedDetailColumns
    },
    setTabList() {
      this.tabList = []
      this.moduleTableRef.getTableData().visibleData.forEach((item) => {
        this.tabList.unshift({
          title: item.module,
          id: item.id
        })
      })
      console.log('setTabList', this.tabList)
      console.log('setTabList', this.detailColumns)
      let len = this.tabList.length
      this.tabIndex = len - 1
      this.handleSelectTab(this.tabIndex)
    },
    detailClickToolBar(e) {
      const selectedRecords = this.detailTableRef.getCheckboxRecords()
      const commonToolbar = ['delete']
      if (selectedRecords.length === 0 && commonToolbar.includes(e.code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'add':
          this.detailAdd()
          break
        case 'delete':
          this.detailDelete()
          break
        case 'colAdd':
          this.colAdd()
          break
        default:
          break
      }
    },
    detailAdd() {
      const item = {}
      this.detailTableRef.insert([item])
      this.$nextTick(() => {
        // 获取最新的表格视图数据
        const currentViewRecords = this.detailTableRef.getTableData().visibleData
        // 将新增的那一条设置为编辑状态
        this.detailTableRef.setEditRow(currentViewRecords[0])
      })
    },
    detailDelete() {
      this.detailTableRef.removeCheckboxRow()
      let currentDetailTableData = this.detailTableRef.getTableData().visibleData
      let tabId = this.tabList[this.tabIndex].id
      this.detailTableData[tabId] = currentDetailTableData
    },
    detailEditComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.detailTableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行保存操作'), type: 'warning' })
            return
          }

          let currentDetailTableData = this.detailTableRef.getTableData().visibleData
          let tabId = this.tabList[this.tabIndex].id
          this.detailTableData[tabId] = currentDetailTableData
        })
      }
    },
    colAdd() {
      let fields = this.currentDetailColumns.filter((e) => e?.field?.includes('fields'))
      if (fields.length === 10) {
        this.$toast({ content: this.$t('最多只能新增10列'), type: 'warning' })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('新增列')
        },
        modal: () => import('./components/ColAddDialog.vue'),
        success: (title) => {
          let k = 1
          for (let i = 1; i <= 10; i++) {
            let isFields = this.currentDetailColumns.every((v) => v?.field !== `fields${i}`)
            if (isFields) {
              k = i
              break
            }
          }
          this.currentDetailColumns.push({
            field: `fields${k}`,
            title,
            editRender: {},
            slots: {
              header: ({ columnIndex }) => {
                return [
                  <div style='display: inline-block'>
                    <span style='margin-right: 10px'>{title}</span>
                    <vxe-icon
                      name='error-circle-fill'
                      style='cursor: pointer'
                      onClick={() => {
                        this.colDelete(columnIndex)
                      }}
                    />
                  </div>
                ]
              },
              edit: ({ row }) => {
                return [
                  <vxe-input
                    v-model={row[`fields${k}`]}
                    placeholder={this.$t('请输入')}
                    transfer
                    clearable
                  />
                ]
              }
            }
          })
          let tabId = this.tabList[this.tabIndex].id
          this.detailColumns[tabId] = [...this.currentDetailColumns]
        }
      })
    },
    colDelete(columnIndex) {
      this.currentDetailColumns.splice(columnIndex, 1)
      let tabId = this.tabList[this.tabIndex].id
      this.detailColumns[tabId] = this.currentDetailColumns
    },
    handleSave() {
      let params = this.getParams()
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认保存？')
        },
        success: () => {
          this.$API.moldManagement.saveReviewAssessmentTemplateApi(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$router.push({
                name: 'review-assessment-template',
                query: {
                  timeStamp: new Date().getTime()
                }
              })
            }
          })
        }
      })
    },
    getParams() {
      let params = { ...this.modelForm }
      params.thereIsSumTable = Number(params.thereIsSumTable)
      params.thereIsCrDefect = Number(params.thereIsCrDefect)
      params.scoringStandard = this.tableRef.getTableData().visibleData.map((item) => {
        return {
          lv: item.lv,
          max: item.max,
          min: item.min
        }
      })
      params.problemLevelDetail = this.levelTableRef.getTableData().visibleData.map((item) => {
        return {
          identification: item.identification,
          definition: item.definition,
          rectificationDate: item.rectificationDate
        }
      })
      params.examinelList = this.moduleTableRef.getTableData().visibleData
      params.examinelList.forEach((item) => {
        item.detailHeader = []
        this.detailColumns[item.id]?.forEach((v) => {
          if (v?.field?.includes('fields')) {
            let field = v.field
            item.detailHeader.push({
              [field]: v.title
            })
          }
        })
      })
      params.examinelList.forEach((item) => {
        if (item.id?.includes('row_')) {
          item.id = null
        }
      })
      params.detailList = []
      this.tabList.forEach((item) => {
        this.detailTableData[item.id].forEach((v) => {
          params.detailList.push({
            ...v,
            moduleHeader: item.title
          })
        })
      })
      params.detailList.forEach((item) => {
        if (item.id?.includes('row_')) {
          item.id = null
        }
      })
      if (this.type === 'copy') {
        params.examinelList.forEach((item) => {
          delete item.id
        })
        params.detailList.forEach((item) => {
          delete item.id
        })
      }
      return params
    },
    handleCancel() {
      this.$router.go(-1)
    },
    handleNext() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.tableRef.getTableData().visibleData.length === 0) {
            this.$toast({ content: this.$t('请新增评分标准'), type: 'warning' })
            return
          }
          if (this.levelTableRef.getTableData().visibleData.length === 0) {
            this.$toast({ content: this.$t('请新增问题等级说明'), type: 'warning' })
            return
          }
          this.isNextStep = true
        }
      })
    },
    handleBack() {
      this.isNextStep = false
    }
  }
}
</script>

<style lang="scss" scoped>
.review-template-detail {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
  .bottom {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 20px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
::v-deep {
  .mt-form textarea.e-input,
  .mt-form .e-input-group textarea.e-input,
  .mt-form .e-input-group.e-control-wrapper textarea.e-input {
    height: 100% !important;
  }
}
</style>
