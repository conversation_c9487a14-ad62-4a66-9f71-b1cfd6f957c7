import { i18n } from '@/main.js'
import dayjs from 'dayjs'

export const statusOptions = [
  { text: i18n.t('待部门确认'), value: 0 },
  { text: i18n.t('待审厂'), value: 1 },
  { text: i18n.t('打分待审核'), value: 2 },
  { text: i18n.t('待供方确认'), value: 3 },
  { text: i18n.t('整改待确认'), value: 4 },
  { text: i18n.t('整改待审核'), value: 5 },
  { text: i18n.t('待OA审批'), value: 6 },
  { text: i18n.t('已完成'), value: 7 }
]

export const examineTypeOptions = [
  { text: i18n.t('年度审厂'), value: 1 },
  { text: i18n.t('季度审厂'), value: 2 },
  { text: i18n.t('新供方审厂'), value: 3 },
  { text: i18n.t('台账审厂'), value: 4 },
  { text: i18n.t('保养审厂'), value: 5 },
  { text: i18n.t('突击审厂'), value: 6 },
  { text: i18n.t('日常稽查'), value: 7 },
  { text: i18n.t('交模验收'), value: 8 },
  { text: i18n.t('其他'), value: 9 }
]

export const examineModeOptions = [
  { text: i18n.t('现场评审'), value: 1 },
  { text: i18n.t('线下资料评审'), value: 2 }
]

export const factoryTypeOptions = [
  { text: i18n.t('模具厂+制件厂'), value: 0 },
  { text: i18n.t('模具厂'), value: 1 },
  { text: i18n.t('制件厂'), value: 2 }
]

export const examineFrequencyOptions = [
  { text: i18n.t('每年'), value: 1 },
  { text: i18n.t('每半年'), value: 2 },
  { text: i18n.t('每季'), value: 3 },
  { text: i18n.t('每月'), value: 4 },
  { text: i18n.t('一次性'), value: 5 }
]

export const options = [
  { text: i18n.t('是'), value: 1 },
  { text: i18n.t('否'), value: 0 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    field: 'examineCode',
    title: i18n.t('审厂单号'),
    slots: {
      default: 'examineCodeDefault'
    }
  },
  {
    field: 'examineName',
    title: i18n.t('审厂主题名称')
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    minWidth: 80,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'examineType',
    title: i18n.t('审厂类型'),
    formatter: ({ cellValue }) => {
      let item = examineTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'examineMode',
    title: i18n.t('审厂方式'),
    formatter: ({ cellValue }) => {
      let item = examineModeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'factoryType',
    title: i18n.t('工厂类型'),
    formatter: ({ cellValue }) => {
      let item = factoryTypeOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'factoryCode',
    title: i18n.t('考核厂'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.factoryName : ''
    }
  },
  {
    field: 'factoryAddress',
    title: i18n.t('考核厂地点')
  },
  {
    field: 'supplierContactName',
    title: i18n.t('供方联系人')
  },
  {
    field: 'supplierContactPhone',
    title: i18n.t('供方联系电话')
  },
  {
    field: 'planExamineDate',
    title: i18n.t('计划审厂日期'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD')
    }
  },
  {
    field: 'relationMouldFlag',
    title: i18n.t('是否关联模具'),
    formatter: ({ cellValue }) => {
      let item = options.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'onlineConfirmFlag',
    title: i18n.t('是否需要各部门线上确认'),
    formatter: ({ cellValue }) => {
      let item = options.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'confirmDuration',
    title: i18n.t('确认时长/天')
  },
  {
    field: 'allowAppealFlag',
    title: i18n.t('是否允许供方申诉'),
    formatter: ({ cellValue }) => {
      let item = options.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'appealEndTime',
    title: i18n.t('供方计划申诉结束时间'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD')
    }
  },
  {
    field: 'appealDuration',
    title: i18n.t('供方申诉时长/天')
  },
  {
    field: 'actualAppealEndTime',
    title: i18n.t('实际供方申诉结束时间'),
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD')
    }
  },
  {
    field: 'examineFrequency',
    title: i18n.t('考核频率'),
    formatter: ({ cellValue }) => {
      let item = examineFrequencyOptions.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'remindStartTime',
    title: i18n.t('审核提醒周期'),
    formatter: ({ cellValue, row }) => {
      return cellValue
        ? dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss') +
            '-' +
            dayjs(Number(row.remindEndTime)).format('YYYY-MM-DD HH:mm:ss')
        : ''
    }
  },
  {
    field: 'orgCode',
    title: i18n.t('使用组织'),
    formatter: ({ cellValue, row }) => {
      return cellValue ? cellValue + '-' + row.orgName : ''
    }
  },
  {
    field: 'autoConfirmFlag',
    title: i18n.t('超期供方是否自动确认'),
    formatter: ({ cellValue }) => {
      let item = options.find((item) => item.value === cellValue)
      return item ? item.text : ''
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人')
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'updateUserName',
    title: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    title: i18n.t('更新时间'),
    minWidth: 160,
    formatter: ({ cellValue }) => {
      return cellValue && dayjs(Number(cellValue)).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    field: 'operate',
    title: i18n.t('操作'),
    fixed: 'right',
    slots: {
      default: 'operateDefault'
    }
  }
]
