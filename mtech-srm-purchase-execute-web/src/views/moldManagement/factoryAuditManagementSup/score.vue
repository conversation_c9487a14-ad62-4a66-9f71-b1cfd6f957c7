<template>
  <div class="factory-audit-score">
    <div class="top">
      <mt-button @click="handleCancel">{{ $t('取消') }}</mt-button>
      <mt-button @click="handleSubmit">{{ $t('提交') }}</mt-button>
    </div>

    <div style="font-weight: 600; margin-bottom: 10px">{{ $t('审厂单号') }}：{{ examineCode }}</div>
    <div>
      <mt-row :gutter="24">
        <mt-col :span="24">
          <mt-tabs
            :e-tab="false"
            :data-source="tabList"
            :selected-item="tabIndex"
            @handleSelectTab="handleSelectTab"
          />
        </mt-col>
      </mt-row>
      <div>
        <mt-form ref="formRef" :model="modelForm" :rules="formRules" style="margin-top: 20px">
          <mt-row :gutter="24">
            <mt-col :span="6">
              <mt-form-item :label="$t('是否同意此结果')" prop="noAgreeFlag">
                <mt-radio v-model="modelForm.noAgreeFlag" :data-source="isAgreeOptions" />
              </mt-form-item>
            </mt-col>
          </mt-row>
        </mt-form>
      </div>
      <div v-if="tabIndex === 0">
        <Summary v-if="summaryFormData" :form-data="summaryFormData" />
      </div>
      <div v-if="tabIndex === 1">
        <ImprovementSummary
          :summary-data="summaryData"
          :can-edit="modelForm.noAgreeFlag === '1'"
          @save="summarySave"
        />
      </div>
      <div v-if="tabIndex === 2">
        <ImprovementDetail
          :detail-data="detailData"
          :can-edit="modelForm.noAgreeFlag === '1'"
          @save="detailSave"
        />
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import Summary from './components/Summary.vue'
import ImprovementSummary from './components/ImprovementSummary.vue'
import ImprovementDetail from './components/ImprovementDetail.vue'
export default {
  components: { Summary, ImprovementSummary, ImprovementDetail },
  data() {
    return {
      tabList: [
        { title: this.$t('汇总') },
        { title: this.$t('待改善要求汇总') },
        { title: this.$t('待改善要求明细') }
      ],
      tabIndex: 0,

      modelForm: {},
      formRules: {
        noAgreeFlag: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      editConfig: {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      summaryFormData: null,
      summaryData: [],
      detailData: [],
      examineCode: null,
      allowAppealFlag: 1 // 是否允许供方申诉,0-否,1-是
    }
  },
  computed: {
    isAgreeOptions() {
      let options = [
        { label: this.$t('同意'), value: '0' },
        { label: this.$t('不同意'), value: '1', disabled: this.allowAppealFlag === 0 }
      ]
      return options
    }
  },
  created() {
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$API.moldManagement.supplierQueryResultAppealApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = res.data
          this.summaryFormData = res.data.summary
          this.summaryData = res.data.improveSummary || []
          this.summaryData.forEach((item) => {
            if (item.finishTime === '0') {
              item.finishTime = null
            }
          })
          this.detailData = res.data.improveDetail
          this.examineCode = res.data.examineCode
          this.allowAppealFlag = res.data.allowAppealFlag
        }
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    summarySave(data) {
      this.summaryData = data
    },
    detailSave(data) {
      this.detailData.forEach((item) => {
        item.detailList = data[item.module]
      })
    },
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let params = this.getParams()
          this.$dialog({
            data: {
              title: this.$t('提示'),
              message: this.$t('确认提交？')
            },
            success: () => {
              this.$API.moldManagement.supplierSubmitResultAppealApi(params).then((res) => {
                if (res.code === 200) {
                  this.$toast({ content: this.$t('提交成功'), type: 'success' })
                  this.$router.push({
                    name: 'factory-audit-management-sup',
                    query: {
                      timeStamp: new Date().getTime()
                    }
                  })
                }
              })
            }
          })
        } else {
          this.$toast({ content: this.$t('请完成必填项后进行提交操作'), type: 'warning' })
        }
      })
    },
    getParams() {
      let params = {
        examineId: this.modelForm.examineId,
        noAgreeFlag: Number(this.modelForm.noAgreeFlag)
      }
      params.summary = this.summaryFormData
      params.improveSummary = cloneDeep(this.summaryData)
      params.improveDetail = this.detailData
      params.improveSummary.forEach((item) => {
        item.finishTime = dayjs(dayjs(item.finishTime).format('YYYY-MM-DD')).valueOf()
      })

      return params
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.factory-audit-score {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
}
</style>
