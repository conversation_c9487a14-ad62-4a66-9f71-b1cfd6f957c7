<template>
  <div>
    <mt-tabs
      :e-tab="false"
      :data-source="tabList"
      :selected-item="tabIndex"
      @handleSelectTab="handleSelectTab"
    />
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="currentDetailTableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template #fileSlot="{ row }">
        <div>
          <span style="color: #409eff; cursor: pointer" @click="handlePreview(row)">
            {{ row.fileName }}
          </span>
        </div>
      </template>
    </sc-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    detailData: {
      type: Array,
      default: () => {
        return []
      }
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  components: { ScTable },
  data() {
    return {
      tabList: [],
      tabIndex: 0,
      tabName: null,

      loading: false,
      editRules: {
        improveIdea: [{ required: true, message: this.$t('必填') }],
        responsiblePerson: [{ required: true, message: this.$t('必填') }],
        fileId: [{ required: true, message: this.$t('必填') }],
        finishTime: [{ required: true, message: this.$t('必填') }]
      },

      currentDetailTableData: [], // 当前显示明细
      detailTableData: {} // 所有模块明细
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'examineType',
          title: this.$t('考核类型')
        },
        {
          field: 'scoreContent',
          title: this.$t('评分内容')
        },
        {
          field: 'problemLevel',
          title: this.$t('问题等级')
        },
        {
          field: 'standardScore',
          title: this.$t('标分')
        },
        {
          field: 'score',
          title: this.$t('得分')
        },
        {
          field: 'explain',
          title: this.$t('稽查发现/得分说明')
        },
        {
          field: 'improveIdea',
          title: this.$t('改善对策')
        },
        {
          field: 'responsiblePerson',
          title: this.$t('责任人')
        },
        {
          field: 'fileId',
          title: this.$t('改善佐证'),
          editRender: {},
          slots: {
            default: 'fileDefault'
          }
        },
        {
          field: 'finishTime',
          title: this.$t('预计完成时间'),
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        }
      ]
    },
    editConfig() {
      return {
        enabled: false,
        trigger: 'click',
        mode: 'row',
        showStatus: false
      }
    }
  },
  watch: {
    detailData: {
      handler(value) {
        this.tabList = value.map((item) => {
          return {
            title: item.module
          }
        })
        this.tabList.forEach((item) => {
          this.detailTableData[item.title] = value.find((v) => v.module === item.title)?.detailList
        })
        this.tabName = this.tabList[this.tabIndex]?.title
        let currentDetailTableData = this.detailTableData[this.tabName] || []
        this.currentDetailTableData = currentDetailTableData
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handlePreview(row) {
      let params = {
        id: row?.fileId,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
      this.tabName = this.tabList[e]?.title
      let currentDetailTableData = this.detailTableData[this.tabName] || []
      this.currentDetailTableData = currentDetailTableData
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行提交操作'), type: 'warning' })
            return
          }

          let currentDetailTableData = this.tableRef.getTableData().visibleData
          let tabName = this.tabList[this.tabIndex].title
          this.detailTableData[tabName] = currentDetailTableData

          this.$emit('save', this.detailTableData)
        })
      }
    }
  }
}
</script>

<style></style>
