<template>
  <div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :row-config="{ height: 64 }"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
      <template #fileSlot="{ row }">
        <div>
          <vxe-button v-if="canEdit" @click="handleUpload(row)">{{ $t('上传附件') }}</vxe-button>
          <div>
            <span style="color: #409eff; cursor: pointer" @click="handlePreview(row)">
              {{ row.fileName }}
            </span>
            <i
              v-if="row.fileName"
              class="vxe-icon-delete"
              style="margin-left: 5px; cursor: pointer"
              @click="handleDelete(row)"
            />
          </div>
        </div>
      </template>
    </sc-table>

    <input type="file" ref="fileRef" style="display: none" @change="chooseFiles" />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    summaryData: {
      type: Array,
      default: () => {
        return []
      }
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  components: { ScTable },
  data() {
    return {
      loading: false,
      tableData: [],
      editRules: {
        improveIdea: [{ required: true, message: this.$t('必填') }],
        responsiblePerson: [{ required: true, message: this.$t('必填') }],
        fileId: [{ required: true, message: this.$t('必填') }],
        finishTime: [{ required: true, message: this.$t('必填') }]
      },

      currentRow: null
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      return [
        {
          title: this.$t('供应商审厂不符合项改善要求书'),
          children: [
            {
              type: 'seq',
              title: this.$t('序号'),
              width: 50,
              fixed: 'left',
              align: 'center'
            },
            {
              title: this.$t('不符合项内容描述/审核发现'),
              children: [
                {
                  field: 'module',
                  title: this.$t('所属模块')
                },
                {
                  field: 'examineType',
                  title: this.$t('考核类型')
                },
                {
                  field: 'scoreContent',
                  title: this.$t('评分内容')
                },
                {
                  field: 'problemLevel',
                  title: this.$t('问题等级')
                },
                {
                  field: 'standardScore',
                  title: this.$t('标分')
                },
                {
                  field: 'score',
                  title: this.$t('得分')
                },
                {
                  field: 'explain',
                  title: this.$t('稽查发现/得分说明')
                }
              ]
            },
            {
              field: 'problemSeriousLevel',
              title: this.$t('问题严重度（必改项/建议项)')
            },
            {
              title: this.$t('供应商回复'),
              children: [
                {
                  field: 'improveIdea',
                  title: this.$t('改善对策'),
                  editRender: {},
                  slots: {
                    edit: ({ row }) => {
                      return [
                        <vxe-input
                          v-model={row.improveIdea}
                          placeholder={this.$t('请输入')}
                          transfer
                          clearable
                        />
                      ]
                    }
                  }
                },
                {
                  field: 'responsiblePerson',
                  title: this.$t('责任人'),
                  editRender: {},
                  slots: {
                    edit: ({ row }) => {
                      return [
                        <vxe-input
                          v-model={row.responsiblePerson}
                          placeholder={this.$t('请输入')}
                          transfer
                          clearable
                        />
                      ]
                    }
                  }
                },
                {
                  field: 'fileId',
                  title: this.$t('改善佐证'),
                  editRender: {},
                  slots: {
                    default: 'fileSlot',
                    edit: 'fileSlot'
                  }
                },
                {
                  field: 'finishTime',
                  title: this.$t('预计完成时间'),
                  editRender: {},
                  slots: {
                    edit: ({ row }) => {
                      return [
                        <vxe-input
                          type={'date'}
                          v-model={row.finishTime}
                          placeholder={this.$t('请选择')}
                          transfer
                          clearable
                          label-format='yyyy-MM-dd'
                          value-format='yyyy-MM-dd'
                        />
                      ]
                    }
                  }
                  // formatter: ({ cellValue }) => {
                  //   let text = ''
                  //   if (cellValue && cellValue !== '0') {
                  //     text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
                  //   }
                  //   return text
                  // }
                }
              ]
            }
          ]
        }
      ]
    },
    editConfig() {
      return {
        enabled: this.canEdit,
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
      }
    }
  },
  watch: {
    summaryData: {
      handler(value) {
        this.tableData = value
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleUpload(row) {
      this.currentRow = row
      this.$refs['fileRef'].click()
    },
    chooseFiles(data) {
      const fileList = Object.values(data?.target?.files)
      if (fileList.length < 1) {
        return
      }
      if (fileList.length > 5) {
        this.$toast({
          content: this.$t('一次性最多选择5个文件'),
          type: 'warning'
        })
        return
      }
      const params = {
        type: 'array',
        limit: 50 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      // 判断当个文件是否过大
      let isOutOfRange = false
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].size > params.limit * 1024) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({ content: params.msg })
        return
      }
      fileList.forEach((item) => {
        const _data = new FormData()
        _data.append('UploadFiles', item)
        _data.append('useType', 1)
        this.uploadFile(_data)
      })
    },
    async uploadFile(fileData) {
      this.$loading()
      const res = await this.$API.fileService.uploadPublicFile(fileData)
      this.$hloading()
      if (res.code === 200) {
        this.tableData.forEach((item) => {
          if (item.id === this.currentRow.id) {
            item.fileId = res.data.id
            item.fileName = res.data.fileName
            item.fileUrl = res.data.url
          }
        })
        this.$emit('save', this.tableData)
      }
    },
    // 预览
    handlePreview(row) {
      let params = {
        id: row?.fileId,
        useType: 2
      }
      this.$API.fileService.getMtPreviewPub(params).then((res) => {
        window.open(res.data)
      })
    },
    // 删除
    handleDelete(row) {
      this.tableData.forEach((item) => {
        if (item.id === row.id) {
          item.fileId = null
          item.fileName = null
          item.fileUrl = null
        }
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行提交操作'), type: 'warning' })
            return
          }

          let data = this.tableRef.getTableData().visibleData
          this.$emit('save', data)
        })
      }
    }
  }
}
</script>

<style></style>
