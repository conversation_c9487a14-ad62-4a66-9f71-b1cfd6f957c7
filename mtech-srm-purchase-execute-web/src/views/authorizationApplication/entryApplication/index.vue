<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="tempPassApplyNo" :label="$t('临时出入证号')" label-style="top">
              <mt-input
                v-model="searchFormModel.tempPassApplyNo"
                :show-clear-button="true"
                :placeholder="$t('请输入临时出入证号')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                @change="(e) => handleDateTimeChange(e, 'createDate')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierCode"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商编码')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierType" :label="$t('供应商类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.supplierType"
                css-class="rule-element"
                :data-source="[{ value: '0', text: $t('其他'), cssClass: 'col-active' }]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择供应商类型')"
              />
            </mt-form-item>
            <mt-form-item prop="tempPassApplyStatus" :label="$t('审批状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.tempPassApplyStatus"
                css-class="rule-element"
                :data-source="[
                  { value: '0', text: $t('新增'), cssClass: 'col-active' },
                  { value: '1', text: $t('审批中'), cssClass: 'col-active' },
                  { value: '2', text: $t('审批通过'), cssClass: 'col-active' },
                  { value: '3', text: $t('驳回'), cssClass: 'col-inactive' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择审批状态')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('临时出入公司')" label-style="top">
              <!-- <mt-input
                v-model="searchFormModel.companyName"
                :show-clear-button="true"
                :placeholder="$t('请输入临时出入公司')"
              /> -->
              <RemoteAutocomplete
                v-model="searchFormModel.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择临时出入公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="newApplyFlag" :label="$t('申请类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.newApplyFlag"
                css-class="rule-element"
                :data-source="[
                  { value: '1', text: $t('新办证'), cssClass: 'col-active' },
                  { value: '2', text: $t('换证'), cssClass: 'col-active' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择申请类型')"
              />
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('有效开始时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.startDate"
                @change="(e) => handleDateTimeChange(e, 'startDate')"
                :placeholder="$t('请选择有效开始时间')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('有效截止时间')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.endDate"
                @change="(e) => handleDateTimeChange(e, 'endDate')"
                :placeholder="$t('请选择有效截止时间')"
              />
            </mt-form-item>
            <mt-form-item prop="userName" :label="$t('姓名')" label-style="top">
              <mt-input
                v-model="searchFormModel.userName"
                :show-clear-button="true"
                :placeholder="$t('请输入姓名')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig } from './config'
import * as UTILS from '@/utils/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [] // 指标名称列表
    }
  },
  computed: {},
  created() {
    let tempPassApplyNo = ''
    if (this.$route.query && this.$route.query.tempPassApplyNo) {
      tempPassApplyNo = this.$route.query.tempPassApplyNo
    }
    this.searchFormModel.tempPassApplyNo = tempPassApplyNo
    this.pageConfig = pageConfig(this)
  },
  mounted() {
    this.$bus.$on('entryApplicationViewOA', (e) => {
      this.$dialog({
        modal: () => import('./components/viewOADialog.vue'),
        data: {
          title: this.$t('查看OA审批节点'),
          row: e
        },
        success: () => {
          // this.confirmSuccess();
        }
      })
    })
  },
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        this.$router.push({
          path: `add-pur-entry-application`,
          query: {
            timeStamp: new Date().getTime()
          }
          // key: utils.randomString(),
          // query: { type: "add" },
        })
      } else if (toolbar.id === 'Save') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        // this.handleSave(selectedRecords)
      } else if (toolbar.id === 'Delete') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
      } else if (toolbar.id === 'Submit') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleSubmit(selectedRecords)
      } else if (toolbar.id === 'Export1') {
        // if (selectedRecords.length === 0) {
        //   this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        //   return
        // }
        this.handleExport(selectedRecords)
      }
    },
    handleExport(selectedRecords) {
      const params = {
        page: { current: 1, size: 9999 },
        ...this.searchFormModel,
        ids: selectedRecords.map((i) => i.id)
      }
      this.$store.commit('startLoading')
      this.$API.authorizationApplication.exportSupPassPage(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleSubmit(records) {
      console.log(this.$t('提交选中的'), records)
    },
    handleClickCellTitle() {},
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        if (!data.companyCode) {
          this.$toast({ content: this.$t('请选择临时出入公司'), type: 'warning' })
          args.cancel = true
          return
        }
        if (!data.userName) {
          this.$toast({ content: this.$t('请填写姓名'), type: 'warning' })
          args.cancel = true
          return
        }
        if (!data.deptName) {
          this.$toast({ content: this.$t('请填写部门'), type: 'warning' })
          args.cancel = true
          return
        }
        const regTel = /^1[3-9]\d{9}$/
        if (data.tel && !regTel.test(data.tel)) {
          this.$toast({ content: this.$t('电话格式有误'), type: 'warning' })
          args.cancel = true
          return
        }
        if (!data.idcardNo) {
          this.$toast({ content: this.$t('请填写身份证信息'), type: 'warning' })
          args.cancel = true
          return
        }
        const reg =
          /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[1-2]\d|3[0-1])\d{3}(?:\d|X)$/

        if (!reg.test(data.idcardNo)) {
          this.$toast({
            content: this.$t(`身份证信息输入有误`),
            type: 'warning'
          })
          return
        }
        if (!data.startDate) {
          this.$toast({
            content: this.$t(`请选择有效开始日期`),
            type: 'warning'
          })
          args.cancel = true
          return
        }
        const today = dayjs(new Date()).format('YYYY-MM-DD')
        const startDate = dayjs(data.startDate).format('YYYY-MM-DD')
        if (new Date(startDate).getTime() < new Date(today).getTime()) {
          this.$toast({
            content: this.$t(`有效开始日期不可早于当前时间`),
            type: 'warning'
          })
          args.cancel = true
          return
        }
        if (!data.endDate) {
          this.$toast({
            content: this.$t(`请选择有效截止日期`),
            type: 'warning'
          })
          args.cancel = true
          return
        }
        const endDate = dayjs(data.endDate).format('YYYY-MM-DD')
        if (new Date(endDate).getTime() < new Date(startDate).getTime()) {
          this.$toast({
            content: this.$t(`有效截止日期不可早于有效开始日期`),
            type: 'warning'
          })
          args.cancel = true
          return
        }
      }
    },
    actionComplete() {
      // const { requestType, data, rowIndex } = args
      // if (requestType === 'save' && args.action === 'add') {
      //   !args.cancel && this.handleSave(data, rowIndex)
      // }
    },
    // 保存
    handleSave(rowData, rowIndex) {
      this.$API.authorizationApplication
        .createSupPass({ insertDtoList: [rowData] })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    }
  },
  beforeDestroy() {
    this.$bus.$off('entryApplicationViewOA')
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
