//现场评审得分设置Tab
import { i18n } from '@/main.js'
import { utils } from '@mtech-common/utils'
import cellUpload from '../components/cellUpload.vue' // 单元格上传
import cellFileView from '../components/cellFileView' // 单元格附件查看
import cellChanged from '@/components/normalEdit/cellChanged' // 单元格被改变（纯展示）
import Vue from 'vue'
import dayjs from 'dayjs'

const statusList = [
  { value: '0', text: i18n.t('新增'), cssClass: 'col-active' },
  { value: '1', text: i18n.t('审批中'), cssClass: 'col-active' },
  { value: '2', text: i18n.t('审批通过'), cssClass: 'col-active' },
  { value: '3', text: i18n.t('驳回'), cssClass: 'col-inactive' }
]

const supplierTypeList = [{ value: '0', text: i18n.t('其他') }]

const newApplyFlag = [
  { value: '1', text: i18n.t('新办证') },
  { value: '2', text: i18n.t('换证') }
]

export const columnDetailData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      width: '150',
      field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
      headerText: i18n.t('addId主键'),
      visible: false,
      isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
      isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
      allowEditing: false
    },
    {
      field: 'serialNumber', // 前端定义
      headerText: i18n.t('序号'),
      allowEditing: false
    },
    {
      field: 'tempPassApplyNo',
      headerText: i18n.t('临时出入证号'),
      allowEditing: false
    },
    {
      field: 'userName',
      headerText: i18n.t('姓名')
    },
    {
      field: 'deptName',
      headerText: i18n.t('部门')
    },
    {
      field: 'idcardNo',
      headerText: i18n.t('身份证信息')
    },
    {
      field: 'tel',
      headerText: i18n.t('电话')
    },
    {
      field: 'startDate',
      headerText: i18n.t('有效起始日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-date-picker
                  :id="data.column.field"
                  v-model="data[data.column.field]"
                  :open-on-focus="true"
                  :allow-edit="false"
                  :placeholder="$t('请选择有效起始日期')"
                ></mt-date-picker>
              `
          })
        }
      }
    },
    {
      field: 'endDate',
      headerText: i18n.t('有效截止日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
            <mt-date-picker
              :id="data.column.field"
              v-model="data[data.column.field]"
              :open-on-focus="true"
              :allow-edit="false"
              :placeholder="$t('请选择有效截止日期')"
            ></mt-date-picker>
              `
          })
        }
      }
    },
    {
      field: 'newApplyFlag',
      headerText: i18n.t('申请类型'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < newApplyFlag.length; i++) {
                  const item = newApplyFlag[i]
                  if (item.value === this.data[this.data.column.field]) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
              <mt-select
                width="100%"
                :id="data.column.field"
                :data-source="newApplyFlag"
                :placeholder="$t('请选择')"
                css-class="input-select"
                v-model="data[data.column.field]"
              ></mt-select>
              `,
            data() {
              return {
                newApplyFlag
              }
            },
            created() {
              if (!this.data[this.data.column.field] && !this.data.id) {
                this.data[this.data.column.field] = '1'
              }
            }
          })
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]
  return column
}

const columnData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'serialNumber', // 前端定义
      headerText: i18n.t('序号'),
      allowEditing: false
    },
    {
      field: 'tempPassApplyNo',
      headerText: i18n.t('临时出入证号'),
      allowEditing: false
    },
    {
      field: 'tempPassApplyStatus',
      headerText: i18n.t('状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: statusList
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span :class="data.tempPassApplyStatus !== '3' ? 'col-active' : 'col-inactive'">{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < statusList.length; i++) {
                  const item = statusList[i]
                  if (item.value == this.data.tempPassApplyStatus) {
                    return item.text
                  }
                }
                return this.$t('新增')
              }
            }
          })
        }
      }
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      allowEditing: false
    },
    // {
    //   field: 'OANode',
    //   headerText: i18n.t('OA当前审批节点'),
    //   sticky: true,
    //   allowEditing: false,
    //   template: () => {
    //     return {
    //       template: Vue.component('headers', {
    //         template: `
    //             <span style="cursor: pointer;color: #00469c;" @click="viewOA">{{ $t('查看') }}</span>
    //           `,
    //         methods: {
    //           viewOA() {
    //             // this.$bus.$emit('entrySupApplicationViewOA', this.data)
    //             if (this.data.oaUrl) {
    //               window.open(this.data.oaUrl)
    //             }
    //           }
    //         }
    //       })
    //     }
    //   },
    //   editTemplate: () => {
    //     return {
    //       template: Vue.component('headers', {
    //         template: `
    //             <span style="cursor: pointer;color: #00469c;" @click="viewOA">{{ $t('查看') }}</span>
    //           `,
    //         methods: {
    //           viewOA() {
    //             // this.$bus.$emit('entrySupApplicationViewOA', this.data)
    //             if (this.data.oaUrl) {
    //               window.open(this.data.oaUrl)
    //             }
    //           }
    //         }
    //       })
    //     }
    //   }
    // },
    {
      field: 'printCnt',
      headerText: i18n.t('打印次数'),
      allowEditing: false
    },
    {
      field: 'userName',
      headerText: i18n.t('姓名'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'deptName',
      headerText: i18n.t('部门'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'tel',
      headerText: i18n.t('电话'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'idcardNo',
      headerText: i18n.t('身份证信息'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'supplierType',
      headerText: i18n.t('供应商类型'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < supplierTypeList.length; i++) {
                  const item = supplierTypeList[i]
                  if (item.value === this.data[this.data.column.field]) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
              <mt-select
                width="100%"
                :id="data.column.field"
                :data-source="supplierTypeList"
                disabled
                :placeholder="$t('请选择')"
                css-class="input-select"
                v-model="data[data.column.field]"
              ></mt-select>
              `,
            data() {
              return {
                supplierTypeList
              }
            },
            created() {
              if (!this.data[this.data.column.field] && !this.data.id) {
                this.data[this.data.column.field] = '0'
              }
            }
          })
        }
      }
    },
    {
      field: 'startDate',
      headerText: i18n.t('有效开始时间'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-date-picker
                  :id="data.column.field"
                  v-model="data[data.column.field]"
                  :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')"
                  :open-on-focus="true"
                  :allow-edit="false"
                  :placeholder="$t('请选择有效开始时间')"
                ></mt-date-picker>
              `
          })
        }
      }
    },
    {
      field: 'endDate',
      headerText: i18n.t('有效截止时间'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-date-picker
                  :id="data.column.field"
                  v-model="data[data.column.field]"
                  :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')"
                  :open-on-focus="true"
                  :allow-edit="false"
                  :placeholder="$t('请选择有效截止时间')"
                ></mt-date-picker>
              `
          })
        }
      }
    },
    {
      field: 'companyCode',
      headerText: i18n.t('临时出入公司'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ data.companyName }}</span>
              `
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-select
                  :id="data.column.field"
                  v-model="data.companyCode"
                  float-label-type="Never"
                  disabled
                  :data-source="companySelect"
                  :fields="{ text: 'orgName', value: 'orgCode' }"
                  :allow-filtering="true"
                  :filtering="inputPersonnel"
                  @change="changecompanySelect"
                  :placeholder="$t('请选择临时出入公司')"
                ></mt-select>
              `,
            data() {
              return {
                companySelect: []
              }
            },
            created() {
              if (this.data.companyCode) {
                this.initialCallInterface(this.data.companyCode)
              }
            },
            mounted() {
              this.inputPersonnel = utils.debounce(this.inputPersonnel, 500)
            },
            methods: {
              // 初始化调用接口
              initialCallInterface(text) {
                //公司
                let parameter = {
                  fuzzyParam: text,
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true,
                  organizationIds: []
                }
                this.$API.contractPrint.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
                  this.companySelect = res.data
                  this.data.companyName = res.data[0].orgName
                  this.data.companyCode = res.data[0].orgCode
                  this.$bus.$emit('companyNameChange', res.data[0].orgName)
                })
              },
              //公司下拉框事件
              changecompanySelect(e) {
                this.data.companyName = e.itemData.orgName
                this.data.companyCode = e.itemData.orgCode
                this.$bus.$emit('companyNameChange', e.itemData.orgName)
              },
              //公司模糊查询事件
              inputPersonnel(e) {
                let parameter = {
                  fuzzyParam: e.text,
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true,
                  organizationIds: []
                }
                this.$API.contractPrint.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
                  this.companySelect = res.data
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'companyName',
      headerText: i18n.t('临时出入公司名称'),
      width: 1,
      editTemplate: () => {
        return {
          template: cellChanged
        }
      }
    },
    {
      field: 'newApplyFlag',
      headerText: i18n.t('申请类型'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < newApplyFlag.length; i++) {
                  const item = newApplyFlag[i]
                  if (item.value === this.data[this.data.column.field]) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
              <mt-select
                width="100%"
                :id="data.column.field"
                :data-source="newApplyFlag"
                :disabled="!(data.printCnt == 0 && data.tempPassApplyStatus === '0')"
                :placeholder="$t('请选择')"
                css-class="input-select"
                v-model="data[data.column.field]"
              ></mt-select>
              `,
            data() {
              return {
                newApplyFlag
              }
            },
            created() {
              if (!this.data[this.data.column.field] && !this.data.id) {
                this.data[this.data.column.field] = '1'
              }
            }
          })
        }
      }
    },
    {
      field: 'attachFilePath',
      headerText: i18n.t('附件信息'),
      allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: () => {
        return {
          template: cellUpload
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="data.authorizeCertStatus === '2'" />
              `
          })
        }
      }
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            // { id: 'Save', icon: 'icon_solid_Save', title: i18n.t('保存') },
            { id: 'CancelEdit', icon: 'icon_solid_Save', title: i18n.t('取消编辑') },
            // { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
            { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
            { id: 'Print', icon: 'icon_list_print', title: i18n.t('打印') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        frozenColumns: 4,
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/deliver/auth/supPass/supplier/query/page',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
                if (item.startDate) {
                  item.startDate = item.startDate.split(' ')[0]
                }
                if (item.endDate) {
                  item.endDate = item.endDate.split(' ')[0]
                }
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
