<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="authCode" :label="$t('授权书号')" label-style="top">
              <mt-input
                v-model="searchFormModel.authCode"
                :show-clear-button="true"
                :placeholder="$t('请输入授权书号')"
              />
            </mt-form-item>
            <mt-form-item prop="idcardNo" :label="$t('身份证号')" label-style="top">
              <mt-input
                v-model="searchFormModel.idcardNo"
                :show-clear-button="true"
                :placeholder="$t('请输入身份证号')"
              />
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商编码')" label-style="top">
              <mt-input
                v-model="searchFormModel.supplierCode"
                :show-clear-button="true"
                :placeholder="$t('请输入供应商编码')"
              />
            </mt-form-item>
            <mt-form-item prop="userName" :label="$t('姓名')" label-style="top">
              <mt-input
                v-model="searchFormModel.userName"
                :show-clear-button="true"
                :placeholder="$t('请输入姓名')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                @change="(e) => handleDateTimeChange(e, 'createDate')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="authorizeCertStatus" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.authorizeCertStatus"
                css-class="rule-element"
                :data-source="[
                  { value: '0', text: $t('新增'), cssClass: 'col-active' },
                  { value: '1', text: $t('审批中'), cssClass: 'col-active' },
                  { value: '2', text: $t('审批通过'), cssClass: 'col-active' },
                  { value: '3', text: $t('驳回'), cssClass: 'col-inactive' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="bizType" :label="$t('业务类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.bizType"
                css-class="rule-element"
                :data-source="[
                  { value: '0', text: $t('领料'), cssClass: '' },
                  { value: '1', text: $t('退料'), cssClass: '' },
                  { value: '2', text: $t('领退料'), cssClass: '' },
                  { value: '3', text: $t('包材回收'), cssClass: '' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择业务类型')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('截止日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.endDate"
                @change="(e) => handleDateTimeChange(e, 'endDate')"
                :placeholder="$t('请选择截止日期')"
              />
            </mt-form-item>
            <mt-form-item prop="companyName" :label="$t('授权公司')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.companyCode"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择授权公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig } from './config'
import * as UTILS from '@/utils/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [] // 指标名称列表
    }
  },
  computed: {},
  created() {
    let authCode = ''
    if (this.$route.query && this.$route.query.authCode) {
      authCode = this.$route.query.authCode
    }
    this.searchFormModel.authCode = authCode
    this.pageConfig = pageConfig(this)
  },
  mounted() {
    this.$bus.$on('applicationFormViewOA', (e) => {
      this.$dialog({
        modal: () => import('./components/viewOADialog.vue'),
        data: {
          title: this.$t('查看OA审批节点'),
          row: e
        },
        success: () => {
          // this.confirmSuccess();
        }
      })
    })
  },
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        this.$router.push({
          path: `add-application-form`,
          query: {
            timeStamp: new Date().getTime()
          }
          // key: utils.randomString(),
          // query: { type: "add" },
        })
      } else if (toolbar.id === 'Save') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleSave(selectedRecords)
        // } else if (toolbar.id === 'Submit') {
        //   if (selectedRecords.length === 0) {
        //     this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        //     return
        //   }
        //   this.handleSubmit(selectedRecords)
      } else if (toolbar.id === 'Export1') {
        // if (selectedRecords.length === 0) {
        //   this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
        //   return
        // }
        this.handleExport(selectedRecords)
      }
    },
    // handleSubmit(records) {
    //   console.log(this.$t('提交选中的'), records)
    // },
    handleExport(selectedRecords) {
      const params = {
        page: { current: 1, size: 9999 },
        ...this.searchFormModel,
        ids: selectedRecords.map((i) => i.id)
      }
      this.$store.commit('startLoading')
      this.$API.authorizationApplication.exportAuthPage(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTitle() {},
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin(args) {
      const { requestType, data } = args
      if (requestType === 'save') {
        if (data.authorizeCertStatus === '2' && data.updateCnt === 0) {
          const updateEndDate = data.updateEndDate
            ? dayjs(data.updateEndDate).format('YYYY-MM-DD')
            : ''
          const endDate = dayjs(data.endDate).format('YYYY-MM-DD')
          if (
            data.updateEndDate &&
            new Date(updateEndDate).getTime() >= new Date(endDate).getTime()
          ) {
            this.$toast({
              content: this.$t('修改授权截止日期必须早于授权截止日期'),
              type: 'warning'
            })
            args.cancel = true
          }
        }
      }
    },
    actionComplete() {
      // const { requestType, data, rowIndex } = args
      // if (requestType === 'save') {
      //   !args.cancel && this.handleSave(data, rowIndex)
      // }
    },
    // 保存
    handleSave(selectedRecords) {
      const params = []
      selectedRecords.forEach((i) => {
        let updateEndDate = ''
        if (i.updateEndDate) {
          updateEndDate = dayjs(i.updateEndDate).format('YYYY-MM-DD')
        } else {
          updateEndDate = ''
        }
        const item = { ...i, updateEndDate, lineId: i.id }
        params.push(item)
      })
      this.$API.authorizationApplication.savePurAuthPage({ dtoList: params }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('保存成功'), type: 'success' })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    }
  },
  beforeDestroy() {
    this.$bus.$off('applicationFormViewOA')
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
