//现场评审得分设置Tab
import { i18n } from '@/main.js'
// import cellUpload from '../components/cellUpload.vue' // 单元格上传
import cellFileView from '../components/cellFileView' // 单元格附件查看
import Vue from 'vue'
import dayjs from 'dayjs'

const statusList = [
  { value: '0', text: i18n.t('新增'), cssClass: 'col-active' },
  { value: '1', text: i18n.t('审批中'), cssClass: 'col-active' },
  { value: '2', text: i18n.t('审批通过'), cssClass: 'col-active' },
  { value: '3', text: i18n.t('驳回'), cssClass: 'col-inactive' }
]

const bizTypeList = [
  { value: '0', text: i18n.t('领料'), cssClass: '' },
  { value: '1', text: i18n.t('退料'), cssClass: '' },
  { value: '2', text: i18n.t('领退料'), cssClass: '' },
  { value: '3', text: i18n.t('包材回收'), cssClass: '' }
]

const columnData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'serialNumber', // 前端定义
      headerText: i18n.t('序号'),
      sticky: true,
      allowEditing: false
    },
    {
      field: 'authCode',
      headerText: i18n.t('授权书号'),
      sticky: true,
      allowEditing: false
    },
    {
      field: 'authorizeCertStatus',
      headerText: i18n.t('状态'),
      allowEditing: false,
      sticky: true,
      valueConverter: {
        type: 'map',
        map: statusList
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span :class="data.authorizeCertStatus !== '3' ? 'col-active' : 'col-inactive'">{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < statusList.length; i++) {
                  const item = statusList[i]
                  if (item.value === this.data.authorizeCertStatus) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      sticky: true,
      allowEditing: false
    },
    {
      field: 'OANode',
      headerText: i18n.t('OA当前审批节点'),
      sticky: true,
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span v-if="data.oaUrl" style="cursor: pointer;color: #00469c;" @click="viewOA">{{$t('查看')}}</span>
                <span v-else style="cursor: not-allowed;color: #888;">{{$t('查看')}}</span>
              `,
            methods: {
              viewOA() {
                // this.$bus.$emit('applicationFormViewOA', this.data)
                if (this.data.oaUrl) {
                  window.open(this.data.oaUrl)
                }
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span v-if="data.oaUrl" style="cursor: pointer;color: #00469c;" @click="viewOA">{{$t('查看')}}</span>
                <span v-else style="cursor: not-allowed;color: #888;">{{$t('查看')}}</span>
              `,
            methods: {
              viewOA() {
                // this.$bus.$emit('applicationFormViewOA', this.data)
                if (this.data.oaUrl) {
                  window.open(this.data.oaUrl)
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      sticky: true,
      allowEditing: false
    },
    {
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      sticky: true,
      allowEditing: false
    },
    {
      field: 'userName',
      headerText: i18n.t('姓名'),
      allowEditing: false
    },
    {
      field: 'idcardNo',
      headerText: i18n.t('身份证信息'),
      allowEditing: false
    },
    {
      field: 'tel',
      headerText: i18n.t('电话'),
      allowEditing: false
    },
    {
      field: 'startDate',
      headerText: i18n.t('授权起始日期'),
      allowEditing: false
    },
    {
      field: 'endDate',
      headerText: i18n.t('授权截止日期'),
      allowEditing: false
    },
    {
      field: 'updateEndDate',
      headerText: i18n.t('修改授权截止日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
            <mt-date-picker
              :id="data.column.field"
              v-model="data[data.column.field]"
              :open-on-focus="true"
              :allow-edit="false"
              :disabled="isDisabled"
              :min="minDate"
              :max="maxDate"
              :placeholder="$t('请选择修改授权截止日期')"
            ></mt-date-picker>
              `,
            data() {
              return {
                isDisabled: false,
                minDate: new Date(),
                maxDate: new Date()
              }
            },
            mounted() {
              const today = new Date(dayjs(new Date())).getTime()
              const endDate = new Date(dayjs(this.data.endDate)).getTime()
              if (
                this.data.authorizeCertStatus !== '2' &&
                this.data.updateCnt !== 0 &&
                endDate < today
              ) {
                this.isDisabled = true
              }
              this.minDate = new Date(this.data.startDate)
              this.maxDate = new Date(this.data.endDate)
            }
          })
        }
      }
    },
    {
      field: 'updateReason',
      headerText: i18n.t('修改授权截止日期原因'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="isDisabled" />
              `,
            data() {
              return {
                isDisabled: false
              }
            },
            mounted() {
              const today = new Date(dayjs(new Date())).getTime()
              const endDate = new Date(dayjs(this.data.endDate)).getTime()
              if (
                this.data.authorizeCertStatus !== '2' &&
                this.data.updateCnt !== 0 &&
                endDate < today
              ) {
                this.isDisabled = true
              }
            }
          })
        }
      }
    },
    {
      field: 'bizType',
      headerText: i18n.t('业务类型'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: bizTypeList
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < bizTypeList.length; i++) {
                  const item = bizTypeList[i]
                  if (item.value === this.data.bizType) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'companyName',
      headerText: i18n.t('授权公司'),
      allowEditing: false
    },
    {
      field: 'attachFilePath',
      headerText: i18n.t('附件信息'),
      allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: () => {
        return {
          template: cellFileView
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      allowEditing: false
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: 'e05d6538-59e5-479f-9d1f-dfba968935bd',
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            // { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            { id: 'Save', icon: 'icon_solid_Save', title: i18n.t('保存') },
            // { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
            { id: 'Export1', icon: 'icon_solid_export', title: i18n.t('导出') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        frozenColumns: 4,
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/deliver/auth/supAuth/buyer/query/page',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
                if (item.startDate) {
                  item.startDate = item.startDate.split(' ')[0]
                }
                if (item.endDate) {
                  item.endDate = item.endDate.split(' ')[0]
                }
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
