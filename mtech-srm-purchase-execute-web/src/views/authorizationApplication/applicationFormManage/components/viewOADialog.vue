<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <mt-data-grid
      class="price-dialog"
      :data-source="sampleData"
      :column-data="sampleColumns"
      ref="dataGrid"
      locale="zh"
    ></mt-data-grid>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      sampleColumns: [
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('OA申请单运号')
        },
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('OA当前审批节点')
        },
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('审批人')
        },
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('OA审批说明')
        },
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('邮箱')
        },
        {
          width: '150',
          field: 'itemCode',
          headerText: this.$t('电话')
        }
      ],
      buttons: [
        // {
        //   click: this.cancel,
        //   buttonModel: { content: this.$t("取消") },
        // },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      sampleData: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    this.$refs['dialog'].ejsRef.show()
    this.getPrice()
  },
  methods: {
    getPrice() {
      this.$API.purchaseRequest
        .getPriceRecord({
          skuId: this.modalData.row.skuId,
          categoryId: this.modalData.row.categoryId,
          siteId: this.modalData.row.siteId,
          itemId: this.modalData.row.itemId,
          tenantId: this.userInfo?.tenantId
        })
        .then((res) => {
          console.log(res)
          this.sampleData = res.data
        })
    },
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
