//现场评审得分设置Tab
import { i18n } from '@/main.js'
import cellUpload from '../components/cellUpload' // 单元格上传
import cellFileView from '../components/cellFileView' // 单元格附件查看
import Vue from 'vue'
import dayjs from 'dayjs'

const statusList = [
  { value: '0', text: i18n.t('新增'), cssClass: 'col-active' },
  { value: '1', text: i18n.t('审批中'), cssClass: 'col-active' },
  { value: '2', text: i18n.t('审批通过'), cssClass: 'col-active' },
  { value: '3', text: i18n.t('驳回'), cssClass: 'col-inactive' }
]

const bizTypeList = [
  { value: '0', text: i18n.t('领料'), cssClass: '' },
  { value: '1', text: i18n.t('退料'), cssClass: '' },
  { value: '2', text: i18n.t('领退料'), cssClass: '' },
  { value: '3', text: i18n.t('包材回收'), cssClass: '' }
]

const columnData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'serialNumber', // 前端定义
      headerText: i18n.t('序号'),
      allowEditing: false
    },
    {
      field: 'authCode',
      headerText: i18n.t('授权书号'),
      allowEditing: false
    },
    {
      field: 'authorizeCertStatus',
      headerText: i18n.t('状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: statusList
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span :class="data.authorizeCertStatus !== '3' ? 'col-active' : 'col-inactive'">{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < statusList.length; i++) {
                  const item = statusList[i]
                  if (item.value === this.data.authorizeCertStatus) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'createDate',
      headerText: i18n.t('创建日期'),
      allowEditing: false
    },
    // {
    //   field: 'OANode',
    //   headerText: i18n.t('OA当前审批节点'),
    //   allowEditing: false,
    //   template: () => {
    //     return {
    //       template: Vue.component('headers', {
    //         template: `
    //             <span style="cursor: pointer;color: #00469c;" @click="viewOA">{{ $t('查看') }}</span>
    //           `,
    //         methods: {
    //           viewOA() {
    //             // this.$bus.$emit('applicationFormSupViewOA', this.data)
    //             if (this.data.oaUrl) {
    //               window.open(this.data.oaUrl)
    //             }
    //           }
    //         }
    //       })
    //     }
    //   },
    //   editTemplate: () => {
    //     return {
    //       template: Vue.component('headers', {
    //         template: `
    //             <span style="cursor: pointer;color: #00469c;" @click="viewOA">{{ $t('查看') }}</span>
    //           `,
    //         methods: {
    //           viewOA() {
    //             // this.$bus.$emit('applicationFormSupViewOA', this.data)
    //             if (this.data.oaUrl) {
    //               window.open(this.data.oaUrl)
    //             }
    //           }
    //         }
    //       })
    //     }
    //   }
    // },
    {
      field: 'printCnt',
      headerText: i18n.t('打印次数'),
      allowEditing: false
    },
    {
      field: 'userName',
      headerText: i18n.t('姓名'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.authorizeCertStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'idcardNo',
      headerText: i18n.t('身份证信息'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.authorizeCertStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'tel',
      headerText: i18n.t('电话'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="!(data.printCnt == 0 && data.authorizeCertStatus === '0')" />
              `
          })
        }
      }
    },
    {
      field: 'startDate',
      headerText: i18n.t('授权起始日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-date-picker
                  :id="data.column.field"
                  v-model="data[data.column.field]"
                  :open-on-focus="true"
                  :allow-edit="false"
                  :disabled="!(data.printCnt == 0 && data.authorizeCertStatus === '0')"
                  :placeholder="$t('请选择授权起始日期')"
                ></mt-date-picker>
              `
          })
        }
      }
    },
    {
      field: 'endDate',
      headerText: i18n.t('授权截止日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
            <mt-date-picker
              :id="data.column.field"
              v-model="data[data.column.field]"
              :open-on-focus="true"
              :allow-edit="false"
              :disabled="!(data.printCnt == 0 && data.authorizeCertStatus === '0')"
              :placeholder="$t('请选择授权截止日期')"
            ></mt-date-picker>
              `
          })
        }
      }
    },
    {
      field: 'bizType',
      headerText: i18n.t('业务类型'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: bizTypeList
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ getStatusText() }}</span>
              `,
            methods: {
              getStatusText() {
                for (let i = 0; i < bizTypeList.length; i++) {
                  const item = bizTypeList[i]
                  if (item.value === this.data.bizType) {
                    return item.text
                  }
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'companyName',
      headerText: i18n.t('授权公司'),
      allowEditing: false
    },
    {
      field: 'attachFilePath',
      headerText: i18n.t('附件信息'),
      // allowEditing: false,
      template: function () {
        return {
          template: cellFileView
        }
      },
      editTemplate: () => {
        return {
          template: cellUpload
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注'),
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-input
                :id="data.column.field" v-model="data[data.column.field]" :disabled="data.authorizeCertStatus === '2'" />
              `
          })
        }
      }
    }
  ]
  return column
}

export const columnDetailData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      width: '150',
      field: 'addId', // 隐藏的主键，获取到数据源时，需要把id赋值给它
      headerText: i18n.t('addId主键'),
      visible: false,
      isIdentity: true, // 加上这个，新增一行的时候，主键不会变成可输入
      isPrimaryKey: true, // 一定要有主键，不然已有的行会被修改掉。。而且水平滚动条会回到最左侧。但现在看来好像并不会。。。
      allowEditing: false
    },
    // {
    //   field: 'serialNumber', // 前端定义
    //   headerText: i18n.t('序号'),
    //   allowEditing: false
    // },
    {
      field: 'authCode',
      headerText: i18n.t('授权书单号'),
      allowEditing: false
    },
    {
      field: 'userName',
      headerText: i18n.t('姓名'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('姓名') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'idcardNo',
      headerText: i18n.t('身份证信息'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('身份证信息') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'tel',
      headerText: i18n.t('电话'),
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('电话') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'startDate',
      headerText: i18n.t('授权起始日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <mt-date-picker
                  :id="data.column.field"
                  v-model="data[data.column.field]"
                  :open-on-focus="true"
                  :allow-edit="false"
                  :placeholder="$t('请选择授权起始日期')"
                ></mt-date-picker>
              `
          })
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('授权起始日期') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'endDate',
      headerText: i18n.t('授权截止日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
            <mt-date-picker
              :id="data.column.field"
              v-model="data[data.column.field]"
              :open-on-focus="true"
              :allow-edit="false"
              :placeholder="$t('请选择授权截止日期')"
            ></mt-date-picker>
              `
          })
        }
      },
      headerTemplate: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <div class="headers">
                  <span style="color: red">*</span>
                  <span class="e-headertext">{{ $t('授权截止日期') }}</span>
                </div>
              `
          })
        }
      }
    },
    {
      field: 'remark',
      headerText: i18n.t('备注')
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      gridId: '716eba2c-d185-49af-afc6-81b8cd9915ec',
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          [
            { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
            // { id: 'Save', icon: 'icon_solid_Save', title: i18n.t('保存') },
            { id: 'CancelEdit', icon: 'icon_solid_Save', title: i18n.t('取消编辑') },
            { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
            { id: 'Print', icon: 'icon_list_print', title: i18n.t('打印') }
          ],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      grid: {
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
          showConfirmDialog: false,
          showDeleteConfirmDialog: false,
          newRowPosition: 'Top'
        },
        columnData: columnData(that),
        frozenColumns: 4,
        asyncConfig: {
          url: '/srm-purchase-execute/tenant/deliver/auth/supAuth/supplier/query/page',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
                if (item.startDate) {
                  item.startDate = item.startDate.split(' ')[0]
                }
                if (item.endDate) {
                  item.endDate = item.endDate.split(' ')[0]
                }
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
