<template>
  <div class="detail-top-info">
    <!-- 顶部信息 -->
    <div class="header-box">
      <div class="middle-blank" />
      <span
        class="header-box-btn"
        v-waves
        type="info"
        @click="$router.push('/purchase-execute/sup-application-form')"
        >{{ $t('返回') }}</span
      >
      <span class="header-box-btn" v-waves type="primary" @click="clickMtbuttonSave">{{
        $t('保存')
      }}</span>
      <span class="header-box-btn" v-waves type="primary" @click="clickMtbuttonPrint">{{
        $t('打印')
      }}</span>
    </div>
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="formObject" :rules="rules">
        <mt-form-item prop="companyId" :label="$t('授权公司')">
          <mt-select
            ref="companyRef1"
            :popup-width="350"
            v-model="formObject.companyId"
            :data-source="companyOptions"
            :show-clear-button="false"
            :fields="{ text: 'labelShow', value: 'orgCode' }"
            :allow-filtering="true"
            :filtering="getCompanyOptions"
            :placeholder="$t('请选择')"
            @change="handleCompanyChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="bizType" :label="$t('业务类型')">
          <mt-select
            v-model="formObject.bizType"
            :data-source="[
              { value: '0', text: $t('领料'), cssClass: '' },
              { value: '1', text: $t('退料'), cssClass: '' },
              { value: '2', text: $t('领退料'), cssClass: '' },
              { value: '3', text: $t('包材回收'), cssClass: '' }
            ]"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择业务类型')"
          />
        </mt-form-item>
      </mt-form>
    </div>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>

<script>
import { columnDetailData } from './config'
import { utils } from '@mtech-common/utils'
import dayjs from 'dayjs'
export default {
  data() {
    return {
      formObject: {},
      pageConfig: [
        {
          toolbar: {
            useBaseConfig: false,
            tools: [
              [
                { id: 'Add', icon: 'icon_solid_Createorder', title: this.$t('新增') },
                { id: 'Delete', icon: 'icon_solid_Delete', title: this.$t('删除') }
              ],
              []
            ]
          },
          useToolTemplate: false,
          grid: {
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true,
              mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
              showConfirmDialog: false,
              showDeleteConfirmDialog: false,
              newRowPosition: 'Top'
            },
            columnData: columnDetailData(),
            lineIndex: 1,
            allowPaging: false,
            showSelected: false,
            asyncConfig: {
              serializeList: (list) => {
                if (list.length > 0) {
                  let serialNumber = 1
                  list.forEach((item) => {
                    // 添加序号
                    item.serialNumber = serialNumber++
                  })
                }
                return list
              }
            }
          }
        }
      ],
      companyOptions: [], //业务公司

      rules: {
        // 公司
        companyId: [{ required: true, message: this.$t('请选择授权公司'), trigger: 'blur' }],
        // 业务类型
        bizType: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }]
      },
      headerId: '',
      isFlag: false
    }
  },
  mounted() {
    this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
    this.getCompanyOptions({ text: '' })
  },
  methods: {
    //业务公司改变
    handleCompanyChange(e) {
      const { itemData } = e
      this.formObject.companyCode = itemData.orgCode
      this.formObject.companyName = itemData.orgName
    },
    //查询业务公司下拉数据
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          const companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    clickMtbuttonSave() {
      if (this.isFlag) {
        return false
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.endEdit()
          let currentRecords = this.$refs.templateRef
            .getCurrentUsefulRef()
            .gridRef.ejsRef.getCurrentViewRecords()
          if (!currentRecords.length) {
            this.$toast({
              content: this.$t(`请先添加数据`),
              type: 'warning'
            })
            return
          }
          const authInsertDtoList = []
          for (let i = 0; i < currentRecords.length; i++) {
            const item = currentRecords[i]
            if (!item.userName) {
              this.$toast({
                content: this.$t(`第{index}行请输入姓名`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.idcardNo) {
              this.$toast({
                content: this.$t(`第{index}行请输入身份证信息`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            const reg =
              /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[1-2]\d|3[0-1])\d{3}(?:\d|X)$/

            if (!reg.test(item.idcardNo)) {
              this.$toast({
                content: this.$t(`第{index}行身份证信息输入有误`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.tel) {
              this.$toast({
                content: this.$t(`第{index}行请输入电话`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            const regTel = /^1[3-9]\d{9}$/

            if (!regTel.test(item.tel)) {
              this.$toast({
                content: this.$t(`第{index}行电话输入有误`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.startDate) {
              this.$toast({
                content: this.$t(`第{index}行请选择授权起始日期`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            const today = dayjs(new Date()).format('YYYY-MM-DD')
            const startDate = dayjs(item.startDate).format('YYYY-MM-DD')
            if (new Date(startDate).getTime() < new Date(today).getTime()) {
              this.$toast({
                content: this.$t(`第{index}行授权起始日期不可早于当前时间`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.endDate) {
              this.$toast({
                content: this.$t(`第{index}行请选择授权截止日期`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            const endDate = dayjs(item.endDate).format('YYYY-MM-DD')
            if (new Date(endDate).getTime() < new Date(startDate).getTime()) {
              this.$toast({
                content: this.$t(`第{index}行授权截止日期不可早于授权起始日期`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            authInsertDtoList.push({
              // headerId: this.headerId,
              userName: item.userName,
              idcardNo: item.idcardNo,
              tel: item.tel,
              startDate,
              endDate,
              remark: item.remark
            })
          }
          const params = {
            id: this.headerId,
            companyCode: this.formObject.companyCode,
            companyName: this.formObject.companyName,
            bizType: this.formObject.bizType,
            authInsertDtoList
          }
          this.isFlag = !this.isFlag
          this.$API.authorizationApplication
            .saveAuthPage(params)
            .then((res) => {
              const { code, data } = res
              if (code === 200) {
                this.headerId = data
                this.$toast({
                  content: this.$t(`保存成功`),
                  type: 'success'
                })
              }
              this.isFlag = !this.isFlag
              this.$router.go(-1)
            })
            .catch(() => {
              this.isFlag = !this.isFlag
            })
        }
      })
    },
    clickMtbuttonPrint() {
      console.log('打印')
      if (!this.headerId) {
        this.$toast({
          content: this.$t(`请先保存数据`),
          type: 'warning'
        })
        return
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('打印后不允许再修改，确认打印？')
        },
        success: () => {
          this.$loading()
          this.$API.authorizationApplication
            .printAuthPage(this.headerId)
            .then((res) => {
              if (res?.data?.type === 'application/json') {
                const reader = new FileReader()
                reader.readAsText(res?.data, 'utf-8')
                reader.onload = () => {
                  console.log('======', reader)
                  const readerRes = reader.result
                  const resObj = JSON.parse(readerRes)
                  this.$toast({
                    content: resObj.msg,
                    type: 'error'
                  })
                }

                return
              }
              const content = res.data
              this.pdfUrl = window.URL.createObjectURL(
                new Blob([content], { type: 'application/pdf' })
              )
              // window.open(this.pdfUrl);
              let date = new Date().getTime()
              let ifr = document.createElement('iframe')
              ifr.style.frameborder = 'no'
              ifr.style.display = 'none'
              ifr.style.pageBreakBefore = 'always'
              ifr.setAttribute('id', 'printPdf' + date)
              ifr.setAttribute('name', 'printPdf' + date)
              ifr.src = this.pdfUrl
              document.body.appendChild(ifr)
              this.doPrint('printPdf' + date)
              window.URL.revokeObjectURL(ifr.src)
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    },
    handleClickToolBar(args) {
      const { toolbar } = args
      if (toolbar.id === 'Add') {
        const item = {
          authCode: null,
          endDate: null,
          idcardNo: null,
          remark: null,
          serialNumber: null,
          startDate: null,
          tel: null,
          userName: null,
          addId: 'add' + Math.random().toString(36).substr(3, 8)
        }
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord(item)
        setTimeout(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        }, 300)
      } else if (toolbar.id === 'Delete') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.top-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  nav {
    flex-shrink: 0;
    //按钮
    .detail-info {
      padding: 20px 20px 3px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .name-wrap {
        span {
          margin-left: 30px;
        }
        span:nth-of-type(1) {
          margin-left: 0;
        }
        // 盘点单号
        .codes {
          font-size: 20px;
          font-family: DINAlternate;
          font-weight: bold;
          color: rgba(41, 41, 41, 1);
        }
        //状态
        .tags {
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: 500;
          padding: 4px;
          border-radius: 2px;
        }
        .tags-0 {
          color: rgb(155, 170, 193);
          background: rgba(155, 170, 193, 0.1);
        }
        .tags-1 {
          color: rgba(237, 161, 51, 1);
          background: rgba(237, 161, 51, 0.1);
        }
        .tags-2 {
          color: rgb(237, 86, 51);
          background: rgba(237, 86, 51, 0.1);
        }
      }
      .btns-wrap {
        /deep/ .mt-button {
          margin-right: 0;
          button {
            // background: transparent;
            // //border: 1px solid rgba(0, 70, 156, 0.1);
            // border-radius: 4px;
            // box-shadow: unset;
            // padding: 6px 12px 4px;
            // font-size: 14px;
            // font-family: PingFangSC;
            // font-weight: 500;
            // color: rgba(0, 70, 156, 1);
            display: block;
            padding: 6px 16px;
            border-radius: 4px;
            font-weight: bold;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            border: 1px solid #4a556b;
            background: #fff;
            color: #4a556b;
          }
        }
        .pack-up {
          display: inline-block;
          position: relative;
        }
      }
    }
    //表单
    .formInput {
      width: 100%;
      padding: 0 20px 0;
      box-sizing: border-box;
      .mt-form {
        width: 100%;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        flex-wrap: wrap;
        .mt-form-item {
          width: 23%;
        }
        .mt-form-item:nth-of-type(1),
        .mt-form-item:nth-of-type(4) {
          margin-left: 0;
        }
        .mt-form-item:nth-of-type(5) {
          width: 100%;
        }
      }
    }
  }
  .mt-template-page {
    flex: 1;
    height: 100%;
  }
}
</style>
