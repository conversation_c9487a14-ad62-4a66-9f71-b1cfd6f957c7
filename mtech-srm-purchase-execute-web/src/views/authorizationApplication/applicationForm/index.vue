<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @actionBegin="actionBegin"
      @actionComplete="actionComplete"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="authCode" :label="$t('授权书号')" label-style="top">
              <mt-input
                v-model="searchFormModel.authCode"
                :show-clear-button="true"
                :placeholder="$t('请输入授权书号')"
              />
            </mt-form-item>
            <mt-form-item prop="idcardNo" :label="$t('身份证号')" label-style="top">
              <mt-input
                v-model="searchFormModel.idcardNo"
                :show-clear-button="true"
                :placeholder="$t('请输入身份证号')"
              />
            </mt-form-item>
            <mt-form-item prop="userName" :label="$t('姓名')" label-style="top">
              <mt-input
                v-model="searchFormModel.userName"
                :show-clear-button="true"
                :placeholder="$t('请输入姓名')"
              />
            </mt-form-item>
            <mt-form-item prop="createDate" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createDate"
                @change="(e) => handleDateTimeChange(e, 'createDate')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="bizType" :label="$t('业务类型')" label-style="top">
              <mt-select
                v-model="searchFormModel.bizType"
                css-class="rule-element"
                :data-source="[
                  { value: '0', text: $t('领料'), cssClass: '' },
                  { value: '1', text: $t('退料'), cssClass: '' },
                  { value: '2', text: $t('领退料'), cssClass: '' },
                  { value: '3', text: $t('包材回收'), cssClass: '' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择业务类型')"
              />
            </mt-form-item>
            <mt-form-item prop="authorizeCertStatus" :label="$t('状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.authorizeCertStatus"
                css-class="rule-element"
                :data-source="[
                  { value: '0', text: $t('新增'), cssClass: 'col-active' },
                  { value: '1', text: $t('审批中'), cssClass: 'col-active' },
                  { value: '2', text: $t('审批通过'), cssClass: 'col-active' },
                  { value: '3', text: $t('驳回'), cssClass: 'col-inactive' }
                ]"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('截止日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.endDate"
                @change="(e) => handleDateTimeChange(e, 'endDate')"
                :placeholder="$t('请选择截止日期')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('授权公司')" label-style="top">
              <mt-select
                ref="companyRef1"
                :popup-width="350"
                v-model="searchFormModel.companyCode"
                :data-source="companyOptions"
                :show-clear-button="true"
                :fields="{ text: 'labelShow', value: 'orgCode' }"
                :allow-filtering="true"
                :filtering="getCompanyOptions"
                :placeholder="$t('请选择授权公司')"
              ></mt-select>
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig } from './config'
import { utils } from '@mtech-common/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      pageConfig: [],
      indicatorTypeList: [], // 自动发计算指标类型列表
      categoryList: [], // 类别列表
      templateIndexMap: [], // 指标名称列表
      companyOptions: [] //业务公司
    }
  },
  computed: {},
  created() {
    this.pageConfig = pageConfig(this)
  },
  mounted() {
    this.getCompanyOptions = utils.debounce(this.getCompanyOptions, 300)
    this.getCompanyOptions({ text: '' })
    this.$bus.$on('applicationFormSupViewOA', (e) => {
      this.$dialog({
        modal: () => import('./components/viewOADialog.vue'),
        data: {
          title: this.$t('查看OA审批节点'),
          row: e
        },
        success: () => {
          // this.confirmSuccess();
        }
      })
    })
  },
  methods: {
    //查询业务公司下拉数据
    getCompanyOptions(e = { text: '' }) {
      this.$API.masterData
        .getCompanyBySup({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: [],
          fuzzyParam: e.text
        })
        .then((res) => {
          const companyOptions = res.data
          companyOptions.forEach((item) => {
            item.labelShow = item.orgCode + ' - ' + item.orgName
          })
          this.companyOptions = companyOptions

          this.$nextTick(() => {
            if (e.updateData && typeof e.updateData == 'function') {
              e.updateData(companyOptions)
            }
          })
        })
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'From'] = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        this.searchFormModel[field + 'To'] = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.searchFormModel[field + 'From'] = null
        this.searchFormModel[field + 'To'] = null
      }
    },
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        // this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
        this.$router.push({
          path: `add-application-form`,
          query: {
            timeStamp: new Date().getTime()
          }
          // key: utils.randomString(),
          // query: { type: "add" },
        })
      } else if (toolbar.id === 'Save') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleSave(selectedRecords)
      } else if (toolbar.id === 'Submit') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅且选择一行数据进行提交'), type: 'warning' })
          return
        }
        this.handleSubmit(selectedRecords)
      } else if (toolbar.id === 'Print') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅选择一行数据进行打印'), type: 'warning' })
          return
        }
        this.handlePrint(selectedRecords)
      } else if (toolbar.id === 'CancelEdit') {
        this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    handleSubmit(records) {
      this.$loading()
      this.$API.authorizationApplication
        .submitAuthPage({ id: records[0]['headerId'] })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('提交成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
          this.$hloading()
        })
        .catch(() => {
          this.$hloading()
        })
        .finally(() => {
          this.$hloading()
        })
    },
    handlePrint(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('打印后不允许再修改，确认打印？')
        },
        success: () => {
          this.$loading()
          this.$API.authorizationApplication
            .printAuthPage(selectedRecords[0]['headerId'])
            .then((res) => {
              if (res?.data?.type === 'application/json') {
                const reader = new FileReader()
                reader.readAsText(res?.data, 'utf-8')
                reader.onload = () => {
                  console.log('======', reader)
                  const readerRes = reader.result
                  const resObj = JSON.parse(readerRes)
                  this.$toast({
                    content: resObj.msg,
                    type: 'error'
                  })
                }

                return
              }
              const content = res.data
              this.pdfUrl = window.URL.createObjectURL(
                new Blob([content], { type: 'text/html;charset=utf-8' })
              )
              // window.open(this.pdfUrl);
              let date = new Date().getTime()
              let ifr = document.createElement('iframe')
              ifr.style.frameborder = 'no'
              ifr.style.display = 'none'
              ifr.style.pageBreakBefore = 'always'
              ifr.setAttribute('id', 'printPdf' + date)
              ifr.setAttribute('name', 'printPdf' + date)
              ifr.src = this.pdfUrl
              document.body.appendChild(ifr)
              this.doPrint('printPdf' + date)
              window.URL.revokeObjectURL(ifr.src)
              // this.$refs.templateRef.refreshCurrentGridData()
              this.$hloading()
            })
            .catch(() => {
              this.$hloading()
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
        this.$refs.templateRef.refreshCurrentGridData()
      }, 100)
    },
    handleClickCellTitle() {},
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    },
    actionBegin() {},
    actionComplete(args) {
      const { requestType, action, data, rowIndex, index } = args
      // if (requestType === 'save') {
      //   !args.cancel && this.handleSave(data, rowIndex)
      // }
      if (requestType === 'save' && action == 'edit') {
        // 完成编辑行
        this.handleSave({ data, rowIndex })
      } else if (requestType === 'save' && action == 'add') {
        // 完成新增行
        this.handleSave({ data, rowIndex: index })
      }
    },
    // 保存
    handleSave(args) {
      const { data, rowIndex } = args
      // const params = []
      // selectedRecords.forEach((i) => {
      //   let updateEndDate = ''
      //   if (i.updateEndDate) {
      //     updateEndDate = dayjs(i.updateEndDate).format('YYYY-MM-DD')
      //   } else {
      //     updateEndDate = ''
      //   }
      //   const item = { ...i, updateEndDate, lineId: i.id }
      //   params.push(item)
      // })
      this.$API.authorizationApplication
        .updateAuthPage(data)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('保存成功'), type: 'success' })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .catch(() => {
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.selectRow(rowIndex)
          this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.startEdit()
        })
    }
  },
  beforeDestroy() {
    this.$bus.$off('applicationFormSupViewOA')
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
