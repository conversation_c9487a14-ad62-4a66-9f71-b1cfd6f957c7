<template>
  <div class="cell-upload" :id="'cell-upload-' + data.index">
    <!-- attachFilePath:{{ data.attachFilePath }} -->
    <mt-input id="attachFilePath" style="display: none" :value="data.attachFilePath"></mt-input>
    <div @click="showFileBaseInfo" class="cell-operable-title">
      {{ fileNums }}
    </div>

    <!-- 需求附件弹窗 -->
    <uploader-dialog @change="fileChange" @confirm="setFile" ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'

// 草稿或审批拒绝状态才能再次上传
export default {
  components: {
    UploaderDialog: () => import('@/components/Upload/uploaderDialog')
  },
  data() {
    return {
      data: {
        // attachFilePath: {},
      },
      headerStatus: null, // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
      uploadFileList: [], // 上传的附件(初始值赋值之前上传过的)
      fileNums: null // 显示的文案 附件数量 / “暂无附件” / “点击上传”
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
  },
  watch: {
    'data.attachFilePath': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.fileNums = `${this.$t('文件数')}${JSON.parse(newVal).length}-${this.$t('上传/查看')}`
        } else {
          this.fileNums = this.$t('点击上传')
        }
        if (this.data.authorizeCertStatus !== '0') {
          this.fileNums = this.$t('查看')
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.data.attachFilePath && this.data.attachFilePath.length) {
        this.uploadFileList = JSON.parse(this.data.attachFilePath)
      }
    })
  },
  methods: {
    showFileBaseInfo() {
      const dialogParams = {
        fileData: cloneDeep(this.uploadFileList),
        isView: this.data.authorizeCertStatus === '0' ? false : true, //是否可上传
        required: false, // 是否必须
        uploadText: this.$t('请点击此处上传文件'),
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 行附件弹窗内文件变动
    fileChange(data) {
      console.log('fileChange', data)
      this.uploadFileList = data
    },
    // 点击行附件上传的确认按钮
    setFile() {
      // const params = {
      //   id: this.data.id,
      //   headerId: this.data.headerId,
      //   attachFilePath: JSON.stringify(this.uploadFileList)
      // }
      // this.$API.authorizationApplication.saveFile(params).then((res) => {
      //   const { code } = res
      //   if (code === 200) {
      //     this.$toast({
      //       content: this.$t(`上传成功`),
      //       type: 'success'
      //     })
      //     // this.$parent.$emit("confirm-function", this.data.attachFilePath );
      //   }
      // })
      this.data.attachFilePath = JSON.stringify(this.uploadFileList)
    }
  }
}
</script>

<style scoped>
.cell-operable-title {
  display: inline-block;
  padding: 10px;
}
</style>
