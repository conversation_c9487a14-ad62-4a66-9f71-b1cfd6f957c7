//现场评审得分设置Tab
import { i18n } from '@/main.js'
import Vue from 'vue'
import dayjs from 'dayjs'

export const statusList = [
  { value: 0, text: i18n.t('新增'), cssClass: 'col-active' },
  { value: 1, text: i18n.t('审批中'), cssClass: 'col-active' },
  { value: 2, text: i18n.t('审批通过'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('审批拒绝'), cssClass: 'col-inactive' }
]

// const newApplyFlag = [
//   { value: '1', text: i18n.t('新办证') },
//   { value: '2', text: i18n.t('换证') }
// ]

const columnData = () => {
  const column = [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'serialNumber', // 前端定义
      headerText: i18n.t('序号'),
      allowEditing: false
    },
    {
      field: 'inOutNum',
      headerText: i18n.t('临时出入证号'),
      allowEditing: false,
      cellTools: []
    },
    {
      field: 'supplierCode',
      headerText: i18n.t('供应商'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ data.supplierCode }} - {{ data.supplierName }}</span>
              `
          })
        }
      }
    },
    {
      field: 'status',
      headerText: i18n.t('状态'),
      allowEditing: false,
      valueConverter: {
        type: 'map',
        map: statusList
      }
    },
    {
      field: 'oaUrl',
      headerText: i18n.t('OA当前审批节点'),
      allowEditing: false,
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span v-if="data.oaUrl" style="cursor: pointer;color: #00469c;" @click="viewOA">{{$t('查看')}}</span>
                <span v-else style="cursor: not-allowed;color: #888;">{{$t('查看')}}</span>
              `,
            methods: {
              viewOA() {
                if (this.data.oaUrl) {
                  window.open(this.data.oaUrl)
                }
              }
            }
          })
        }
      }
    },
    {
      field: 'personnelTypeDesc',
      headerText: i18n.t('人员分类')
    },
    {
      field: 'receptionistName',
      headerText: i18n.t('接待人姓名')
    },
    {
      field: 'receptionistPhone',
      headerText: i18n.t('接待人电话')
    },
    {
      field: 'managerName',
      headerText: i18n.t('接待人直属领导')
    },
    {
      field: 'accessValidityStartDate',
      headerText: i18n.t('门禁有效开始时间'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      }
    },
    {
      field: 'accessValidityEndDate',
      headerText: i18n.t('门禁有效截止时间'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data[this.data.column.field]) {
                  return dayjs(this.data[this.data.column.field]).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      }
    },
    {
      field: 'openingRegion',
      headerText: i18n.t('开通区域'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `
                <span>{{ data.openingRegion }}</span>
              `
          })
        }
      }
    },
    {
      field: 'onSiteRepresentativeName',
      headerText: i18n.t('驻场代表姓名')
    },
    {
      field: 'onSiteRepresentativePhone',
      headerText: i18n.t('驻场代表手机号')
    },
    {
      field: 'onSiteRepresentativeCompany',
      headerText: i18n.t('驻场代表所属公司')
    },
    {
      field: 'visitorCount',
      headerText: i18n.t('来访总人数'),
      allowEditing: false
    },
    {
      field: 'applicationReason',
      headerText: i18n.t('申请理由')
    },
    {
      field: 'visitorCompany',
      headerText: i18n.t('来访人所属公司')
    },
    {
      field: 'visitorName',
      headerText: i18n.t('来访人姓名')
    },
    {
      field: 'visitorPhone',
      headerText: i18n.t('来访人手机号')
    },
    {
      field: 'visitorLicensePlate',
      headerText: i18n.t('来访人车牌号')
    },
    {
      field: 'visitUserIdcardNo',
      headerText: i18n.t('来访人身份证号')
    },
    {
      field: 'visitorNum',
      headerText: i18n.t('来访证编号')
    },
    {
      field: 'certComplianceDesc',
      headerText: i18n.t('是否符合办证条件')
      // template: () => {
      //   return {
      //     template: Vue.component('headers', {
      //       template: `
      //           <span>{{ getStatusText() }}</span>
      //         `,
      //       methods: {
      //         getStatusText() {
      //           const certComplianceList = [
      //             { value: 1, text: '' },
      //             { value: 0, text: i18n.t('是') }
      //           ]
      //           for (let i = 0; i < certComplianceList.length; i++) {
      //             const item = certComplianceList[i]
      //             if (item.value === this.data[this.data.column.field]) {
      //               return item.text
      //             }
      //           }
      //         }
      //       }
      //     })
      //   }
      // }
    },
    // {
    //   field: 'newApplyFlag',
    //   headerText: i18n.t('资料是否合格'),
    //   template: () => {
    //     return {
    //       template: Vue.component('headers', {
    //         template: `
    //             <span>{{ getStatusText() }}</span>
    //           `,
    //         methods: {
    //           getStatusText() {
    //             for (let i = 0; i < newApplyFlag.length; i++) {
    //               const item = newApplyFlag[i]
    //               if (item.value === this.data[this.data.column.field]) {
    //                 return item.text
    //               }
    //             }
    //           }
    //         }
    //       })
    //     }
    //   }
    // },
    // {
    //   field: 'status',
    //   headerText: i18n.t('同步状态'),
    //   allowEditing: false,
    //   valueConverter: {
    //     type: 'map',
    //     map: statusList
    //   }
    // },
    // {
    //   field: 'syncReason',
    //   headerText: i18n.t('同步失败原因')
    // },
    {
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      field: 'createTime',
      headerText: i18n.t('创建时间')
    },
    {
      field: 'updateUserName',
      headerText: i18n.t('更新人')
    },
    {
      field: 'updateTime',
      headerText: i18n.t('更新时间')
    }
  ]
  return column
}

export const pageConfig = (that) => {
  const config = [
    {
      isUseCustomSearch: true,
      isCustomSearchRules: true,
      isUseCustomEditor: true,
      toolbar: {
        useBaseConfig: false,
        tools: [
          that.isSup
            ? [
                { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
                { id: 'Edit', icon: 'icon_solid_Createorder', title: i18n.t('编辑') },
                { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
                { id: 'Submit', icon: 'icon_solid_Submit', title: i18n.t('提交') },
                { id: 'Export1', icon: 'icon_solid_export', title: i18n.t('导出') }
              ]
            : [{ id: 'Export1', icon: 'icon_solid_export', title: i18n.t('导出') }],
          ['Filter', 'Refresh', 'Setting']
        ]
      },
      useToolTemplate: false,
      gridId: that.isSup
        ? '4e3cade6-e486-4407-aad2-6bbd5daddb59'
        : '8fd770ce-32ca-4aa9-b9de-b2dfa596a278',
      grid: {
        columnData: columnData(),
        frozenColumns: 4,
        asyncConfig: {
          url: that.isSup
            ? '/srm-purchase-execute/tenant/lcd/park/apply/supplier/page'
            : '/srm-purchase-execute/tenant/lcd/park/apply/buyer/page',
          serializeList: (list) => {
            if (list.length > 0) {
              let serialNumber = 1
              list.forEach((item) => {
                // 添加序号
                item.serialNumber = serialNumber++
              })
            }
            return list
          }
        }
      }
    }
  ]
  return config
}
