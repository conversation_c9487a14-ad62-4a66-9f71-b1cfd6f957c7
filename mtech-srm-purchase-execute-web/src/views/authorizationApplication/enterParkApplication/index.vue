<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="inOutNum" :label="$t('临时出入证号')" label-style="top">
              <mt-input
                v-model="searchFormModel.inOutNum"
                :show-clear-button="true"
                :placeholder="$t('请输入临时出入证号')"
              />
            </mt-form-item>
            <mt-form-item prop="status" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.status"
                css-class="rule-element"
                :data-source="statusList"
                :fields="{ text: 'text', value: 'value' }"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item
              prop="onSiteRepresentativeName"
              :label="$t('驻场代表姓名')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.onSiteRepresentativeName"
                :show-clear-button="true"
                :placeholder="$t('请输入驻场代表姓名')"
              />
            </mt-form-item>
            <mt-form-item
              prop="onSiteRepresentativePhone"
              :label="$t('驻场代表手机号')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.onSiteRepresentativePhone"
                :show-clear-button="true"
                :placeholder="$t('请输入驻场代表手机号')"
              />
            </mt-form-item>
            <mt-form-item
              prop="onSiteRepresentativeCompany"
              :label="$t('驻场代表所属公司')"
              label-style="top"
            >
              <mt-input
                v-model="searchFormModel.onSiteRepresentativeCompany"
                :show-clear-button="true"
                :placeholder="$t('请输入驻场代表所属公司')"
              />
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="userNameLeader" :label="$t('接待人直属领导')" label-style="top">
              <mt-input
                v-model="searchFormModel.userNameLeader"
                :show-clear-button="true"
                :placeholder="$t('请输入接待人直属领导')"
              />
            </mt-form-item>
            <mt-form-item prop="receptionistName" :label="$t('接待人姓名')" label-style="top">
              <mt-input
                v-model="searchFormModel.receptionistName"
                :show-clear-button="true"
                :placeholder="$t('请输入接待人姓名')"
              />
            </mt-form-item>
            <mt-form-item prop="receptionistPhone" :label="$t('接待人电话')" label-style="top">
              <mt-input
                v-model="searchFormModel.receptionistPhone"
                :show-clear-button="true"
                :placeholder="$t('请输入接待人电话')"
              />
            </mt-form-item>
            <mt-form-item
              prop="accessValidityStartDate"
              :label="$t('门禁有效开始时间')"
              label-style="top"
            >
              <mt-date-range-picker
                v-model="searchFormModel.accessValidityStartDate"
                @change="(e) => handleDateTimeChange(e, 'accessValidityStartDate')"
                :placeholder="$t('请选择门禁有效开始时间')"
              />
            </mt-form-item>
            <mt-form-item
              prop="accessValidityEndDate"
              :label="$t('门禁有效截止时间')"
              label-style="top"
            >
              <mt-date-range-picker
                v-model="searchFormModel.accessValidityEndDate"
                @change="(e) => handleDateTimeChange(e, 'accessValidityEndDate')"
                :placeholder="$t('请选择门禁有效截止时间')"
              />
            </mt-form-item>
            <mt-form-item prop="applicationReason" :label="$t('申请理由')" label-style="top">
              <mt-input
                v-model="searchFormModel.applicationReason"
                :show-clear-button="true"
                :placeholder="$t('请输入申请理由')"
              />
            </mt-form-item>
            <mt-form-item prop="visitorCompany" :label="$t('来访人所属公司')" label-style="top">
              <mt-input
                v-model="searchFormModel.visitorCompany"
                :show-clear-button="true"
                :placeholder="$t('请输入来访人所属公司')"
              />
            </mt-form-item>
            <mt-form-item prop="visitorName" :label="$t('来访人姓名')" label-style="top">
              <mt-input
                v-model="searchFormModel.visitorName"
                :show-clear-button="true"
                :placeholder="$t('请输入来访人姓名')"
              />
            </mt-form-item>
            <mt-form-item prop="visitorPhone" :label="$t('来访人手机号')" label-style="top">
              <mt-input
                v-model="searchFormModel.visitorPhone"
                :show-clear-button="true"
                :placeholder="$t('请输入来访人手机号')"
              />
            </mt-form-item>
            <mt-form-item prop="visitorLicensePlate" :label="$t('来访人车牌号')" label-style="top">
              <mt-input
                v-model="searchFormModel.visitorLicensePlate"
                :show-clear-button="true"
                :placeholder="$t('请输入来访人车牌号')"
              />
            </mt-form-item>
            <mt-form-item prop="visitUserIdcardNo" :label="$t('来访人身份证号')" label-style="top">
              <mt-input
                v-model="searchFormModel.visitUserIdcardNo"
                :show-clear-button="true"
                :placeholder="$t('请输入来访人身份证号')"
              />
            </mt-form-item>
            <mt-form-item prop="visitorNum" :label="$t('来访证编号')" label-style="top">
              <mt-input
                v-model="searchFormModel.visitorNum"
                :show-clear-button="true"
                :placeholder="$t('请输入来访证编号')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig, statusList } from './config'
import * as UTILS from '@/utils/utils'

export default {
  components: {},
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      statusList,
      searchFormModel: {},
      pageConfig: [],
      isSup: false
    }
  },
  created() {
    if (this.$route.name?.includes('sup-')) {
      this.isSup = true
    }
    this.pageConfig = pageConfig(this)
  },
  mounted() {},
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'S'] = this.getUnix(
          dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        )
        this.searchFormModel[field + 'E'] = this.getUnix(
          dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
        )
      } else {
        this.searchFormModel[field + 'S'] = null
        this.searchFormModel[field + 'E'] = null
      }
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'inOutNum') {
        let type = 'detail'
        if (data['status'] == 0 || data['status'] == 3) {
          type = 'edit'
        }
        this.$router.push({
          path: this.isSup ? `sup-enter-park-detail` : `pur-enter-park-detail`,
          query: {
            type,
            id: data['inOutId'],
            timeStamp: new Date().getTime()
          }
        })
      }
    },
    handleClickToolBar(args) {
      const { toolbar, grid } = args
      const selectedRecords = grid.getSelectedRecords()

      if (toolbar.id === 'Add') {
        this.$router.push({
          path: this.isSup ? `sup-enter-park-detail` : `pur-enter-park-detail`,
          query: {
            type: 'add',
            timeStamp: new Date().getTime()
          }
        })
      } else if (toolbar.id === 'Edit') {
        if (selectedRecords.length !== 1) {
          this.$toast({ content: this.$t('请仅且选择一行数据'), type: 'warning' })
          return
        }
        if (selectedRecords[0]['status'] != 0 && selectedRecords[0]['status'] != 3) {
          this.$toast({ content: this.$t('只有新建、审批拒绝状态的单据可以修改'), type: 'warning' })
          return
        }
        this.$router.push({
          path: this.isSup ? `sup-enter-park-detail` : `pur-enter-park-detail`,
          query: {
            type: 'edit',
            id: selectedRecords[0]['inOutId'],
            timeStamp: new Date().getTime()
          }
        })
      } else if (toolbar.id === 'Delete') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleDelete(selectedRecords)
      } else if (toolbar.id === 'Submit') {
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请先至少选择一行数据'), type: 'warning' })
          return
        }
        this.handleSubmit(selectedRecords)
      } else if (toolbar.id === 'Export1') {
        this.handleExport(selectedRecords)
      }
    },
    handleExport(selectedRecords) {
      let obj = JSON.parse(sessionStorage.getItem(this.pageConfig[0].gridId))?.visibleCols
      const headerMap = {}
      if (obj !== undefined && obj.length) {
        obj?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber' && i.field !== 'status') {
            headerMap[i.field] = i.headerText
          }
          if (i.field === 'status') {
            headerMap['statusDesc'] = i.headerText
          }
        })
      } else {
        this.pageConfig[0].grid.columnData?.forEach((i) => {
          if (i.field !== 'customChecked' && i.field !== 'serialNumber' && i.field !== 'status') {
            headerMap[i.field] = i.headerText
          }
          if (i.field === 'status') {
            headerMap['statusDesc'] = i.headerText
          }
        })
      }
      const params = {
        page: { current: 1, size: 9999 },
        ...this.searchFormModel,
        ids: selectedRecords.map((i) => i.id),
        headerMap
      }
      this.$store.commit('startLoading')
      this.getExportApi()(params)
        .then((res) => {
          const fileName = UTILS.getHeadersFileName(res)
          UTILS.download({ fileName: `${fileName}`, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    getExportApi() {
      if (this.isSup) {
        return this.$API.authorizationApplication.exportParkApplySup
      } else {
        return this.$API.authorizationApplication.exportParkApplyPur
      }
    },
    handleDelete(records) {
      console.log(this.$t('删除选中的'), records)
      const deleteList = []
      for (let i = 0; i < records.length; i++) {
        const item = records[i]
        if (item['status'] != 0 && item['status'] != 3) {
          this.$toast({ content: this.$t('只有新建、审批拒绝状态的单据可以删除'), type: 'warning' })
          return
        }
        deleteList.push(item['id'])
      }
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.authorizationApplication
            .batchDelByIds(deleteList)
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleSubmit(records) {
      if (records.length < 1) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // 只能批量选择新建、审批拒绝状态的单据提交
      const submitIds = []
      for (let i = 0; i < records.length; i++) {
        const item = records[i]
        if (item.status !== 0 && item.status !== 3) {
          this.$toast({ content: this.$t('只能选择新建、审批拒绝状态的单据提交'), type: 'warning' })
          return
        }
        submitIds.push(item.inOutId)
      }
      this.$store.commit('startLoading')
      this.$API.authorizationApplication
        .submitParkApplySup(submitIds)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({
              content: this.$t(`提交成功`),
              type: 'success'
            })
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.full-height {
  height: 100%;
  /deep/ .template-wrap {
    overflow-y: auto;
  }
}
</style>
