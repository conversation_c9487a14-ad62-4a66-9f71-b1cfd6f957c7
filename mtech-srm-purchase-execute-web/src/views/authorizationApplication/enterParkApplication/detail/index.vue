<template>
  <div class="detail-top-info" style="padding-right: 20px">
    <!-- 顶部信息 -->
    <div class="header-box">
      <div class="middle-blank" />
      <span
        v-if="pageType !== 'detail' && isSup"
        class="header-box-btn"
        v-waves
        type="info"
        @click="clickMtbuttonSave"
        >{{ $t('保存') }}</span
      >
      <span
        v-if="
          pageType !== 'detail' && isSup && (formObject.status === 0 || formObject.status === 3)
        "
        class="header-box-btn"
        v-waves
        type="primary"
        @click="clickMtbuttonSubmit"
        >{{ $t('提交') }}</span
      >
      <span class="header-box-btn" v-waves type="info" @click="$router.go(-1)">{{
        $t('返回')
      }}</span>
    </div>
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="formObject" :rules="rules">
        <mt-form-item prop="inOutNum" :label="$t('临时出入证号')">
          <mt-input v-model="formObject.inOutNum" disabled />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-input v-model="formObject.supplierCode" disabled />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')">
          <mt-select
            v-model="formObject.status"
            :data-source="[
              { value: 0, text: $t('新建'), cssClass: 'col-active' },
              { value: 1, text: $t('审批中'), cssClass: 'col-active' },
              { value: 2, text: $t('审批通过'), cssClass: 'col-active' },
              { value: 3, text: $t('审批拒绝'), cssClass: 'col-inactive' }
            ]"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            disabled
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="personnelTypeDesc" :label="$t('人员分类')">
          <mt-input v-model="formObject.personnelTypeDesc" disabled />
        </mt-form-item>
        <mt-form-item prop="receptionistNumber" :label="$t('接待人姓名')">
          <mt-select
            ref="companyRef1"
            :popup-width="350"
            v-model="formObject.receptionistNumber"
            :data-source="employeeOptions"
            :show-clear-button="false"
            :fields="{ text: 'labelShow', value: 'employeeCode' }"
            :allow-filtering="true"
            :filtering="getEmployeeOptions"
            :placeholder="$t('请选择')"
            :filter-bar-placeholder="$t('请输入完整工号查询，如：00196883')"
            @change="handleEmployeeChange"
            :disabled="pageType === 'detail' || !isSup"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="receptionistPhone" :label="$t('接待人电话')">
          <mt-input
            v-model="formObject.receptionistPhone"
            :disabled="pageType === 'detail' || !isSup"
          />
        </mt-form-item>
        <mt-form-item prop="managerNum" :label="$t('接待人直属领导')">
          <mt-select
            ref="companyRef1"
            :popup-width="350"
            v-model="formObject.managerNum"
            :data-source="managerOptions"
            :show-clear-button="false"
            :fields="{ text: 'labelShow', value: 'employeeCode' }"
            :allow-filtering="true"
            :filtering="getManagerOptions"
            :placeholder="$t('请选择')"
            :filter-bar-placeholder="$t('请输入完整工号查询，如：00196883')"
            @change="handleManagerChange"
            :disabled="pageType === 'detail' || !isSup"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="accessValidityStartDate" :label="$t('门禁有效开始日期')" class="">
          <mt-date-picker
            :show-clear-button="true"
            :min="new Date()"
            :allow-edit="false"
            :placeholder="$t('门禁有效开始日期')"
            v-model="formObject.accessValidityStartDate"
            :disabled="pageType === 'detail' || !isSup"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="accessValidityEndDate" :label="$t('门禁有效结束日期')" class="">
          <mt-date-picker
            :show-clear-button="true"
            :min="new Date(formObject.accessValidityStartDate)"
            :allow-edit="false"
            :placeholder="$t('门禁有效开始日期')"
            v-model="formObject.accessValidityEndDate"
            :disabled="pageType === 'detail' || !isSup"
          ></mt-date-picker>
        </mt-form-item>
        <mt-form-item prop="openingRegion" :label="$t('开通区域')">
          <mt-input v-model="formObject.openingRegion" disabled />
        </mt-form-item>
        <mt-form-item prop="onSiteRepresentativeName" :label="$t('驻场代表姓名')">
          <mt-input
            v-model="formObject.onSiteRepresentativeName"
            :disabled="pageType === 'detail' || !isSup"
          />
        </mt-form-item>
        <mt-form-item prop="onSiteRepresentativePhone" :label="$t('驻场代表手机号')">
          <mt-input
            v-model="formObject.onSiteRepresentativePhone"
            :disabled="pageType === 'detail' || !isSup"
          />
        </mt-form-item>
        <mt-form-item prop="onSiteRepresentativeCompany" :label="$t('驻场代表所属公司')">
          <mt-input
            v-model="formObject.onSiteRepresentativeCompany"
            :disabled="pageType === 'detail' || !isSup"
          />
        </mt-form-item>
        <mt-form-item prop="visitorCount" :label="$t('来访总人数')">
          <mt-input
            type="number"
            v-model="formObject.visitorCount"
            :min="1"
            :placeholder="$t('请输入')"
            :show-clear-button="true"
            :disabled="pageType === 'detail' || !isSup"
          />
        </mt-form-item>
        <mt-form-item prop="applicationReason" :label="$t('申请理由')">
          <mt-input
            v-model="formObject.applicationReason"
            :disabled="pageType === 'detail' || !isSup"
          />
        </mt-form-item>
      </mt-form>
    </div>
    <div class="table-area">
      <mt-tabs
        :e-tab="false"
        :data-source="tabList"
        :selected-item="tabIndex"
        @handleSelectTab="(e) => (tabIndex = e)"
      />
      <div>
        <PersonList
          ref="personnelList"
          v-show="tabIndex === 0"
          :page-type="pageType"
          :is-sup="isSup"
        />
        <!-- <pictureUpload ref="pictureUpload" v-show="tabIndex === 1" :page-type="pageType" /> -->
        <AttachmentList
          ref="attachmentList"
          v-show="tabIndex === 1"
          :page-type="pageType"
          :is-sup="isSup"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { RegExpMap } from '@/utils/constant.js'
import { utils } from '@mtech-common/utils'
import AttachmentList from '../components/attachmentList.vue'
import PersonList from '../components/personnelList.vue'
import dayjs from 'dayjs'
export default {
  components: {
    PersonList,
    AttachmentList
  },
  data() {
    return {
      tabList: [
        { title: this.$t('申请权限人员名单') },
        // { title: this.$t('照片') },
        { title: this.$t('附件') }
      ],
      tabIndex: 0,
      formObject: {
        status: 0,
        openingRegion: this.$t('2号门、3号门，3号门单车闸'),
        personnelType: 1,
        personnelTypeDesc: this.$t('驻厂人员')
      },
      employeeOptions: [], // 接待人下拉列表
      managerOptions: [], // 接待人直属领导下拉列表

      rules: {
        // 接待人姓名
        receptionistNumber: [
          { required: true, message: this.$t('请选择接待人姓名'), trigger: 'blur' }
        ],
        // 接待人电话
        receptionistPhone: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('请输入接待人电话')))
              } else if (
                value &&
                !RegExpMap.phoneNumRegCN.test(value) &&
                !RegExpMap.phoneNumRegVN.test(value)
              ) {
                callback(new Error(this.$t('输入的手机号有误')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        // 接待人直属领导姓名
        managerNum: [
          { required: true, message: this.$t('请选择接待人直属领导姓名'), trigger: 'blur' }
        ],
        // 门禁有效开始日期
        accessValidityStartDate: [
          { required: true, message: this.$t('请选择门禁有效开始日期'), trigger: 'blur' }
        ],
        // 门禁有效结束日期
        accessValidityEndDate: [
          { required: true, message: this.$t('请选择门禁有效结束日期'), trigger: 'blur' }
        ],
        // 驻场代表姓名
        onSiteRepresentativeName: [
          { required: true, message: this.$t('请输入驻场代表姓名'), trigger: 'blur' }
        ],
        // 驻场代表手机号
        onSiteRepresentativePhone: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (
                value &&
                !RegExpMap.phoneNumRegCN.test(value) &&
                !RegExpMap.phoneNumRegVN.test(value)
              ) {
                callback(new Error(this.$t('输入的手机号有误')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        // 驻场代表所属公司
        onSiteRepresentativeCompany: [
          { required: true, message: this.$t('请输入驻场代表所属公司'), trigger: 'blur' }
        ],
        // 来访总人数
        visitorCount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error(this.$t('请输入来访总人数')))
              } else if (value && value < 1) {
                callback(new Error(this.$t('来访总人数需大于等于1')))
              } else if (!RegExpMap.integerReg.test(value)) {
                callback(new Error(this.$t('请输入正整数')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        // 申请理由
        applicationReason: [{ required: true, message: this.$t('请输入申请理由'), trigger: 'blur' }]
      },
      headerId: '',
      isSup: false,
      pageType: 'detail'
    }
  },
  mounted() {
    if (this.$route.name?.includes('sup-')) {
      this.isSup = true
    }
    if (this.$route.query.type) {
      this.pageType = this.$route.query.type
    }
    if (this.pageType === 'add') {
      const userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
      this.formObject.supplierCode = userInfo.enterpriseCode || userInfo.accountName
    }
    if (this.$route.query.id) {
      this.headerId = this.$route.query.id
    }
    this.getEmployeeOptions = utils.debounce(this.getEmployeeOptions, 300)
    this.getManagerOptions = utils.debounce(this.getManagerOptions, 300)
    this.getDatailData()
  },
  methods: {
    getDatailData() {
      if (this.pageType !== 'add') {
        this.$API.authorizationApplication
          .getParkApplyDetailSup({ id: this.headerId })
          .then((res) => {
            const { code, data } = res
            if (code === 200) {
              this.formObject = data[0]
              this.getEmployeeOptions({ text: this.formObject.receptionistNumber })
              this.getManagerOptions({ text: this.formObject.managerNum })
              this.$refs.personnelList.initData(data)
              this.getAttachmentList()
            }
          })
      }
    },
    getAttachmentList() {
      this.$API.authorizationApplication.queryTvFilesList({ docId: this.headerId }).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.$refs.attachmentList.initData(data)
        }
      })
    },
    // 员工下拉改变
    handleEmployeeChange(e) {
      const { itemData } = e
      this.$set(this.formObject, 'receptionistNumber', itemData.employeeCode)
      this.$set(this.formObject, 'receptionistCode', itemData.externalCode)
      this.$set(this.formObject, 'receptionistName', itemData.employeeName)
      this.$set(this.formObject, 'receptionistPhone', itemData.phoneNum)
    },
    // 直属领导下拉改变
    handleManagerChange(e) {
      const { itemData } = e
      this.$set(this.formObject, 'managerNum', itemData.employeeCode)
      this.$set(this.formObject, 'managerCode', itemData.externalCode)
      this.$set(this.formObject, 'managerName', itemData.employeeName)
    },
    // 查询员工下拉
    getEmployeeOptions(e = { text: '' }) {
      this.$API.masterData
        .findByEmployeeCode(
          //   {
          //   organizationLevelCodes: ['ORG02', 'ORG01'],
          //   orgType: 'ORG001PRO',
          //   includeItself: true,
          //   organizationIds: [],
          //   fuzzyParam: e.text
          // }
          { employeeCode: e.text }
        )
        .then((res) => {
          if (res.data && res.data.id) {
            const employeeOptions = [res.data]
            employeeOptions.forEach((item) => {
              item.labelShow = item.employeeCode + ' - ' + item.employeeName
            })
            this.employeeOptions = employeeOptions

            this.$nextTick(() => {
              if (e.updateData && typeof e.updateData == 'function') {
                e.updateData(employeeOptions)
              }
            })
          } else {
            this.employeeOptions = []
          }
        })
    },
    // 查询员工下拉
    getManagerOptions(e = { text: '' }) {
      this.$API.masterData
        .findByEmployeeCode(
          //   {
          //   organizationLevelCodes: ['ORG02', 'ORG01'],
          //   orgType: 'ORG001PRO',
          //   includeItself: true,
          //   organizationIds: [],
          //   fuzzyParam: e.text
          // }
          { employeeCode: e.text }
        )
        .then((res) => {
          if (res.data && res.data.id) {
            const employeeOptions = [res.data]
            employeeOptions.forEach((item) => {
              item.labelShow = item.employeeCode + ' - ' + item.employeeName
            })
            this.managerOptions = employeeOptions

            this.$nextTick(() => {
              if (e.updateData && typeof e.updateData == 'function') {
                e.updateData(employeeOptions)
              }
            })
          } else {
            this.managerOptions = []
          }
        })
    },
    clickMtbuttonSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('提交前请先确认已保存当前数据！')
        },
        success: () => {
          // 只能批量选择新建、审批拒绝状态的单据提交
          this.$store.commit('startLoading')
          this.$API.authorizationApplication
            .submitParkApplySup([this.headerId])
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t(`提交成功`),
                  type: 'success'
                })
                this.$router.go(-1)
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    clickMtbuttonSave() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.formObject.accessValidityStartDate && this.formObject.accessValidityEndDate) {
            if (
              new Date(
                dayjs(this.formObject.accessValidityEndDate).format('YYYY-MM-DD 00:00:00')
              ).getTime() <
              new Date(
                dayjs(this.formObject.accessValidityStartDate).format('YYYY-MM-DD 00:00:00')
              ).getTime()
            ) {
              this.$toast({
                content: this.$t(`门禁有效结束日期不可早于门禁有效开始日期`),
                type: 'warning'
              })
              return
            }
          }
          const personnelList = this.$refs.personnelList.tableData
          const attachmentList = this.$refs.attachmentList.tableData
          if (!personnelList.length) {
            this.$toast({
              content: this.$t(`申请权限人员名单请先添加数据`),
              type: 'warning'
            })
            return
          }
          const applyPersonList = []
          const fileLists = []
          for (let i = 0; i < attachmentList.length; i++) {
            const item = { ...attachmentList[i] }
            if (item.id?.includes('row_')) {
              delete item.id
            }
            if (item.isValid === 1 && !item.sysFileId) {
              this.$toast({
                content: this.$t(`附件第{index}行请上传文件`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.docType) {
              this.$toast({
                content: this.$t(`附件第{index}行请选择文件分类`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (item.isNeedSave) {
              if (item.id) {
                delete item.id
              }
              fileLists.push(item)
            }
          }
          for (let i = 0; i < personnelList.length; i++) {
            const item = personnelList[i]
            if (!item.visitorPhone) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行请输入手机号`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (
              !RegExpMap.phoneNumRegCN.test(item.visitorPhone) &&
              !RegExpMap.phoneNumRegVN.test(item.visitorPhone)
            ) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行手机号输入有误`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.visitorName) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行请输入姓名`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.visitUserIdcardNo) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行请输入身份证号`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            item.visitUserIdcardNo = item.visitUserIdcardNo.trim()
            if (!RegExpMap.idCardReg.test(item.visitUserIdcardNo)) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行身份证号输入有误`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.visitorCompany) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行请输入来访人所属公司`, {
                  index: i + 1
                }),
                type: 'warning'
              })
              return
            }
            if (!item.visitorLicensePlate) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行请输入车牌号`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            if (!item.photoDto || !item.photoDto?.sysFileId) {
              this.$toast({
                content: this.$t(`申请权限人员名单第{index}行请上传照片`, { index: i + 1 }),
                type: 'warning'
              })
              return
            }
            const obj = { ...item }
            if (obj.id?.includes('row_')) {
              delete obj.id
            }
            applyPersonList.push(obj)
          }
          const params = {
            // id: this.headerId,
            ...this.formObject,
            accessValidityStartDate: new Date(
              dayjs(this.formObject.accessValidityStartDate).format('YYYY-MM-DD 00:00:00')
            ).getTime(),
            accessValidityEndDate: new Date(
              dayjs(this.formObject.accessValidityEndDate).format('YYYY-MM-DD 23:59:29')
            ).getTime(),
            applyPersonList,
            fileLists
          }
          if (this.headerId) {
            params.id = this.headerId
          }
          this.$store.commit('startLoading')
          this.getSaveApi()(params)
            .then((res) => {
              const { code, data } = res
              if (code === 200) {
                this.headerId = data
                this.$toast({
                  content: this.$t(`保存成功`),
                  type: 'success'
                })
                this.$router.replace({
                  path: this.isSup ? `sup-enter-park-detail` : `pur-enter-park-detail`,
                  query: {
                    type: 'edit',
                    id: this.headerId,
                    timeStamp: new Date().getTime()
                  }
                })
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    getSaveApi() {
      if (this.headerId) {
        return this.$API.authorizationApplication.updateParkApplySup
      } else {
        return this.$API.authorizationApplication.saveParkApplySup
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-area {
  /deep/.tab-item2 div {
    // background: red !important;
    &:before {
      content: '*';
      color: #f44336;
      font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system',
        'BlinkMacSystemFont';
      font-size: 12px;
      font-weight: normal;
      margin-right: -4px;
    }
  }
}
</style>
