<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :buttons="buttons"
    :header="modalData.title"
  >
    <div class="picture-upload-area">
      <div class="header-area">
        {{
          this.$t(
            '声明：本人授权TCL王牌电器（惠州）有限公司采集本人的身份证号码、手机号码等个人信息，用于配合《海关高级认证企业标准》要求，以便于贵司进行园区出入安全管理。贵司会在本人申请临时出入证期间内保留本人的个人信息，并在授权终止后删除并不予以保留，除非依照法律法规规定另行取得本人的授权。贵司会采取合理必要的措施来保护本人的个人信息，以尽到信息安全保护义务。本人在此声明已知悉并理解上述授权条款，本授权书自本人提供个人信息之日起自动生效。'
          )
        }}
      </div>
      <div class="main-area">
        <div class="main-area-left">{{ $t('照片') }}</div>
        <div class="main-area-right">
          <div class="main-area-notice">
            {{ this.$t('1. 内部员工照片命名：姓名 _ 工号。（例如 XX_00XXXX ）') }}<br />
            {{
              this.$t('2. 非内部员工照片命名： 姓名 _ 身份证号码。（例如 XX_410XXXXXXXXXXXXXXX ）')
            }}<br />
            {{
              this.$t(
                '3. 图片格式只支持 jpg ，多照片请批量选择上传，大小约 5MB ( 可用画图软件调整像素为 640*480 或以上 ) 。'
              )
            }}
            <br />{{ this.$t('4. 露出额头及耳朵，人脸正面免冠近期大头照。') }} <br />{{
              this.$t('5. 照片白底、无逆光、无 PS, 无过度美颜处理。')
            }}
            <br />{{ this.$t('6. 两眼之间的像素点 > 60') }}
          </div>
          <div class="upload-area" @click="handleUpload">
            <div v-if="!fileData.length" class="plus-icon"></div>
            <div v-else class="upload-file-area">
              <p v-for="(item, index) in fileData" :key="index">
                {{ item.fileName }}<span @click.stop="handlerDownload(item)">{{ $t('下载') }}</span
                ><span v-if="modalData.editable" @click.stop="handleDelete(item)">{{
                  $t('删除')
                }}</span>
              </p>
            </div>
            <div v-if="modalData.editable" class="upload-area-button">
              {{ this.$t('上传图片，请')
              }}<span style="color: #2783fe">{{ this.$t('点击上传') }}</span
              ><span class="main-area-notice">*</span>
            </div>
          </div>
          <!-- multiple -->
          <input
            v-if="isUpload"
            type="file"
            ref="uploadIptRef"
            class="upload-input"
            style="visibility: hidden"
            accept="image/jpeg"
            @change="chooseFiles"
          />
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { download } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isUpload: true,
      fileData: [],
      buttons: [
        // {
        //   click: this.cancel,
        //   buttonModel: { content: this.$t("取消") },
        // },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    if (this.modalData.fileData) {
      this.fileData = [this.modalData.fileData]
    }
    this.$refs['dialog'].ejsRef.show()
  },
  methods: {
    handleUpload() {
      if (this.fileData.length) {
        this.$toast({
          content: this.$t('仅可上传一张照片')
        })
        return
      }
      if (this.modalData.editable) {
        this.$refs.uploadIptRef.click()
      }
    },
    handleDelete(file) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          this.$loading()
          const params = [file.sysFileId]
          this.$API.authorizationApplication
            .delTvFilesList(params)
            .then((res) => {
              const { code } = res
              if (code === 200) {
                this.fileData = this.fileData.filter((item) => {
                  return item.sysFileId !== file.sysFileId
                })
              }
            })
            .finally(() => {
              this.$hloading()
            })
        }
      })
    },
    handlerDownload(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.sysFileId || data.id
        })
        .then((res) => {
          download({ fileName: data.fileName, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },

    // 点击上传文件
    async chooseFiles() {
      let _files = event.target.files
      let params = {
        type: 'array',
        maxLimit: 5 * 1024 * 1024,
        // maxLimit: 200 * 1024,
        minLimit: 1 * 1024,
        // minLimit: 150 * 1024,
        msg: this.$t('单个文件，限制5MB')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.maxLimit || _files[i].size < params.minLimit) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$store.commit('startLoading')
      for (let i = 0; i < _files.length; i++) {
        let file = new FormData()
        file.append('UploadFiles', _files[i])
        const res = await this.$API.fileService.uploadPublicFile(file)
        this.handleUploadFiles(res?.data)
        this.isUpload = false
        this.isUpload = true
      }
      this.$store.commit('endLoading')
      // this.$store.commit('endLoading')
      // this.$API.fileService.uploadPrivateFile(_data).then((res) => {
      //   this.$store.commit('endLoading')
      //   this.handleUploadFiles(res?.data)
      //   this.isUpload = false
      //   setTimeout(() => {
      //     this.isUpload = true
      //   }, 300)
      // })
    },

    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, remoteUrl, sysName, createTime } = data
      const { username: createUserName } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      this.fileData.push({
        // docId: 0,
        docType: '12',
        fileName: fileName,
        fileSize: fileSize,
        fileType: fileType,
        // remoteUrl: remoteUrl,
        sysFileId: id,
        sysName: sysName,
        url: remoteUrl,
        createUserName, // 创建人
        createTime // 创建时间
      })
    },
    confirm() {
      if (this.modalData.editable) {
        this.$emit('confirm-function', this.fileData)
      } else {
        this.$emit('cancel-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.picture-upload-area {
  height: 100%;
  border: #e8eaec solid 1px;
  display: flex;
  flex-direction: column;
  .header-area {
    height: 20%;
    padding: 8px;
    font-weight: bold;
    background: #fafafa;
    line-height: 20px;
    border-bottom: #e8eaec solid 1px;
  }
  .main-area {
    min-height: 30vh;
    flex: 1;
    display: flex;
    &-left {
      border-right: #e8eaec solid 1px;
      padding: 8px;
      width: 20%;
      display: flex;
      align-items: center;
      background: #fafafa;
    }
    &-right {
      padding: 8px;
      width: 100%;
      .main-area-notice {
        color: #f44336;
        line-height: 20px;
      }
    }
  }
  .upload-area {
    min-width: 200px;
    min-height: 200px;
    background: #fafafa;
    border: #eee dashed 1px;
    margin-top: 20px;
    cursor: pointer;
    .plus-icon {
      border: 1px dashed #000;
      height: 60px;
      margin: 50px auto 0;
      position: relative;
      width: 60px;
      &:before {
        background: #98aac3;
        content: ' ';
        display: inline-block;
        height: 2px;
        left: -1px;
        position: absolute;
        top: 50%;
        width: 60px;
      }
      &:after {
        background: #98aac3;
        content: ' ';
        display: inline-block;
        height: 60px;
        left: 50%;
        position: absolute;
        top: -1px;
        width: 2px;
      }
    }
    .upload-file-area {
      margin: 40px auto 0;
      text-align: center;
      p {
        padding: 5px;
      }
      span {
        color: #2783fe;
        margin-left: 10px;
      }
    }
    .upload-area-button {
      text-align: center;
      padding: 5px;
      margin-bottom: 30px;
    }
  }
}
</style>
