<template>
  <div>
    <span class="picture-cell-content" @click="handleCellClick">{{ cellContent }}</span>
  </div>
</template>

<script>
export default {
  props: {
    editable: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      cellContent: this.$t('暂无附件')
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.editable) {
          if (newVal?.sysFileId) {
            this.cellContent = `${this.$t('上传/查看')}`
          } else {
            this.cellContent = this.$t('点击上传')
          }
        } else {
          if (newVal?.sysFileId) {
            this.cellContent = `${this.$t('查看')}`
          } else {
            this.cellContent = this.$t('暂无附件')
          }
        }
      },
      immediate: true
    },
    editable: {
      handler(newVal) {
        if (newVal) {
          if (this.value?.sysFileId) {
            this.cellContent = `${this.$t('上传/查看')}`
          } else {
            this.cellContent = this.$t('点击上传')
          }
        } else {
          if (this.value?.sysFileId) {
            this.cellContent = `${this.$t('查看')}`
          } else {
            this.cellContent = this.$t('暂无附件')
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleCellClick() {
      if (this.cellContent !== this.$t('暂无附件')) {
        // 打开弹窗
        this.$dialog({
          modal: () => import('./pictureUpload.vue'),
          data: {
            title: this.$t('上传照片'),
            fileData: this.value,
            editable: this.editable
          },
          success: (fileData) => {
            this.$emit('input', fileData[0])
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.picture-cell-content {
  color: #2783fe;
  font-size: 14px;
  cursor: pointer;
  text-align: left;
}
</style>
