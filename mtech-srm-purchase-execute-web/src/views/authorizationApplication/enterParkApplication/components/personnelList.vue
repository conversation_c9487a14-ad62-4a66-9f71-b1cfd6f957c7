<template>
  <ScTable
    ref="xTable"
    class="vxe-table-area"
    :row-config="{ height: 50 }"
    :columns="columns"
    :table-data="tableData"
  >
    <template v-if="pageType !== 'detail' && isSup" slot="custom-tools">
      <vxe-button
        v-for="item in toolbar"
        :key="item.code"
        :status="item.status"
        size="small"
        @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
        >{{ item.name }}</vxe-button
      >
    </template>
    <template #phoneDefault="{ row }">
      <div class="celltool-area">
        <p :style="row.visitorPhone ? 'height: 30px; line-height: 30px' : ''" v-if="!row.editable">
          {{ row.visitorPhone }}
        </p>
        <vxe-input v-else v-model="row.visitorPhone" />
        <div v-if="pageType !== 'detail' && isSup">
          <p v-if="!row.editable">
            <span @click="row.editable = true">{{ $t('编辑') }}</span
            ><span @click="handleDelete([row])">{{ $t('删除') }}</span>
          </p>
          <p v-else>
            <span @click="row.editable = false">{{ $t('完成') }}</span>
          </p>
        </div>
      </div>
    </template>
    <template #photoDtoDefault="{ row }">
      <div class="celltool-area">
        <pictureCell v-model="row.photoDto" :editable="row.editable" />
      </div>
    </template>
  </ScTable>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import pictureCell from './pictureCell.vue'
export default {
  components: {
    ScTable,
    pictureCell
  },
  props: {
    pageType: {
      type: String,
      default: 'detail'
    },
    isSup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'vxe-icon-square-plus',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('删除'),
          icon: 'vxe-icon-delete',
          status: 'info'
        }
      ],
      tableData: [],
      columns: [
        {
          type: 'checkbox',
          width: 50,
          ignore: true
          // fixed: 'left'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50
        },
        {
          field: 'visitorPhone',
          title: this.$t('来访人手机号'),
          minWidth: 130,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('来访人手机号')}
                </span>
              ]
            },
            default: 'phoneDefault'
          }
        },
        {
          field: 'visitorName',
          title: this.$t('来访人姓名'),
          minWidth: 110,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('来访人姓名')}
                </span>
              ]
            },
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input v-show={row.editable === true} v-model={row.visitorName} />
                  <span v-show={row.editable === false}>{row.visitorName}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'visitUserIdcardNo',
          title: this.$t('来访人身份证号'),
          minWidth: 140,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('来访人身份证号')}
                </span>
              ]
            },
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input v-show={row.editable === true} v-model={row.visitUserIdcardNo} />
                  <span v-show={row.editable === false}>{row.visitUserIdcardNo}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'visitorCompany',
          title: this.$t('来访人所属公司'),
          minWidth: 140,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('来访人所属公司')}
                </span>
              ]
            },
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input v-show={row.editable === true} v-model={row.visitorCompany} />
                  <span v-show={row.editable === false}>{row.visitorCompany}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'visitorLicensePlate',
          title: this.$t('车牌号'),
          minWidth: 100,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('车牌号')}
                </span>
              ]
            },
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input v-show={row.editable === true} v-model={row.visitorLicensePlate} />
                  <span v-show={row.editable === false}>{row.visitorLicensePlate}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'visitorNum',
          title: this.$t('来访证编号'),
          minWidth: 120,
          showOverflow: true,
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <vxe-input v-show={row.editable === true} v-model={row.visitorNum} />
                  <span v-show={row.editable === false}>{row.visitorNum}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'photoDto',
          title: this.$t('照片'),
          minWidth: 120,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('照片')}
                </span>
              ]
            },
            default: 'photoDtoDefault'
          }
        },
        {
          field: 'certCompliance',
          title: this.$t('是否符合办证条件'),
          minWidth: 150,
          showOverflow: true,
          formatter: ({ cellValue }) => {
            const certComplianceOptions = [
              { label: '', value: 1 },
              { label: this.$t('是'), value: 0 }
            ]
            let item = certComplianceOptions.find((itm) => itm.value === cellValue)
            return item ? item.label : ''
          }
          // editRender: { name: 'input', attrs: { disabled: true } }
        }
      ]
    }
  },
  methods: {
    initData(data) {
      this.tableData = data.map((i) => {
        return {
          ...i,
          editable: false,
          photoDto: {
            sysFileId: i.photoId,
            fileName: i.photoName
          }
        }
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      if (code === 'Add') {
        this.tableData.push({
          // visitorPhone: '13767108557',
          editable: false
        })
      } else if (code === 'Delete') {
        const selectedRecords = $grid.getCheckboxRecords()
        if (selectedRecords.length === 0) {
          this.$toast({ content: this.$t('请选择要删除的行'), type: 'warning' })
          return
        }
        this.handleDelete(selectedRecords)
      }
    },
    handleDelete(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const deleteList = []
          // 遍历已勾选的数据 有id的就是线上数据需要调删除接口
          const arr = this.tableData.filter((i) => {
            return !selectedRecords.some((j) => {
              if (j && j.id && !j.id.includes('row_')) {
                deleteList.push(j.id)
              }
              if (j && j.id) {
                return j.id === i.id
              }
            })
          })
          if (deleteList.length) {
            const params = Array.from(new Set(deleteList)) // 去个重
            this.$API.authorizationApplication.batchDelByIds(params).then((res) => {
              const { code } = res
              if (code === 200) {
                this.afterDeleteEvent(arr)
              }
            })
            return
          }
          // 删除后刷新列表
          this.afterDeleteEvent(arr)
        }
      })
    },
    afterDeleteEvent(arr) {
      this.$toast({
        content: this.$t('删除成功'),
        type: 'success'
      })
      this.tableData = arr
    }
  }
}
</script>

<style lang="scss" scoped>
.vxe-table-area {
  width: 100%;
  .celltool-area {
    span {
      color: #2783fe;
      font-size: 14px;
      cursor: pointer;
      text-align: left;
      margin-right: 5px;
    }
    p span {
      font-size: 12px;
    }
  }
}
</style>
