<template>
  <ScTable
    ref="xTable"
    class="vxe-table-area"
    :row-config="{ height: 50 }"
    :columns="columns"
    :table-data="tableData"
    :sortable="false"
  >
    <template v-if="pageType !== 'detail' && isSup" slot="custom-tools">
      <vxe-button
        v-for="item in toolbar"
        :key="item.code"
        :status="item.status"
        size="small"
        @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
        >{{ item.name }}</vxe-button
      >
      <input
        v-if="isUpload"
        type="file"
        ref="uploadIptRef"
        class="upload-input"
        style="visibility: hidden"
        @change="chooseFiles"
      />
    </template>
    <template #fileNameDefault="{ row, rowIndex }">
      <div class="celltool-area">
        <p>
          {{ row.fileName }}
        </p>
        <div>
          <p v-if="row.fileName">
            <span
              v-if="pageType !== 'detail' && isSup"
              @click="handleUpload(rowIndex, row.docType)"
              >{{ $t('重新上传') }}</span
            ><span @click="handlerDownload(row)">{{ $t('下载') }}</span>
          </p>
          <p v-else>
            <span
              v-if="pageType !== 'detail' && isSup"
              @click="handleUpload(rowIndex, row.docType)"
              >{{ $t('上传') }}</span
            >
          </p>
        </div>
      </div>
    </template>
    <template #fileTemplateDefault="{ row }">
      <div v-if="row.fileTemplate && row.fileTemplate.fileName" class="celltool-area">
        <p>
          {{ row.fileTemplate.fileName }}
        </p>
        <p>
          <span @click="handlerDownload(row.fileTemplate)">{{ $t('下载') }}</span>
        </p>
      </div>
    </template>
  </ScTable>
</template>

<script>
import { download } from '@/utils/utils'
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable
  },
  props: {
    pageType: {
      type: String,
      default: 'detail'
    },
    isSup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      toolbar: [
        {
          code: 'Add',
          name: this.$t('新增'),
          icon: 'vxe-icon-square-plus',
          status: 'info'
        },
        {
          code: 'Delete',
          name: this.$t('批量删除'),
          icon: 'vxe-icon-delete',
          status: 'info'
        },
        {
          code: 'Download',
          name: this.$t('批量下载已上传的文件'),
          icon: 'vxe-icon-delete',
          status: 'info'
        }
      ],
      tableData: [
        {
          fileTemplate: {
            fileName: '《承包、承租业务安全管理协议 》.pdf',
            href: './static/contractAgreement.pdf'
          },
          docType: '13',
          isValid: 1,
          index: 0
        },
        {
          fileTemplate: {
            fileName: '《临时出入证申请表》.doc',
            href: './static/《临时出入证申请表》.doc'
          },
          docType: '14',
          isValid: 1,
          index: 1
        },
        {
          fileTemplate: {
            fileName: '《面部识别信息授权表》.pdf',
            href: './static/《面部识别信息授权表》.pdf'
          },
          docType: '15',
          isValid: 1,
          index: 2
        },
        {
          fileTemplate: {
            fileName: '《TCL液晶产业园临时出入证》办理承诺协议.pdf',
            href: './static/《TCL液晶产业园临时出入证》办理承诺协议.pdf'
          },
          docType: '16',
          isValid: 1,
          index: 3
        },
        {
          docType: '17',
          isValid: 1,
          index: 4
        }
      ],
      fileTypeList: [
        {
          label: this.$t('承包、承租业务安全管理协议'),
          value: '13',
          fileName: '《承包、承租业务安全管理协议 》.pdf',
          docType: '13',
          isValid: 1
        },
        {
          label: this.$t('临时出入证申请'),
          value: '14',
          fileName: '《临时出入证申请表》.doc',
          docType: '14',
          isValid: 1
        },
        {
          label: this.$t('面部识别信息授权表'),
          value: '15',
          fileName: '《面部识别信息授权表》.pdf',
          docType: '15',
          isValid: 1
        },
        {
          label: this.$t('《TCL液晶产业园临时出入证》办理承诺协议'),
          value: '16',
          fileName: '《TCL液晶产业园临时出入证》办理承诺协议.pdf',
          docType: '16',
          isValid: 1
        },
        {
          label: this.$t('身份证正反面'),
          value: '17',
          isValid: 0
        },
        {
          label: this.$t('入液晶园-其他'),
          value: '18',
          isValid: 0
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50
        },
        {
          field: 'fileName',
          title: this.$t('文件名称'),
          minWidth: 120,
          showOverflow: true,
          slots: {
            default: 'fileNameDefault'
          }
        },
        {
          field: 'fileTemplate',
          title: this.$t('要求提供的文件模板'),
          minWidth: 160,
          showOverflow: true,
          slots: {
            default: 'fileTemplateDefault'
          }
        },
        {
          field: 'docType',
          title: this.$t('文件分类'),
          minWidth: 270,
          showOverflow: true,
          slots: {
            header: () => {
              return [
                <span>
                  <span style='color: #f44336;'>*</span>
                  {this.$t('文件分类')}
                </span>
              ]
            },
            default: ({ row, rowIndex }) => {
              return [
                <div>
                  <vxe-select
                    v-show={
                      rowIndex > 4 &&
                      this.pageType !== 'detail' &&
                      this.isSup &&
                      row.id.includes('row_')
                    }
                    v-model={row.docType}
                    options={this.fileTypeList}
                    transfer
                    onchange={({ value }) => {
                      this.fileTypeList.forEach((i) => {
                        if (i.value === value) {
                          row.fileTemplate = i
                          row.isValid = i.isValid
                          row.isNeedSave = true
                        }
                      })
                    }}
                  />
                  <span
                    v-show={
                      rowIndex <= 4 ||
                      (rowIndex > 4 &&
                        (this.pageType === 'detail' || !this.isSup || !row.id.includes('row_')))
                    }
                  >
                    {this.fileTypeList.filter((i) => i.value === row.docType)[0]?.label}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'isValid',
          title: this.$t('是否必须上传'),
          minWidth: 120,
          showOverflow: true,
          slots: {
            default: ({ row }) => {
              return [
                <div>
                  <span>{row.isValid === 1 ? this.$t('是') : this.$t('否')}</span>
                </div>
              ]
            }
          }
        },
        {
          field: 'fileSize',
          title: this.$t('文件大小'),
          minWidth: 80,
          formatter: ({ cellValue }) => {
            let size = ''
            if (cellValue) {
              size = `${Math.round(cellValue / 1024)}KB`
            }
            return size
          },
          showOverflow: true
        },
        {
          field: 'createUserName',
          title: this.$t('创建人'),
          minWidth: 80,
          showOverflow: true
        },
        {
          field: 'createTime',
          title: this.$t('创建时间'),
          minWidth: 110,
          showOverflow: true
        }
      ],
      isUpload: true,
      handleIndex: 0,
      handleDocType: null
    }
  },
  mounted() {
    this.getAttachmentTempList()
  },
  methods: {
    initData(data) {
      data.forEach((item, index) => {
        if (index < 5) {
          const row = {
            ...this.tableData[index],
            ...item
          }
          this.$set(this.tableData, index, row)
        } else {
          const row = {
            ...item
          }
          if (row.docType == 13 || row.docType == 14 || row.docType == 15 || row.docType == 16) {
            row.isValid = 1
            this.fileTypeList.forEach((itm) => {
              if (row.docType == itm.value) {
                row.fileTemplate = {
                  fileName: itm.fileName,
                  sysFileId: itm.sysFileId
                }
              }
            })
          }
          this.tableData.push(row)
        }
      })
    },
    getAttachmentTempList() {
      this.$API.authorizationApplication
        .queryTvFilesList({ docId: '434073908255658080' })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            data.forEach((i) => {
              this.fileTypeList.forEach((item) => {
                if (i.docType == item.value) {
                  item.fileName = i.fileName
                  item.sysFileId = i.sysFileId
                }
              })
              this.tableData.forEach((item) => {
                if (i.docType == item.docType) {
                  item.fileTemplate = {
                    fileName: i.fileName,
                    sysFileId: i.sysFileId
                  }
                }
              })
            })
          }
        })
    },
    handleUpload(rowIndex, docType) {
      if (this.pageType !== 'detail' && this.isSup) {
        this.handleIndex = rowIndex
        this.handleDocType = docType
        this.$refs.uploadIptRef.click()
      }
    },

    // 点击上传文件
    async chooseFiles() {
      let _files = event.target.files
      let params = {
        type: 'array',
        maxLimit: 50 * 1024 * 1024,
        msg: this.$t('单个文件，限制50M')
      }
      if (_files.length < 1) {
        this.$toast({
          content: this.$t('您未选择需要上传的文件.')
        })
        return
      }
      let _data = new FormData(),
        isOutOfRange = false
      for (let i = 0; i < _files.length; i++) {
        _data.append('UploadFiles', _files[i])
        if (_files[i].size > params.maxLimit) {
          isOutOfRange = true
          break
        }
      }
      if (isOutOfRange) {
        this.$toast({
          content: params.msg
        })
        return
      }
      this.$store.commit('startLoading')
      this.$API.fileService
        .uploadPublicFile(_data)
        .then((res) => {
          this.handleUploadFiles(res?.data)
          this.isUpload = false
          setTimeout(() => {
            this.isUpload = true
          }, 300)
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },

    //执行上传文件
    handleUploadFiles(data) {
      let { id, fileName, fileSize, fileType, remoteUrl, sysName, createTime } = data
      const { username: createUserName } = JSON.parse(sessionStorage.getItem('userInfo')) // 从 sessionStorage 获取当前用户信息
      const fileData = {
        // docId: 0,
        docType: this.handleDocType,
        fileName,
        fileSize,
        fileType,
        sysFileId: id,
        sysName,
        url: remoteUrl,
        createUserName, // 创建人
        createTime // 创建时间
      }
      // const handleRow = this.tableData[this.handleIndex]
      // if (this.handleIndex <= 3 && fileName !== handleRow?.fileTemplate?.fileName) {
      //   // 前四行，要求上传的文件名要和文件模板名一致
      //   this.$toast({
      //     content: this.$t(
      //       `上传失败，请下载${
      //         this.fileTypeList
      //           .filter((i) => i.value === this.handleDocType)[0]
      //           ?.fileName?.split('.')[0]
      //       }文件模板签署再上载！`
      //     )
      //   })
      //   return
      // }
      if (this.tableData[this.handleIndex] && this.tableData[this.handleIndex]['sysFileId']) {
        this.$API.authorizationApplication
          .delTvFilesList([this.tableData[this.handleIndex]['sysFileId']])
          .then((res) => {
            const { code } = res
            if (code === 200) {
              this.$set(this.tableData, this.handleIndex, {
                ...this.tableData[this.handleIndex],
                ...fileData,
                lineNo: this.handleIndex,
                isNeedSave: true
              })
            }
          })
        return
      }
      this.$set(this.tableData, this.handleIndex, {
        ...this.tableData[this.handleIndex],
        ...fileData,
        lineNo: this.handleIndex,
        isNeedSave: true
      })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      if (code !== 'Add' && selectedRecords.length === 0) {
        this.$toast({ content: this.$t('请选择要操作的行'), type: 'warning' })
        return
      }
      if (code === 'Add') {
        this.tableData.push({
          index: this.tableData.length
        })
      } else if (code === 'Delete') {
        if (selectedRecords.some((i) => i.index <= 4)) {
          this.$toast({ content: this.$t('前五行不可删除'), type: 'warning' })
          return
        }
        this.handleDelete(selectedRecords)
      } else if (code === 'Download') {
        selectedRecords.forEach((i) => {
          if (i.sysFileId) {
            this.handlerDownload(i)
          }
        })
      }
    },
    handlerDownload(data) {
      this.$store.commit('startLoading')
      this.$API.fileService
        .downloadPrivateFile({
          id: data.sysFileId || data.id
        })
        .then((res) => {
          download({ fileName: data.fileName, blob: res.data })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    handleDelete(selectedRecords) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: () => {
          const deleteList = []
          // 遍历已勾选的数据 有id的就是线上数据需要调删除接口
          const arr = this.tableData.filter((i) => {
            return !selectedRecords.some((j) => {
              if (j && j.id && !j.id.includes('row_')) {
                deleteList.push(j.sysFileId)
              }
              if (j && j.id) {
                return j.id === i.id
              }
            })
          })
          if (deleteList.length) {
            const params = Array.from(new Set(deleteList)) // 去个重
            this.$API.authorizationApplication.delTvFilesList(params).then((res) => {
              const { code } = res
              if (code === 200) {
                this.afterDeleteEvent(arr)
              }
            })
            return
          }
          // 删除后刷新列表
          this.afterDeleteEvent(arr)
        }
      })
    },
    afterDeleteEvent(arr) {
      this.$toast({
        content: this.$t('删除成功'),
        type: 'success'
      })
      this.tableData = arr
    }
  }
}
</script>

<style lang="scss" scoped>
.vxe-table-area {
  width: 100%;
  .celltool-area {
    span {
      color: #2783fe;
      font-size: 14px;
      cursor: pointer;
      text-align: left;
      margin-right: 5px;
    }
    p span {
      font-size: 12px;
    }
  }
}
</style>
