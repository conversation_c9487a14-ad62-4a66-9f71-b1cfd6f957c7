<!--寄售合同打印-->
<!--
 * @Author: your name
 * @Date: 2021-09-29 09:53:32
 * @LastEditTime: 2022-01-11 09:56:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\MaterialSite.vue
-->
<template>
  <div class="full-height">
    <mt-template-page
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './config/index'
import Vue from 'vue'
export default {
  data() {
    return {
      pageConfig: PAGE_PLUGIN,
      newJITData: [] //选择完JIT以后的数组
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'Download') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //采方寄售合同打印
      if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },
    //采方寄售合同打印
    handleClickDownload(_selectRows) {
      let ids = []
      _selectRows.map((item) => {
        ids.push(item.id)
      })
      this.$API.contractPrint.printConsignment(ids).then((res) => {
        // console.log(res, "采方寄售合同打印");
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = function () {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
