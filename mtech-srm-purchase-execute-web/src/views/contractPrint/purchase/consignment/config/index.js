import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
// import { i18n } from "@/main.js";
const toolbar = [
  {
    id: 'Download',
    icon: 'icon_solid_Createorder',
    title: i18n.t('寄售合同打印')
  }
]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        '-1': i18n.t('关闭'),
        0: i18n.t('未发布'),
        1: i18n.t('发布待确认'),
        2: i18n.t('反馈正常'),
        3: i18n.t('反馈异常')
      }
    }
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司')
  },
  {
    field: 'supplierName',
    // width: "200",
    headerText: i18n.t('供应商')
  },
  {
    field: 'reconciliationCode',
    // width: "200",
    headerText: i18n.t('对账单号')
  },
  {
    field: 'reconciliationTypeName',
    // width: "200",
    headerText: i18n.t('对账单类型')
  },
  {
    field: 'publishTime',
    // width: "200",
    headerText: i18n.t('对账日期')
  },
  {
    field: 'createUserName',
    // width: "200",
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    // width: "200",
    headerText: i18n.t('创建日期')
  }
]
export const PAGE_PLUGIN = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData,
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/reconciliationHeader/queryBuilder',
        params: {
          condition: 'and',
          defaultRules: [
            {
              label: i18n.t('对账类型'),
              field: 'reconciliationTypeName',
              type: 'string',
              operator: 'equal',
              value: i18n.t('寄售')
            }
          ]
        }
      }
    }
  }
]
