<!--合同生成与发布配置-->
<template>
  <div class="full-height">
    <mt-template-page
      v-if="!showHidden"
      ref="tepPage"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @refreshList="refreshList"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>
    <div class="templateObj" v-else>
      <requireDetail
        ref="requireDetail"
        :edit-template="editTemplate"
        :template-obj="templateObj"
        @clicksave="clicksave"
      ></requireDetail>
    </div>
    <!-- 配置导入弹框 -->
    <uploadExcelDialog
      ref="uploadExcelRefs"
      :down-template-name="downTemplateName"
      :down-template-params="downTemplateParams"
      :upload-params="uploadParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></uploadExcelDialog>
  </div>
</template>

<script>
import { PAGE_PLUGIN, columnData } from './config/index'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    requireDetail: require('./components/richtextDialog.vue').default,
    uploadExcelDialog: require('./components/uploadExcelDialog.vue').default
  },
  data() {
    return {
      pageConfig: PAGE_PLUGIN,
      companyDrawerArr: [],
      templateObj: {},
      showHidden: false,
      editTemplate: {},
      //--------导入
      downTemplateName: this.$t('订单配置模板'),
      downTemplateParams: {}, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {}
    }
  },
  computed: {},
  mounted() {
    this.initialInterfaceCall()
  },
  methods: {
    initialInterfaceCall() {
      //选择合同
      this.$API.contractPrint.initContractPrint().then((res) => {
        for (const key in res.data) {
          this.companyDrawerArr.push({
            text: res.data[key],
            value: key
          })
        }
        this.$set(this.pageConfig[0].grid, 'columnData', columnData(res.data))
      })
    },
    //头部点击
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (_selectRows.length <= 0 && (e.toolbar.id == 'delete' || e.toolbar.id == 'edit')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //新增
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      }
      //删除
      if (e.toolbar.id === 'delete') {
        this.handleClickdelete(_selectRows)
      }
      //编辑
      if (e.toolbar.id === 'edit') {
        this.handleClickEdit(_selectRows)
      }
      //导入
      if (e.toolbar.id === 'upload') {
        this.handleClickUpload()
      }
      //导出
      if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },
    //行内点击
    handleClickCellTool(e) {
      let takeEffect = ''
      if (e.tool.id == 'enable') {
        // console.log(e, "点击启用");
        takeEffect = '1'
      }
      if (e.tool.id == 'disable') {
        // console.log("点击停用");
        takeEffect = '0'
      }
      let parameter = {
        id: e.data.id,
        takeEffect: takeEffect
      }
      this.$API.contractPrint.contractStatus(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    //新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "./components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: this.$t('新增'),
          headStates: 'Add'
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //删除
    handleClickdelete(_selectRows) {
      console.log(_selectRows)
      let parameter = []
      _selectRows.map((item) => {
        parameter.push(item.id)
      })
      this.$API.contractPrint.contractPrintConfigdelete(parameter).then(() => {
        this.$refs.tepPage.refreshCurrentGridData()
        this.$toast({
          content: this.$t('操作成功'),
          type: 'success'
        })
      })
    },
    handleClickUpload() {
      //导入
      this.downTemplateParams = {
        condition: '',
        page: { current: 1, size: 10 },
        pageFlag: false,
        defaultRules: []
      }
      this.uploadParams = {}
      this.requestUrls = {
        templateUrlPre: 'purchaseOrder',
        templateUrl: 'poTimeConfigDownload',
        uploadUrl: 'poTimeConfigUpload'
      }
      this.showUploadExcel(true)
    },
    // 上传成功后，获取到的数据
    upExcelConfirm() {
      this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.hide()
      this.$toast({ content: this.$t('导入成功'), type: 'success' })
      this.updateList()
    },
    //更新数据
    updateList() {
      this.$refs[`tepPage`].refreshCurrentGridData()
    },
    showUploadExcel(flag) {
      if (flag) {
        console.log(this.$refs.requireDetail, 'this.$refs.uploadExcelRefs')
        this.$refs.uploadExcelRefs.uploadData = null // 清空数据
        this.$refs.uploadExcelRefs.fileLength = 0
        this.$refs.uploadExcelRefs.$refs.uploader.files = []
        this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRefs.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    //导出
    handleClickDownload() {
      let rule = this.$refs.tepPage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let params = {
        condition: rule.condition || '',
        page: { current: 1, size: 10 },
        rules: rule.rules || []
      }
      this.$store.commit('startLoading')
      this.$API.contractPrint.contractPrintConfigexport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
        console.log(fileName)
      })
    },
    //编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: this.$t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: "./components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: this.$t('编辑'),
          headStates: 'edit',
          data: _selectRows
        },
        success: () => {
          this.$refs.tepPage.refreshCurrentGridData()
          this.$toast({
            content: this.$t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    //行内----编辑模板
    refreshList(item) {
      // console.log(item,"点击编辑模板携带的单行的数据和模板信息");
      this.templateObj = item
      let id = this.templateObj?.data?.id ?? ''
      let templateCode = this.templateObj?.item?.value ?? ''
      let parameter = {
        contractPrintId: id,
        templateCode: templateCode
      }
      this.$API.contractPrint.contractPrintTemplateQueryInfo(parameter).then((res) => {
        console.log(res.data)
        this.showHidden = true
        this.editTemplate = res.data
      })
    },
    //点击保存
    clicksave() {
      this.showHidden = false
    }
  }
}
</script>

<style lang="scss">
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
.editTemplate {
  width: 200px;
  span {
    color: #00469c;
    cursor: pointer;
  }
  span:nth-of-type(1) {
    margin-right: 10px;
  }
}
.templateObj {
  height: 100%;
}
</style>
