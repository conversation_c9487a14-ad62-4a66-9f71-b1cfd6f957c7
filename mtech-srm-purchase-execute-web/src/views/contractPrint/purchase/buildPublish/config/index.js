import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-09-29 09:58:01
 * @LastEditTime: 2021-12-30 13:54:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\config\materialSite.config.js
 */
import Vue from 'vue'
// import { i18n } from "@/main.js";
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('导入') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('导出') }
]
export const columnData = (companyDrawerArr) => [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'takeEffect',
    // width: "200",
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('停用'),
        1: i18n.t('启用')
      }
    },
    cellTools: [
      {
        id: 'disable',
        icon: '', // icon_list_disable
        permission: ['O_02_0509'],
        title: i18n.t('停用'),
        visibleCondition: (data) => data.takeEffect == 1 // 启用
      },
      {
        id: 'enable',
        icon: '', // icon_list_enable
        permission: ['O_02_0510'],
        title: i18n.t('启用'),
        visibleCondition: (data) => data.takeEffect == 0 // 停用
      }
    ]
  },
  {
    field: 'configWay',
    headerText: i18n.t('配置方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('公司'),
        2: i18n.t('供应商'),
        12: '公司+供应商'
      }
    }
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司')
  },
  {
    field: 'supplierName',
    // width: "200",
    headerText: i18n.t('供应商')
  },
  {
    field: 'companyDrawer',
    // width: "200",
    headerText: i18n.t('公司-出票方合同'),
    valueConverter: {
      type: 'map',
      map: companyDrawerArr
    }
  },
  {
    field: 'drawerCompany',
    // width: "200",
    headerText: i18n.t('出票方-供应商合同'),
    valueConverter: {
      type: 'map',
      map: companyDrawerArr
    }
  },
  {
    field: 'printWay',
    // width: "200",
    headerText: i18n.t('打印汇总方式'),
    valueConverter: {
      type: 'map',
      map: {
        1: i18n.t('按单打印'),
        2: i18n.t('按天打印')
      }
    }
  },
  {
    field: 'orderAuto',
    // width: "200",
    headerText: i18n.t('按订单自动生成'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'autoIssue',
    // width: "200",
    headerText: i18n.t('自动发布'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'editTemplate',
    width: '300',
    headerText: i18n.t('编辑模版'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template:
            "<div class='editTemplate'><span v-for='(item,index) in editTemplateArr' @click='clickeditTemplate(item,index)' :key='index'>{{item.text}}</span></div>",
          data() {
            return {
              data: {},
              editTemplateArr: []
            }
          },
          mounted() {
            this.contractfunction(this.data.editTemplate)
          },
          methods: {
            contractfunction(editTemplate) {
              for (const key in companyDrawerArr) {
                if (editTemplate[0].text == key) {
                  editTemplate[0].text = companyDrawerArr[key]
                  editTemplate[0].value = key
                }
                if (editTemplate[1].text == key) {
                  editTemplate[1].text = companyDrawerArr[key]
                  editTemplate[1].value = key
                }
              }
              this.editTemplateArr = editTemplate.filter((item) => item.text != '')
              // console.log(this.editTemplateArr);
            },
            clickeditTemplate(item) {
              let parameter = {
                item: item,
                data: this.data
              }
              this.$parent.$emit('refreshList', parameter)
            }
          }
        })
      }
    }
  }
]
export const PAGE_PLUGIN = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      columnData: [],
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/contractPrintConfig/query',
        params: {},
        serializeList: (list) => {
          return list.map((item) => {
            return {
              ...item,
              editTemplate: [
                {
                  code: 'companyDrawer',
                  text: item.companyDrawer
                },
                {
                  code: 'drawerCompany',
                  text: item.drawerCompany
                }
              ]
            }
          })
        }
      }
    }
  }
]
