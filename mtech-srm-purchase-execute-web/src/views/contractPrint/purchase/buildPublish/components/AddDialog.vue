<template>
  <!-- @beforeClose="cancel" -->
  <mt-dialog
    ref="dialog"
    css-class="dialog-main"
    :buttons="buttons"
    :header="header"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <mt-form class="dialog-mt-form" ref="dialogRef" :model="formObject" :rules="formRules">
        <mt-form-item prop="configWay" :label="$t('配置方式')">
          <mt-select
            v-if="headStates == 'Add'"
            v-model="formObject.configWay"
            float-label-type="Never"
            :data-source="configWayArr"
            @change="changeconfigWay"
            :placeholder="$t('请选择配置方式')"
          ></mt-select>
          <mt-select
            v-else
            v-model="formObject.configWay"
            :data-source="configWayArr"
            :disabled="headStates == 'edit'"
            float-label-type="Never"
            :placeholder="$t('请选择配置方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="companyCode" :label="$t('公司名称')">
          <mt-select
            v-if="headStates == 'Add'"
            v-model="formObject.companyCode"
            float-label-type="Never"
            :data-source="companySelect"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :allow-filtering="true"
            :disabled="formObject.configWay == ''"
            :filtering="inputPersonnel"
            @change="changecompanySelect"
            :placeholder="$t('请选择公司')"
          ></mt-select>
          <mt-input
            v-else
            v-model="formObject.companyName"
            :disabled="headStates == 'edit'"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('供应商')">
          <mt-select
            v-if="headStates == 'Add'"
            v-model="formObject.supplierCode"
            float-label-type="Never"
            :data-source="siteSelect"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :allow-filtering="true"
            :disabled="weight <= 1 || formObject.configWay == '' || formObject.configWay == '1'"
            :filtering="inputPersonnelsupplier"
            @change="changesiteSelect"
            :placeholder="formObject.configWay == '1' ? '' : $t('请选择供应商')"
          ></mt-select>
          <mt-input
            v-else
            v-model="formObject.supplierName"
            float-label-type="Never"
            :disabled="weight <= 1"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="companyDrawer" :label="$t('公司-出票方合同')">
          <mt-select
            v-model="formObject.companyDrawer"
            float-label-type="Never"
            :show-clear-button="true"
            :data-source="companyDrawerArr"
            @change="changecompanyDrawer"
            :placeholder="$t('请选择公司-出票方合同')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="drawerCompany" :label="$t('出票方-供应商合同')">
          <mt-select
            v-model="formObject.drawerCompany"
            float-label-type="Never"
            :show-clear-button="true"
            :data-source="companyDrawerArr"
            @change="changedrawerCompany"
            :placeholder="$t('请选择出票方-供应商合同')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="printWay" :label="$t('打印汇总方式')">
          <mt-select
            v-model="formObject.printWay"
            float-label-type="Never"
            :data-source="printingCompany"
            @change="changeprintWay"
            :placeholder="$t('请选择打印汇总方式')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="orderAuto" :label="$t('按订单自动生成')">
          <mt-select
            v-model="formObject.orderAuto"
            float-label-type="Never"
            :data-source="orderAutoArr"
            @change="changeorderAuto"
            :placeholder="$t('是否按订单自动生成')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="autoIssue" :label="$t('自动发布')">
          <mt-select
            v-model="formObject.autoIssue"
            float-label-type="Never"
            :data-source="autoIssueArr"
            @change="changeautoIssue"
            :placeholder="$t('是否自动发布')"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  data() {
    return {
      //按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      //v-model获取的值
      formObject: {
        configWay: '', //配置方式
        companyCode: '', //公司编码
        companyName: '', //公司名称
        supplierCode: null, //供应商编码
        supplierName: null, //供应商名称
        companyDrawer: '', //公司-出票方合同
        drawerCompany: '', //出票方-供应商合同
        printWay: '', //打印汇总方式
        orderAuto: '', //按订单自动生成
        autoIssue: '' //自动发布
        // takeEffect: "", //生效
      },
      //必填项
      formRules: {
        configWay: [
          {
            required: true,
            message: this.$t('配置方式'),
            trigger: 'blur'
          }
        ],
        companyCode: [
          {
            required: true,
            message: this.$t('公司名称'),
            trigger: 'blur'
          }
        ],
        printWay: [
          {
            required: true,
            message: this.$t('打印汇总方式'),
            trigger: 'blur'
          }
        ],
        orderAuto: [
          {
            required: true,
            message: this.$t('按订单自动生成'),
            trigger: 'blur'
          }
        ],
        autoIssue: [
          {
            required: true,
            message: this.$t('自动发布'),
            trigger: 'blur'
          }
        ]
      },
      //配置方式
      configWayArr: [
        { text: this.$t('公司'), value: '1' },
        { text: this.$t('公司+供应商'), value: '12' }
      ],
      //公司下拉框
      companySelect: [],
      //工厂下拉框
      siteSelect: [],
      //公司-出票方合同  出票方-供应商合同
      companyDrawerArr: [],
      //打印总汇方式
      printingCompany: [
        { text: this.$t('按单打印'), value: '1' },
        { text: this.$t('按天打印'), value: '2' }
      ],
      //按订单自动生成
      orderAutoArr: [
        { text: this.$t('否'), value: '0' },
        { text: this.$t('是'), value: '1' }
      ],
      //按订单自动生成
      autoIssueArr: [
        { text: this.$t('否'), value: '0' },
        { text: this.$t('是'), value: '1' }
      ],
      // //按订单自动生成
      // takeEffectArr: [
      //   { text: this.$t("否"), value: "0" },
      //   { text: this.$t("是"), value: "1" },
      // ],
      weight: 0 //权重值判断那些下拉框可以编辑
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        buttons: this.buttons
      }
    },
    header() {
      return this.modalData.title
    },
    headStates() {
      return this.modalData.headStates
    },
    salesData() {
      return this.modalData.data
    }
  },
  mounted() {
    this.$refs['dialog'].ejsRef.show()
    this.inputPersonnel = utils.debounce(this.inputPersonnel, 1000)
    this.inputPersonnelsupplier = utils.debounce(this.inputPersonnelsupplier, 1000)
    if (this.headStates == 'Add') {
      this.initialCallInterface()
    } else if (this.headStates == 'edit') {
      this.editCallInterface()
    }
  },
  methods: {
    //新增调用接口
    initialCallInterface() {
      //公司
      let parameter = {
        fuzzyParam: '',
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.contractPrint.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })

      //选择合同
      this.$API.contractPrint.initContractPrint().then((res) => {
        console.log(res.data)
        // this.companyDrawerArr = res.data;
        for (const key in res.data) {
          console.log(res.data[key], key, 'key')
          this.companyDrawerArr.push({
            text: res.data[key],
            value: key
          })
        }
      })
    },
    //配置方式
    changeconfigWay(e) {
      console.log(e.value)
      this.formObject.configWay = e.value
      this.formObject.companyName = ''
      this.formObject.companyCode = ''
      this.formObject.supplierCode = null
      this.formObject.supplierName = null
    },
    //公司模糊查询事件
    inputPersonnel(e) {
      let parameter = {
        fuzzyParam: e.text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.contractPrint.findSpecifiedChildrenLevelOrgs(parameter).then((res) => {
        this.companySelect = res.data
      })
    },
    //公司下拉框事件
    changecompanySelect(e) {
      if (this.headStates == 'Add') {
        this.formObject.companyName = e.itemData.orgName
        this.formObject.companyCode = e.itemData.orgCode
        this.formObject.supplierCode = null
        this.formObject.supplierName = null
        if (this.formObject.configWay == '12') {
          let parameter = {
            organizationCode: e.itemData.orgCode,
            fuzzyNameOrCode: ''
          }
          this.weight = 2 //让供应商下拉框可以选择
          this.$API.contractPrint.criteriaQuery(parameter).then((res) => {
            this.siteSelect = res.data
          })
        }

        console.log(this.formObject, 'this.formObject')
      }
    },
    //供应商模糊查询
    inputPersonnelsupplier(e) {
      let data = {
        organizationCode: this.formObject.companyCode,
        fuzzyNameOrCode: e.text
      }
      this.$API.contractPrint.criteriaQuery(data).then((res) => {
        this.siteSelect = res.data
      })
    },
    //供应商下拉框事件
    changesiteSelect(e) {
      if (this.headStates == 'Add') {
        this.formObject.supplierCode = e.itemData.supplierCode
        this.formObject.supplierName = e.itemData.supplierName
      }
    },
    //公司-出票方合同
    changecompanyDrawer(e) {
      this.formObject.companyDrawer = e?.value ?? ''
      if (
        this.formObject.companyDrawer &&
        this.formObject.drawerCompany &&
        this.formObject.companyDrawer == this.formObject.drawerCompany
      ) {
        this.$toast({
          content: this.$t('公司-出票方合同、出票方-供应商合同不能选择同一模板'),
          type: 'warning'
        })
      }
    },
    //出票方-供应商合同下拉框事件
    changedrawerCompany(e) {
      this.formObject.drawerCompany = e?.value ?? ''
      if (
        this.formObject.companyDrawer &&
        this.formObject.drawerCompany &&
        this.formObject.companyDrawer == this.formObject.drawerCompany
      ) {
        this.$toast({
          content: this.$t('公司-出票方合同、出票方-供应商合同不能选择同一模板'),
          type: 'warning'
        })
      }
    },
    //打印汇总方式下拉框事件
    changeprintWay(e) {
      this.formObject.printWay = e?.value ?? ''
    },
    //按订单自动生成
    changeorderAuto(e) {
      console.log(e)
    },
    //自动发布
    changeautoIssue(e) {
      console.log(e)
    },
    //编辑回选
    editCallInterface() {
      let salesData = this.salesData[0]
      //选择合同
      this.$API.contractPrint.initContractPrint().then((res) => {
        for (const key in res.data) {
          console.log(res.data[key], key, 'key')
          this.companyDrawerArr.push({
            text: res.data[key],
            value: key
          })
        }
        this.formObject.companyDrawer = salesData.companyDrawer
        this.formObject.drawerCompany = salesData.drawerCompany
      })
      this.formObject.id = salesData.id
      this.formObject.companyName = salesData.companyName
      this.formObject.supplierName = salesData.supplierName
      this.formObject.configWay = String(salesData.configWay)
      this.formObject.printWay = String(salesData.printWay)
      this.formObject.orderAuto = String(salesData.orderAuto)
      this.formObject.autoIssue = String(salesData.autoIssue)
    },
    //点击确认
    confirm() {
      console.log(this.formObject)
      let formObject = this.formObject
      if (formObject.configWay == '') {
        this.$toast({
          content: this.$t('配置方式不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.companyName == '') {
        this.$toast({
          content: this.$t('公司名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.configWay != '1' && formObject.supplierName == null) {
        this.$toast({
          content: this.$t('供应商名称不能为空'),
          type: 'warning'
        })
        return
      }
      if (!formObject.companyDrawer && !formObject.drawerCompany) {
        this.$toast({
          content: this.$t('至少选择一个合同模板'),
          type: 'warning'
        })
        return
      }
      if (
        this.formObject.companyDrawer &&
        this.formObject.drawerCompany &&
        this.formObject.companyDrawer == this.formObject.drawerCompany
      ) {
        this.$toast({
          content: this.$t('公司-出票方合同、出票方-供应商合同不能选择同一模板'),
          type: 'warning'
        })
        return
      }
      if (formObject.printWay == '') {
        this.$toast({
          content: this.$t('打印汇总方式不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.orderAuto == '') {
        this.$toast({
          content: this.$t('按订单自动生成不能为空'),
          type: 'warning'
        })
        return
      }
      if (formObject.autoIssue == '') {
        this.$toast({
          content: this.$t('自动发布不能为空'),
          type: 'warning'
        })
        return
      }
      if (this.headStates == 'Add') {
        console.log(this.formObject, 'this.formObjectAdd')
        this.$API.contractPrint.saveAndUpdateData(this.formObject).then(() => {
          this.$emit('confirm-function')
        })
      }
      if (this.headStates == 'edit') {
        let parameter = {}
        parameter.id = this.formObject.id
        parameter.companyDrawer = this.formObject?.companyDrawer ?? ''
        parameter.drawerCompany = this.formObject?.drawerCompany ?? ''
        parameter.printWay = this.formObject.printWay
        parameter.orderAuto = this.formObject.orderAuto
        parameter.autoIssue = this.formObject.autoIssue
        console.log(parameter)
        this.$API.contractPrint.saveAndUpdateData(parameter).then(() => {
          this.$emit('confirm-function')
        })
      }
    },
    //点击取消
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss">
.dialog-main {
  .e-dlg-content {
    padding: 0;
    .dialog-content {
      padding: 40px;
      .mt-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .mt-form-item {
          width: 400px;
        }
      }
    }
  }
}
</style>
