<!--合同打印-->
<template>
  <div class="hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
// import { download, getHeadersFileName } from "@/utils/utils";
import Vue from 'vue'
export default {
  data() {
    return {
      pageConfig: pageConfig
    }
  },
  props: {},
  computed: {},
  mounted() {},
  methods: {
    // 刷新页面
    initialCallInterface() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: 'message/tenant/account/queryBuilder',
        params: {}
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 点击表头
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (_selectRows.length <= 0 && e.toolbar.id === 'Download') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },

    // 打印合同
    handleClickDownload(_selectRows) {
      let ids = []
      _selectRows.map((item) => {
        ids.push(item.id)
      })
      console.log(ids, 'ids')
      this.$API.contractPrint.printOrder(ids).then((res) => {
        console.log('=========', res)
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = function () {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
}
</script>

<style lang="scss">
.hander {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .template-height {
    flex: 1;
    .OfferBid-status0 {
      width: 56px;
      height: 20px;
      background: #f4f4f4;
      border-radius: 2px;
      padding: 4px;
      color: #9a9a9a;
    }
    .OfferBid-status1 {
      width: 56px;
      height: 20px;
      background: #eef2f9;
      border-radius: 2px;
      padding: 4px;
      color: #6386c1;
    }
    .OfferBid-status2 {
      width: 56px;
      height: 20px;
      background: #eef2f9;
      border-radius: 2px;
      padding: 4px;
      color: #6386c1;
    }
  }
}
</style>
