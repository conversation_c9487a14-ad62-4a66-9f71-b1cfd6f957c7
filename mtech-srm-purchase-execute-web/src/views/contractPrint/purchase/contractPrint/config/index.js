import { i18n } from '@/main.js'
import UTILS from '@/utils/utils'
const toolbar = [{ id: 'Download', icon: 'icon_solid_Download', title: i18n.t('合同打印') }]
// const map = [
//   {
//     status: '-1',
//     label: i18n.t('关闭'),
//     cssClass: ['OfferBid-status0']
//   },
//   {
//     status: 1,
//     label: i18n.t('未发布'),
//     cssClass: ['OfferBid-status0']
//   },
//   {
//     status: 2,
//     label: i18n.t('发布待确认'),
//     cssClass: ['OfferBid-status1']
//   },
//   {
//     status: 3,
//     label: i18n.t('反馈正常'),
//     cssClass: ['OfferBid-status0']
//   },
//   {
//     status: 4,
//     label: i18n.t('反馈异常'),
//     cssClass: ['OfferBid-status0']
//   }
// ]
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'showStatus',
    headerText: i18n.t('状态')
    // valueConverter: {
    //   type: "map",
    //   map: map,
    //   fields: { text: "label", value: "status" },
    // },
  },
  {
    field: 'urgentStatus',
    // width: "200",
    headerText: i18n.t('加急状态'),
    valueConverter: {
      type: 'map',
      map: { 0: i18n.t('未加急'), 1: i18n.t('加急') }
    }
  },
  {
    field: 'orderCode',
    // width: "200",
    headerText: i18n.t('采购订单号')
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('采购订单类型')
  },
  {
    field: 'supplierName',
    // width: "200",
    headerText: i18n.t('供应商')
  },
  {
    field: 'supplierAdd',
    // width: "200",
    headerText: i18n.t('供应商地点')
  },
  {
    field: 'buyerGroupName',
    // width: "200",
    headerText: i18n.t('采购组织')
  },
  {
    field: 'buyerOrgName',
    // width: "200",
    headerText: i18n.t('采购组')
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司')
  },
  {
    field: 'urgentTime',
    // width: "200",
    headerText: i18n.t('加急时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length >= 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  }
]
export const pageConfig = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      height: 'auto',
      columnData,
      // dataSource: []
      asyncConfig: {
        url: '/srm-purchase-execute/tenant/purOrder/query'
        // params: {},
      }
    }
  }
]
