import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'
import { judgeFormatCodeName } from '@/utils/utils'

export const statusOptions = [
  { value: 1, text: i18n.t('待确认'), cssClass: 'col-abnormal' },
  { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已退回'), cssClass: 'col-normal' },
  { value: 4, text: i18n.t('已作废'), cssClass: 'col-published' }
]

export const recoTypeOptions = [
  { value: 'B', text: i18n.t('寄售对账单'), cssClass: '' },
  { value: 'D', text: i18n.t('寄售追溯对账单'), cssClass: '' }
]

export const columnData = [
  {
    width: '200',
    field: 'contractCode',
    headerText: i18n.t('合同编码')
  },
  {
    width: '200',
    field: 'recoCode',
    headerText: i18n.t('对账单号'),
    cellTools: []
  },
  {
    width: '160',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      //0-已确认，1. 已作废，2. 已退回，3. 未确认，-1. 已关闭
      map: statusOptions
    }
    // cellTools: [
    //   {
    //     id: 'close',
    //     icon: 'icon_list_close',
    //     title: i18n.t('关闭'),
    //     permission: ['O_02_0398'],
    //     visibleCondition: (data) => {
    //       return (
    //         data.sourcePath == 0 && data.status >= 0 && data.status !== 1 && data.invoiceStatus == 0
    //       )
    //     }
    //   },
    //   {
    //     id: 'publish',
    //     icon: 'icon_list_issue',
    //     title: i18n.t('发布'),
    //     permission: ['O_02_0396'],
    //     visibleCondition: (data) => {
    //       return data.sourcePath == 0 && data.status == 0
    //     }
    //   },
    //   {
    //     id: 'exceptionHandle',
    //     icon: 'icon_list_issue',
    //     title: i18n.t('异常处理'),
    //     permission: ['O_02_0396'], // 异常处理
    //     visibleCondition: (data) => {
    //       return data.sourcePath == 0 && data.status != 3
    //     }
    //   }
    //   // {
    //   //   id: "cancelPublish",
    //   //   icon: "icon_table_cancel",
    //   //   title: i18n.t("取消发布"),
    //   //   permission: ["O_02_0400"],
    //   //   visibleCondition: (data) => {
    //   //     return data.sourcePath == 0 && data.status == 1;
    //   //   },
    //   // },
    // ]
  },
  // {
  //   // width: "150",
  //   field: "companyCode",
  //   headerText: i18n.t("公司编号"),
  // },
  {
    width: '250',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: MasterDataSelect.businessCompany,
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.companyCode, data?.companyName)
    }
  },
  // {
  //   width: "230",
  //   field: "supplierCode",
  //   headerText: i18n.t("供应商编号"),
  // },
  {
    width: '250',
    field: 'supplierCode',
    headerText: i18n.t('供应商'),
    valueAccessor: (field, data) => {
      return judgeFormatCodeName(data?.supplierCode, data?.supplierName)
    }
  },
  {
    field: 'recoTypeCode',
    headerText: i18n.t('对账类型'),
    valueConverter: {
      type: 'map',
      map: recoTypeOptions
    }
  },
  {
    field: 'materialVoucherYear',
    // width: "200",
    headerText: i18n.t('对账日期')
  },
  {
    // width: "150",
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    // width: "150",
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    searchOptions: MasterDataSelect.dateRange
  }
]
