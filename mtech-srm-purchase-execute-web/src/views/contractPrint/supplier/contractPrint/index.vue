<!--供方合同打印-->
<!--<template>
  <div>{{ $t('供方合同打印') }}</div>
</template> -->
<!--
 * @Author: your name
 * @Date: 2021-09-29 09:53:32
 * @LastEditTime: 2022-01-11 09:56:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\views\material\MaterialSite.vue
-->
<template>
  <div class="full-height">
    <div>
      <mt-template-page
        ref="tepPage"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @handleCustomSearch="search"
        @handleCustomReset="resetForm"
        @handleClickToolBar="handleClickToolBar"
      >
        <template v-slot:quick-search-form>
          <div class="custom-form-box">
            <mt-form ref="searchFormRef" :model="searchFormModel">
              <mt-form-item prop="poNumber" :label="$t('订单号')" label-style="top">
                <mt-input
                  v-model="searchFormModel.poNumber"
                  :show-clear-button="true"
                  :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
                />
              </mt-form-item>
              <mt-form-item prop="createTime" :label="$t('订单凭证日期')" label-style="top">
                <mt-date-range-picker
                  v-model="searchFormModel.createTime"
                  :show-clear-button="true"
                  :placeholder="$t('请选择')"
                  @change="(e) => createTimeChange(e, 'createTime')"
                />
              </mt-form-item>
              <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
                <RemoteAutocomplete
                  style="flex: 1"
                  v-model="searchFormModel.companyCode"
                  url="/masterDataManagement/auth/company/auth-fuzzy"
                  multiple
                  :params="{
                    organizationLevelCodes: ['ORG02', 'ORG01'],
                    orgType: 'ORG001PRO',
                    includeItself: true
                  }"
                  :placeholder="$t('请选择公司')"
                  :fields="{ text: 'orgName', value: 'orgCode' }"
                  records-position="data"
                ></RemoteAutocomplete>
              </mt-form-item>
              <!-- <mt-form-item prop="orderTypeName" :label="$t('订单类型')" label-style="top">
                <mt-input
                  v-model="searchFormModel.orderTypeName"
                  :placeholder="$t('请输入订单类型')"
                  :show-clear-button="true"
                />
              </mt-form-item>
              <mt-form-item prop="urgentStatus" :label="$t('加急状态')" label-style="top">
                <mt-select
                  v-model="searchFormModel.urgentStatus"
                  :show-clear-button="true"
                  :data-source="urgentStatusList"
                  :placeholder="$t('请输入加急状态')"
                />
              </mt-form-item> -->
            </mt-form>
          </div>
        </template>
      </mt-template-page>
    </div>
    <mt-page
      class="full-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totals"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
    />
  </div>
</template>

<script>
import { PAGE_PLUGIN } from './config/index'
import Vue from 'vue'
import dayjs from 'dayjs'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      pageSettings: {
        current: 1,
        pageCount: 5,
        totals: 0,
        totalRecordsCount: 0,
        pageSize: 10,
        pageSizes: [10, 20, 50, 100, 200]
      },
      urgentStatusList: [
        { text: this.$t('未加急'), value: 0 },
        { text: this.$t('加急'), value: 1 }
      ],
      pageConfig: PAGE_PLUGIN,
      newJITData: [], //选择完JIT以后的数组
      searchFormModel: {
        // 订单号
        poNumber: '',
        createTime: '',
        // 公司
        companyCode: [],
        // 订单类型
        orderTypeName: '',
        // 加急状态
        urgentStatus: ''
      }
    }
  },
  computed: {},
  mounted() {},
  methods: {
    // 查询
    search() {
      this.pageSettings.current = 1
      this.handleSearch()
    },
    // 分页查询
    currentChange(currentPage) {
      this.pageSettings.current = currentPage
      this.handleSearch()
    },
    // 页大小切换查询
    sizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleSearch()
    },
    // 查询
    handleSearch() {
      this.$store.commit('startLoading')
      const param = {
        ...this.searchFormModel,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (!param.companyCode) {
        param.companyCode = []
      }
      this.$API.contract
        .supplierContrantPrint(param)
        .then((res) => {
          this.$set(this.pageConfig[0].grid, 'dataSource', res.data.records)
          this.pageSettings.totals = Math.ceil(Number(res.data.total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = res.data.total
        })
        .catch((err) => {
          this.$toast({
            content: err.msg,
            type: 'error'
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 重置表单
    resetForm() {
      for (let key in this.searchFormModel) {
        this.searchFormModel[key] = null
      }
    },
    // 创建日期选择
    createTimeChange(e, prefix) {
      if (e.startDate) {
        this.searchFormModel[prefix + 'S'] = dayjs(e.startDate).valueOf()
        this.searchFormModel[prefix + 'E'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchFormModel[prefix + 'S'] = null
        this.searchFormModel[prefix + 'E'] = null
      }
    },
    handleClickToolBar(e) {
      const _selectRows = e.gridRef.getMtechGridRecords()
      if (_selectRows.length <= 0 && e.toolbar.id == 'Download') {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      //合同打印
      if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      } else if (e.toolbar.id === 'printEnZh') {
        this.handleClickDownload(_selectRows, 'En')
      }
    },
    //合同打印
    handleClickDownload(_selectRows, flag) {
      let ids = []
      _selectRows.map((item) => {
        ids.push(item.id)
      })
      const _API =
        flag && flag === 'En'
          ? this.$API.contractPrint.supPrintOrderEnZh
          : this.$API.contractPrint.supPrintOrderContract
      _API(ids).then((res) => {
        console.log(res, '供方合同打印')
        if (res?.data?.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(res?.data, 'utf-8')
          reader.onload = function () {
            console.log('======', reader)
            const readerRes = reader.result
            const resObj = JSON.parse(readerRes)
            Vue.prototype.$toast({
              content: resObj.msg,
              type: 'error'
            })
          }

          return
        }
        const content = res.data
        let pdfUrl = window.URL.createObjectURL(new Blob([content], { type: 'application/pdf' }))

        let date = new Date().getTime()
        let ifr = document.createElement('iframe')
        ifr.style.frameborder = 'no'
        ifr.style.display = 'none'
        ifr.style.pageBreakBefore = 'always'
        ifr.setAttribute('id', 'printPdf' + date)
        ifr.setAttribute('name', 'printPdf' + date)
        ifr.src = pdfUrl
        document.body.appendChild(ifr)
        this.doPrint('printPdf' + date)
        window.URL.revokeObjectURL(ifr.src)
      })
    },
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
        // this.pdfLoading = false;
      }, 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-subtitle {
  border-left: 3px solid #00469c;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #2f353c;
  padding-left: 10px;
}
</style>
