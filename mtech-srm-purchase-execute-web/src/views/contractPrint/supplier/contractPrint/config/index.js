import { i18n } from '@/main.js'
import UTILS from '@/utils/utils'
const toolbar = {
  tools: [
    [
      { id: 'Download', icon: 'icon_solid_Createorder', title: i18n.t('打印中文合同') },
      { id: 'printEnZh', icon: 'icon_slid_print', title: i18n.t('打印英文合同') }
    ],
    []
  ]
}
const columnData = [
  {
    width: '50',
    type: 'checkbox'
  },
  {
    field: 'contractNo',
    headerText: i18n.t('合同编码')
  },
  // 单据状态：2-审批通过；4-关闭；5-完成；
  // {
  //   field: 'status',
  //   headerText: i18n.t('状态'),
  //   valueConverter: {
  //     type: 'map',
  //     map: { 2: i18n.t('审批通过'), 4: i18n.t('关闭'), 5: i18n.t('完成') }
  //   }
  // },
  // {
  //   field: 'urgentStatus',
  //   // width: "200",
  //   headerText: i18n.t('加急状态'),
  //   valueConverter: {
  //     type: 'map',
  //     map: { 0: i18n.t('未加急'), 1: i18n.t('加急') }
  //   }
  // },
  {
    field: 'orderCode',
    // width: "200",
    headerText: i18n.t('订单号')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    // width: "200",
    headerText: i18n.t('供应商名称')
  },
  // {
  //   field: 'supplierAdd',
  //   // width: "200",
  //   headerText: i18n.t('供应商地点')
  // },
  {
    field: 'updateTime',
    headerText: i18n.t('更新日期'),
    template: () => {
      const template = {
        template: `<span>{{ data.updateTime | transformValue }}</span>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transformValue(val) {
            if (val && val.length === 13) {
              return UTILS.formateTime(new Date(Number(val)), 'YYYY-mm-dd HH:MM:SS')
            }
            return ''
          }
        }
      }
      return { template }
    }
  },
  {
    field: 'orderTime',
    headerText: i18n.t('订单凭证日期'),
    template: function () {
      const ItemTemplate = {
        template: `<div>{{ data.orderTime | transformValue }}</div>`,
        data() {
          return {
            data: {}
          }
        },
        filters: {
          transformValue(val) {
            if (val && val.length === 13) {
              return UTILS.formateTime(new Date(Number(val)), 'YYYY-mm-dd')
            }
            return ''
          }
        },
        methods: {}
      }
      return { template: ItemTemplate }
    }
  },
  {
    field: 'orderTypeName',
    headerText: i18n.t('订单类型')
  },
  {
    field: 'buyerGroupName',
    // width: "200",
    headerText: i18n.t('采购组织')
  },
  {
    field: 'buyerOrgName',
    // width: "200",
    headerText: i18n.t('采购组')
    // searchOptions: MasterDataSelect.businessGroupIn,
  },
  {
    field: 'companyName',
    // width: "200",
    headerText: i18n.t('公司'),
    searchOptions: {
      renameField: 'companyCode'
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('业务实体')
  },
  {
    field: 'urgentTime',
    // width: "200",
    headerText: i18n.t('加急时间'),
    valueConverter: {
      type: 'function',
      filter: (e) => {
        if (e && e.length >= 13) {
          e = Number(e)
          return UTILS.dateFormat(e, 'Y-m-d')
        } else {
          return ''
        }
      }
    }
  }
]
export const PAGE_PLUGIN = [
  {
    toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    isUseCustomSearch: true,
    isCustomSearchHandle: true,
    grid: {
      allowPaging: false,
      columnData
      // asyncConfig: {
      //   url: '/srm-purchase-execute/tenant/supOrder/query',
      //   params: {}
      // }
    }
  }
]
