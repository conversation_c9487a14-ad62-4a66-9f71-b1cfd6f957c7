<template>
  <!-- 采方-对账协同-采购对账单列表 -->
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickToolBar="toolbarClick"
      @handleCustomReset="handleCustomReset"
      @handleClickCellTitle="handleClickCellTitle"
      class="template-height"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <mt-form ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="orderCode" :label="$t('订单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.orderCode"
                :show-clear-button="true"
                :placeholder="$t('支持粘贴多个精准及单个右模糊查询')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('公司')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.companyCode"
                url="/masterDataManagement/auth/company/auth-fuzzy"
                multiple
                :show-select-all="true"
                :popup-width="'280'"
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                :placeholder="$t('请选择公司')"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item v-if="!isSup" prop="supplierCode" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                style="flex: 1"
                v-model="searchFormModel.supplierCode"
                url="/masterDataManagement/tenant/supplier/queryScreen"
                multiple
                :show-select-all="true"
                :popup-width="'280'"
                :placeholder="$t('请选择供应商')"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                records-position="data"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="createTime" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.createTime"
                @change="(e) => handleDateTimeChange(e, 'createTime')"
                :placeholder="$t('请选择创建日期')"
              />
            </mt-form-item>
            <mt-form-item prop="publishDate" :label="$t('发布日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.materialVoucherYear"
                @change="(e) => handleDateTimeChange(e, 'publishDate')"
                :placeholder="$t('请选择发布日期')"
              />
            </mt-form-item>
            <mt-form-item prop="confirmDate" :label="$t('确认日期')" label-style="top">
              <mt-date-range-picker
                style="flex: 1"
                v-model="searchFormModel.materialVoucherYear"
                @change="(e) => handleDateTimeChange(e, 'confirmDate')"
                :placeholder="$t('请选择确认日期')"
              />
            </mt-form-item>
            <mt-form-item v-if="!isSup" prop="orderType" :label="$t('订单类型')" label-style="top">
              <mt-multi-select
                style="flex: 1"
                :show-clear-button="false"
                :popup-width="230"
                v-model="searchFormModel.orderTypeCode"
                :allow-filtering="true"
                filter-type="Contains"
                :data-source="orderTypeList"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
            <mt-form-item v-if="!isSup" prop="status" :label="$t('屏采订单状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.status"
                :show-clear-button="true"
                :data-source="orderStatusList"
                :placeholder="$t('请输入加急状态')"
              />
            </mt-form-item>
            <!-- <mt-form-item prop="urgentStatus" :label="$t('加急状态')" label-style="top">
              <mt-select
                v-model="searchFormModel.urgentStatus"
                :show-clear-button="true"
                :data-source="urgentStatusList"
                :placeholder="$t('请输入加急状态')"
              />
            </mt-form-item> -->
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { constantColumnData, errorInfoColumn } from './config/index.js'
import { BASE_TENANT } from '@/utils/constant'
// import { download, getHeadersFileName } from '@/utils/utils'
export default {
  provide() {
    return {
      searchFormModelList: [this.searchFormModel]
    }
  },
  data() {
    return {
      searchFormModel: {},
      urgentStatusList: [
        { text: this.$t('未加急'), value: 0 },
        { text: this.$t('加急'), value: 1 }
      ],
      orderStatusList: [
        { text: this.$t('发布待确认'), value: 0 },
        { text: this.$t('反馈正常'), value: 1 },
        { text: this.$t('反馈异常'), value: 2 }
      ],
      orderTypeList: [
        { text: this.$t('T1-内销标准采购订单'), value: 'T1' },
        { text: this.$t('T2-外销标准采购订单'), value: 'T2' },
        { text: this.$t('T4-显示器件类采购订单'), value: 'T4' },
        { text: this.$t('TC-香港第三方直送采购订单'), value: 'TC' }
      ],
      pageConfig: [],
      isSup: false
    }
  },
  created() {
    let columnData = []
    if (this.$route.name === 'supplier-screenDemandPrint') {
      this.isSup = true
      columnData = constantColumnData(this.isSup)
      this.pageConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: {
            tools: [
              [
                {
                  id: 'batchDownload',
                  icon: 'icon_solid_export',
                  title: this.$t('批量下载')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/screen/supOrder/printQuery`,
              recordsPosition: 'data.records',
              params: {}
            },
            lineSelection: 0,
            // lineIndex: 1,
            frozenColumns: 1
          }
        }
      ]
    } else {
      columnData = constantColumnData(this.isSup).concat(errorInfoColumn)
      this.pageConfig = [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          isUseCustomSearch: true,
          isCustomSearchRules: true,
          toolbar: {
            tools: [
              [
                {
                  id: 'reborn',
                  icon: 'icon_solid_Refresh',
                  title: this.$t('重新生成PDF')
                },
                {
                  id: 'batchDownload',
                  icon: 'icon_solid_export',
                  title: this.$t('批量下载')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            columnData,
            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/screen/buyerOder/printQueryScreen`,
              recordsPosition: 'data.records',
              params: {}
            },
            lineSelection: 0,
            // lineIndex: 1,
            frozenColumns: 7
          }
        }
      ]
    }
  },
  methods: {
    handleClickCellTitle(e) {
      if (e.field === 'recoCode') {
        // 将信息放到 localStorage
        // localStorage.setItem('statementTvData', JSON.stringify(e.data))
        // this.$router.push(`/purchase-execute/pur-query-statement-detail-tv?id=${e.data.id}&type=1`)
      }
    },
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        this.searchFormModel[field + 'S'] = new Date(e.startDate).getTime()
        this.searchFormModel[field + 'E'] = new Date(e.endDate).getTime()
      } else {
        this.searchFormModel[field + 'S'] = null
        this.searchFormModel[field + 'E'] = null
      }
    },
    toolbarClick(e) {
      const getMtechGridRecords = e.gridRef.getMtechGridRecords()
      if (getMtechGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // if (getMtechGridRecords.length != 1 && e.toolbar.id == 'batchDownload') {
      //   this.$toast({ content: this.$t('仅支持单行打印'), type: 'warning' })
      //   return
      // }
      const idList = e.gridRef.getMtechGridRecords().map((i) => i.id)
      if (e.toolbar.id == 'batchDownload') {
        // 批量下载
        this.handlePrint(idList)
      } else if (e.toolbar.id === 'reborn') {
        // 重新生成
        this.handleReborn(idList)
      }
    },
    // 重新生成
    handleReborn(idList) {
      this.$API.contractPrint.screenGeneratePdf(idList).then((res) => {
        const { code } = res
        if (code === 200) {
          this.$toast({
            content: this.$t('重新生成成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 下载
    handlePrint(idList) {
      this.$store.commit('startLoading')
      this.getDownloadApi()(idList).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    getDownloadApi() {
      if (!this.isSup) {
        return this.$API.contractPrint.batchDownload
      }
      return this.$API.contractPrint.batchDownloadSup
    },
    // 重置查询条件
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
    }
  }
}
</script>

<style></style>
