import { i18n } from '@/main.js'
import Vue from 'vue'
import dayjs from 'dayjs'

export const statusOptions = [
  { value: 1, text: i18n.t('待确认'), cssClass: 'col-abnormal' },
  { value: 2, text: i18n.t('已确认'), cssClass: 'col-active' },
  { value: 3, text: i18n.t('已退回'), cssClass: 'col-normal' },
  { value: 4, text: i18n.t('已作废'), cssClass: 'col-published' }
]

export const constantColumnData = (isSup) => [
  {
    width: '100',
    field: 'showStatus',
    headerText: i18n.t('状态')
    // valueConverter: {
    //   type: 'map',
    //   //0-已确认，1. 已作废，2. 已退回，3. 未确认，-1. 已关闭
    //   map: statusOptions
    // }
  },
  {
    width: '100',
    field: 'version',
    headerText: i18n.t('发布状态'),
    template: () => {
      return {
        template: Vue.component('showStatus', {
          template: `
            <div class="approve-config-box">
              {{orderTypeName}}
            </div>`,
          data: function () {
            return { data: {} }
          },
          computed: {
            orderTypeName() {
              let str = i18n.t('新订单')
              // if (this.data.version === 1) {
              //   str = i18n.t('新订单')
              // }
              if (this.data.version > 1) {
                str = i18n.t('订单变更')
              }
              return str
            }
          }
        })
      }
    }
  },
  {
    width: '170',
    field: 'orderTypeName',
    headerText: i18n.t('订单类型'),
    visible: !isSup
    // valueConverter: {
    //   type: 'map',
    //   //0-已确认，1. 已作废，2. 已退回，3. 未确认，-1. 已关闭
    //   map: statusOptions
    // }
  },
  {
    width: '100',
    field: 'orderCode',
    headerText: i18n.t('订单号')
  },
  {
    width: '120',
    field: 'supplierCode',
    headerText: i18n.t('供应商代码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  // {
  //   width: '250',
  //   field: 'supplierAdd',
  //   headerText: i18n.t('供应商地点')
  // },
  {
    field: 'orderTime',
    width: '100',
    headerText: i18n.t('创建日期'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field]) {
                return dayjs(Number(this.data[this.data.column.field])).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    field: 'buyerUserName',
    width: '120',
    headerText: i18n.t('创建人')
  },
  {
    field: 'publishTime',
    width: '100',
    headerText: i18n.t('发布日期'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field]) {
                return dayjs(Number(this.data[this.data.column.field])).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    width: '100',
    field: 'buyerOrgCode',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('业务实体')
  },
  {
    width: '100',
    field: 'urgentTime',
    headerText: i18n.t('加急时间'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `<span>{{ getTimeFmt() }}</span>`,
          methods: {
            getTimeFmt() {
              if (this.data[this.data.column.field] && this.data[this.data.column.field] !== '0') {
                return dayjs(Number(this.data[this.data.column.field])).format('YYYY-MM-DD')
              }
              return ''
            }
          }
        })
      }
    }
  },
  {
    width: '120',
    field: 'supplierContractUrl',
    headerText: i18n.t('供应商PDF'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div>
                <span v-if="data.supplierContractId && data.supplierContractId !== '0'" style="cursor: pointer;color: #00469c; margin-right: 5px;" @click="viewOA">{{$t('预览')}}</span>
                <span v-else style="cursor: not-allowed;color: #888; margin-right: 5px;">{{$t('预览')}}</span>
                <span v-if="data.supplierContractUrl" style="cursor: pointer;color: #00469c;" @click="downloadOA">{{$t('下载')}}</span>
                <span v-else style="cursor: not-allowed;color: #888;">{{$t('下载')}}</span>
              </div>
            `,
          methods: {
            viewOA() {
              if (this.data.supplierContractId) {
                let params = {
                  id: this.data.supplierContractId,
                  useType: 2
                }
                this.$API.fileService.getMtPreviewPub(params).then((res) => {
                  window.open(`${res.data}`)
                })
              }
            },
            downloadOA() {
              if (this.data.supplierContractUrl) {
                window.open(this.data.supplierContractUrl)
              }
            }
          }
        })
      }
    }
  },
  {
    width: '120',
    field: 'saleContractUrl',
    headerText: i18n.t('加点合同pdf'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div>
                <span v-if="data.saleContractId && data.saleContractId !== '0'" style="cursor: pointer;color: #00469c; margin-right: 5px;" @click="viewOA">{{$t('预览')}}</span>
                <span v-else style="cursor: not-allowed;color: #888; margin-right: 5px;">{{$t('预览')}}</span>
                <span v-if="data.saleContractUrl" style="cursor: pointer;color: #00469c;" @click="downloadOA">{{$t('下载')}}</span>
                <span v-else style="cursor: not-allowed;color: #888;">{{$t('下载')}}</span>
              </div>
            `,
          methods: {
            viewOA() {
              if (this.data.saleContractId) {
                let params = {
                  id: this.data.saleContractId,
                  useType: 2
                }
                this.$API.fileService.getMtPreviewPub(params).then((res) => {
                  window.open(`${res.data}`)
                })
              }
            },
            downloadOA() {
              if (this.data.saleContractUrl) {
                window.open(this.data.saleContractUrl)
              }
            }
          }
        })
      }
    }
  }
]

export const errorInfoColumn = [
  {
    width: '200',
    field: 'generatePdfErrorInfo',
    headerText: i18n.t('未生成PDF错误信息')
  }
]
