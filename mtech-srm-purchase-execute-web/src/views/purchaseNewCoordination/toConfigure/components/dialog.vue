<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="companyCode" :label="$t('采方公司')">
        <mt-select
          v-model="formData.companyCode"
          :data-source="companyOptions"
          :disabled="isDisabled"
          :show-clear-button="false"
          :fields="{ text: 'label', value: 'orgCode' }"
          :placeholder="$t('请选择业务公司')"
          @change="handleCompanyChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="siteCode" :label="$t('工厂')">
        <mt-select
          v-model="formData.siteCode"
          :data-source="factoryOptions"
          :disabled="isDisabled"
          :show-clear-button="false"
          :fields="{ text: 'label', value: 'siteCode' }"
          :placeholder="$t('請输入工厂编码或者是名称')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseType" :label="$t('VMI仓库类型')">
        <mt-select
          v-model="formData.vmiWarehouseType"
          :data-source="[{ text: $t('第三方物流'), value: 0 }]"
          :show-clear-button="false"
          :disabled="true"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t('請选择VMI仓库类型')"
        ></mt-select>
      </mt-form-item>
      <template>
        <mt-form-item prop="thirdPartyLogisticsCode" :label="$t('钢材供应商公司')">
          <mt-select
            v-model="formData.thirdPartyLogisticsCode"
            :data-source="supplierThirdOptions"
            :show-clear-button="false"
            :disabled="isDisabled"
            :allow-filtering="true"
            @change="thirdPartyLogisticsCodeChange"
            :filtering="getThirdParty"
            :fields="{ text: 'supplierName', value: 'supplierCode' }"
            :placeholder="$t('请选择钢材供应商公司')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓编码')">
          <mt-select
            v-model="formData.vmiWarehouseCode"
            :data-source="vmiHouseOptions"
            @change="vmiWarehouseClick"
            :disabled="isDisabled"
            :show-clear-button="false"
            :fields="{ text: 'vmiWarehouseCode', value: 'vmiWarehouseCode' }"
            :placeholder="$t('请输入VMI编码或名称')"
            :filtering="getVmiHouse"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓名称')">
          <mt-input v-model="formData.vmiWarehouseName" :disabled="true"></mt-input>
        </mt-form-item>
        <!-- <mt-form-item prop="needItem" :label="$t('是否管理物料')">
          <mt-select
            v-model="formData.needItem"
            :data-source="operateTypeOptions"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="goodsReceiptType" :label="$t('VMI入库类型')">
          <mt-select
            v-model="formData.goodsReceiptType"
            :data-source="storageTypeOptions"
            :show-clear-button="true"
            :multiline="false"
            :placeholder="$t('请输入VMI入库类型')"
          ></mt-select>
        </mt-form-item> -->
        <!-- <mt-form-item prop="advanceTime" :label="$t('入库提前天数')">
          <mt-input-number
            v-model="formData.advanceTime"
            :show-clear-button="true"
            :multiline="false"
            :placeholder="$t('请输入入库提前天数')"
          ></mt-input-number>
        </mt-form-item> -->
      </template>
      <!-- <template v-else-if="formData.vmiWarehouseType === 1">
        <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓编码')">
          <mt-select
            v-model="formData.vmiWarehouseCode"
            :data-source="vmiHouseOptions"
            :show-clear-button="false"
            :fields="{ text: 'vmiWarehouseCode', value: 'vmiWarehouseCode' }"
            :placeholder="$t('请输入VMI编码或名称')"
            :filtering="getVmiHouse"
            :allowFiltering="true"
            :showClearButton="true"
          ></mt-select>
        </mt-form-item>
      </template> -->
    </mt-form>
  </mt-dialog>
</template>

<script>
import { utils } from '@mtech-common/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      operateTypeOptions: [
        // { text: this.$t("定时发布"), value: "0" },
        { text: this.$t('是'), value: 1 },
        { text: this.$t('否'), value: 0 }
      ], // 定时发布/反馈 下拉数据
      storageTypeOptions: [],
      dialogTitle: this.$t('新建'),
      rules: {
        companyCode: [
          {
            required: true,
            message: this.$t('请选择采方公司'),
            trigger: 'blur'
          }
        ],
        siteCode: [{ required: true, message: this.$t('请输入工厂'), trigger: 'blur' }],
        vmiWarehouseType: [
          {
            required: true,
            message: this.$t('請选择VMI仓库类型'),
            trigger: 'blur'
          }
        ],
        thirdPartyLogisticsCode: [
          {
            required: true,
            message: this.$t('请选择钢材供应商公司'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请输入VMI仓库名称'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应商编码或者名称'),
            trigger: 'blur'
          }
        ],
        needItem: [
          {
            required: true,
            message: this.$t('请选择是否管理物料'),
            trigger: 'blur'
          }
        ],
        goodsReceiptType: [
          {
            required: true,
            message: this.$t('请选择VMI送货类型'),
            trigger: 'blur'
          }
        ],
        advanceTime: [
          {
            required: true,
            message: this.$t('请输入提前送货天数'),
            trigger: 'blur'
          }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      isDisabled: false,
      formData: {
        advanceTime: 0,
        buyerTenantId: 0,
        companyCode: '',
        vmiWarehouseType: 0,
        companyName: '',
        goodsReceiptType: '',
        goodsReceiptTypeDesc: '',
        needBatch: 0,
        needItem: 0,
        processorTenantId: 0,
        siteCode: '',
        siteName: '',
        status: 1, //永远有效 和后端约定
        supplierCode: null,
        supplierName: '',
        thirdPartyLogisticsCode: null,
        thirdPartyLogisticsName: '',
        vmiWarehouseCode: '',
        vmiWarehouseName: ''
      },
      factoryId: '',
      companyId: '',
      supplierThird: '',
      supplier: '',
      warehouseCode: '',
      companyOptions: [],
      factoryOptions: [],
      vmiHouseOptions: [],
      supplierThirdOptions: [],
      supplierOptions: [],
      FactoryParam: { parentId: '', tenantId: '' }
    }
  },
  mounted() {
    console.log()
    this.$refs.dialog.ejsRef.show()
    this.getCompanyOptions()
    this.getSupplier()
    this.getThirdParty()
    this.getVmiHouse()
    this.getStorageTypeOptions()
    this.getVmiHouse = utils.debounce(this.getVmiHouse, 300)
    if (this.modalData?.row) {
      this.formData = {}
      this.formData = this.modalData.row
      this.isDisabled = true
      this.getVMICode()
    }
  },
  methods: {
    getVMICode() {
      this.$API.newVmi.findByCode({ code: this.formData.vmiWarehouseCode }).then((res) => {
        this.formData.vmiWarehouseType = res.data.vmiWarehouseType
        this.getVmiHouse()
      })
    },
    vmiWarehouseClick(e) {
      this.formData.vmiWarehouseName = e.itemData.vmiWarehouseName
    },
    // 获取入库类型数据
    getStorageTypeOptions() {
      this.$API.masterData
        .getDictCode({
          dictCode: 'vmiGoodsReceiptType'
        })
        .then((res) => {
          this.storageTypeOptions = res.data.map((item) => ({
            text: item.itemName,
            value: item.itemCode
          }))
        })
    },
    getCompanyOptions() {
      //查询业务公司下拉数据
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          console.log(res.data, 'res.datares.data')
          res.data.forEach((item) => {
            item.label = item.orgCode + `-` + item.orgName
          })
          this.companyOptions = res.data
        })
    },
    getFactoryOptions(params) {
      if (!params) return
      //查询业务公司下拉数据
      this.$API.masterData.findSiteInfoByParentId(params).then((res) => {
        console.log(res.data, '-=-=-=')
        res.data.forEach((item) => {
          item.label = item.siteCode + '-' + item.siteName
        })
        this.factoryOptions = res.data
      })
    },
    getThirdParty(e) {
      this.$API.masterData.getSupplier({ fuzzyNameOrCode: e?.text }).then((res) => {
        let data = res.data.filter((item) => item.supplierName)
        this.supplierThirdOptions = data
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(data)
          }
        })
      })
    },
    // 获取供应商下拉
    getSupplier(e) {
      this.$API.masterData.getSupplier({ fuzzyNameOrCode: e?.text }).then((res) => {
        let data = res.data.filter((item) => item.supplierName)
        this.supplierOptions = data

        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(data)
          }
        })
      })
    },
    thirdPartyLogisticsCodeChange() {
      this.getVmiHouse()
    },
    getVmiHouse(e = { text: '' }) {
      let obj = {
        defaultRules: [
          {
            field: 'vmiWarehouseName',
            operator: 'contains',
            value: e.text
          },
          {
            field: 'status',
            operator: 'equal',
            value: '1'
          },
          {
            field: 'companyCode',
            operator: 'contains',
            value:
              this.formData.vmiWarehouseType === 0
                ? this.formData.thirdPartyLogisticsCode
                : this.formData.supplierCode
          },
          {
            field: 'vmiWarehouseType',
            operator: 'contains',
            value: this.formData.vmiWarehouseType
          }
        ],
        page: {
          current: 1,
          size: 100
        }
      }
      this.$API.newVmi.getVmiHouse(obj).then((res) => {
        this.vmiHouseOptions = res.data.records
      })
    },

    handleCompanyChange(e) {
      this.FactoryParam.parentId = e.itemData.id
      this.FactoryParam.tenantId = e.itemData.tenantId
      this.getFactoryOptions(this.FactoryParam)
    },
    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      let _this = this
      this.$refs.ruleForm.validate((valid) => {
        this.companyOptions.forEach((item) => {
          if (item.orgCode === _this.formData.companyCode) _this.formData.companyName = item.orgName
        })
        if (this.modalData?.row) {
          this.$API.newVmi.addwarehoseAndSupplier(_this.formData).then((res) => {
            if (res.code === 200) {
              this.$emit('confirm-function')
              this.$toast({ content: this.$t('执行成功'), type: 'success' })
            } else {
              this.$toast({ content: this.$t('执行失败'), type: 'error' })
            }
          })
          return
        }
        if (valid) {
          this.$API.newVmi.addwarehoseAndSupplier(_this.formData).then((res) => {
            if (res.code === 200) {
              this.$emit('confirm-function')
              this.$toast({ content: this.$t('执行成功'), type: 'success' })
            } else {
              this.$toast({ content: this.$t('执行失败'), type: 'error' })
            }
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
