<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <mt-form ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="supplierCode" :label="$t('钢材供应商')">
        <mt-select
          v-model="formData.supplierCode"
          :data-source="supplierOptions"
          :show-clear-button="false"
          :disabled="isDisabled"
          :allow-filtering="true"
          :filtering="getSupplier"
          :fields="{ text: 'label', value: 'supplierCode' }"
          :placeholder="$t('钢材供应商')"
          @change="handleCompanyChange"
        ></mt-select>
      </mt-form-item>
      <!-- <mt-form-item prop="vmiWarehouseType" :label="$t('仓库类型')">
        <mt-select
          v-model="formData.vmiWarehouseType"
          :data-source="storageTypeOptions"
          :show-clear-button="true"
          :multiline="false"
          :placeholder="$t('仓库类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="stockType" :label="$t('库存状况')">
        <mt-multi-select
          v-model="formData.stockType"
          :dataSource="stockTypeSelect"
          :showClearButton="true"
          placeholder="请选择库存状况"
        ></mt-multi-select>
      </mt-form-item> -->
      <mt-form-item prop="vmiWarehouseName" :label="$t('VMI仓库名称')">
        <mt-input
          v-model="formData.vmiWarehouseName"
          :placeholder="$t('请输入VMI仓库名称')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseCode" :label="$t('VMI仓库编码')">
        <mt-input
          v-model="formData.vmiWarehouseCode"
          :disabled="isDisabled"
          :placeholder="$t('请输入VMI仓库编码')"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="vmiWarehouseAddress" :label="$t('仓库地址')">
        <mt-input
          v-model="formData.vmiWarehouseAddress"
          :placeholder="$t('请填写仓库地址')"
        ></mt-input>
      </mt-form-item>

      <!-- <mt-form-item prop="needBatch" :label="$t('是否开启批次管理')">
        <mt-select
          v-model="formData.needBatch"
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 },
          ]"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item> -->
      <!-- <mt-form-item prop="needIqc" :label="$t('是否需要IQC判定')">
        <mt-select
          v-model="formData.needIqc"
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 },
          ]"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item> -->
      <mt-form-item prop="status" :label="$t('是否有效')">
        <mt-select
          v-model="formData.status"
          :data-source="[
            { text: $t('是'), value: 1 },
            { text: $t('否'), value: 0 }
          ]"
          :placeholder="$t('请选择')"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      operateTypeOptions: [
        { text: this.$t('是'), value: '1' },
        { text: this.$t('否'), value: '2' }
      ],
      storageTypeOptions: [
        { text: this.$t('第三方物流'), value: 0 },
        { text: this.$t('供方本厂'), value: 1 }
      ],
      isDisabled: false,
      // 库存状态
      // stockTypeSelect: [
      //   { text: this.$t("合格库存"), value: 0 },
      //   { text: this.$t("待检库存"), value: 1 },
      //   { text: this.$t("出库不合格库存"), value: 2 },
      //   { text: this.$t("不合格库存"), value: 3 },
      // ],
      dialogTitle: this.$t('新建'),
      rules: {
        supplierCode: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        vmiWarehouseType: [
          {
            required: true,
            message: this.$t('请选择仓库类型'),
            trigger: 'blur'
          }
        ],
        // stockType: [
        //   {
        //     required: true,
        //     message: this.$t("请选择库存状态"),
        //     trigger: "blur",
        //   },
        // ],
        vmiWarehouseName: [
          {
            required: true,
            message: this.$t('请输入仓库名称'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseCode: [
          {
            required: true,
            message: this.$t('请输入仓库编码'),
            trigger: 'blur'
          }
        ],
        vmiWarehouseAddress: [
          {
            required: true,
            message: this.$t('请输入仓库地址'),
            trigger: 'blur'
          }
        ],
        needIqc: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        needBatch: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('请选择'), trigger: 'blur' }]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        supplierCode: '',
        companyName: '',
        vmiWarehouseType: '',
        vmiWarehouseName: '',
        vmiWarehouseCode: '',
        vmiWarehouseAddress: '',
        needIqc: '',
        needBatch: '',
        status: ''
        // stockType: "",
      },
      factoryId: '',
      companyId: '',
      supplierThird: '',
      supplier: '',
      warehouseCode: '',
      companyOptions: [],
      factoryOptions: [],
      vmiHouseOptions: [],
      supplierThirdOptions: [],
      supplierOptions: [],
      FactoryParam: { parentId: '', tenantId: '' }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
    // this.getCompanyOptions();
    const e = {}
    if (this.modalData?.row) {
      this.isDisabled = true
      e.text = this.modalData?.row.supplierName
    } else {
      this.isDisabled = false
    }
    this.getSupplier(e)
    this.getVmiHouse()
    if (this.modalData?.row) {
      this.formData = {
        ...this.modalData.row
      }
    }
  },
  methods: {
    getCompanyOptions() {
      //查询业务公司下拉数据
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          res.data.forEach((item) => (item.nameAndCode = item.orgCode + '--' + item.orgName))
          this.companyOptions = res.data
        })
    },
    getFactoryOptions(params) {
      if (!params) return
      //查询业务公司下拉数据
      this.$API.masterData.findSiteInfoByParentId(params).then((res) => {
        // console.log(res.data, "-=-=-=");
        this.factoryOptions = res.data
      })
    },
    // 获取供应商下拉
    getSupplier(e) {
      this.$API.masterData.getSupplier({ fuzzyNameOrCode: e?.text }).then((res) => {
        res.data.forEach((item) => {
          item.label = item.supplierCode + '-' + item.supplierName
        })
        let data = res.data.filter((item) => item.supplierName)
        this.supplierThirdOptions = data
        this.supplierOptions = data

        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(data)
          }
        })
      })
    },
    getVmiHouse() {
      this.$API.vmi
        .getVmiHouse({
          page: {
            current: 1,
            size: 10
          }
        })
        .then((res) => {
          this.vmiHouseOptions = res.data.records
        })
    },

    handleCompanyChange(e) {
      this.FactoryParam.parentId = e.itemData.id
      this.FactoryParam.tenantId = e.itemData.tenantId
      this.getFactoryOptions(this.FactoryParam)
    },
    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      let stockString = ''
      // let stockArr = JSON.parse(JSON.stringify(this.formData.stockType));
      // stockArr.forEach((item, index) => {
      //   if (this.formData.stockType.length - 1 != index) {
      //     stockString += item + ",";
      //   } else {
      //     stockString += item;
      //   }
      // });
      let formObj = JSON.parse(JSON.stringify(this.formData))
      formObj.stockType = stockString
      // let _this = this;
      this.supplierOptions.forEach((item) => {
        // if (item.orgCode === formObj.companyCode) {
        //   formObj.companyName = item.supplierName;
        // }
        if (item.supplierCode === formObj.supplierCode) {
          formObj.companyCode = item.supplierCode
          formObj.companyName = item.supplierName
        }
      })
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$API.newVmi.addWarehose(formObj).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('执行成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .e-searcher {
  width: 100% !important;
}
</style>
