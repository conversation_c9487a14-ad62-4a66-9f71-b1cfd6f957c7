<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
  >
    <div style="color: red">
      {{ $t('提示 ： 采购组/计划组/物料组/物料编码 四者必填其一 物料编码支持*通配符') }}
    </div>
    <mt-data-grid
      ref="dataGrid"
      :data-source="dataSource"
      :column-data="columnData"
      :edit-settings="editing"
      :toolbar="toolbar"
      @actionComplete="actionComplete"
      @toolbarClick="toolbarClick"
    ></mt-data-grid>
  </mt-dialog>
</template>

<script>
import { i18n } from '@/main.js'
import Vue from 'vue'
export default {
  props: {
    selectid: { type: String, default: '' },
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const _this = this
    return {
      editing: {
        allowEditing: true, //是否允许编辑
        allowDeleting: true, //是否允许删除
        allowAdding: true //是否允许新增
      },
      dialogTitle: this.$t('维护物料'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      dataSource: [],
      purchaseGroupOptions: [],
      planningGroupOptions: [],
      itemGroupOptions: [],
      purchaseGroupOptionsText: [],
      planningGroupOptionsText: [],
      itemGroupOptionsText: [],
      typeOptions: [
        { text: this.$t('采购入库'), value: '1' },
        { text: this.$t('采购出库'), value: '2' },
        { text: this.$t('销售出库'), value: '3' },
        { text: this.$t('销售退回'), value: '4' }
      ],
      columnData: [
        {
          width: '50',
          type: 'checkbox',
          showInColumnChooser: false //隐藏在列选择器中的过滤
        },
        {
          field: 'purchaseGroupName',
          headerText: i18n.t('采购组'),
          editTemplate: function () {
            return {
              template: Vue.component('purchaseGroupName', {
                template: `<mt-select id="purchaseGroupName" v-model="data.purchaseGroupName" :dataSource="dataSource"  ></mt-select>`,
                data() {
                  return {
                    dataSource: _this.purchaseGroupOptionsText
                  }
                }
              })
            }
          }
        },
        {
          field: 'planningGroupName',
          headerText: i18n.t('计划组'),
          editTemplate: function () {
            return {
              template: Vue.component('planningGroupName', {
                template: `<mt-select  id="planningGroupName" v-model="data.planningGroupName" :dataSource="dataSource" ></mt-select>`,
                data() {
                  return {
                    dataSource: _this.planningGroupOptionsText
                  }
                }
              })
            }
          }
        },
        {
          field: 'itemGroupName',
          headerText: i18n.t('物料组'), // sortsObj,sortsElem;
          editTemplate: function () {
            return {
              template: Vue.component('itemGroup', {
                template: `<mt-select  id="itemGroupName" v-model="data.itemGroupName" :dataSource="dataSource"  ></mt-select>`,
                data() {
                  return {
                    dataSource: _this.itemGroupOptionsText
                  }
                }
              })
            }
          }
        },
        {
          field: 'itemCode',
          headerText: i18n.t('物料编码'),
          editTemplate: function () {
            return {
              template: Vue.component('itemCode', {
                template: `<mt-input id="itemCode" v-model="data.itemCode"  ></mt-input>`,
                data() {
                  return {}
                }
              })
            }
          }
        },
        {
          field: 'goodsReceiptType',
          headerText: i18n.t('VMI入库类型'), // sortsObj,sortsElem;
          headerTemplate: () => {
            return {
              template: Vue.component('headers', {
                template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('VMI入库类型')}}</span>
              </div>
            `
              })
            }
          },
          validationRules: {
            required: true
          },
          editTemplate: function () {
            return {
              template: Vue.component('goodsReceiptType', {
                template: `<mt-select id="goodsReceiptType" v-model="data.goodsReceiptType" :dataSource="dataSource" ></mt-select>`,
                data() {
                  return {
                    dataSource: [
                      { text: this.$t('采购入库'), value: this.$t('采购入库') },
                      { text: this.$t('采购出库'), value: this.$t('采购出库') },
                      { text: this.$t('销售出库'), value: this.$t('销售出库') },
                      { text: this.$t('销售退回'), value: this.$t('销售退回') }
                    ]
                  }
                }
              })
            }
          }
        }
        // {
        //   field: "advanceTime",
        //   headerText: this.$t("VMI入库提前天数"),
        //   editType: "numericedit",
        //   validationRules: {
        //     required: true,
        //   },
        //   edit: {
        //     params: {
        //       min: 0,
        //     },
        //   },
        // },
      ],
      toolbar: [
        'Add',
        {
          text: this.$t('删除'),
          id: 'delete',
          prefixIcon: 'e-delete'
        },
        'Edit',
        'Cancel',
        {
          text: this.$t('保存'),
          id: 'saveData',
          prefixIcon: 'e-edit'
        }
        // "Update",
      ] //编辑和取消、保存按钮
    }
  },
  async mounted() {
    this.getDataSource()
    this.getData1()
    this.getData2()
    this.getData3()
  },
  methods: {
    actionComplete(args) {
      const { rowIndex } = args
      if (args.requestType === 'beginEdit') {
        //开始编辑
      } else if (args.requestType == 'save') {
        console.log(args.data, '-save編輯完成=-=')
        //保存编辑
        this.addOrUpdateLineData(args.data, rowIndex) //单条新增
      }
    },

    getDataSource() {
      this.$API.vmi.getLastList({ id: this.modalData?.id }).then((res) => {
        this.dataSource = res.data || []
        this.$refs.dialog.ejsRef.show()
      })
    },
    getData1() {
      this.$API.masterData.getbussinessGroup({ groupTypeName: this.$t('采购组') }).then((res) => {
        if (res.data?.length > 0) {
          res.data.forEach((item) => {
            this.purchaseGroupOptions.push({
              text: item.groupName,
              value: item.groupCode
            })
            this.purchaseGroupOptionsText.push({
              text: item.groupName,
              value: item.groupName
            })
          })
        }
      })
    },
    getData2() {
      this.$API.masterData.postItemGroupCriteriaQuery().then((res) => {
        if (res.data?.length > 0) {
          res.data.forEach((item) => {
            this.itemGroupOptions.push({
              text: item.name,
              value: item.code
            })
            this.itemGroupOptionsText.push({
              text: item.name,
              value: item.name
            })
          })
        }
      })
    },
    getData3() {
      this.$API.masterData.getbussinessGroup({ groupTypeName: this.$t('计划组') }).then((res) => {
        if (res.data?.length > 0) {
          res.data.forEach((item) => {
            this.planningGroupOptions.push({
              text: item.groupName,
              value: item.groupCode
            })
            this.planningGroupOptionsText.push({
              text: item.groupName,
              value: item.groupName
            })
          })
        }
      })
    },
    toolbarClick(args) {
      console.log('toolbarClick', args)
      // 自定义了一个行内新增按钮，为了给新增的这一行赋默认值
      if (args.item.id == 'saveData') {
        this.savedata()
      } else if (args.item.id == 'delete') {
        this.deleteBatch()
      }
    },
    deleteBatch() {
      const data = this.$refs.dataGrid.ejsRef.getSelectedRecords()
      if (data.length <= 0) {
        this.$toast({
          content: this.$t('请选择一行'),
          type: 'error'
        })
        return
      }
      const ids = data.reduce((pre, now) => {
        pre.push(now.id)
        return pre
      }, [])
      this.$API.vmi.batchDelete({ ids }).then(() => {
        this.getDataSource()
      })
    },
    savedata() {
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.$refs.dialog.ejsRef.hide()
    },
    addOrUpdateLineData(data, rowIndex) {
      const vmiSupplierRelId = this.modalData?.id
      const validate =
        !data.purchaseGroupName && !data.planningGroupName && !data.itemGroupName && !data.itemCode
      if (validate) {
        this.$toast({
          content: this.$t('采购组、计划组、物料组、物料编码必选其一'),
          type: 'error'
        })
        //开启继续编辑状态
        this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
        this.$refs.dataGrid.ejsRef.startEdit()
        return
      }
      data.vmiSupplierRelId = vmiSupplierRelId
      this.purchaseGroupOptions.forEach((v) => {
        if (v.text === data.purchaseGroupName) {
          data.purchaseGroup = v.value
        }
      })
      this.planningGroupOptions.forEach((v) => {
        if (v.text === data.planningGroupName) {
          data.planningGroup = v.value
        }
      })
      this.itemGroupOptions.forEach((v) => {
        if (v.text === data.itemGroupName) {
          data.itemGroup = v.value
        }
      })

      if (data.id) {
        this.$API.vmi
          .updateBatchWarehose(data)
          .then((res) => {
            if (res.code === 200) {
              this.getDataSource()
              this.$refs.dataGrid.refresh() // 更新表格数据
              // this.handleClose()
              this.$toast({ content: this.$t('执行成功'), type: 'success' })
            } else {
              this.$toast({ content: this.$t('执行失败'), type: 'error' })
            }
          })
          .catch(() => {
            this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
            this.$refs.dataGrid.ejsRef.startEdit()
          })
      } else {
        this.$API.vmi
          .addWarehoseRel(data)
          .then((res) => {
            // console.log(res, 9999);
            if (res.code === 200) {
              this.getDataSource()
              this.$refs.dataGrid.refresh() // 更新表格数据
            }
          })
          .catch(() => {
            this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
            this.$refs.dataGrid.ejsRef.startEdit()
          })
      }
    },
    addBathOrUpdateLineData(data, rowIndex) {
      //批量暂时不用了
      const vmiSupplierRelId = this.modalData?.id
      if (data.length <= 0) {
        alert(this.$t('未维护物料数据'))
        return
      }
      const validate = data.some(
        (item) => !item.purchaseGroup && !item.planningGroup && !item.itemGroup && !item.itemCode
      )
      if (validate) {
        this.$toast({
          content: this.$t('采购组、计划组、物料组、物料编码必选其一'),
          type: 'error'
        })
        //开启继续编辑状态
        this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
        this.$refs.dataGrid.ejsRef.startEdit()
        return
      }
      data.forEach((item) => {
        item.vmiSupplierRelId = vmiSupplierRelId
        this.purchaseGroupOptions.forEach((v) => {
          if (v.text === item.purchaseGroup) {
            item.purchaseGroup = v.value
          }
        })
        this.planningGroupOptions.forEach((v) => {
          if (v.text === item.planningGroup) {
            item.planningGroup = v.value
          }
        })
        this.itemGroupOptions.forEach((v) => {
          if (v.text === item.itemGroup) {
            item.itemGroup = v.value
          }
        })
      })
      this.$API.vmi
        .addBatchWarehose(data)
        .then((res) => {
          console.log(res, 9999)
          if (res.code === 200) {
            this.getDataSource()
            this.$refs.dataGrid.refresh() // 更新表格数据
          }
        })
        .catch(() => {
          this.$refs.dataGrid.ejsRef.selectRow(rowIndex)
          this.$refs.dataGrid.ejsRef.startEdit()
        })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>
<style lang="scss" scoped></style>
