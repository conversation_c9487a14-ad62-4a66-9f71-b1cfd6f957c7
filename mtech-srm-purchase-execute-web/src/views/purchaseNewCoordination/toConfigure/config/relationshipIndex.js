import { i18n } from '@/main.js'
import Vue from 'vue'
// VMI仓库与供应商关系配置
export const headColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    ignore: true,
    showInColumnChooser: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    width: '150',
    field: 'id',
    headerText: i18n.t('物料管理'),
    cellTools: [],
    valueConverter: {
      type: 'function',
      filter: () => {
        return i18n.t(`物料管理`)
      }
    }
  },
  {
    width: '200',
    field: 'companyName',
    headerText: i18n.t('采方公司'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.companyCode}}-{{data.companyName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
              <div class="headers">
                <span>{{data.siteCode}}-{{data.siteName}}</span>
              </div>
            `,
          data() {
            return {
              data: {}
            }
          }
        })
      }
    }
  },
  {
    width: '150',
    field: 'thirdPartyLogisticsCode',
    headerText: i18n.t('钢材供应商公司编码')
  },
  {
    width: '150',
    field: 'thirdPartyLogisticsName',
    headerText: i18n.t('钢材供应商公司名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓描述')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商描述')
  },
  {
    width: '150',
    field: 'goodsReceiptTypeDesc',
    headerText: i18n.t('VMI入库类型')
  },
  {
    width: '150',
    field: 'advanceTime',
    headerText: i18n.t('VMI库提前期')
  },
  {
    width: '150',
    field: 'needItem',
    headerText: i18n.t('是否管理物料'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },

  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间')
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('是否有效'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('无效'),
        1: i18n.t('有效')
      }
    }
  }
]
