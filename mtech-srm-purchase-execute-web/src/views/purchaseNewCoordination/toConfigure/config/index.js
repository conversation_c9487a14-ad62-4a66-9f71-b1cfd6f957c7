import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// VMI配置管理
export const headColumn = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    showInColumnChooser: false,
    ignore: true,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑')
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  },
  {
    width: '200',
    field: 'companyCode',
    headerText: i18n.t('供应商编号')
  },
  {
    width: '150',
    field: 'companyName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓描述')
  },
  {
    width: '150',
    field: 'vmiWarehouseAddress',
    headerText: i18n.t('仓库地址')
  },
  {
    width: '150',
    field: 'vmiWarehouseType',
    headerText: i18n.t('仓库类型'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('第三方物流'),
        1: i18n.t('供方本厂')
      }
    }
  },
  {
    width: '150',
    field: 'needBatch',
    headerText: i18n.t('是否开启批次管理'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    width: '150',
    field: 'needIqc',
    headerText: i18n.t('是否需要IQC判定'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('库存状况'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('无效'),
        1: i18n.t('有效')
      }
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('是否有效'),
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('无效'),
        1: i18n.t('有效')
      }
    }
  }
]
