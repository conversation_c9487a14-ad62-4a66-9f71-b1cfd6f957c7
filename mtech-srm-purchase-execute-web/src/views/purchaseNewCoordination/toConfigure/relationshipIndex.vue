// VMI仓库与供应商关系配置
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>

    <!-- 导入弹框 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :down-template-params="downTemplateParams"
      :request-urls="requestUrls"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    ></upload-excel-dialog>
  </div>
</template>

<script>
import { headColumn } from './config/relationshipIndex.js'
import * as UTILS from '@/utils/utils'
export default {
  components: {
    uploadExcelDialog: () => import('@/components/Upload/uploadExcelDialog.vue')
  },
  data() {
    return {
      downTemplateParams: {
        pageFlag: false
      }, // 下载模板参数
      uploadParams: {}, // 明细行上传excel的
      requestUrls: {
        templateUrlPre: 'vmi',
        templateUrl: 'downloadWarehoseAnd',
        uploadUrl: 'importWarehoseAnd'
      },
      selectId: null,
      pageConfig: [
        {
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1274'],
                  title: this.$t('新增')
                },
                {
                  id: 'delete',
                  icon: 'icon_solid_Delete',
                  permission: ['O_02_1275'],
                  title: this.$t('删除')
                }
              ],
              [
                'Filter',
                {
                  id: 'import',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导入'),
                  permission: ['O_02_1279']
                },
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出'),
                  permission: ['O_02_1280']
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '5d2dec68-b21d-403e-8d05-c4108391bec7',
          grid: {
            columnData: headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi_warehouse_supplier_rel/page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ]
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      // 打开物料管理弹窗
      if (e.field === 'id') {
        //物料管理
        this.$dialog({
          modal: () => import('./components/dialogTable.vue'),
          data: {
            id: e.data.id
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    // 表格头部操作
    handleClickToolBar(e) {
      const selectRows = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'add') {
        // this.$refs.configListDialog.initDialog();
        this.handleAdd()
      } else if (e.toolbar.id === 'delete' && selectRows.length > 0) {
        // 删除弹窗
        let ids = selectRows.map((item) => item.id)
        this.handleDelete(ids)
      } else if (e.toolbar.id === 'import') {
        // 导入
        this.showUploadExcel(true)
      } else if (e.toolbar.id === 'export') {
        // 导出
        this.exportExcel()
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    updateList() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 接受弹窗新增得数据
    dilogCogfim() {
      this.updateList()
    },
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.dilogCogfim()
    },
    exportExcel() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let obj = JSON.parse(
        sessionStorage.getItem('5d2dec68-b21d-403e-8d05-c4108391bec7')
      ).visibleCols
      let field = []
      if (obj !== undefined) {
        obj.forEach((item) => {
          field.push(item.field)
        })
      } else {
        headColumn.detailedColumn.forEach((item) => {
          field.push(item.field)
        })
      }
      const params = {
        page: { current: 1, size: 100000 },
        ...queryBuilderRules,
        pageFlag: true,
        sortedColumnStr: field.toString()
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.newVmi.downloadWarehose(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = UTILS.getHeadersFileName(res)
        UTILS.download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = null // 清空数据
        this.$refs.uploadExcelRef.fileLength = 0
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除吗？')
        },
        success: () => {
          this.$API.newVmi.deletewarehoseAndSupplier({ ids }).then(() => {
            // console.log(res, "0-0-0");
            this.updateList()
          })
        }
      })
    },
    handleAdd(row) {
      this.$dialog({
        modal: () => import('./components/dialog.vue'),
        data: {
          title: this.$t('完善信息'),
          row: row || null
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'Delete') {
        this.handleDelete([e.data.id])
      } else if (e.tool.id == 'Edit') {
        this.handleAdd(e.data)
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
