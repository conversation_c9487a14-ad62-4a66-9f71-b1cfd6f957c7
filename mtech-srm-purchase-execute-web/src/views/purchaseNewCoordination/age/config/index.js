import { i18n } from '@/main.js'

export const columnObj = {
  // 卷号维度
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'siteName',
      headerText: i18n.t('工厂')
    },
    {
      width: '200',
      field: 'siteCode',
      headerText: i18n.t('工厂编码')
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '150',
      field: 'itemCode', //supplierCode
      headerText: i18n.t('物料编码')
    },
    {
      width: '150',
      field: 'itemName', //supplierName
      headerText: i18n.t('物料名称')
    },

    {
      width: '150',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    {
      width: '150',
      field: 'count',
      headerText: i18n.t('卷重')
    },
    {
      width: '150',
      field: 'stockDays',
      headerText: i18n.t('库龄')
    }
  ]
}
