// 报表
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'

import { columnObj } from './config/index.js'

export default {
  data() {
    return {
      pageConfig: [
        {
          tab: { title: this.$t('钢材卷号库龄') },
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0122",
          dataSource: [],
          useToolTemplate: false,
          gridId: '2DA42EFB-B066-54B3-E7FC-E26F923D7B00',
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [],
            asyncConfig: {
              url: '/statistics/tenant/steelStock/buyer/stock-day-query'
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 头部操作
    handleClickToolBar(args) {
      let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      let obj = JSON.parse(
        sessionStorage.getItem('2DA42EFB-B066-54B3-E7FC-E26F923D7B00')
      ).visibleCols
      let field = []
      if (obj !== undefined) {
        obj.forEach((item) => {
          field.push(item.field)
        })
      } else {
        columnObj.headColumn.forEach((item) => {
          field.push(item.field)
        })
      }
      if (args.toolbar.id === 'export1') {
        let params = {
          page: { current: 1, size: 1000 },
          rules: rule.rules || [],
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination.batchTENANTAgeViewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
