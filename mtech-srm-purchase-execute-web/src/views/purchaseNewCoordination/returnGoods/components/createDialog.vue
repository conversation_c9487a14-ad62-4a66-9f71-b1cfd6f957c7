<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('新建')"
      :buttons="buttons"
      @close="handleClose"
      :open="onOpen"
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="false"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { createColumnObj } from '../config/index.js'

export default {
  props: {
    ids: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1092"],
                  title: this.$t('冲销')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '191760b6-cee5-4e12-ae7d-cef3a15ca8ff',

          grid: {
            columnData: createColumnObj,
            dataSource: [],

            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi_stock/page-query',
              defaultRules: [
                {
                  field: 'count',
                  operator: 'greaterthan',
                  value: '0'
                }
              ],
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],

      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()

    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        if (e.toolbar.id === 'add') {
          let row = e.grid.getSelectedRecords()
          let rowLine = []
          row.forEach((item) => {
            rowLine.push({
              siteCode: item.siteCode,
              supplierCode: item.supplierCode,
              vmiWarehouseCode: item.vmiWarehouseCode
            })
          })

          var run = {}
          rowLine = rowLine.reduce(function (item, next) {
            run[next.siteCode]
              ? ''
              : run[next.supplierCode]
              ? ''
              : run[next.vmiWarehouseCode]
              ? ''
              : (run[next.siteCode] = true && item.push(next))
            return item
          }, [])
          console.log(rowLine)
          console.log(row)
          let obj = []
          rowLine.forEach((item) => {
            let newArr = row.filter(
              (e) =>
                item.siteCode === e.siteCode &&
                item.supplierCode === e.supplierCode &&
                item.vmiWarehouseCode === e.vmiWarehouseCode
            )
            obj.push({
              itemList: newArr,
              operationType: 0,
              siteCode: newArr[0].siteCode, // 工厂编码
              siteName: newArr[0].siteName, // 工厂
              supplierCode: newArr[0].supplierCode, // 原材料供应商编码
              vmiWarehouseAddress: newArr[0].vmiWarehouseAddress, // 送货地址
              vmiWarehouseCode: newArr[0].vmiWarehouseCode, // VMI仓编码
              vmiWarehouseName: newArr[0].vmiWarehouseName // VMI仓
            })
          })
          // row.forEach((item) => {
          //   obj.push({
          //     itemList: [{ ...item }],
          //     operationType: 0,
          //     siteCode: item.siteCode, // 工厂编码
          //     siteName: item.siteName, // 工厂
          //     supplierCode: item.supplierCode, // 原材料供应商编码
          //     vmiWarehouseAddress: item.vmiWarehouseAddress, // 送货地址
          //     vmiWarehouseCode: item.vmiWarehouseCode, // VMI仓编码
          //     vmiWarehouseName: item.vmiWarehouseName, // VMI仓
          //   });
          // });
          console.log(obj)
          // let params = {
          //   siteCode: row.siteCode, // 工厂编码
          //   supplierCode: row.supplierCode, // 原材料供应商编码
          //   vmiWarehouseCode: row.vmiWarehouseCode, // VMI仓编码
          //   processorCode: row.processorCode, // 领料供应商编码（加工商）
          //   vmiWarehouseAddress: row.vmiWarehouseAddress, // 送货地址
          //   addressCode: row.consigneeAddressCode,
          //   addressContactNumber: row.consigneePhone,
          //   addressContactPerson: row.consigneeName,
          //   addressId: row.addressId,
          //   remark: row.remark, // 备注
          //   itemList: row, // 创建VMI领料单请求明细参数
          // };
          // if (this.$route.query.id) {
          //   params["id"] = this.$route.query.id;
          // }
          // params["operationType"] = isSave ? 1 : 2; // 操作类型，1-保存/2-提交

          this.$API.purchaseCoordination.VmiSteelReturnedOrder(obj).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: this.$t('创建成功'),
                type: 'success'
              })

              // this.goBack();
            } else {
              this.$toast({ content: this.$t('提交失败'), type: 'error' })
            }
          })
          this.handleClose()
        }
      }
    },

    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
