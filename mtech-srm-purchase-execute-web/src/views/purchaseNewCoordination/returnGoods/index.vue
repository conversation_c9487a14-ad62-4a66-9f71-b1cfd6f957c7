// VMI退货管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      :permission-obj="permissionObj"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'
import { columnObj } from './config/index.js'
export default {
  components: {
    // deliveryDialog,
    createDialog: require('./components/createDialog').default
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0130' },
          { dataPermission: 'b', permissionCode: 'T_02_0131' }
        ]
      },
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          dataPermission: 'a',
          permissionCode: 'T_02_0130',
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'create',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('创建vmi冲销单'),
                  permission: ['O_02_1269']
                }
              ],
              ['Filter', 'Refresh', 'Setting'] //筛选  刷新   设置
            ]
          },
          // gridId: '1e841de6-2e58-498a-bd39-1d516ab11947',
          grid: {
            columnData: columnObj.headColumn,
            asyncConfig: {
              url: this.$API.purchaseCoordination.VMINewBuyerWarehousingReturnGoods, //采方退货头视图
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
              // recordsPosition: "data",   //相当于res   默认的是data.records
            },
            frozenColumns: 1 //冻结表格的一列
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          dataPermission: 'a',
          permissionCode: 'T_02_0131',
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ] //筛选  刷新   设置
          },
          gridId: '40e0531e-be78-4a16-b499-ce76f5ee4172',
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: this.$API.purchaseCoordination.postNewReturnQueryDetail, //采方明细视图列表
              rules: [
                {
                  field: 'vmi_order_type',
                  // type:"string",
                  operator: 'equal',
                  value: 3
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleDialogShow(flag) {
      this.deliveryShow = flag

      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        tabIndex: '',
        status: '',
        id: ''
      }
      if (e.tabIndex === 0) {
        //tabIndex:  0是头视图 ,1是明视图
        obj.tabIndex = 0
      } else {
        obj.tabIndex = 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.status = e.data.status
        obj.id = e.data.vmiOrderId ?? e.data.id //明细视图ID 和 头视图ID
        this.redirectPage('purchase-execute/new-coordination-returnGoods-details', obj)
      }
    },
    // 表格上方操作
    handleClickToolBar(e) {
      // 采方接收  批量
      if (e.toolbar.id === 'create') {
        this.deliveryShow = true
        // let _selectRows = e.grid.getSelectedRecords();
        // if (_selectRows.length <= 0) {
        //   this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
        //   return;
        // }
      }

      if (e.toolbar.id === 'export1') {
        let rule = this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let obj = JSON.parse(
          sessionStorage.getItem('40e0531e-be78-4a16-b499-ce76f5ee4172')
        )?.visibleCols
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || []
        }
        params.rules = [
          ...params.rules,
          {
            field: 'vmi_order_type',
            // type:"string",
            operator: 'equal',
            value: 3
          }
        ]
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination
          .postBuyerWarehousingReturnExport(params, field)
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)

            download({ fileName: `${fileName}`, blob: res.data })
          })
      }
    },
    // 行内操作
    handleClickCellTool(e) {
      if (e.tool.id == 'confirm') {
        this.$dialog({
          data: {
            title: this.$t('接收'),
            message: this.$t('确认接收吗？')
          },
          success: () => {
            this.confirm(e.data.id)
          }
        })
      } else if (e.tool.id == 'rejected') {
        this.$dialog({
          data: {
            title: this.$t('退回'),
            message: this.$t('确认退回吗？')
          },
          success: () => {
            this.rejected(e.data.id)
          }
        })
      }
    },
    // 接收
    confirm(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnConfirm(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('接收成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('接收失败'), type: 'error' })
        }
      })
    },
    // 退回
    rejected(data) {
      let obj = {
        ids: [data]
      }
      this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('退回成功'), type: 'success' })
          // 刷新当前 Grid
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: this.$t('退回失败'), type: 'error' })
        }
      })
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.full-height {
  height: 100%;
}
</style>
