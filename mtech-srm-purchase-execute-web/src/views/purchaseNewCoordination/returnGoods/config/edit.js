import { i18n } from '@/main.js'
export const editSettings = {
  allowEditing: true,
  allowAdding: true,
  allowDeleting: true,
  mode: 'Normal', // 选择默认模式，双击整行可以进行编辑
  showConfirmDialog: false,
  showDeleteConfirmDialog: false,
  newRowPosition: 'Bottom'
}

export const toolbarOptions = [
  'Add',
  // "Edit",
  'Delete',
  // "Update", // 产品默认不要
  // "Cancel",
  {
    text: i18n.t('获取数据'),
    prefixIcon: 'e-expand',
    id: 'DataBtn'
  }
]

export const dataSource = [
  {
    visiIpt: i18n.t('我是第一行')
  }
]

import { TextBox, NumericTextBox } from '@syncfusion/ej2-inputs'
// import { Query } from "@syncfusion/ej2-data";
// import Vue from "vue";

/**
 * 单元格需要被改变时，可以通过xxxObj.value=‘’。。。或者通过bus监听（cellChanged组件）
 * 多个需要写xxxObj时，可以通用commonEdit配置
 * 如果不能用ej2自带格式的，需要手写template/editTemplate
 */

export const checkCol = [
  // {
  //   width: "70",
  //   type: "checkbox",
  //   allowEditing: false,
  //   showInColumnChooser: false,
  // },
  {
    width: 0,
    field: 'otherData',
    headerText: i18n.t('额外数据'),
    // visible: false, // 设置成不显示就会导致赋值不上，因为没有这个元素了
    allowEditing: false
    // editTemplate: function () {
    //   return {
    //     template: cellChanged,
    //   };
    // },
  }
]

// 因为xxxEle，xxxObj需要配置全局，可以在此批量生成。配置这个为了能实时 得到/改变 该单元格的数据
const numberFields = ['normalNumber1', 'normalNumber2', 'resNumber']
var wholeObj = {}
numberFields.forEach((item) => {
  wholeObj[`${item}Ele`] = null
  wholeObj[`${item}Obj`] = null
})

var commonEdit = (params) => {
  // type:单元格的类型 等于ej2包含的类型，默认字符串类型
  // field: 列的field，必填
  // pld：placeholder，可不填
  // canEdit：是否可以编辑，默认可以
  // required: 是否必填，默认非必填。。必填时给cssClass赋值isRequired
  // callback: 回调（按照业务逻辑来）
  // calc：计算参数，仅举例（按照业务逻辑来）
  let {
    type = 'stringedit',
    field,
    pld,
    canEdit = true,
    required = false,
    // callback,
    calc
  } = params
  return {
    create: () => {
      wholeObj[`${field}Ele`] = document.createElement('input')
      return wholeObj[`${field}Ele`]
    },
    read: () => {
      return wholeObj[`${field}Obj`].value
    },
    destroy: () => {
      wholeObj[`${field}Obj`].destroy()
    },
    write: (args) => {
      console.log(args, 77790000)
      switch (type) {
        case 'stringedit':
          wholeObj[`${field}Obj`] = new TextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            change: (e) => {
              console.log(i18n.t('我改变了'), calc, e, wholeObj)
            }
          })
          break
        case 'numericedit':
          wholeObj[`${field}Obj`] = new NumericTextBox({
            enabled: canEdit,
            cssClass: required ? 'isRequired' : null,
            value: args.rowData[args.column.field],
            placeholder: pld,
            floatLabelType: 'Never',
            change: (e) => {
              // console.log(i18n.t("我改变了"), calc, e, wholeObj);
              if (calc && calc.length) {
                // 以简单乘法计算为例
                if (wholeObj[`${calc[0]}Obj`] && wholeObj[`${calc[1]}Obj`]) {
                  wholeObj[`${calc[1]}Obj`].value = wholeObj[`${calc[0]}Obj`].value * e.value
                }
              }
            }
          })
          break
        default:
          break
      }
      wholeObj[`${field}Obj`].appendTo(wholeObj[`${field}Ele`])
    }
  }
}

// ToDo
// 默认值、校验、级联
// var beDropedEle, beDropedObj;
export const columnData = () => {
  return [
    {
      width: '150',
      field: 'lineNumber',
      headerText: i18n.t('行号'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'normalIpt',
      headerText: i18n.t('关联采购订单号'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'normalNumber',
      headerText: i18n.t('关联采购订单行号'),
      allowEditing: false
    },
    // {
    //   width: "150",
    //   field: "currency",
    //   headerText: i18n.t("下拉框"),
    //   editType: "dropdownedit",
    //   edit: {
    //     params: {
    //       allowFiltering: true,
    //       dataSource: params.currencyData,
    //       fields: { value: "currencyName", text: "currencyName" },
    //       query: new Query(),
    //       actionComplete: () => false,
    //       change: (e) => {
    //         console.log("我是下拉的change事件", e);
    //       },
    //     },
    //   },
    // },
    // {
    //   width: "150",
    //   field: "normalDate",
    //   headerText: i18n.t("日期选择器"),
    //   editType: "datepickeredit",
    //   format: "yMd",
    // },
    // {
    //   width: "150",
    //   field: "normalCheck",
    //   headerText: i18n.t("复选框"),
    //   editType: "booleanedit",
    //   // 修改显示
    //   valueAccessor: function (field, data) {
    //     return data[field] ? "成功" : "失败";
    //   },
    // },
    {
      width: '150',
      field: 'materialCode',
      headerText: i18n.t('物料编码'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'materialDescribe',
      headerText: i18n.t('物料名称'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'normalNumber1',
      headerText: i18n.t('退货数量'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'normalNumber2',
      headerText: i18n.t('实收数量'),
      editType: 'numericedit',
      edit: commonEdit({
        type: 'numericedit',
        field: 'normalNumber2',
        pld: i18n.t('实收数量请输入'),
        calc: ['normalNumber1', 'resNumber']
      })
    },
    {
      width: '150',
      field: 'encryptionNumber',
      headerText: i18n.t('卷号'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'company',
      headerText: i18n.t('单位'),
      allowEditing: false
    },
    {
      width: '150',
      field: 'procurementGroup',
      headerText: i18n.t('采购组'),
      allowEditing: false
    },
    {
      field: 'sendTime', // 卷号
      headerText: i18n.t('卷号')
    }
  ]
}
