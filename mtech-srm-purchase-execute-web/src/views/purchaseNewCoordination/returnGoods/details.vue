// 供方退货管理
<template>
  <div class="full-height pt20">
    <!-- 头部信息 -->
    <top-info
      class="flex-keep"
      :header-info="headerInfo"
      @goBack="goBack"
      @doExpand="doExpand"
      @squareAccept="squareAccept"
      @squareReturn="squareReturn"
    ></top-info>
    <mt-template-page
      ref="templateRef"
      :current-tab="0"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { headColumn } from './config/details.js'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue')
  },
  data() {
    return {
      isEditStatus: false, // 正处于编辑状态
      pageConfig: [
        {
          tab: { title: this.$t('物料信息') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          gridId: '46ac1092-bdb2-406d-bbea-a15d3d4ce23a',
          grid: {
            columnData: headColumn,
            editSettings: {
              allowEditing: true,
              allowAdding: true,
              allowDeleting: true
            },
            dataSource: [],
            // asyncConfig: {
            //   url: this.$API.purchaseCoordination.postNewBuyerWarehousingReturnDetail,
            //   params:{
            //     id:0,
            //   }
            //   // recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
      ],
      headerInfo: {} //头部信息
    }
  },
  mounted() {
    this.getDetailData()
  },
  methods: {
    // 跳转详情
    handleClickCellTitle() {},
    // 表格头部操作
    handleClickToolBar() {},
    // 行内操作
    handleClickCellTool() {},
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    // 返回
    goBack() {
      this.$router.go(-1)
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 采方接收
    squareAccept() {
      this.$dialog({
        data: {
          title: this.$t('采方接收'),
          message: this.$t('是否确认接收')
        },
        success: () => {
          let objIds = {
            ids: [this.$route.query.id]
          }
          this.$API.purchaseCoordination.postBuyerWarehousingReturnConfirm(objIds).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('接收成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
        }
      })
    },
    //采方退回
    squareReturn() {
      this.$dialog({
        data: {
          title: this.$t('采访退回'),
          message: this.$t('是否确认退回？')
        },
        success: () => {
          let objIds = {
            ids: [this.$route.query.id]
          }
          this.$API.purchaseCoordination.postBuyerWarehousingReturnRejected(objIds).then((res) => {
            if (res.code == 200) {
              this.$toast({ content: this.$t('接收成功'), type: 'success' })
              this.$router.go(-1)
              // 刷新当前 Grid
              // this.$refs.templateRef.refreshCurrentGridData();
            }
          })
        }
      })
    },
    // 详情数据接口
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      this.$API.purchaseCoordination.postNewBuyerWarehousingReturnDetail(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo = res.data
          this.pageConfig[0].grid.dataSource = res.data.vmiOrderItemResponses
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .flex-keep {
  margin-top: 10px;
}
.full-height {
  height: 100%;
}
/deep/ .mt-tabs ul.tab-container li.tab-item2.tab-item2--line.active {
  border: none;
  font-weight: 100;
}
</style>
