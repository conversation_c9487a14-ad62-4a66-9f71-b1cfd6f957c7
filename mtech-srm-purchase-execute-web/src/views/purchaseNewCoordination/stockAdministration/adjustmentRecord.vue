// VMI库存调整管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config/adjustmentRecord.js'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      pageConfig: [
        {
          useToolTemplate: false,
          title: this.$t('汇总列表'),
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [],
              [
                'Filter',
                {
                  id: 'export',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                },
                'Refresh',
                'Setting'
              ]
            ]
          },
          gridId: '3ba9b389-b065-444b-846a-2b10da63c675',
          grid: {
            columnData: columnData,
            dataSource: [
              {
                billCode: '01',
                status: 1,
                factoryName: this.$t('测试1'),
                typeCode: 'leixing',
                factoryCode: '001',
                // factoryName: this.$t('京东工厂'),
                warehouseName: this.$t('仓库名称'),
                materialCode: '0001',
                warehousedDescribe: this.$t('仓描述'),
                supplierCode: '002222',
                supplierDescribe: this.$t('供应商描述'),
                makeDate: '2022-03-04',
                makePeople: this.$t('制单人')
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiAllocationOrder/buyer-page-query',
              recordsPosition: 'data.records'
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    // 跳转详情
    handleClickCellTitle(e) {
      if (e.field === 'vmiOrderCode') {
        let obj = {
          details: '',
          orderStatus: e.data.vmiOrderType,
          id: e.data.id,
          status: e.data.status
        }
        if (e.data.status == 0 || e.data.status == 1) {
          obj.details = 0
        } else {
          obj.details = 1
        }
        // 根据类型判断   类型字段暂时没有返回   先用状态代替（自定义代替）
        if (e.data.vmiOrderType === 4) {
          // 跳入导入
          this.redirectPage('purchase-execute/coordination-stock-stock', obj)
        } else if (e.data.vmiOrderType === 6) {
          // 跳入物料替换
          this.redirectPage('purchase-execute/coordination-stock-replace', obj)
        } else if (e.data.vmiOrderType === 5) {
          // 跳入物料调拨
          this.redirectPage('purchase-execute/coordination-stock-allocation', obj)
        }
      }
    },
    handleClickToolBar(e) {
      if (e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'export') {
        // 导出
        console.log(e.grid.getSelectedRecords())
      } else if (e.toolbar.id === 'refreshDataByLocal') {
        // 调用刷新
      } else if (e.grid.getSelectedRecords().length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'accept') {
        this.$dialog({
          data: {
            title: this.$t('接受'),
            message: this.$t('确认接受吗？')
          },
          success: () => {
            this.accept(e.data.id, e.data.vmiOrderType)
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.cancel(e.data.id, e.data.vmiOrderType)
          }
        })
      }
    },
    // 刷新页面
    refreshColumns() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 接收 采方，供方公用一个接口
    accept(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationReceive'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceReceive'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('提交成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('提交失败'), type: 'error' })
        }
      })
    },
    // 取消 三方共用一个接口
    cancel(data, valType) {
      let apiInterface = ''
      if (valType == 5) {
        apiInterface = 'purchaseAllocationBackOff'
      } else if (valType == 6) {
        apiInterface = 'purchaseReplaceBackOff'
      }
      let obj = {
        ids: []
      }
      obj.ids.push(data)
      debugger
      this.$API.purchaseCoordination[apiInterface](obj).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('取消成功'), type: 'success' })
          this.refreshColumns()
        } else {
          this.$toast({ content: this.$t('取消失败'), type: 'error' })
        }
      })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
.titleColor {
  color: #00469c;
}
</style>
