// import Vue from "vue";
import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

// 库存调整管理
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'vmiOrderCode',
    headerText: i18n.t('单据号'),
    cellTools: []
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    width: '150',
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: {
        0: i18n.t('新建'),
        1: i18n.t('待确认'),
        2: i18n.t('已接收'),
        8: i18n.t('已完成'),
        9: i18n.t('已取消')
      }
    },
    cellTools: [
      {
        id: 'accept',
        // icon: "icon_solid_pushorder",
        title: i18n.t('接收'),
        visibleCondition: (data) => {
          if (data.status == 1 && data.vmiOrderType !== 4) return true
        }
      },
      {
        id: 'cancel',
        // icon: "icon_solid_pushorder",
        title: i18n.t('取消'),
        visibleCondition: (data) => {
          if (data.status == 1 && data.vmiOrderType !== 4) return true
        }
      }
    ]
  },
  {
    field: 'vmiOrderTypeDesc',
    headerText: i18n.t('类型')
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('制单日期'),
    searchOptions: {
      ...MasterDataSelect.timeRange
    }
  },
  {
    width: '150',
    field: 'createUserName',
    headerText: i18n.t('制单人')
  }
  // {
  //   width: "150",
  //   field: "VMIWarehouseCode",
  //   headerText: i18n.t("接收日期"),
  // },
]
