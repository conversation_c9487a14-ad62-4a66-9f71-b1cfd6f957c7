// import Vue from "vue";
import { i18n } from '@/main.js'
// 库存调整管理
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '150',
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '150',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    width: '150',
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '150',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('合格库存数量')
  },
  {
    width: '150',
    field: 'qcCount',
    headerText: i18n.t('待检库存数量')
  },
  {
    width: '150',
    field: 'lockCount',
    headerText: i18n.t('出库锁定库存数量')
  },
  {
    width: '150',
    field: 'ineffectivenessLockCount',
    headerText: i18n.t('不合格库存数量')
  },
  {
    width: '150',
    field: 'siteName',
    headerText: i18n.t('VMI仓创建人')
  }
]
