import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'rowNum',
    headerText: i18n.t('行号')
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'sourceStockType',
    headerText: i18n.t('转出库存状态')
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('调拨数量')
  },
  {
    width: '150',
    field: 'enterStatus',
    headerText: i18n.t('转入库存状态')
  },
  {
    width: '150',
    field: 'itemUnit',
    headerText: i18n.t('单位')
  },
  {
    width: '150',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组')
  },
  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
