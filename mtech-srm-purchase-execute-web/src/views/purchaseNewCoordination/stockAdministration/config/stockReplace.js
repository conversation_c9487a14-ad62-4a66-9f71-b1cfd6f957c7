import { i18n } from '@/main.js'
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    width: '200',
    field: 'rowNum',
    headerText: i18n.t('行号')
  },
  {
    width: '200',
    field: 'itemCode',
    headerText: i18n.t('调出物料编码')
  },
  {
    width: '150',
    field: 'itemName',
    headerText: i18n.t('调出物料名称')
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('调出采购组')
  },
  {
    width: '150',
    field: 'enterStatus',
    headerText: i18n.t('库存状况')
  },
  {
    width: '150',
    field: 'batchCode',
    headerText: i18n.t('卷号')
  },
  {
    width: '150',
    field: 'count',
    headerText: i18n.t('调出数量')
  },
  {
    width: '150',
    field: 'exchangeItemCode',
    headerText: i18n.t('调入物料编码')
  },
  {
    width: '150',
    field: 'exchangeItemName',
    headerText: i18n.t('调入物料名称')
  },
  {
    width: '150',
    field: 'itemUnit',
    headerText: i18n.t('单位')
  },

  {
    width: '150',
    field: 'remark',
    headerText: i18n.t('行备注')
  }
]
