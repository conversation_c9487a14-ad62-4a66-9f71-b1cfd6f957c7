// VMI领料管理-采方
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :permission-obj="permissionObj"
      :current-tab="currentTab"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <steelDemandList slot="slot-0" class="full-height" />
      <steelDemandDetail slot="slot-1" class="full-height" />
      <steelDemandrelation slot="slot-2" class="full-height" />
    </mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'

import { columnObj } from './config/index.js'
export default {
  components: {
    // deliveryDialog,
    createDialog: require('./components/createDialog').default,
    steelDemandList: require('./components/steelDemandList').default,
    steelDemandDetail: require('./components/steelDemandDetail').default,
    steelDemandrelation: require('./components/steelDemandrelation').default
  },
  data() {
    return {
      currentTab: 0, //代表当前默认加载显示的Tab索引
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0132' },
          { dataPermission: 'b', permissionCode: 'T_02_0133' }
        ]
      },
      pageConfig: [
        {
          tab: { title: this.$t('钢材需求单') }
        },
        {
          tab: { title: this.$t('钢材需求明细') }
        },
        {
          tab: { title: this.$t('钢材需求和调料对应关系') }
        },
        {
          tab: { title: this.$t('调料单') },
          useBaseConfig: false, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          useToolTemplate: false,
          dataPermission: 'a',
          permissionCode: 'T_02_0132',
          // grid:'cd06cc3c-58a2-48ef-a0d7-5e0ec4883f40',
          toolbar: {
            tools: [
              [
                {
                  id: 'add',
                  icon: 'icon_solid_Createorder',
                  permission: ['O_02_1270'],
                  title: this.$t('创建VMI领料单')
                },
                {
                  id: 'Delete',
                  icon: 'icon_solid_Delete',
                  permission: ['O_02_1271'],
                  title: this.$t('删除VMI领料单')
                },
                {
                  id: 'Submit',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('提交')
                },
                {
                  id: 'Import',
                  icon: 'icon_solid_Import',
                  permission: ['O_02_1271'],
                  title: this.$t('导入')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          grid: {
            // allowGrouping: true, // 拖动表格列置此以聚焦
            columnData: columnObj.headColumn,
            // dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/buyer-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('调料明细') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'a',
          permissionCode: 'T_02_0133',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'download',
                  icon: 'icon_solid_export',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: 'B4E2D15B-C5A7-F563-0493-CE1EB13DB6A6',
          grid: {
            columnData: columnObj.detailedColumn,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-pickup-order/buyer-item-page-query'
              // recordsPosition: "data",
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null,
      deliveryShow: false
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('createDate')
  },
  methods: {
    handleDialogShow(flag) {
      this.deliveryShow = flag
    },
    handleAddDialogShow() {
      this.deliveryShow = false
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        id: '',
        details: 1
      }
      if (e.field === 'vmiOrderCode') {
        obj.id = e.data.vmiOrderId ?? e.data.id
        this.redirectPage('purchase-execute/new-coordination-picking-establish', obj)
      }
    },
    // 头部跳转
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Import') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('上传/导入'),
            importApi: this.$API.thirdPartyVMICollaboration.buyerExcelImport,
            downloadTemplateApi: this.$API.thirdPartyVMICollaboration.buyerExportOutInventoryItem,
            paramsKey: 'excel',
            asyncParams: {
              // requestJson: JSON.stringify(parameter),
            }
          },
          success: () => {
            // 导入之后刷新列表
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        return
      }
      if (e.toolbar.id === 'download') {
        // 导出
        this.downLoadExport()
      }
      if (e.toolbar.id === 'add') {
        this.deliveryShow = true

        // VMI领料单-采方-创建VMI领料单-跳转到新建页面
        // this.redirectPage(
        //   "purchase-execute/coordination-picking-establish",
        //   {}
        // );
      } else if (e.toolbar.id == 'Delete') {
        // VMI领料单-采方-批量删除
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('确认删除吗？')
          },
          success: () => {
            let ids = _selectRows.map((item) => item.id)
            this.$API.purchaseCoordination.VMINewPickupOrderDelete({ ids: ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('删除成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else if (e.toolbar.id == 'Submit') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        const ids = []
        for (let i = 0; i < _selectRows.length; i++) {
          const element = _selectRows[i]
          if (element.status !== '0' && element.status !== 0) {
            this.$toast({
              content: this.$t('所选数据存在非新建状态的数据'),
              type: 'warning'
            })
            return
          }
          ids.push(element.id)
        }
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.$API.purchaseCoordination.VMINewPickupOrderBatchFastSubmit({ ids }).then((res) => {
              if (res.code == 200) {
                this.$toast({
                  content: this.$t('提交成功'),
                  type: 'success'
                })
                // 刷新当前 Grid
                this.$refs.templateRef.refreshCurrentGridData()
              }
            })
          }
        })
      } else {
        // 调用刷新方法
      }
    },
    downLoadExport() {
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      console.log(queryBuilderRules)
      let obj = JSON.parse(
        sessionStorage.getItem('B4E2D15B-C5A7-F563-0493-CE1EB13DB6A6')
      ).visibleCols
      let field = []
      if (obj !== undefined) {
        obj.forEach((item) => {
          field.push(item.field)
        })
      } else {
        columnObj.detailedColumn.forEach((item) => {
          field.push(item.field)
        })
      }
      const params = {
        page: { current: 1, size: 1000 },
        ...queryBuilderRules,
        sortedColumnStr: field.toString()
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.thirdPartyVMICollaboration.postVmiSteelPickupBuyerExport(params).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'submit') {
        this.$dialog({
          data: {
            title: this.$t('提交'),
            message: this.$t('确认提交吗？')
          },
          success: () => {
            this.$API.purchaseCoordination
              .VMINewPickupOrderFastSubmit({ id: e.data.id })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('提交成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      } else if (e.tool.id == 'cancel') {
        this.$dialog({
          data: {
            title: this.$t('取消'),
            message: this.$t('确认取消吗？')
          },
          success: () => {
            this.$API.purchaseCoordination
              .VMINewPickupOrderCancel({ id: e.data.id })
              .then((res) => {
                if (res.code == 200) {
                  this.$toast({
                    content: this.$t('取消成功'),
                    type: 'success'
                  })
                  // 刷新当前 Grid
                  this.$refs.templateRef.refreshCurrentGridData()
                }
              })
          }
        })
      }
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
