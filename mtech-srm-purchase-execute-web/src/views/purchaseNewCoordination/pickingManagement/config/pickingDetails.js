import { i18n } from '@/main.js'
import Vue from 'vue'
import { MasterDataSelect } from '@/utils/constant'

// VMI配置管理
export const headColumnCreate = (that, status) => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '100',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '150',
      field: 'stockCount',
      headerText: i18n.t('库存数量')
    },
    // {
    //   width: "200",
    //   field: "vmiOrderCode",
    //   headerText: i18n.t("VMI调料单号"),
    // },
    // {
    //   width: "100",
    //   field: "rowNum",
    //   headerText: i18n.t("计划组"), // 缺
    // },
    {
      width: '100',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组')
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '150',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    // {
    //   width: "150",
    //   field: "currentPickupAmount",
    //   headerText: i18n.t("本次调料金额"),
    // },
    // {
    //   width: "150",
    //   field: "availablePickupAmount",
    //   headerText: i18n.t("可调料金额"),
    // },
    // {
    //   width: "150",
    //   field: "count",
    //   headerText: i18n.t("可调料数量"),
    // },
    {
      width: '100',
      field: 'count',
      headerText: i18n.t('卷重')
    },
    {
      width: '100',
      field: 'orderCode',
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '150',
      field: 'itemUnit',
      headerText: i18n.t('单位'),
      template: () => {
        return {
          template: Vue.component('actionInput', {
            template: `<div>{{data.itemUnit}}-{{data.itemUnitDescription}}</div>`,
            data: function () {
              return { data: {} }
            },
            mounted() {},
            methods: {}
          })
        }
      }
    },
    {
      width: '100',
      field: 'currentPickupAmount',
      headerText: i18n.t('金额')
    },
    {
      width: '200',
      field: 'remark',
      headerText: i18n.t('行备注'),
      template: () => {
        return {
          template: Vue.component('remark', {
            template: `<mt-input v-if="statusCreate" v-model="data.remark" @change="handleChange" type="text" :placeholder="$t('请输入备注')"></mt-input>
            <span v-else>{{data.remark}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                console.log(e)
                console.log(this.data)
                that.$set(that.pageConfig[0].grid.dataSource[this.data.index], 'remark', e)
                // sessionStorage.setItem("createDate", JSON.stringify(this.headerInfo));
                // that.pageConfig[0].grid.dataSource[this.data.index].remark = e;s
                // that.$set(
                //   that.formObject.itemList[Number(this.data.index)],
                //   "remark",
                //   e
                // );
              }
            }
          })
        }
      }
    },
    {
      width: '170',
      field: 'createUserName',
      headerText: i18n.t('创建人')
    },
    {
      width: '170',
      field: 'createTime',
      headerText: i18n.t('创建时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      }
    }
  ]
}

export const headColumnDetail = (that, status) => {
  return [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '100',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料描述')
    },
    // {
    //   width: "150",
    //   field: "stockType",
    //   headerText: i18n.t("库存状态"),
    //   valueConverter: {
    //     type: "map",
    //     map: { 0: i18n.t("合格库存"), 1: i18n.t("待检库存"), 2: "不合格库存"},
    //   },
    // },
    {
      width: '150',
      field: 'takeNo', //
      headerText: i18n.t('车号')
    },
    {
      width: '150',
      field: 'stockCount',
      headerText: i18n.t('库存数量')
    },
    {
      width: '150',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    // {
    //   width: "150",
    //   field: "currentPickupAmount",
    //   headerText: i18n.t("本次调料金额"),
    // },
    // {
    //   width: "150",
    //   field: "availablePickupAmount",
    //   headerText: i18n.t("可调料金额"),
    // },
    // {
    //   width: "150",
    //   field: "countLimit",
    //   headerText: i18n.t("可领料数量"),
    // },
    {
      width: '150',
      field: 'checkCount',
      headerText: i18n.t('领料数量'),
      template: () => {
        return {
          template: Vue.component('checkCount', {
            template: `<mt-input-number
                        v-if="statusCreate"
                        v-model="data.checkCount"
                        :min="1"
                        :max="data.countLimit"
                        cssClass="e-outline"
                        :show-clear-button="false"
                        @change="handleChange"
                        :placeholder="$t('请输入领料数量')">
                      </mt-input-number>
            <span v-else>{{data.checkCount}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                that.$set(that.formObject.itemList[Number(this.data.index)], 'checkCount', e)
              }
            }
          })
        }
      }
    },
    {
      width: '150',
      field: 'itemUnit',
      headerText: i18n.t('单位'),
      template: () => {
        return {
          template: Vue.component('actionInput', {
            template: `<div>{{data.itemUnit}}-{{data.itemUnitDescription}}</div>`,
            data: function () {
              return { data: {} }
            },
            mounted() {},
            methods: {}
          })
        }
      }
    },
    {
      width: '150',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组')
    },
    {
      width: '200',
      field: 'remark',
      headerText: i18n.t('行备注'),
      template: () => {
        return {
          template: Vue.component('remark', {
            template: `<mt-input v-if="statusCreate" v-model="data.remark" @change="handleChange" type="text" :placeholder="$t('请输入备注')"></mt-input>
            <span v-else>{{data.remark}}</span>`,
            data() {
              return { data: {} }
            },
            computed: {
              statusCreate() {
                return status == 0
              }
            },
            methods: {
              handleChange(e) {
                that.$set(that.formObject.itemList[Number(this.data.index)], 'remark', e)
              }
            }
          })
        }
      }
    },
    {
      width: '170',
      field: 'orderCode',
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '170',
      field: 'lineNo',
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '170',
      field: 'steelDemandCode',
      headerText: i18n.t('调料需求单号')
    },
    {
      width: '170',
      field: 'steelDemandLineNo',
      headerText: i18n.t('调料需求单行号')
    }
  ]
}

// 领料-采方-创建-选择VMI库存行
// let params =  qualificationColumns[0].type === "checkbox"?
//                  qualificationColumns.unshift({width: "50",  type: "checkbox",}):
//                  qualificationColumns.shift({width: "50",  type: "checkbox",})
export const pickingDetailsPageConfigCreate = (this_, status, dataSource = []) => [
  {
    tab: { title: this_.$t('物料信息') },
    useBaseConfig: false, // 使用组件中的toolbar配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: {
      useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          'delete'
          // {
          //   id: "Delete",
          //   icon: "icon_solid_Delete",
          //   // permission: ["O_02_1086"],
          //   title: this.$t("删除VMI领料单"),
          // },
        ]
        // ["Filter", "Refresh", "Setting"],
      ]
    },
    // gridId: "5a10ee74-bf2b-4ddf-b319-4c1961753f6d",
    grid: {
      allowPaging: false, // 单据详情页不需要分页
      columnData: headColumnCreate(this_, status),

      dataSource: dataSource,

      frozenColumns: 1
    }
  }
]

// 领料-详情
export const pickingDetailsPageConfigDetail = (this_, status, dataSource = []) => [
  {
    tab: { title: this_.$t('物料信息') },
    useBaseConfig: false, // 使用组件中的toolbar配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: {
      // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'Delete',
            icon: 'icon_solid_Delete',
            // permission: ["O_02_1085"],
            visibleCondition: () => status === 1 || status === 0 || status === 2,
            title: i18n.t('删除')
          }
        ],
        // ["Filter", "Refresh", "Setting"],
        ['Refresh', 'Setting']
      ]
    },
    // gridId: "b1581ae0-3a42-47bc-8eaa-748f9a741a60",
    grid: {
      height: 405,
      allowPaging: false, // 单据详情页不需要分页
      columnData: headColumnDetail(this_, status),
      // dataSource: [
      //   {
      //     companyCode: "630847",
      //     companyName: "TCL空调器有限公司生产工厂",
      //     warehousingCode: "w20211227001",
      //     warehouseCode: "0100001",
      //     warehouseDescribe: "VMI红物流芜湖威灵电机",
      //     WarehouseAddress: i18n.t("深圳市福田区下梅林梅华路"),
      //     WarehouseType: i18n.t("随着后勤"),
      //     isPassword: i18n.t("是"),
      //     isScreening: i18n.t("是"),
      //     stockStatus: i18n.t("待检库存"),
      //     createPeople: "jcs",
      //     createTime: "2021-12-28",
      //     isEffective: i18n.t("是"),
      //   },
      // ],
      dataSource: dataSource,
      // asyncConfig: {
      //   url: "/srm-purchase-execute/tenant/vmi-pickup-order/detail",
      //   recordsPosition: "data.records.vmiOrderItemResponses",
      // },
      frozenColumns: 1
    }
  }
]

// 领料-供方-确认后-打印
export const pickingDetailsPageConfigPrint = (this_, status, dataSource = []) => [
  {
    tab: { title: this_.$t('物料信息') },
    useBaseConfig: false, // 使用组件中的toolbar配置
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    toolbar: {
      // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
      tools: [
        [
          {
            id: 'print',
            icon: 'icon_solid_Createorder',
            title: this_.$t('打印')
          }
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    grid: {
      allowPaging: false, // 单据详情页不需要分页
      columnData: headColumnDetail(this_, status),
      // dataSource: [
      //   {
      //     companyCode: "630847",
      //     companyName: "TCL空调器有限公司生产工厂",
      //     warehousingCode: "w20211227001",
      //     warehouseCode: "0100001",
      //     warehouseDescribe: "VMI红物流芜湖威灵电机",
      //     WarehouseAddress: i18n.t("深圳市福田区下梅林梅华路"),
      //     WarehouseType: i18n.t("随着后勤"),
      //     isPassword: i18n.t("是"),
      //     isScreening: i18n.t("是"),
      //     stockStatus: i18n.t("待检库存"),
      //     createPeople: "jcs",
      //     createTime: "2021-12-28",
      //     isEffective: i18n.t("是"),
      //   },
      // ],
      dataSource: dataSource,
      // asyncConfig: {
      //   url: "/srm-purchase-execute/tenant/vmi-pickup-order/detail",
      //   recordsPosition: "data.records.vmiOrderItemResponses",
      // },
      frozenColumns: 1
    }
  }
]
