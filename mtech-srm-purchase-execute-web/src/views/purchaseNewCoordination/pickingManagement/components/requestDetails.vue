<template>
  <div class="detail-top-info" style="padding-right: 20px">
    <!-- 顶部信息 -->
    <div class="header-box">
      <div class="middle-blank" />
      <span class="header-box-btn" v-waves type="info" @click="$router.go(-1)">{{
        $t('返回')
      }}</span>
      <span
        v-if="formObject.status === 1"
        class="header-box-btn"
        v-waves
        type="info"
        @click="handleSubmit"
        >{{ $t('通过') }}</span
      >
      <span
        v-if="formObject.status === 1"
        class="header-box-btn"
        v-waves
        type="primary"
        @click="handleCancel"
        >{{ $t('退回') }}</span
      >
    </div>
    <div class="main-bottom">
      <mt-form ref="ruleForm" :model="formObject">
        <mt-form-item prop="createUserName" :label="$t('创建人')">
          <mt-input v-model="formObject.createUserName" disabled />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')" class="">
          <mt-input v-model="formObject.createTime" disabled />
        </mt-form-item>
        <mt-form-item prop="updateUserName" :label="$t('更新人')">
          <mt-input v-model="formObject.updateUserName" disabled />
        </mt-form-item>
        <mt-form-item prop="updateTime" :label="$t('更新时间')" class="">
          <mt-input v-model="formObject.updateTime" disabled />
        </mt-form-item>
        <mt-form-item prop="demandCode" :label="$t('需求单编号')">
          <mt-input v-model="formObject.demandCode" disabled />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')">
          <mt-select
            v-model="formObject.status"
            :data-source="statusOptions"
            :fields="{ text: 'text', value: 'value' }"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            disabled
            :placeholder="$t('请选择状态')"
          />
        </mt-form-item>
        <mt-form-item prop="siteCode" :label="$t('工厂')">
          <mt-input v-model="formObject.siteCode" disabled />
        </mt-form-item>
        <mt-form-item prop="supplierCode" :label="$t('钣金厂')">
          <mt-input v-model="formObject.supplierCode" disabled />
        </mt-form-item>
        <mt-form-item prop="sendAddress" :label="$t('送货地址')">
          <mt-input v-model="formObject.sendAddress" disabled />
        </mt-form-item>
        <mt-form-item prop="financeShareOrgName" :label="$t('财务共享组织')">
          <mt-input v-model="formObject.financeShareOrgName" disabled />
        </mt-form-item>
        <mt-form-item prop="financeShareCompany" :label="$t('财务共享公司代码')">
          <mt-input v-model="formObject.financeShareCompany" disabled />
        </mt-form-item>
        <mt-form-item prop="payAccount" :label="$t('应付账款')">
          <mt-input v-model="formObject.payAccount" disabled />
        </mt-form-item>
        <mt-form-item prop="receiveAccount" :label="$t('应收账款')">
          <mt-input v-model="formObject.receiveAccount" disabled />
        </mt-form-item>
        <mt-form-item prop="onWayAccount" :label="$t('其它在途单据送货金额')">
          <mt-input v-model="formObject.onWayAccount" disabled />
        </mt-form-item>
        <mt-form-item prop="amountLimit" :label="$t('倒挂额度')">
          <mt-input v-model="formObject.amountLimit" disabled />
        </mt-form-item>
        <mt-form-item prop="remainAmount" :label="$t('剩途可调料金额')">
          <mt-input v-model="formObject.remainAmount" disabled />
        </mt-form-item>
        <mt-form-item prop="amount" :label="$t('本次调料金额')">
          <mt-input v-model="formObject.amount" disabled />
        </mt-form-item>
        <mt-form-item prop="rejectReason" :label="$t('退回原因')" class="full-width">
          <mt-input v-model="formObject.rejectReason" disabled />
        </mt-form-item>
      </mt-form>
    </div>
    <div class="table-area">
      <ScTable
        ref="xTable"
        class="vxe-table-area"
        :row-config="{ height: 50 }"
        :columns="scColumns"
        :table-data="tableData"
      />
    </div>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { scColumns, statusOptions } from '../config/steelDemandColumn'
export default {
  components: {
    ScTable
  },
  data() {
    return {
      statusOptions,
      scColumns,
      tableData: [],
      formObject: {},
      headerId: ''
    }
  },
  mounted() {
    if (this.$route.query?.id) {
      this.$loading()
      this.headerId = this.$route.query.id
      this.getDatailData()
      this.getTableData()
    }
  },
  methods: {
    getDatailData() {
      this.$API.thirdPartyVMICollaboration
        .getSteelDemandHeaderByIdApi({ id: this.headerId })
        .then((res) => {
          const { code, data } = res
          if (code === 200) {
            this.formObject = data
          }
        })
    },
    getTableData() {
      this.$API.thirdPartyVMICollaboration
        .pageDetailBuyerSteelDemandApi({
          demandId: this.headerId
        })
        .then((res) => {
          this.$hloading()
          const { code, data } = res
          if (code === 200) {
            this.tableData = data
          }
        })
    },
    handleSubmit() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认通过当前数据吗？')
        },
        success: () => {
          this.$store.commit('startLoading')
          this.$API.thirdPartyVMICollaboration
            .passSteelDemandApi({
              id: this.headerId
            })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t(`通过成功`),
                  type: 'success'
                })
                this.$router.go(-1)
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleCancel() {
      this.$dialog({
        data: {
          title: this.$t('退回')
        },
        modal: () =>
          import(/* webpackChunkName: "./components/rejectDialog.vue" */ './rejectDialog.vue'),
        success: (rejectReason) => {
          // 退回
          let params = {
            id: this.headerId,
            rejectReason
          }
          this.$store.commit('startLoading')
          this.$API.thirdPartyVMICollaboration
            .backSteelDemandApi(params)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  content: this.$t(`退回成功`),
                  type: 'success'
                })
                this.$router.go(-1)
              }
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-area {
  /deep/.tab-item2 div {
    // background: red !important;
    &:before {
      content: '*';
      color: #f44336;
      font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system',
        'BlinkMacSystemFont';
      font-size: 12px;
      font-weight: normal;
      margin-right: -4px;
    }
  }
}
</style>
