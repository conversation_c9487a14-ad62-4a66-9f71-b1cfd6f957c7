// VMI库存管理
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    class="display-block"
    :header="dialogTitle"
    :buttons="buttons"
    :open="onOpen"
    width="900px"
  >
    <div class="full-height pt20">
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="true"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @currentChange="handleCurrentChange"
      ></mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { columnData } from '../config/table.js'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      dialogTitle: this.$t('选择'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          title: this.$t('汇总列表'),
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          gridId: '283bd956-9db6-4535-b73c-086bee671d31',
          grid: {
            columnData: columnData,
            // lineIndex: 1,
            // autoWidthColumns: columnData.length + 1,
            dataSource: [
              {
                factoryCode: '01',
                factoryName: this.$t('测试1'),
                supplierCode: '001',
                supplierName: this.$t('京东'),
                warehouseCode: this.$t('仓库编码'),
                warehouseName: this.$t('仓库名称'),
                materialCode: '0001',
                materialName: this.$t('物料名称'),
                purchaseGroup: this.$t('采购组'),
                stockStatus: this.$t('充足'),
                batchNumber: 'SN0003',
                stockNumber: '99',
                founder: 'jcs'
              }
            ],
            // asyncConfig: {
            //   url: "/srm-purchase-execute/tenant/pe/business/configs",
            //   recordsPosition: "data",
            // },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null,
      listId: []
    }
  },

  methods: {
    handleCurrentChange() {
      debugger
      console.log('currentChange', 8888888999999)
    },
    // 头部点击
    handleClickToolBar(e) {
      if (!(e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'choice')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      // if (e.grid.getSelectedRecords().length > 0 && e.toolbar.id === 'choice') {
      // } else {
      //   this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      //   return
      // }
    },
    handleClickCellTool(e) {
      console.log(e)
    },
    // 打开弹窗
    initDialog() {
      this.$refs.dialog.ejsRef.show()
    },
    onOpen(args) {
      args.preventFocus = true
    },
    // 点击确认按钮
    confirm() {
      this.listId = this.$refs.templateRef?.getCurrentUsefulRef()?.ejsRef?.getSelectedRecords()
      this.$emit('dilogData', this.listId)
      this.$refs.dialog.ejsRef.hide()
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
