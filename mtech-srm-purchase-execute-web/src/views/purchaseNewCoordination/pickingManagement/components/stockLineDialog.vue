<template>
  <!-- 选择VMI库存行==添加 -->
  <mt-dialog ref="thresholdDialog" :buttons="buttons" :header="header" @beforeClose="cancel">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      @handleClickCellTitle="handleClickCellTitle"
      @checkBoxChange="rowSelected"
    ></mt-template-page>
  </mt-dialog>
</template>
<script>
import { stockLineColumns } from '../config/stockLineColumns.js'
export default {
  data() {
    let objArr = [
      {
        field: 'siteCode',
        label: this.$t('工厂编号'),
        operator: 'contains',
        type: 'string',
        value: 'CGGC'
      },
      {
        field: 'supplierCode',
        label: this.$t('供应商编码'),
        operator: 'contains',
        type: 'string',
        value: '3'
      },
      {
        field: 'vmiWarehouseCode',
        label: this.$t('VMI仓编码'),
        operator: 'contains',
        type: 'string',
        value: 'ddd'
      }
    ]

    objArr[0].value = this.modalData.fieldObj.siteCode
    objArr[1].value = this.modalData.fieldObj.supplierCode
    objArr[2].value = this.modalData.fieldObj.vmiWarehouseCode

    return {
      pageConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [[], ['Filter', 'Refresh', 'Setting']]
          },
          gridId: '0b9b7453-6c86-4cf6-afbc-0d69da1828ca',
          grid: {
            lineIndex: true,
            columnData: stockLineColumns,
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmi_stock/page-query',
              afterAsyncData: (res) => {
                if (res.code === 200 && res.data.records) {
                  this.dataSource = res.data.records
                  let arrNum = []
                  this.dataSource.map((i, idx) => {
                    this.info.map((ic) => {
                      if (i.itemCode === ic.itemCode) {
                        arrNum.push(idx)
                      }
                    })
                  })
                  setTimeout(() => {
                    this.$refs.templateRef.getCurrentTabRef().grid.selectRows(arrNum, true)
                  }, 300)
                }
              },
              defaultRules: [
                {
                  field: 'count',
                  label: this.$t('库存数量'),
                  operator: 'greaterthan',
                  type: 'string',
                  value: '0'
                },
                ...objArr
              ]
            }
          }
        }
      ],
      buttons: [
        {
          click: this.cancel,
          // buttonModel: { content: this.$t("取消") },
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          // buttonModel: { isPrimary: "true", content: this.$t("添加") },
          buttonModel: { isPrimary: 'true', content: this.$t('添加') }
        }
      ],
      dataSource: [],
      itemList: [],
      apiWaitingQuantity: 0 // 调用的api正在等待数
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    info() {
      return this.modalData.info
    }
  },
  created() {
    // this.pageConfig = []
    // this.pageConfig[0].grid.asyncConfig.rules = this.pageConfig[0].grid.asyncConfig.rules.concat(objArr)
  },
  mounted() {
    this.show()
    this.itemList = JSON.parse(JSON.stringify(this.modalData.info))
  },
  methods: {
    rowSelected(e) {
      let selectArr = []
      let noselectArr = []
      this.dataSource.forEach((item, index) => {
        if (e.selectedRowIndexes.indexOf(index) != -1) {
          selectArr.push(item)
        } else {
          noselectArr.push(item)
        }
      })
      selectArr.forEach((item) => {
        let bol = this.itemList.some((e) => {
          return e.id == item.id
        })
        if (!bol) {
          this.itemList.push(item)
        }
      })
      for (let i = 0; i < this.itemList.length; i++) {
        let bol = noselectArr.some((item) => {
          return item.id == this.itemList[i].id
        })
        if (bol) {
          this.itemList.splice(i, 1)
          i--
        }
      }
    },
    handleClickCellTitle(e) {
      if (e.field) {
        let link = document.createElement('a') // 创建元素
        link.style.display = 'none'
        link.id = new Date().getTime()
        link.href = e.data.modelUrl
        link.setAttribute('download', `${e.data.modelName}`) // 给下载后的文件命名
        document.body.appendChild(link)
        link.click() // 点击下载
      }
    },
    show() {
      this.$refs['thresholdDialog'].ejsRef.show()
    },
    hide() {
      this.$refs['thresholdDialog'].ejsRef.hide()
    },
    confirm() {
      // let arr = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.getSelectedRecords(); //得到选择行

      // if (this.itemList.length == 0) return this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
      if (this.itemList.length == 0)
        return this.$toast({
          content: this.$t('请先选择一行'),
          type: 'warning'
        })
      this.apiStartLoading()
      let selectedItemList = JSON.parse(JSON.stringify(this.itemList))
      // 根据 “卷号” 查询 “可领料数量”、“采购订单号”、“采购订单行号”（批量）
      let batchCodes = selectedItemList.map((item) => {
        let newItem = {
          batchCode: item.batchCode,
          count: item.count,
          itemCode: item.itemCode
        }
        return newItem
      })
      this.queryItemLimit(batchCodes, selectedItemList, (fullItemList) => {
        // 请求完，整理好数据后再让弹窗消失，因为这几个字段都是必传项，请求失败后，主页面也提交保存不了。
        this.$emit('confirm-function', fullItemList)
      })
    },
    // 根据 “卷号” 查询 “可领料数量”、“采购订单号”、“采购订单行号”（批量）
    queryItemLimit(batchCodes, selectedItemList, cb) {
      this.$API.purchaseCoordination
        .queryItemLimit(batchCodes)
        .then((res) => {
          this.apiEndLoading()
          if (res.code == 200) {
            let resData = res.data || []
            resData.forEach((item, index) => {
              selectedItemList[index]['rowNum'] = index + 1 // 行号
              selectedItemList[index]['countLimit'] = item.countLimit // 可领料数量
              selectedItemList[index].orderCode = ''
              selectedItemList[index].lineNo = ''
              selectedItemList[index].planGroupCode = ''
              this.getBusiness(item.itemCode).then((res) => {
                selectedItemList[index].planGroupCode = res.data[0].groupCode
              })
              // if (item.poMatchResponses && item.poMatchResponses.length == 1) {
              //   selectedItemList[index]["orderCode"] = item.poMatchResponses[index].orderCode; // 关联采购订单号
              //   selectedItemList[index]["lineNo"] = item.poMatchResponses[index].lineNo; // 关联采购订单行号
              // }else
              // debugger
              // 如果说poMatchResponses为空   关联采购订单号与关联采购订单行号就会变成自己选择，否则就会批次卷号带出
              if (item.poMatchResponses && item.poMatchResponses.length > 0) {
                item.lineSelect = JSON.parse(JSON.stringify(item.poMatchResponses))
                item.poMatchResponses.forEach((ele) => {
                  ele.value = ele.orderCode
                  ele.text = ele.orderCode
                })
                item.lineSelect.forEach((ele) => {
                  ele.value = ele.lineNo
                  ele.text = ele.lineNo
                })
                selectedItemList[index].orderCodeArr = item.poMatchResponses // 关联采购订单号
                selectedItemList[index].lineNoArr = item.lineSelect // 关联采购订单行号
              } else {
                selectedItemList[index].orderCodeArr = [] // 关联采购订单号
                selectedItemList[index].lineNoArr = [] // 关联采购订单行号
              }
            })
            if (!!cb && typeof cb === 'function') {
              cb(selectedItemList)
            }
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    cancel() {
      this.$emit('cancel-function')
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    },
    // 调用计划组接口
    getBusiness(data) {
      let obj = {
        businessGroupTypeCode: 'BG001JH',
        itemCode: data
      }
      return this.$API.purchaseCoordination.VMIPickupOrderQueryBusiness(obj)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .form-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
/deep/ .relative-select {
  display: flex;
  justify-content: space-between;
  .mt-drop-down-tree {
    width: 300px;
  }
}
</style>
