<template>
  <div>
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :header="$t('新建')"
      :buttons="buttons"
      @close="handleClose"
      :height="690"
      :width="1200"
      :open="onOpen"
    >
      <mt-template-page
        ref="templateRef"
        :hidden-tabs="false"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      ></mt-template-page>
    </mt-dialog>
  </div>
</template>

<script>
import { createColumnObj } from '../config/index.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  props: {
    ids: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      pageConfig: [
        {
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'add',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1092"],
                  title: this.$t('创建')
                },
                {
                  id: 'export',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1092"],
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '90b7d2a6-72c5-4308-9b04-6572a915ac61',

          grid: {
            columnData: createColumnObj,
            dataSource: [],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi_stock/page-query',
              defaultRules: [
                {
                  field: 'count',
                  operator: 'greaterthan',
                  value: '0'
                }
              ],
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],

      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()

    // this.$refs.ruleForm.resetFields();
  },

  methods: {
    // 切换方式
    selectChange(e) {
      console.log(e)
      this.$refs.addForm.resetFields()
      console.log(this.addForm)
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'export') {
        this.exportDetail()
        return
      }
      // const mtechGridRecords = e.grid.getSelectedRecords();
      const mtechGridRecords = e.gridRef.getMtechGridRecords()
      if (mtechGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
      } else {
        if (e.toolbar.id === 'add') {
          for (let item of mtechGridRecords) {
            if (
              mtechGridRecords[0].siteCode !== item.siteCode ||
              mtechGridRecords[0].supplierCode !== item.supplierCode ||
              mtechGridRecords[0].vmiWarehouseCode !== item.vmiWarehouseCode
            ) {
              this.$toast({
                content: this.$t('只可以创建同一工厂同一供应商同一仓库的po'),
                type: 'warning'
              })
              return
            }
          }
          let obj = mtechGridRecords
          obj[0].processorCode = ''
          sessionStorage.setItem('createDate', JSON.stringify(mtechGridRecords))
          this.redirectPage('purchase-execute/new-coordination-picking-establish', {})
          this.handleClose()
        }
      }
    },
    exportDetail() {
      let obj = JSON.parse(
        sessionStorage.getItem('90b7d2a6-72c5-4308-9b04-6572a915ac61')
      )?.visibleCols
      let field = []
      if (obj !== undefined && obj.length) {
        obj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      } else {
        createColumnObj.forEach((item) => {
          if (item.field) {
            field.push(item.field)
          }
        })
      }
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        defaultRules: [
          {
            field: 'count',
            operator: 'greaterthan',
            value: '0'
          }
        ],
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件
      this.$store.commit('startLoading')
      this.$API.purchaseCoordination.VMINewPickupExport(params, field).then((res) => {
        this.$store.commit('endLoading')
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    onOpen(args) {
      args.preventFocus = true
      this.$refs.ruleForm.clearValidate()
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    },

    handleClose() {
      this.$emit('handleDialogShow', false)
    }
  }
}
</script>

<style></style>
