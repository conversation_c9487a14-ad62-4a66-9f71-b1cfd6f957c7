// 采方IQC
<template>
  <div class="full-height pt20 vertical-flex-box">
    <div>
      <top-info
        class="flex-keep"
        :header-info="headerInfo"
        @goBack="goBack"
        @doExpand="doExpand"
        @submitVerification="submitVerification"
        @submitChange="submitChange"
      ></top-info>
    </div>
    <hr />
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page v-show="tabIndex === 0" :template-config="componentConfig">
      <div slot="slot-0" :style="{ padding: '30px' }">
        <!-- <br /> -->
        <mt-data-grid
          ref="dataGrid"
          :data-source="dataSource"
          :column-data="columnData"
          :edit-settings="editing"
          :toolbar="toolbar"
          @actionBegin="actionBegin"
        ></mt-data-grid>
      </div>
    </mt-template-page>
    <div v-show="tabIndex === 1" :style="{ padding: '30px' }">
      <logistics-info :logistics-data="logisticsData"></logistics-info>
    </div>
  </div>
</template>

<script>
import { columnData, editing } from './config/establish'
export default {
  components: {
    TopInfo: () => import('./components/topInfo.vue'),
    LogisticsInfo: () => import('./components/logisticsInfo.vue')
  },
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: false // 使用组件中的toolbar配置
        }
      ],
      dataSource: [],
      columnData: columnData,
      // toolbar: ["Edit", "Cancel", "Update"], //编辑和取消、保存按钮
      editing: editing,
      headerInfo: {
        sentenceStatus: false,
        verificationStatus: false
      }, //头部信息
      logisticsData: {}, // 物流信息
      unqualifiedList: [
        {
          label: this.$t('包装不符'),
          value: '0'
        },
        {
          label: this.$t('其他不良'),
          value: '1'
        },
        {
          label: this.$t('外观不符'),
          value: '2'
        },
        {
          label: this.$t('内容不符'),
          value: '3'
        },
        {
          label: this.$t('材质不符'),
          value: '4'
        },
        {
          label: this.$t('标志不符'),
          value: '5'
        },
        {
          label: this.$t('尺寸不符'),
          value: '6'
        },
        {
          label: this.$t('认知不符'),
          value: '7'
        },
        {
          label: this.$t('性能不符'),
          value: '8'
        },
        {
          label: this.$t('错混料'),
          value: '9'
        }
      ],
      tabSource: [
        {
          title: this.$t('物料信息')
        }
      ],
      tabIndex: 0
    }
  },
  mounted() {
    // _thisFunction(this)
    this.getDetailData()
  },
  created() {},
  methods: {
    // 切换
    handleSelectTab(e) {
      this.tabIndex = e
      // 切换时结束编辑
      this.$refs.dataGrid.ejsRef.endEdit()
    },
    goBack() {
      this.$router.go(-1)
    },
    // 展开收起的hearder
    doExpand() {
      this.$refs.templateRef?.resetGridHeight && this.$refs.templateRef.resetGridHeight()
    },
    // 提交校验结果
    // submitVerification() {
    //   let obj = {
    //     id: this.$route.query.id,
    //     itemList: this.dataHandle(this.$refs.dataGrid.ejsRef.getCurrentViewRecords()),
    //     operationType: 2,
    //   };
    //   this.submitData(obj);
    // },
    // 提交改判结果
    // submitChange() {
    //   let obj = {
    //     id: this.$route.query.id,
    //     itemList: this.dataHandle(
    //       this.$refs.dataGrid.ejsRef.getCurrentViewRecords()
    //     ),
    //   };
    //   this.changeData(obj);
    // },
    // 转换不合格分类
    switchData(data) {
      let statusType = ''
      switch (data) {
        case this.$t('包装不符'):
          statusType = 0
          break
        case this.$t('其他不良'):
          statusType = 1
          break
        case this.$t('外观不符'):
          statusType = 2
          break
        case this.$t('内容不符'):
          statusType = 3
          break
        case this.$t('材质不符'):
          statusType = 4
          break
        case this.$t('标志不符'):
          statusType = 5
          break
        case this.$t('尺寸不符'):
          statusType = 6
          break
        case this.$t('认知不符'):
          statusType = 7
          break
        case this.$t('性能不符'):
          statusType = 8
          break
        case this.$t('错混料'):
          statusType = 9
          break
        default:
          break
      }
      return statusType
    },
    // 转换不合格分类
    switchData2(data) {
      let statusType = ''
      switch (data) {
        case 0:
          statusType = this.$t('包装不符')
          break
        case 1:
          statusType = this.$t('其他不良')
          break
        case 2:
          statusType = this.$t('外观不符')
          break
        case 3:
          statusType = this.$t('内容不符')
          break
        case 4:
          statusType = this.$t('材质不符')
          break
        case 5:
          statusType = this.$t('标志不符')
          break
        case 6:
          statusType = this.$t('尺寸不符')
          break
        case 7:
          statusType = this.$t('认知不符')
          break
        case 8:
          statusType = this.$t('性能不符')
          break
        case 9:
          statusType = this.$t('错混料')
          break
        default:
          break
      }
      return statusType
    },
    // 提交的数据处理
    dataHandle(data) {
      let arrObj = {}
      let arr = []
      let _this = this
      data.forEach((item) => {
        arrObj = {
          id: '',
          qcRemark: '',
          qcReport: '',
          unqualifiedTypes: ''
        }
        arrObj.qcRemark = item.qcRemark
        arrObj.id = item.id
        arrObj.unqualifiedTypes = _this.switchData(item.unqualifiedTypes)
        if (item.reportText === this.$t('不合格')) {
          arrObj.qcReport = '0'
        } else {
          arrObj.qcReport = '1'
        }
        arr.push(arrObj)
      })
      return arr
    },
    actionBegin(args) {
      if (args.requestType === 'beginEdit') {
        // if (args.rowIndex == 2) {
        //   args.cancel = true; //禁止行编辑
        // }
        // 当不是编辑页时，就是详情页 不可编辑
        if (this.$route.query.status != 2 && this.$route.query.status != 8) args.cancel = true //禁止行编辑
      }

      if (args.requestType == 'save') {
        console.log(args)
        //更新质检编辑完毕后的视图
        args.data.reportText = this.dataSource[args.rowIndex].reportText
        args.data.unqualifiedTypes = this.dataSource[args.rowIndex].unqualifiedTypes
      }
      if (args.data && args.data.reportText === this.$t('不合格') && !args.data.unqualifiedTypes) {
        this.$toast({
          content: this.$t('质检结果不合格，不合格原因必选,请完善哦~'),
          type: 'warning'
        })
        setTimeout(() => {
          this.$refs.dataGrid.ejsRef.selectRow(0)
          this.$refs.dataGrid.ejsRef.startEdit()
        })
      }
    },
    // 详情数据接口
    getDetailData() {
      let obj = {
        id: this.$route.query.id
      }
      let _this = this
      this.$API.purchaseCoordination.VmiPostBuyerWarehousingDetail(obj).then((res) => {
        if (res.code === 200) {
          this.headerInfo = res.data
          this.logisticsData = res.data.orderLogistic[0]
          this.dataSource = res.data.vmiOrderItemResponses
          this.dataSource.forEach((item) => {
            if (item.qcReport == 0) {
              item.reportText = this.$t('不合格')
            } else {
              item.reportText = this.$t('合格')
            }
            if (item.unqualifiedTypes)
              item.unqualifiedTypes = _this.switchData2(Number(item.unqualifiedTypes))
          })
          // 如果非已完成与已接受不可改判与校验   非这两种情况就清空表格头部操作
          if (this.$route.query.status === '2') {
            this.headerInfo.verificationStatus = true
          } else if (this.$route.query.status === '8') {
            this.headerInfo.sentenceStatus = true
          } else {
            this.toolbar = []
          }
          // code-name    头部以这种展示
          this.headerInfo.siteName = res.data.siteCode + '-' + res.data.siteName
          this.headerInfo.supplierCode = res.data.supplierCode + '-' + res.data.supplierName
          this.headerInfo.vmiWarehouseCode =
            res.data.vmiWarehouseCode + '-' + res.data.vmiWarehouseName
        }
      })
    }
    // 提交校验结果
    // submitData(data) {
    //   this.$API.purchaseCoordination
    //     .postBuyerWarehousingPreservation(data)
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.$toast({ content: this.$t("提交成功"), type: "success" });
    //         this.goBack()
    //       } else {
    //         this.$toast({ content: this.$t("提交失败"), type: "success" });
    //       }
    //     });
    // },
    // 改判接口
    // changeData(data) {
    //   this.$API.purchaseCoordination
    //     .postBuyerWarehousingChange(data)
    //     .then((res) => {
    //       if (res.code === 200) {
    //         this.$toast({ content: this.$t("改判成功"), type: "success" });
    //         this.goBack()
    //       } else {
    //         this.$toast({ content: this.$t("改判失败"), type: "success" });
    //       }
    //     });
    // },
  }
}
</script>

<style scoped lang="scss"></style>
