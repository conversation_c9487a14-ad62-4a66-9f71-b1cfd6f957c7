import { i18n } from '@/main.js'
// import cellChanged from "@/components/normalEdit/cellChanged";
const numberFields = ['normalNumber1', 'normalNumber2', 'resNumber']
var wholeObj = {}
numberFields.forEach((item) => {
  wholeObj[`${item}Ele`] = null
  wholeObj[`${item}Obj`] = null
})
// tab
export const Tab = {
  list: 1, // 物料信息
  details: 2 // 物流信息
}

// 送货类型:1-关联采购订单,2-无采购订单
export const DeliveryType = {
  linked: 1, // 关联采购订单
  notLink: 2 // 无采购订单
}
// 送货类型 text
export const DeliveryTypeText = {
  [DeliveryType.linked]: i18n.t('关联采购订单'),
  [DeliveryType.notLink]: i18n.t('无采购订单')
}
// 送货类型 对应的 Options
export const DeliveryTypeOptions = [
  {
    // 关联采购订单
    value: DeliveryType.linked,
    text: DeliveryTypeText[DeliveryType.linked],
    cssClass: ''
  },
  {
    // 无采购订单
    value: DeliveryType.notLink,
    text: DeliveryTypeText[DeliveryType.notLink],
    cssClass: ''
  }
]

export const Status = {
  new: 0, // 新建
  beConfirmed: 1, // 待确认
  cancelled: 2, // 已接收
  // returned: 3, // 已退回
  received: 8, // 已完成
  cancel: 9 // 已取消
}
// 状态 text
export const StatusText = {
  [Status.beConfirmed]: i18n.t('待确认'),
  [Status.cancelled]: i18n.t('已接收'),
  [Status.received]: i18n.t('已完成'),
  [Status.new]: i18n.t('新建'),
  [Status.cancel]: i18n.t('已取消')
}
// 状态 对应的 css class
export const StatusCssClass = {
  [Status.beConfirmed]: 'col-active',
  [Status.cancelled]: 'col-active',
  [Status.cancel]: 'col-active',
  [Status.received]: 'col-active',
  [Status.new]: 'col-inactive'
}
// 状态 对应的 Options
export const StatusOptions = [
  {
    // 新建
    value: Status.new,
    text: StatusText[Status.new],
    cssClass: StatusCssClass[Status.new]
  },
  {
    // 发货中
    value: Status.shipping,
    text: StatusText[Status.shipping],
    cssClass: StatusCssClass[Status.shipping]
  },
  {
    // 已完成
    value: Status.completed,
    text: StatusText[Status.completed],
    cssClass: StatusCssClass[Status.completed]
  },
  {
    // 已取消
    value: Status.cancelled,
    text: StatusText[Status.cancelled],
    cssClass: StatusCssClass[Status.cancelled]
  },
  {
    // 已关闭
    value: Status.closed,
    text: StatusText[Status.closed],
    cssClass: StatusCssClass[Status.closed]
  }
]

// 发货方式:1-快递配送，2-物流配送
export const ShippingType = {
  express: 1, // 快递配送
  logistics: 2 // 物流配送
}
// 发货方式 text
export const ShippingTypeText = {
  [ShippingType.express]: i18n.t('快递配送'),
  [ShippingType.logistics]: i18n.t('物流配送')
}
// 发货方式 对应的 Options
export const ShippingTypeOptions = [
  {
    // 快递配送
    value: ShippingType.express,
    text: ShippingTypeText[ShippingType.express],
    label: ShippingTypeText[ShippingType.express],
    cssClass: ''
  },
  {
    // 物流配送
    value: ShippingType.logistics,
    text: ShippingTypeText[ShippingType.logistics],
    label: ShippingTypeText[ShippingType.logistics],
    cssClass: ''
  }
]
