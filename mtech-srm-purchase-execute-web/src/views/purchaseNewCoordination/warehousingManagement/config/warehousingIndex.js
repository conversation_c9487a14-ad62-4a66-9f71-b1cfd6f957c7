import { i18n } from '@/main.js'
import { timeNumberToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

export const columnObj = {
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI入库单号'),
      cellTools: []
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '120',
      field: 'onWayStatus',
      headerText: i18n.t('在途状态'),
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        operator: 'in'
      },
      cssClass: '',
      valueConverter: {
        type: 'map',
        map: [
          { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
          { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
          { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
          { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
          { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
          { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '95',
      field: 'siteCode',
      headerText: i18n.t('工厂编码')
    },
    {
      width: '225',
      field: 'siteName',
      headerText: i18n.t('工厂')
    },
    {
      width: '105',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓编码')
    },
    {
      width: '115',
      field: 'vmiWarehouseName',
      headerText: i18n.t('仓库名称')
    },
    {
      width: '110',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      width: '195',
      field: 'supplierName',
      headerText: i18n.t('供应商')
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '80',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'submitTime',
      headerText: i18n.t('制单时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    },
    {
      width: '150',
      field: 'confirmTime',
      headerText: i18n.t('接收日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    }
  ],
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'vmiOrderCode',
      headerText: i18n.t('VMI入库单号')
    },
    {
      width: '80',
      field: 'status',
      headerText: i18n.t('状态'),
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('新建'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('待确认'), cssClass: 'col-active' },
          { value: 2, text: i18n.t('已接收'), cssClass: 'col-active' },
          { value: 8, text: i18n.t('已完成'), cssClass: 'col-active' },
          { value: 9, text: i18n.t('已取消'), cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '120',
      field: 'onWayStatus',
      headerText: i18n.t('在途状态'),
      searchOptions: {
        elementType: 'multi-select',
        showSelectAll: true,
        operator: 'in'
      },
      cssClass: '',
      valueConverter: {
        type: 'map',
        map: [
          { text: i18n.t('未出发'), value: 0, cssClass: 'col-active' },
          { text: i18n.t('已入园'), value: 1, cssClass: 'col-active' },
          { text: i18n.t('已出发'), value: 2, cssClass: 'col-active' },
          { text: i18n.t('已报到'), value: 3, cssClass: 'col-active' },
          { text: i18n.t('已取消'), value: 4, cssClass: 'col-active' },
          { text: i18n.t('已关闭'), value: 5, cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '70',
      field: 'rowNum',
      headerText: i18n.t('行号')
    },
    {
      width: '115',
      field: 'itemCode',
      headerText: i18n.t('物料编码')
    },
    {
      width: '395',
      field: 'itemName',
      headerText: i18n.t('物料名称')
    },
    {
      width: '85',
      field: 'purchaseGroupName',
      headerText: i18n.t('采购组')
    },
    {
      width: '65',
      field: 'count',
      headerText: i18n.t('卷重')
    },
    {
      width: '150',
      field: 'takeNo',
      headerText: i18n.t('车号')
    },
    {
      width: '150',
      field: 'batchCode',
      headerText: i18n.t('卷号')
    },
    {
      width: '65',
      field: 'itemUnit',
      headerText: i18n.t('单位')
    },
    {
      width: '65',
      field: 'remarks',
      headerText: i18n.t('备注')
    },
    // {
    //   width: "100",
    //   field: "qcReport",
    //   headerText: i18n.t("质检结果"),
    // },
    // {
    //   width: "110",
    //   field: i18n.t("unqualifiedTypes"),
    //   headerText: i18n.t("不合格分类"),
    // },
    // {
    //   width: "100",
    //   field: "qcRemark",
    //   headerText: i18n.t("质检备注"),
    // },
    {
      width: '140',
      field: 'orderCode',
      headerText: i18n.t('关联采购订单号')
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('关联采购订单行号')
    },
    {
      width: '95',
      field: 'siteCode',
      headerText: i18n.t('工厂编码')
    },
    {
      width: '230',
      field: 'siteName',
      headerText: i18n.t('工厂名称')
    },
    {
      width: '115',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓编码')
    },

    {
      width: '120',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓库名称')
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码')
    },
    {
      width: '115',
      field: 'supplierName',
      headerText: i18n.t('供应商名称')
    },
    {
      width: '170',
      field: 'vmiWarehouseAddress',
      headerText: i18n.t('送货地址')
    },
    {
      width: '95',
      field: 'createUserName',
      headerText: i18n.t('制单人')
    },
    {
      width: '150',
      field: 'submitTime',
      headerText: i18n.t('制单时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange
        // renameField: "order.deliveryTime",
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    },
    {
      width: '150',
      field: 'confirmTime',
      headerText: i18n.t('接收日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange
      },
      valueConverter: {
        type: 'function',
        filter: (e) => {
          if (e && !isNaN(e) && `${e}`.length == 13) {
            e = Number(e)
            return timeNumberToDate({
              formatString: 'YYYY-mm-dd HH:MM:SS',
              value: e
            })
          } else {
            return '-'
          }
        }
      }
    }
  ]
}
