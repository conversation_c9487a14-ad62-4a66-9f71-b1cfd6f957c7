// VMI入库管理
<template>
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      current-tab="0"
      :permission-obj="permissionObj"
      :hidden-tabs="false"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
  </div>
</template>

<script>
import { columnObj } from './config/warehousingIndex.js'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    // addDialog: require("./components/addDialog.vue").default,
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          // 当前的dom元素
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0128' },
          { dataPermission: 'b', permissionCode: 'T_02_0129' }
        ]
      },
      pageConfig: [
        {
          tab: { title: this.$t('头视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useToolTemplate: false,
          useCombinationSelection: false,
          dataPermission: 'a',
          permissionCode: 'T_02_0128',
          toolbar: {
            // useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
            tools: [
              [
                {
                  id: 'export1',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1092"],
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '157e73aa-7bb6-4e22-81f6-8ac37627669a',
          grid: {
            columnData: columnObj.headColumn,
            dataSource: [
              {
                vmiOrderCode: 'w20211227001',
                status: 1,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/buyer-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        },
        {
          tab: { title: this.$t('明细视图') },
          useBaseConfig: true, // 使用组件中的toolbar配置
          dataPermission: 'b',
          permissionCode: 'T_02_0129',
          useToolTemplate: false,
          useCombinationSelection: false,
          toolbar: {
            tools: [
              [
                {
                  id: 'export2',
                  // icon: "icon_solid_Createorder",
                  // permission: ["O_02_1092"],
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '8a4d70b6-254a-41a7-9006-604df39db5ae',
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [
              {
                warehousingCode: 'w20211227001',
                status: 1,
                rowNumber: 10,
                materialCode: '630847',
                materialName: this.$t('自攻特攻GB/T'),
                procurementGroup: this.$t('采购组01'),
                number: 500,
                company: this.$t('件'),
                remarks: this.$t('备注文本字段'),
                qualityResult: this.$t('不合格'),
                unqualifiedClassification: this.$t('不良价值'),
                qualityRemarks: this.$t('质检备注内容'),
                relationCode: 'D848940',
                relationRowCode: 10,
                factoryCode: '630847',
                factoryDescribe: this.$t('TCL空调器有限公司生产工厂'),
                warehouseCode: '0100001',
                warehouseDescribe: this.$t('VMI红物流芜湖威灵电机'),
                supplierCode: 'G 89001',
                materialDescribe: this.$t('广东惠利普智能科技股份有限公司'),
                goodsAddress: this.$t('深圳市福田区下梅林梅华路'),
                createTime: '2021-12-27 13:50：33',
                receiveTime: '2021-12-30'
              }
            ],
            asyncConfig: {
              url: '/srm-purchase-execute/tenant/vmiSteel/vmi-receive-order/buyer-item-page-query',
              recordsPosition: 'data.records',
              serializeList: this.serialize
            },
            frozenColumns: 1
          }
        }
      ],
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    serialize(data) {
      data.forEach((item) => {
        item.submitTime = Number(item.submitTime)
        item.confirmTime = Number(item.confirmTime)
      })
      return data
    },
    // 跳转详情
    handleClickCellTitle(e) {
      let obj = {
        status: '',
        id: ''
      }
      if (e.field === 'vmiOrderCode') {
        obj.status = e.data.status
        obj.id = e.data.id
        this.redirectPage('purchase-execute/new-coordination-warehousing-establish', obj)
      }
    },
    // 头部跳转
    handleClickToolBar(e) {
      // if (
      //   e.grid.getSelectedRecords().length <= 0 &&
      //   e.toolbar.id === "export"
      // ) {
      //   this.$toast({ content: this.$t("请先选择一行"), type: "warning" });
      //   return;
      // } else {
      //   // 调用导出方法·
      //   let _selectRows = e.grid.getSelectedRecords();
      //   console.log(_selectRows, 7777);
      //   debugger;
      // }
      if (e.toolbar.id === 'export1' || e.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('157e73aa-7bb6-4e22-81f6-8ac37627669a')
        )?.visibleCols
        if (e.toolbar.id === 'export2') {
          obj = JSON.parse(
            sessionStorage.getItem('8a4d70b6-254a-41a7-9006-604df39db5ae')
          )?.visibleCols
        }
        let field = []
        if (obj !== undefined && obj.length) {
          obj.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        } else {
          const columnDataList =
            e.toolbar.id === 'export2' ? columnObj.detailedColumn : columnObj.headColumn
          columnDataList.forEach((item) => {
            if (item.field) {
              field.push(item.field)
            }
          })
        }
        const queryBuilderRules =
          this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        const params = {
          page: { current: 1, size: 10000 },
          ...queryBuilderRules
        } // 筛选条件
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination.VMIWarehousingIndexpExport(params, field).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    },
    handleClickCellTool(e) {
      console.log(e)
    },
    redirectPage(route, param) {
      this.$router.push({ path: `/${route}`, query: param })
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
