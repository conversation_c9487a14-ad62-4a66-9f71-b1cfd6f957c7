// 报表
<template>
  <div class="full-height pt20">
    <mt-tabs :e-tab="false" :data-source="tabSource" @handleSelectTab="handleSelectTab"></mt-tabs>
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      v-show="tabIndex === 0"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <mt-template-page
      ref="templateRef1"
      v-show="tabIndex === 1"
      :hidden-tabs="true"
      :template-config="pageConfig1"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>
    <create-dialog
      v-if="deliveryShow"
      ref="child"
      @handleAddDialogShow="handleAddDialogShow"
      @handleDialogShow="handleDialogShow"
    ></create-dialog>
  </div>
</template>

<script>
import { download, getHeadersFileName } from '@/utils/utils'

import { columnObj } from './config/index.js'

export default {
  data() {
    return {
      tabIndex: 0,
      tabSource: [
        {
          title: this.$t('卷号维度')
        },
        {
          title: this.$t('订单维度')
        }
      ],

      // permissionObj: {
      //   permissionNode: {
      //     // 当前的dom元素
      //     code: "ignore-element",
      //     type: "remove",
      //   },
      //   childNode: [
      //     { dataPermission: "a", permissionCode: "T_02_0122" },
      //     { dataPermission: "b", permissionCode: "T_02_0123" },
      //   ],
      // },
      pageConfig: [
        {
          // tab: { title: this.$t("卷号维度") },
          toolbar: {
            tools: [
              [
                {
                  id: 'export1',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          // dataPermission: "a",
          // permissionCode: "T_02_0122",
          dataSource: [],
          useToolTemplate: false,
          gridId: '0AFBF56D-4E55-70ED-ADFE-0ADE5316D0A1',

          grid: {
            columnData: columnObj.headColumn,
            dataSource: [],
            asyncConfig: {
              ignoreDefaultSearch: true,
              url: '/srm-purchase-execute/tenant/vmiSteel/report/buyer/batchView',
              rules: [
                {
                  field: 'header.orderStatus',
                  operator: 'equal',
                  value: 0
                }
              ]
            },
            frozenColumns: 1
          }
        }
      ],
      pageConfig1: [
        {
          // tab: { title: this.$t("订单维度") },
          useBaseConfig: true, // 使用组件中的toolbar配置
          useCombinationSelection: false,
          // dataPermission: "b",
          // permissionCode: "T_02_0123",
          toolbar: {
            tools: [
              [
                {
                  id: 'export2',
                  icon: 'icon_solid_Createorder',
                  title: this.$t('导出')
                }
              ],
              ['Filter', 'refresh', 'setting']
            ]
          },
          useToolTemplate: false,
          gridId: '993BC577-F11B-F81E-C5C1-8305BD0BEC61',
          grid: {
            columnData: columnObj.detailedColumn,
            dataSource: [],
            asyncConfig: {
              // ignoreDefaultSearch: true,
              url: '/srm-purchase-execute/tenant/vmiSteel/report/buyer/orderView'
            },
            frozenColumns: 1
          }
        }
      ],
      deliveryShow: false,

      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickCellTitle(e) {
      console.log(e)
    },
    handleClickCellTool(e) {
      console.log(e)
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    // 头部操作
    handleClickToolBar(args) {
      let rule =
        this.tabIndex === 0
          ? this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
          : this.$refs.templateRef1.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}

      if (args.toolbar.id === 'export1') {
        let obj = JSON.parse(
          sessionStorage.getItem('0AFBF56D-4E55-70ED-ADFE-0ADE5316D0A1')
        ).visibleCols
        let field = []
        if (obj !== undefined) {
          obj.forEach((item) => {
            field.push(item.field)
          })
        } else {
          columnObj.headColumn.forEach((item) => {
            field.push(item.field)
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || [],
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination.batchViewViewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
      if (args.toolbar.id === 'export2') {
        let obj = JSON.parse(
          sessionStorage.getItem('993BC577-F11B-F81E-C5C1-8305BD0BEC61')
        ).visibleCols
        let field = []
        if (obj !== undefined) {
          obj.forEach((item) => {
            field.push(item.field)
          })
        } else {
          columnObj.detailedColumn.forEach((item) => {
            field.push(item.field)
          })
        }
        let params = {
          page: { current: 1, size: 10000 },
          rules: rule.rules || [],
          sortedColumnStr: field.toString()
        }
        this.$store.commit('startLoading')
        this.$API.purchaseCoordination.orderViewViewExport(params).then((res) => {
          this.$store.commit('endLoading')
          const fileName = getHeadersFileName(res)

          download({ fileName: `${fileName}`, blob: res.data })
        })
      }
    }
  }
}
</script>

<style style="scss" scoped>
.set-country {
  height: 100%;
}
</style>
