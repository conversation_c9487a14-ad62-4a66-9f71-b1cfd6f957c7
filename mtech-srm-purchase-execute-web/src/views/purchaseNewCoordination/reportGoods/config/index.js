import { i18n } from '@/main.js'
import { MasterDataSelect } from '@/utils/constant'

export const columnObj = {
  // 卷号维度
  headColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'siteName',
      headerText: i18n.t('工厂'),
      searchOptions: {
        renameField: 'header.siteName'
      },
      ignore: true
    },
    {
      width: '200',
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      searchOptions: {
        ...MasterDataSelect.factoryAddress,
        renameField: 'header.siteCode'
      }
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      searchOptions: {
        renameField: 'header.supplierCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      searchOptions: {
        renameField: 'header.supplierName'
      }
    },
    {
      width: '150',
      field: 'vmiWarehouseCode',
      headerText: i18n.t('VMI仓编码'),
      searchOptions: {
        renameField: 'header.vmiWarehouseCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'vmiWarehouseName',
      headerText: i18n.t('VMI仓名称'),
      searchOptions: {
        renameField: 'header.vmiWarehouseName'
      }
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      searchOptions: {
        renameField: 'item.itemCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      searchOptions: {
        renameField: 'item.itemName'
      }
    },

    {
      width: '150',
      field: 'orderCode',
      headerText: i18n.t('订单号'),
      searchOptions: {
        renameField: 'item.orderCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'lineNo',
      headerText: i18n.t('订单行'),
      searchOptions: {
        renameField: 'item.lineNo',
        operator: 'equal'
      }
    },
    {
      width: '150',
      field: 'releasedTime',
      headerText: i18n.t('下达日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange,
        renameField: 'order.releasedTime'
      }
    },
    {
      width: '150',
      field: 'deliveryTime',
      headerText: i18n.t('交货日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange,
        renameField: 'order.deliveryTime'
      }
    },
    // {
    //   width: "150",
    //   field: "quantity",
    //   headerText: i18n.t("订单量"),
    //   ignore: true,

    //   searchOptions: {
    //     renameField: "header.quantity",
    //   },
    // },
    {
      width: '150',
      field: 'batchCode',
      headerText: i18n.t('卷号'),
      searchOptions: {
        renameField: 'item.batchCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'takeNo',
      headerText: i18n.t('车号'),
      searchOptions: {
        renameField: 'item.takeNo'
      }
    },
    {
      width: '150',
      field: 'waitForReceiveQuantity',
      headerText: i18n.t('待入VMI量'),
      ignore: true,

      searchOptions: {
        renameField: 'header.waitForReceiveQuantity'
      }
    },
    {
      width: '150',
      field: 'receivedQuantity',
      headerText: i18n.t('VMI接收量'),
      ignore: true,

      searchOptions: {
        renameField: 'header.receivedQuantity'
      }
    },
    {
      width: '150',
      field: 'waitForPickUpQuantity',
      headerText: i18n.t('VMI可调料量'),
      ignore: true,

      searchOptions: {
        renameField: 'header.waitForPickUpQuantity'
      }
    },
    {
      width: '150',
      field: 'pickedUpQuantity',
      headerText: i18n.t('调料量'),
      ignore: true,

      searchOptions: {
        renameField: 'header.pickedUpQuantity'
      }
    },
    {
      width: '0',
      field: 'orderStatus',
      headerText: i18n.t('调料情况'),
      searchOptions: {
        renameField: 'header.orderStatus'
      },
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('未完成调料'), cssClass: '' },
          { value: 1, text: i18n.t('已完成调料'), cssClass: '' }
        ]
      }
    },
    {
      width: '150',
      field: 'pickUpTime',
      headerText: i18n.t('调料时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange,
        renameField: 'item.pickUpTime'
      }
    },
    {
      width: '150',
      field: 'transitQuantity',
      headerText: i18n.t('送货待接收量'),
      ignore: true,

      searchOptions: {
        renameField: 'header.transitQuantity'
      }
    },
    {
      width: '150',
      field: 'deliveryReceivedQuantity',
      headerText: i18n.t('送货已接收量'),
      ignore: true,

      searchOptions: {
        renameField: 'header.deliveryReceivedQuantity'
      }
    },
    {
      width: '150',
      field: 'receivedTime', //orderCode
      headerText: i18n.t('入库时间'),
      searchOptions: {
        ...MasterDataSelect.timeRange,
        renameField: 'item.receivedTime'
      }
    }
  ],
  // 订单维度
  detailedColumn: [
    {
      width: '50',
      type: 'checkbox',
      showInColumnChooser: false
    },
    {
      width: '200',
      field: 'siteName',
      headerText: i18n.t('工厂'),
      searchOptions: {
        renameField: 'detail.siteName'
      },
      ignore: true
    },
    {
      width: '200',
      field: 'siteCode',
      headerText: i18n.t('工厂编码'),
      searchOptions: {
        ...MasterDataSelect.factoryAddress,
        renameField: 'detail.siteCode'
      }
    },
    {
      width: '150',
      field: 'supplierCode',
      headerText: i18n.t('供应商编码'),
      searchOptions: {
        renameField: 'header.supplierCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },

    {
      width: '150',
      field: 'supplierName',
      headerText: i18n.t('供应商名称'),
      searchOptions: {
        renameField: 'header.supplierName'
      }
    },
    {
      width: '150',
      field: 'itemCode',
      headerText: i18n.t('物料编码'),
      searchOptions: {
        renameField: 'detail.itemCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'itemName',
      headerText: i18n.t('物料名称'),
      searchOptions: {
        renameField: 'detail.itemName'
      }
    },

    {
      width: '150',
      field: 'orderCode',
      headerText: i18n.t('订单号'),
      searchOptions: {
        renameField: 'detail.orderCode',
        maxQueryValueLength: 20000,
        operator: 'likeright'
      }
    },
    {
      width: '150',
      field: 'itemNo',
      headerText: i18n.t('订单行'),
      searchOptions: {
        renameField: 'detail.itemNo',
        operator: 'equal'
      }
    },
    {
      width: '150',
      field: 'releasedTime',
      headerText: i18n.t('下达日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange,
        renameField: 'order.releasedTime'
      }
    },
    {
      width: '150',
      field: 'deliveryTime',
      headerText: i18n.t('交货日期'),
      searchOptions: {
        ...MasterDataSelect.timeRange,
        renameField: 'order.deliveryTime'
      }
    },
    {
      width: 0,
      field: 'orderStatus',
      headerText: i18n.t('订单状态'),
      searchOptions: {
        renameField: 'order.orderStatus'
      },
      valueConverter: {
        type: 'map',
        map: [
          { value: 0, text: i18n.t('未完结'), cssClass: 'col-active' },
          { value: 1, text: i18n.t('已完结'), cssClass: 'col-active' }
        ]
      }
    },
    {
      width: '150',
      field: 'quantity',
      headerText: i18n.t('订单量'),
      ignore: true
    },
    {
      width: '150',
      field: 'receivedQuantity',
      headerText: i18n.t('VMI接收量'),
      ignore: true
    },
    {
      width: '150',
      field: 'notDeliveryQuantity',
      headerText: i18n.t('订单未发货量'),
      ignore: true
    },
    {
      width: '150',
      field: 'waitForReceiveQuantity',
      headerText: i18n.t('待入VMI量'),
      ignore: true
    },
    {
      width: '150',
      field: 'waitForPickUpQuantity', //checkCount
      headerText: i18n.t('VMI可调料量'),
      ignore: true
    },
    {
      width: '150',
      field: 'pickedUpQuantity', //itemUnit
      headerText: i18n.t('调料量'),
      ignore: true
    },
    {
      width: '150',
      field: 'transitQuantity', //purchaseGroupName
      headerText: i18n.t('送货待接收量'),
      ignore: true
    },
    {
      width: '150',
      field: 'deliveryReceivedQuantity', //remark
      headerText: i18n.t('送货已接收量'),
      ignore: true
    },
    {
      width: '150',
      field: 'notFinishQuantity', //orderCode
      headerText: i18n.t('订单未结量'),
      ignore: true
    }
  ]
}
