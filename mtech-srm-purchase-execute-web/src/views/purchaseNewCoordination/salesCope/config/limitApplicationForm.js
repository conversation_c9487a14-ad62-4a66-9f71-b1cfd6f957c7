import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

export const statusOptions = [
  {
    text: i18n.t('新建'),
    value: 1
  },
  {
    text: i18n.t('审批中'),
    value: 2
  },
  {
    text: i18n.t('已审结'),
    value: 3
  },
  {
    text: i18n.t('已取消'),
    value: 4
  }
]

export const toolbar = [
  {
    code: 'add',
    title: i18n.t('新增'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'edit',
    title: i18n.t('编辑'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'cancel',
    title: i18n.t('取消'),
    icon: '',
    status: 'primary'
  }
]

export const toolbarDetail = [
  {
    code: 'export',
    title: i18n.t('导出'),
    icon: '',
    status: 'primary'
  }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 70
  },
  {
    width: '70',
    type: 'seq',
    title: i18n.t('序号'),
    showOverflow: true
  },
  {
    width: '100',
    field: 'status',
    title: i18n.t('状态'),
    showOverflow: true,
    slots: {
      default: 'statusTransform'
    }
  },
  {
    width: '160',
    field: 'applyNo',
    title: i18n.t('申请单号'),
    slots: {
      default: 'applyNo'
    },
    showOverflow: true
  },
  {
    width: '200',
    field: 'applyTitle',
    title: i18n.t('申请主题'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'orgCode',
    title: i18n.t('财务共享组织编码'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'orgName',
    title: i18n.t('财务共享组织名称'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'validPeriodTo',
    title: i18n.t('额度有效期至'),
    showOverflow: true
  },
  {
    width: '120',
    field: 'createUserName',
    title: i18n.t('申请人'),
    showOverflow: true
  },
  {
    width: '150',
    field: 'createTime',
    title: i18n.t('创建时间'),
    showOverflow: true
  },
  {
    width: '150',
    field: 'approveEndTime',
    title: i18n.t('审结时间'),
    showOverflow: true
  },
  {
    width: '160',
    field: 'processCode',
    title: i18n.t('OA流程编号'),
    showOverflow: true
  }
]

export const columnDataDetail = [
  {
    width: '70',
    type: 'seq',
    title: i18n.t('序号'),
    showOverflow: true
  },
  {
    width: '160',
    field: 'applyNo',
    title: i18n.t('申请单号'),
    showOverflow: true
  },
  {
    width: '100',
    field: 'status',
    title: i18n.t('行号'),
    showOverflow: true
  },
  {
    width: '100',
    field: 'status',
    title: i18n.t('状态'),
    showOverflow: true,
    slots: {
      default: 'statusTransform'
    }
  },
  {
    width: '200',
    field: 'applyTitle',
    title: i18n.t('申请主题'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'orgCode',
    title: i18n.t('财务共享组织编码'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'orgName',
    title: i18n.t('财务共享组织名称'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'companyCode',
    title: i18n.t('所辖公司编码'),
    showOverflow: true
  },
  {
    width: '160',
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    showOverflow: true
  },
  {
    width: '160',
    field: 'customerCode',
    title: i18n.t('客户编码'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'customerName',
    title: i18n.t('客户名称'),
    showOverflow: true
  },
  {
    width: '120',
    field: 'amt',
    title: i18n.t('倒挂额度'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'validPeriodTo',
    title: i18n.t('额度有效期至'),
    showOverflow: true
  },
  {
    width: '120',
    field: 'createUserName',
    title: i18n.t('申请人'),
    showOverflow: true
  },
  {
    width: '150',
    field: 'createTime',
    title: i18n.t('创建时间'),
    showOverflow: true
  },
  {
    width: '150',
    field: 'approveEndTime',
    title: i18n.t('审结时间'),
    showOverflow: true
  }
]
