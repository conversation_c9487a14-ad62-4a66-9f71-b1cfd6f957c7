import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

export const toolbar = [
  {
    code: 'export',
    title: i18n.t('导出'),
    icon: 'vxe-icon-cloud-download',
    status: 'primary'
  }
]

export const columnData = [
  {
    width: '70',
    type: 'seq',
    title: i18n.t('序号'),
    showOverflow: true
  },
  {
    width: '150',
    field: 'orgCode',
    title: i18n.t('财务共享组织'),
    showOverflow: true
  },
  {
    width: '200',
    field: 'companyCodeArr',
    title: i18n.t('所辖公司编码'),
    showOverflow: true
  },
  {
    width: '120',
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    showOverflow: true
  },
  {
    width: '250',
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    showOverflow: true
  },
  {
    width: '120',
    field: 'customerCode',
    title: i18n.t('客户编码'),
    showOverflow: true
  },
  {
    width: '250',
    field: 'customerName',
    title: i18n.t('客户名称'),
    showOverflow: true
  },
  {
    width: '150',
    field: 'amount',
    title: i18n.t('倒挂额度(元)'),
    showOverflow: true
  },
  {
    field: 'validPeriodTo',
    title: i18n.t('有效期至'),
    showOverflow: true
  }
]
