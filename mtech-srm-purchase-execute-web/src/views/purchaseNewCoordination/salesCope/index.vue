<!-- 销售委外倒挂额度 -->
<template>
  <div>
    <mt-template-page :template-config="templateConfig" :permission-obj="permissionObj">
      <!-- 额度查询 -->
      <template slot="slot-0">
        <quota-inquiry />
      </template>
      <!-- 额度申请表 -->
      <template slot="slot-1">
        <limit-application-form />
      </template>
      <!-- 申请表明细 -->
      <template slot="slot-2">
        <limit-application-form-detail />
      </template>
    </mt-template-page>
  </div>
</template>
<script>
import quotaInquiry from './quotaInquiry.vue'
import limitApplicationForm from './limitApplicationForm.vue'
import limitApplicationFormDetail from './limitApplicationFormDetail.vue'
export default {
  components: {
    quotaInquiry,
    limitApplicationForm,
    limitApplicationFormDetail
  },
  data() {
    return {
      permissionObj: {
        permissionNode: {
          code: 'ignore-element',
          type: 'remove'
        },
        childNode: [
          { dataPermission: 'a', permissionCode: 'T_02_0181' },
          { dataPermission: 'b', permissionCode: 'T_02_0182' },
          { dataPermission: 'c', permissionCode: 'T_02_0183' }
        ]
      },
      templateConfig: [
        {
          title: this.$t('额度查询'),
          dataPermission: 'a',
          permissionCode: 'T_02_0181'
        },
        {
          title: this.$t('额度申请单'),
          dataPermission: 'b',
          permissionCode: 'T_02_0182'
        },
        {
          title: this.$t('额度申请单明细'),
          dataPermission: 'c',
          permissionCode: 'T_02_0183'
        }
      ]
    }
  },
  methods: {}
}
</script>
