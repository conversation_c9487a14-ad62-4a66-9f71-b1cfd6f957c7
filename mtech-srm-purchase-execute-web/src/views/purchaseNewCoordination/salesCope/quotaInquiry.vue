<template>
  <div class="full-height">
    <collapse-search @reset="handleCustomReset" @search="handleCustomSearch">
      <mt-form ref="searchForm" :model="searchForm">
        <!-- 财务共享组织 -->
        <mt-form-item :label="$t('财务共享组织')" prop="orgCode">
          <mt-select
            v-model="searchForm.orgCode"
            :data-source="orgCodeOptions"
            placeholder=""
            :fields="{ text: 'theCodeName', value: 'orgCode' }"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 供应商 -->
        <mt-form-item :label="$t('供应商')" prop="supplierCode">
          <mt-select
            v-model="searchForm.supplierCode"
            :data-source="supplierOptions"
            placeholder=""
            :filtering="searchSupplierList"
            :fields="{ text: 'theCodeName', value: 'supplierCode' }"
            :allow-filtering="true"
            :show-clear-button="true"
            @change="supplierChange"
          />
        </mt-form-item>
        <!-- 有效期至 -->
        <mt-form-item :label="$t('有效期至')">
          <mt-date-range-picker
            v-model="searchForm.dateList"
            :show-clear-button="true"
            placeholder=""
            @change="(e) => dateChange(e)"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <Sc-table
        ref="xTable"
        class="xTable-class"
        :columns="columnData"
        :table-data="tableData"
        row-class-name="table-row-class"
        :row-config="{ height: 300 }"
        show-overflow
        :min-height="600"
        header-align="center"
        border="none"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
      >
        <!-- 按钮区 -->
        <template slot="custom-tools">
          <vxe-button
            v-for="item of toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolbar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.title }}</vxe-button
          >
        </template>
      </Sc-table>
      <!-- 分页器 -->
      <mt-page
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="currentChange"
        @sizeChange="sizeChange"
      />
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { columnData, toolbar } from './config'
import ScTable from '@/components/ScTable/src/index'
import collapseSearch from '@/components/collapseSearch'
import { addCodeNameKeyInList, download, getHeadersFileName } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  components: {
    collapseSearch,
    ScTable
  },
  data() {
    return {
      searchForm: {
        orgCode: null,
        supplierCode: null,
        dateList: null,
        validPeriodToStartTime: '',
        validPeriodToEndTime: ''
      },
      columnData,
      orgCodeOptions: [],
      supplierOptions: [],
      tableData: [],
      toolbar,
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalRecordsCount: 0,
        totalPages: 0,
        pageSizes: [10, 20, 50, 100, 200],
        pageSize: 10
      }
    }
  },
  mounted() {
    // 获取 - 财务共享组织
    this.getOrgCode()
    // 获取供应商下拉集
    this.searchSupplierList = utils.debounce(this.searchSupplierList, 1000)
    this.searchSupplierList()
    // 获取共享组织列表
    this.getOrgCodeList()
    // 页面初始化时 - 加载列表数据
    this.handleCustomSearch()
  },
  methods: {
    handleClickToolbar(e) {
      const { code } = e
      if (code === 'export') {
        this.handleExport()
      }
    },
    handleExport() {
      const param = {
        ...this.searchForm
      }
      this.$store.commit('startLoading')
      this.$API.outsourcingNew.exportSalesOutsourcingMoney(param).then((res) => {
        this.$store.commit('endLoading')
        if (res) {
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
        }
      })
    },
    // 获取共享组织列表
    getOrgCodeList() {
      this.$API.outsourcingNew.getOrgCodeList().then((res) => {
        const _list = res.data || []
        this.orgCodeOptions = addCodeNameKeyInList({
          firstKey: 'orgCode',
          secondKey: 'orgName',
          list: _list
        })
      })
    },
    currentChange(current) {
      this.pageSettings.current = current
      this.handleCustomSearch()
    },
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.handleCustomSearch()
    },
    getOrgCode() {},
    dateChange(e) {
      if (e.startDate) {
        this.searchForm['validPeriodToStartTime'] = dayjs(e.startDate).valueOf()
        this.searchForm['validPeriodToEndTime'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchForm['validPeriodToStartTime'] = null
        this.searchForm['validPeriodToEndTime'] = null
      }
    },
    supplierChange(e) {
      if (e.itemData) {
        this.searchForm['supplierCode'] = e.itemData.supplierCode
        this.searchForm['supplierName'] = e.itemData.supplierName
      } else {
        this.searchForm['supplierCode'] = null
        this.searchForm['supplierName'] = null
      }
    },
    searchSupplierList(args = { text: '' }) {
      const { text } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData.getSupplier(params).then((res) => {
        this.supplierOptions.length = 0
        const list = res.data || []
        this.supplierOptions = addCodeNameKeyInList({
          firstKey: 'supplierCode',
          secondKey: 'supplierName',
          list
        }).filter((n) => !!n.supplierCode)
      })
    },
    handleCustomReset() {
      for (let key in this.searchForm) {
        this.searchForm[key] = null
      }
    },
    handleCustomSearch() {
      this.$store.commit('startLoading')
      const param = {
        ...this.searchForm,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (param.dateList) {
        delete param.dateList
      }
      this.$API.outsourcingNew
        .getSalesOutsourcingMoney(param)
        .then((res) => {
          if (res && res.code === 200) {
            this.tableData = res.data?.records || []
            this.pageSettings.totalPages = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = Number(res.data.total)
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
