<template>
  <div class="full-height">
    <collapse-search @reset="handleCustomReset" @search="handleCustomSearch">
      <mt-form ref="searchForm" :model="searchForm">
        <!-- 申请单号 -->
        <mt-form-item :label="$t('申请单号')" prop="applyNo">
          <mt-input v-model="searchForm.applyNo" placeholder="" :show-clear-button="true" />
        </mt-form-item>
        <!-- 财务共享组织 -->
        <mt-form-item :label="$t('财务共享组织')" prop="orgCode">
          <mt-select
            v-model="searchForm.orgCode"
            :data-source="orgCodeOptions"
            placeholder=""
            :fields="{ text: 'theCodeName', value: 'orgCode' }"
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 申请人 -->
        <mt-form-item :label="$t('申请人')" prop="createUserName">
          <mt-select
            v-model="searchForm.createUserName"
            :data-source="personList"
            placeholder=""
            :filtering="searchPersons"
            :fields="{ text: 'theCodeName', value: 'employeeName' }"
            :allow-filtering="true"
            :show-clear-button="true"
            @change="supplierChange"
          />
        </mt-form-item>
        <!-- 状态 -->
        <mt-form-item :label="$t('状态')" prop="status">
          <mt-select
            v-model="searchForm.status"
            :data-source="statusOptions"
            placeholder=""
            :show-clear-button="true"
          />
        </mt-form-item>
        <!-- 创建日期 -->
        <mt-form-item :label="$t('创建日期')">
          <mt-date-range-picker
            v-model="searchForm.createDate"
            :show-clear-button="true"
            placeholder=""
            @change="(e) => dateChange(e, 'createDate')"
          />
        </mt-form-item>
        <!-- 审结日期 -->
        <mt-form-item :label="$t('审结日期')">
          <mt-date-range-picker
            v-model="searchForm.approveEndTime"
            :show-clear-button="true"
            placeholder=""
            @change="(e) => dateChange(e, 'approveEndTime')"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <Sc-table
        ref="xTable"
        class="xTable-class"
        :columns="columnData"
        :table-data="tableData"
        row-class-name="table-row-class"
        :row-config="{ height: 300 }"
        show-overflow
        :min-height="600"
        header-align="center"
        border="none"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        :edit-config="{}"
      >
        <!-- 按钮区 -->
        <template slot="custom-tools">
          <vxe-button
            v-for="item of toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolbar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.title }}</vxe-button
          >
        </template>
        <template #applyNo="{ row }">
          <span :style="applyNoStyle" @click="toEditInfo(row)">
            {{ row.applyNo }}
          </span>
        </template>
        <template #statusTransform="{ row }">
          <span>{{ statusTransformValue(row.status) }}</span>
        </template>
      </Sc-table>
      <!-- 分页器 -->
      <mt-page
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        :total-pages="pageSettings.totalPages"
        @currentChange="currentChange"
        @sizeChange="sizeChange"
      />
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { columnData, toolbar, statusOptions } from './config/limitApplicationForm'
import ScTable from '@/components/ScTable/src/index'
import collapseSearch from '@/components/collapseSearch'
import { addCodeNameKeyInList } from '@/utils/utils'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
export default {
  components: {
    collapseSearch,
    ScTable
  },
  data() {
    return {
      applyNoStyle: {
        textDecoration: 'underline',
        color: 'blue',
        cursor: 'pointer'
      },
      searchForm: {
        applyNo: '',
        orgCode: null,
        createUserName: '',
        createDate: [],
        approveEndTime: [],
        approveEndTimeStart: '',
        approveEndTimeEnd: '',
        status: '',
        createDateStart: '',
        createDateEnd: ''
      },
      columnData,
      orgCodeOptions: [],
      personList: [],
      statusOptions,
      tableData: [],
      toolbar,
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalRecordsCount: 0,
        totalPages: 0,
        pageSizes: [10, 20, 50, 100, 200],
        pageSize: 10
      }
    }
  },
  mounted() {
    this.$bus.$on('updateList', () => {
      this.handleCustomSearch()
    })
    // 获取 - 财务共享组织
    this.getOrgCode()
    // 获取供应商下拉集
    this.searchSupplierList = utils.debounce(this.searchSupplierList, 1000)
    this.searchPersons = utils.debounce(this.searchPersons, 1000)
    this.searchSupplierList()
    this.searchPersons()
    // 获取共享组织列表
    this.getOrgCodeList()
    // 初始化页面时 - 加载列表数据
    this.handleCustomSearch()
  },
  methods: {
    toEditInfo(row) {
      const _row = cloneDeep(row)
      this.$router.push({
        path: 'new-coordination-toConfigure-salesCope-application',
        query: {
          date: new Date().getTime(),
          isEdit: ['1', 1].includes(row.status) ? true : false,
          row: _row
        }
      })
    },
    // 新增申请单
    handleClickToolbar(args) {
      const { code, $grid } = args
      const _rowSelect = $grid.getCheckboxRecords()
      if (code === 'add') {
        this.$router.push({
          path: 'new-coordination-toConfigure-salesCope-application',
          query: { date: new Date().getTime(), isEdit: 'new', row: null }
        })
        return
      }
      if (_rowSelect && _rowSelect.length <= 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请选择一条数据')
        })
        return
      }
      if (code === 'edit') {
        if (_rowSelect.length > 1) {
          this.$toast({
            type: 'warning',
            content: this.$t('只能编辑一条数据')
          })
          return
        }
        if (![1, '1'].includes(_rowSelect[0].status)) {
          this.$toast({
            type: 'warning',
            content: this.$t('只能选择 “新建” 状态的申请单进行编辑')
          })
          return
        }
        this.$router.push({
          path: 'new-coordination-toConfigure-salesCope-application',
          query: {
            date: new Date().getTime(),
            isEdit: true,
            row: _rowSelect[0]
          }
        })
        return
      }
      if (code === 'cancel') {
        for (let i = 0; i < _rowSelect.length; i++) {
          if (![1, '1'].includes(_rowSelect[i].status) || _rowSelect[i].processCode) {
            this.$toast({
              type: 'warning',
              content: this.$t('只能选择“新建”状态且未提交过OA审批的申请单取消')
            })
            return
          }
        }
        this.subApplicationForm(_rowSelect)
        return
      }
    },
    subApplicationForm(list) {
      const ids = list.map((item) => item.id).filter((n) => !!n)
      this.$store.commit('startLoading')
      this.$API.outsourcingNew
        .cancelApplicationForm({ ids })
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg || '取消成功')
            })
            return
          }
          this.$toast({
            type: 'success',
            content: this.$t(res.msg || '取消失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'success',
            content: this.$t(err.msg || '操作失败')
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    statusTransformValue(val) {
      if (!val) return ''
      const _obj = statusOptions.filter((item) => item.value === val)[0]
      return _obj.text
    },
    getRow(data) {
      console.log(data)
    },
    // 获取共享组织列表
    getOrgCodeList() {
      this.$API.outsourcingNew.getOrgCodeList().then((res) => {
        const _list = res.data || []
        this.orgCodeOptions = addCodeNameKeyInList({
          firstKey: 'orgCode',
          secondKey: 'orgName',
          list: _list
        })
      })
    },
    currentChange(current) {
      this.pageSettings.current = current
      this.handleCustomSearch()
    },
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
      this.handleCustomSearch()
    },
    getOrgCode() {},
    dateChange(e, flag) {
      if (e.startDate) {
        this.searchForm[flag + 'Start'] = dayjs(e.startDate).valueOf()
        this.searchForm[flag + 'End'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchForm[flag + 'Start'] = null
        this.searchForm[flag + 'End'] = null
      }
    },
    supplierChange(e) {
      if (e.itemData) {
        this.searchForm['supplierCode'] = e.itemData.supplierCode
        this.searchForm['supplierName'] = e.itemData.supplierName
      } else {
        this.searchForm['supplierCode'] = null
        this.searchForm['supplierName'] = null
      }
    },
    searchPersons(args = { text: '' }) {
      const { text: fuzzyName } = args
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = res.data || []
        this.personList = addCodeNameKeyInList({
          firstKey: 'employeeName',
          list: tmp
        })
      })
    },
    handleCustomReset() {
      for (let key in this.searchForm) {
        this.searchForm[key] = null
      }
    },
    handleCustomSearch() {
      this.$store.commit('startLoading')
      const param = {
        ...this.searchForm,
        page: {
          current: this.pageSettings.current,
          size: this.pageSettings.pageSize
        }
      }
      if (param.createDate) {
        delete param.createDate
      }
      if (param.approveEndTime) {
        delete param.approveEndTime
      }
      this.$API.outsourcingNew
        .getApplicationFormQuery(param)
        .then((res) => {
          if (res && res.code === 200) {
            this.tableData = res.data?.records || []
            this.pageSettings.totalPages = Math.ceil(
              Number(res.data.total) / this.pageSettings.pageSize
            )
            this.pageSettings.totalRecordsCount = Number(res.data.total)
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
