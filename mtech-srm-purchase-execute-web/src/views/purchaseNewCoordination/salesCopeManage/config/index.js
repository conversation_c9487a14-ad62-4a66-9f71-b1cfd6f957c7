import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'
import { codeNameColumn } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '150',
    field: 'indexs',
    headerText: i18n.t('序号'),
    showInColumnChooser: false,
    valueConverter: {
      type: 'function',
      filter: (e, row) => {
        return Number(+row.index + 1)
      }
    },
    cellTools: [
      {
        id: 'Edit',
        icon: 'icon_list_edit',
        title: i18n.t('编辑'),
        // visibleCondition: (e) => e.status == 0, // 按产品彭云要求任何状态下都可以编辑和删除 -- lbj.2023.04.25
        permission: ['O_02_0689']
      },
      {
        id: 'Delete',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        permission: ['O_02_0610']

        // visibleCondition: (e) => e.status == 0,
      }
    ],
    ignore: true
  },
  {
    width: '150',
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: [
        {
          value: 1,
          text: i18n.t('有效'),
          cssClass: 'col-inactive'
        },
        {
          value: -1,
          text: i18n.t('无效'),
          cssClass: 'col-active'
        }
      ]
    },
    cellTools: [
      {
        id: 'active',
        icon: 'icon_list_disable',
        title: i18n.t('启用'),
        visibleCondition: (e) => e.status == -1
      },
      {
        id: 'inActive',
        icon: 'icon_list_enable',
        title: i18n.t('停用'),
        visibleCondition: (e) => e.status == 1
      }
    ]
  },
  {
    width: '150',
    field: 'companyCode',
    headerText: i18n.t('公司'),
    searchOptions: {
      ...MasterDataSelect.businessCompany
    },
    template: codeNameColumn({
      firstKey: 'companyCode',
      secondKey: 'companyName'
    }),
    allowFiltering: false // 不可是使用过滤搜索，因为是 code-name 形式
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    width: '200',
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'customerCode',
    headerText: i18n.t('客户编码')
  },
  {
    width: '200',
    field: 'customerName',
    headerText: i18n.t('客户名称')
  },
  {
    width: '150',
    field: 'amount',
    headerText: i18n.t('销售委外金额')
  },
  {
    width: '120',
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    width: '150',
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    template: timeDate({ dataKey: 'createTime', isDateTime: true })
  },
  {
    width: '150',
    field: 'updateTime',
    headerText: i18n.t('最后更新时间'),
    template: timeDate({ dataKey: 'updateTime', isDateTime: true })
  },
  {
    width: '150',
    field: 'updateUserName',
    headerText: i18n.t('最后更新人姓名')
  }
]
