<template>
  <div class="full-height">
    <mt-template-page
      ref="pickGridRef"
      :template-config="componentConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>

<script>
import { BASE_TENANT } from '@/utils/constant'
import { columnData } from './config'
export default {
  data() {
    return {
      componentConfig: [
        {
          useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
          useBaseConfig: false, //代表不使用组件中的toolbar配置，使用当前项的toolbar
          useCombinationSelection: false,
          // toolbar: [
          //   [
          //     []
          //     // {
          //     //   id: "Add",
          //     //   icon: "icon_solid_Createorder",
          //     //   title: this.$t("新增"),
          //     //   // permission: ["O_02_0436"],
          //     // },
          //     // {
          //     //   id: "Delete",
          //     //   icon: "icon_solid_Delete",
          //     //   title: this.$t("删除"),
          //     //   // permission: ["O_02_0437"],
          //     // },
          //     ["Filter", "Refresh", "Setting"],
          //   ],
          // ],
          toolbar: [
            [
              {
                id: 'Add',
                icon: 'icon_solid_Createorder',
                title: this.$t('新增')
                // permission: ["O_02_0609"],
              },
              {
                id: 'Delete',
                icon: 'icon_solid_Delete',
                title: this.$t('删除')
                // permission: ["O_02_0610"],
              }
            ],
            ['Filter', 'Refresh', 'Setting']
          ],
          // gridId: this.$tableUUID.outsourcing.pickQuantityConfig.list,

          grid: {
            lineSelection: true,
            columnData: columnData,
            frozenColumns: 1,

            dataSource: [],
            asyncConfig: {
              url: `${BASE_TENANT}/saleOutsourceAmountConfig/page`
            }
          }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      console.log('点击了顶部的按钮', e)
      if (e.toolbar.id == 'Delete') {
        let selectRows = e.gridRef.getMtechGridRecords()
        if (!selectRows.length) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }
        if (selectRows.find((i) => i.status == 1)) {
          this.$toast({
            content: this.$t('请选择已停用状态的数据进行删除操作'),
            type: 'warning'
          })
          return
        }
        let ids = selectRows.map((item) => item.id)
        console.log(ids)
        this.handleDelete(ids)
      } else if (e.toolbar.id == 'Add') {
        this.handleRow()
      }
    },
    handleClickCellTool(e) {
      if (e.tool.id == 'Edit') {
        this.handleRow(e.data)
      } else if (e.tool.id == 'Delete') {
        this.handleDelete([e.data.id])
      } else if (['active', 'inActive'].includes(e.tool.id)) {
        let _toStatus = e.data.status === 1 ? -1 : 1
        this.handleActive(e.data.id, _toStatus)
      }
    },

    handleRow(row) {
      this.$dialog({
        modal: () => import('./components/addDialog.vue'),
        data: {
          title: this.$t('编辑'),
          row: row || null
        },
        success: () => {
          this.refreshGrid()
        }
      })
    },

    handleDelete(ids) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('确认删除选中的行？')
        },
        success: () => {
          this.$API.purchaseCoordination.deleteNewStockSetting({ ids: ids }).then(() => {
            this.refreshGrid()
          })
        }
      })
    },

    // 启用禁用
    handleActive(id, toStatus) {
      this.$API.purchaseCoordination
        .activeNewStockSetting({ ids: [id], targetState: toStatus })
        .then(() => {
          this.refreshGrid()
        })
    },

    refreshGrid() {
      this.$toast({ content: this.$t('操作成功'), type: 'success' })
      this.$refs.pickGridRef.refreshCurrentGridData()
    }
  }
}
</script>

<style></style>
