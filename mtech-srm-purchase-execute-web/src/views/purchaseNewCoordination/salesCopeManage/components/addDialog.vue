<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="headerText"
    :buttons="buttons"
    @close="handleClose"
  >
    <mt-form ref="addFormRef" :model="addForm" :rules="addRules">
      <mt-form-item prop="companyCode" :label="$t('公司')">
        <debounce-filter-select
          v-model="addForm.companyCode"
          :request="getCompany"
          :data-source="companyOptions"
          :fields="{ text: 'theCodeName', value: 'orgCode' }"
          :value-template="companyCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="companyCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <debounce-filter-select
          v-model="addForm.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="customerCode" :label="$t('客户')">
        <mt-select
          allow-filtering="true"
          v-model="addForm.customerCode"
          :data-source="customerCodeOptions"
          :placeholder="$t('请选择')"
          :fields="{ text: 'customerName', value: 'customerCode' }"
          :filtering="getCustomer"
          @change="companyChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('销售委外金额')">
        <mt-input v-model="addForm.amount"></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { utils } from '@mtech-common/utils'

import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      headerText: this.$t('新增'),
      supplierOptions: [],
      buyerOrgOptions: [],
      customerCodeOptions: [],
      companyOptions: [], // 公司 下拉选项
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商
      addForm: {
        customerCode: '',
        customerName: '',
        amount: '',
        companyCode: '',
        companyName: '',
        supplierCode: '',
        supplierName: ''
      },
      typeList: [
        {
          text: this.$t('工厂+加工方+采购组（原材料）'),
          value: 1
        },
        {
          text: this.$t('工厂+加工方'),
          value: 2
        },
        {
          text: this.$t('工厂'),
          value: 3
        }
      ],
      buyerOrgValueTemplate: codeNameColumn({
        firstKey: 'groupCode',
        secondKey: 'groupName'
      }), // 采购组
      addRules: {
        companyCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        settingType: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        buyerOrgCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        stockStatus: [
          {
            required: true,
            message: this.$t('请选择是否限制可用库存数量'),
            trigger: 'blur'
          }
        ],
        demandStatus: [
          {
            required: true,
            message: this.$t('请选择是否限制最大需求数量'),
            trigger: 'blur'
          }
        ]
      },
      requestUrl: {
        pre: 'masterData',
        url: 'postSiteFuzzyQuery'
      },
      requestKey: 'fuzzyParam',
      otherParams: { dataLimit: 1000 },
      labelShowObj: { code: 'siteCode', name: 'siteName' }
    }
  },
  mounted() {
    console.log('我是弹窗')
    this.$refs.dialog.ejsRef.show()
    this.getBuyerOrgList({ text: undefined })
    this.getSupplier({ text: this.addForm.supplierCode })
    this.getCompany({ text: undefined })
    this.getCustomer = utils.debounce(this.getCustomer, 500)

    this.getCustomer({ text: '' })

    this.$nextTick(() => {
      this.$refs.addFormRef.resetFields()
      if (this.modalData) {
        this.headerText = this.modalData?.title
        if (this.modalData?.row) {
          this.addForm = {
            ...this.modalData.row,
            stockStatus: this.modalData.row.stockStatus ? true : false,
            demandStatus: this.modalData.row.demandStatus ? true : false
          }
          console.log(this.addForm)
        }
      }
    })
  },
  methods: {
    companyChange(e) {
      if (e.itemData) {
        this.addForm.companyCode = e.itemData.companyCode
        this.addForm.companyName = e.itemData.companyName
      } else {
        this.addForm.companyCode = null
        this.addForm.companyName = null
      }
    },
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    getCustomer(e) {
      //客户
      let str = e?.text || ''
      let params = {
        fuzzyNameOrCode: str
      }
      this.$API.masterData.getCustomer(params).then((res) => {
        this.customerCodeOptions = res.data || []
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(this.customerCodeOptions)
          }
        })
        if (this.header?.customerCode) this.ruleForm.customerCode = this.header?.customerCode
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.supplierCode = itemData.supplierCode
        this.addForm.supplierName = itemData.supplierName
      } else {
        this.addForm.supplierCode = ''
        this.addForm.supplierName = ''
      }
    },
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.buyerOrgId = itemData.id
        this.addForm.buyerOrgCode = itemData.groupCode
        this.addForm.buyerOrgName = itemData.groupName
      } else {
        this.addForm.buyerOrgId = ''
        this.addForm.buyerOrgCode = ''
        this.addForm.buyerOrgName = ''
      }
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.companyId = itemData.id
        this.addForm.companyCode = itemData.orgCode
        this.addForm.companyName = itemData.orgName
      } else {
        this.addForm.companyId = ''
        this.addForm.companyCode = ''
        this.addForm.companyName = ''
      }
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          console.log(this.addForm)
          let params = {
            ...this.addForm
          }
          this.$API.purchaseCoordination.saveNewStockSetting(params).then(() => {
            this.$emit('confirm-function')
          })
        }
      })
    },

    handleChange(e) {
      this.addForm.siteId = e.itemData?.id
      this.addForm.siteName = e.itemData?.siteName
    }
  }
}
</script>

<style></style>
