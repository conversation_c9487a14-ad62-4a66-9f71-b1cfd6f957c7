<template>
  <div class="full-height">
    <div class="btn-list">
      <span>
        <mt-row :gutter="48">
          <mt-col :offset="isEdit ? 0 : 16" :span="8">
            <vxe-button status="primary" @click="todoRouter" size="small">
              {{ $t('返回') }}
            </vxe-button>
          </mt-col>
          <mt-col v-if="isEdit" :span="8">
            <vxe-button status="primary" @click="submit('save')" size="small">
              {{ $t('保存') }}
            </vxe-button>
          </mt-col>
          <mt-col v-if="isEdit" :span="8">
            <vxe-button status="primary" @click="submit('submit')" size="small">
              {{ $t('提交') }}
            </vxe-button>
          </mt-col>
        </mt-row>
      </span>
    </div>
    <div class="submit-form">
      <mt-form ref="searchForm" :model="searchForm" :rules="rulesForm">
        <mt-row :gutter="24">
          <mt-col :span="8">
            <!-- 申请单号 -->
            <mt-form-item :label="$t('申请单号')" prop="applyNo">
              <mt-input v-model="searchForm.applyNo" placeholder="" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- 状态 -->
            <mt-form-item :label="$t('状态')" prop="status">
              <mt-select
                v-model="searchForm.status"
                :data-source="statusOptions"
                placeholder=""
                :disabled="true"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- 申请人 -->
            <mt-form-item :label="$t('申请人')" prop="createUserId">
              <mt-select
                v-model="searchForm.createUserId"
                :data-source="personList"
                placeholder=""
                :filtering="searchPersons"
                :fields="{ text: 'theCodeName', value: 'uid' }"
                :allow-filtering="true"
                :show-clear-button="true"
                :disabled="!isEdit"
                @change="createUserChange"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="8">
            <!-- 创建日期 -->
            <mt-form-item :label="$t('创建时间')">
              <mt-date-picker v-model="searchForm.createTime" :disabled="true" placeholder=" " />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- 审结日期 -->
            <mt-form-item :label="$t('审结日期')">
              <mt-date-picker
                v-model="searchForm.approveEndTime"
                :disabled="true"
                placeholder=" "
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- OA流程单号 -->
            <mt-form-item :label="$t('OA流程单号')" prop="processCode">
              <mt-input v-model="searchForm.processCode" placeholder="" :disabled="true" />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="20">
            <!-- 申请主题 -->
            <mt-form-item :label="$t('申请主题')" prop="applyTitle">
              <mt-input
                v-model="searchForm.applyTitle"
                placeholder=""
                :show-clear-button="true"
                :disabled="!isEdit"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="24">
            <!-- 申请说明 -->
            <mt-form-item :label="$t('申请说明')" prop="applyDesc">
              <mt-input
                type="textarea"
                v-model="searchForm.applyDesc"
                placeholder=""
                :show-clear-button="true"
                :disabled="!isEdit"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>
        <mt-row :gutter="24">
          <mt-col :span="8">
            <!-- 财务共享组织 -->
            <mt-form-item :label="$t('财务共享组织')" prop="orgCode">
              <mt-select
                v-model="searchForm.orgCode"
                :data-source="orgCodeOptions"
                placeholder=""
                :fields="{ text: 'theCodeName', value: 'orgCode' }"
                :show-clear-button="true"
                :disabled="!isEdit"
                @change="orgCodeChange"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- 所辖公司编码 -->
            <mt-form-item :label="$t('所辖公司编码')" prop="companyCode">
              <mt-input v-model="searchForm.companyCode" placeholder="" :disabled="true" />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <!-- 额度有效期至(含) -->
            <mt-form-item :label="$t('额度有效期至(含)')" prop="validPeriodTo">
              <mt-date-picker
                v-model="searchForm.validPeriodTo"
                :show-clear-button="true"
                placeholder=""
                :disabled="!isEdit"
              />
            </mt-form-item>
          </mt-col>
          <mt-col :span="8">
            <mt-form-item :label="$t('附件')" label-style="top">
              <uploadFile
                v-if="searchForm.id || isEdit"
                ref="uploadFile"
                v-model="searchForm.files"
                :row-id="searchForm.id"
                @listChange="fileListChange"
                :disabled="!isEdit"
              ></uploadFile>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
    </div>
    <div class="flex-fit" ref="tableContainer" id="forecast-manage-table-container">
      <Sc-table
        ref="xTable"
        class="xTable-class"
        :columns="columnData"
        :table-data="tableData"
        row-class-name="table-row-class"
        :row-config="{ height: 300 }"
        show-overflow
        :min-height="600"
        header-align="center"
        border="none"
        align="center"
        style="padding-top: unset"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        :edit-config="{}"
      >
        <!-- 按钮区 -->
        <template v-if="isEdit" slot="custom-tools">
          <vxe-button
            v-for="item of toolbar"
            :key="item.code"
            :status="item.status"
            size="small"
            @click="handleClickToolbar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.title }}</vxe-button
          >
        </template>
        <template #statusTransform="{ data }">
          <span>{{ statusTransformValue(data[0].status) }}</span>
        </template>
      </Sc-table>
      <!-- 分页器 -->
      <!-- <mt-page
        class="flex-keep custom-page"
        :page-settings="pageSettings"
        @currentChange="currentChange"
        @sizeChange="sizeChange"
      /> -->
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import { columnData, toolbar, statusOptions } from './config/index'
import ScTable from '@/components/ScTable/src/index'
import uploadFile from './components/uploadFile.vue'
import { addCodeNameKeyInList, getHeadersFileName, downLoad } from '@/utils/utils'
import dayjs from 'dayjs'
export default {
  components: {
    ScTable,
    uploadFile
  },
  data() {
    return {
      rulesForm: {
        applyTitle: [
          {
            required: true,
            trigger: ['blur'],
            message: this.$t('请输入申请主题')
          }
        ],
        orgCode: [
          {
            required: true,
            trigger: ['blur'],
            message: this.$t('请选择财务共享组织')
          }
        ],
        validPeriodTo: [
          {
            required: true,
            trigger: ['blur'],
            message: this.$t('请选择额度有效期')
          }
        ]
      },
      isEdit: true,
      detailIsPush: false,
      searchForm: {
        id: null,
        dateList: null,
        applyNo: '',
        orgCode: null,
        createUserName: '',
        createUserId: '',
        createTime: '',
        approveEndTime: '',
        status: '',
        processCode: '',
        applyTitle: '',
        applyDesc: '',
        companyCode: '',
        validPeriodTo: '',
        files: []
      },
      columnData,
      orgCodeOptions: [],
      personList: [],
      statusOptions,
      tableData: [],
      toolbar,
      pageSettings: {
        current: 1,
        pageCount: 5,
        totalRecordsCount: 0,
        totalPages: 0,
        pageSizes: [10, 20, 50, 100, 200],
        pageSize: 10
      }
    }
  },
  mounted() {
    const query = this.$route.query
    // 获取 - 财务共享组织
    this.getOrgCode()
    // 获取供应商下拉集
    this.searchSupplierList = utils.debounce(this.searchSupplierList, 1000)
    this.searchPersons = utils.debounce(this.searchPersons, 1000)
    this.searchSupplierList()
    this.searchPersons()
    // 获取共享组织列表
    this.getOrgCodeList()
    if (query.isEdit == 'true') {
      this.searchForm.id = query.row?.id
      this.handleCustomSearch(query.row)
      this.getApplicationReportDetails({ applyNo: query.row.applyNo || '' })
      this.isEdit = true
    } else if (query.isEdit === 'new') {
      this.$set(this.searchForm, 'status', 1)
      // 获取申请单号
      this.getApplyNoInfo()
      this.isEdit = true
    } else {
      this.searchForm.id = query.row.id
      this.handleCustomSearch(query?.row || query.id)
      this.getApplicationReportDetails({ applyNo: query?.row?.applyNo || query.applyNo || '' })
      this.isEdit = false
    }
  },
  methods: {
    fileListChange(data) {
      this.searchForm.files = (data.fileRequestList || []).map((item) => {
        if (item.fileId) {
          item['sysFileId'] = item.fileId
        }
        return item
      })
    },
    getApplyNoInfo() {
      this.$API.outsourcingNew.getApplyNoInfo().then((res) => {
        if (res && res.code === 200) {
          this.searchForm.applyNo = res.data
        }
      })
    },
    createUserChange(e) {
      if (e.itemData) {
        this.searchForm.createUserName = e.itemData.employeeName
        this.searchForm.createUserId = e.itemData.uid
      } else {
        this.searchForm.createUserName = null
        this.searchForm.createUserId = null
      }
    },
    orgCodeChange(e) {
      if (e.itemData) {
        this.searchForm.companyCode = e.itemData.companyList.map((item) => item.companyCode).join()
      } else {
        this.searchForm.companyCode = ''
      }
    },
    todoRouter() {
      this.$router.go(-1)
      this.$bus.$emit('updateList')
    },
    saveApplicationFormInfo() {
      const _this = this
      const param = { ...this.searchForm }
      param.validPeriodTo = dayjs(param.validPeriodTo).valueOf()
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.$store.commit('startLoading')
          this.$API.outsourcingNew
            .saveApplicationForm(param)
            .then((res) => {
              if (res && res.code === 200) {
                for (let key in _this.searchForm) {
                  if (res.data[key]) {
                    this.searchForm[key] = res.data[key]
                  }
                }
                this.$toast({
                  type: 'success',
                  content: this.$t(res.msg || '保存成功')
                })
                return
              }
              this.$toast({
                type: 'error',
                content: this.$t(res.msg || '保存失败')
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    submitApplicationForm() {
      const param = { ...this.searchForm }
      param.validPeriodTo = dayjs(param.validPeriodTo).valueOf()
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.$store.commit('startLoading')
          this.$API.outsourcingNew
            .addApplicationForm(param)
            .then((res) => {
              if (res.code === 200) {
                this.$toast({
                  type: 'success',
                  content: this.$t('提交成功')
                })
                this.todoRouter()
                return
              }
              this.$toast({
                type: 'error',
                content: this.$t('提交失败')
              })
            })
            .catch((err) => {
              this.$t({
                type: 'error',
                content: this.$t(err.msg || '操作失败')
              })
            })
            .finally(() => {
              this.$store.commit('endLoading')
            })
        }
      })
    },
    handleSubInfo(flag) {
      if (flag === 'save') {
        this.saveApplicationFormInfo()
        return
      }
      this.submitApplicationForm()
    },
    submit(flag) {
      if (this.detailIsPush) {
        this.handleSubInfo(flag)
        return
      }
      if (this.tableData.length > 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('明细数据未保存')
        })
      } else {
        this.$toast({
          type: 'warning',
          content: this.$t('明细数据不能为空')
        })
      }

      // this.$dialog({
      //   data: {
      //     title: this.$t('提示'),
      //     message: this.$t('明细数据未保存，是否保存或提交数据?')
      //   },
      //   success: () => {
      //     this.handleSubInfo(flag)
      //   }
      // })
    },
    // 新增申请单
    handleClickToolbar(args) {
      const { code, $grid } = args
      const _rowSelect = $grid.getCheckboxRecords()
      if (code === 'add') {
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('新增'),
            isEdit: false,
            row: this.searchForm
          },
          success: (data) => {
            if (data?.supplierCode && data?.customerCode) {
              this.detailIsPush = false
            }
            // this.handleCustomSearch({ applyNo: this.searchForm.applyNo })
            this.tableData = [{ ...data }].concat(this.tableData)
            // this.$toast({
            //   type: 'success',
            //   content: this.$t('新增成功')
            // })
          }
        })
        return
      }
      if (code === 'save') {
        if (this.tableData.length <= 0) {
          this.$toast({
            type: 'warning',
            content: this.$t('请先新增数据后再保存!!!')
          })
          return
        }
        const _tableData = this.tableData.map((item) => {
          if (item?.id?.includes('row')) {
            item.id = null
          }
          item.applyNo = this.searchForm.applyNo
          return item
        })
        this.$store.commit('startLoading')
        this.$API.outsourcingNew
          .saveApplicationFormDetails(_tableData)
          .then((res) => {
            if (res && res.code === 200) {
              this.detailIsPush = true
              this.$toast({
                type: 'success',
                content: this.$t('保存成功')
              })
              return
            }
            this.$toast({
              type: 'error',
              content: this.$t(res.msg || '保存失败')
            })
          })
          .catch((err) => {
            this.$toast({
              type: 'error',
              content: this.$t(err.msg || '保存失败')
            })
          })
          .finally(() => {
            this.$store.commit('endLoading')
          })
        return
      }
      if (code === 'import') {
        this.$dialog({
          modal: () => import('@/components/uploadDialog'),
          data: {
            title: this.$t('上传/导入'),
            importApi: this.$API.outsourcingNew.importApplicationFormDetails,
            downloadTemplateApi: this.$API.outsourcingNew.downloadTemplate,
            paramsKey: 'excel',
            asyncParams: {
              applyNo: this.searchForm.applyNo
            },
            downloadTemplateParams: {
              applyNo: this.searchForm.applyNo
            }
          },
          success: () => {
            // 导入之后刷新列表
            const _locationList = this.tableData.filter((item) => item.id.includes('row'))
            this.$store.commit('startLoading')
            this.$API.outsourcingNew
              .getApplicationReportDetails({ applyNo: this.searchForm.applyNo })
              .then((res) => {
                if (res && res.code === 200) {
                  this.tableData = _locationList.concat(res.data || [])
                  return
                }
              })
              .catch((err) => {
                this.$toast({
                  type: 'error',
                  content: this.$t(err.msg || '操作失败')
                })
              })
              .finally(() => {
                this.$store.commit('endLoading')
              })
          }
        })
        return
      }
      if (code === 'export') {
        this.$store.commit('startLoading')
        this.$API.outsourcingNew
          .downloadTemplate({ applyNo: this.searchForm.applyNo })
          .then((res) => {
            this.$store.commit('endLoading')
            const fileName = getHeadersFileName(res)
            downLoad({ fileName: `${fileName}`, blob: res.data })
          })
        return
      }
      if (_rowSelect && _rowSelect.length <= 0) {
        this.$toast({
          type: 'warning',
          content: this.$t('请选择一条数据')
        })
        return
      }
      if (code === 'edit') {
        if (_rowSelect.length > 1) {
          this.$toast({
            type: 'warning',
            content: this.$t('只能编辑一条数据')
          })
          return
        }
        _rowSelect[0].isEdit = true
        this.$dialog({
          modal: () => import('./components/addDialog.vue'),
          data: {
            title: this.$t('编辑'),
            isEdit: true,
            row: _rowSelect[0]
          },
          success: (data) => {
            // this.handleCustomSearch({ applyNo: this.searchForm.applyNo })
            this.tableData.forEach((item) => {
              if (item.id === data.id) {
                for (let key in item) {
                  if (data[key]) {
                    item[key] = data[key]
                  }
                }
                item.isEdit = false
              }
            })
            this.$toast({
              type: 'success',
              content: this.$t('操作成功')
            })
          }
        })
        return
      }
      if (code === 'delete') {
        const _ids = _rowSelect.map((item) => item.id)
        const _serverIds = _ids.filter((n) => n && !n.includes('row'))
        const _locationIds = _ids.filter((n) => n && n.includes('row'))
        this.handleDeleteLocationInfo(_locationIds)
        if (_serverIds.length <= 0) {
          this.handleDeleteLocationInfo(_locationIds)
          this.$toast({
            type: 'success',
            content: this.$t('删除成功')
          })
          return
        }
        this.$store.commit('startLoading')
        this.$API.outsourcingNew
          .deleteApplicationFormDetails({ ids: _serverIds })
          .then((res) => {
            if (res && res.code === 200) {
              this.$toast({
                type: 'success',
                content: this.$t(res.msg || '删除成功')
              })
              this.handleDeleteLocationInfo(_locationIds)
              this.getApplicationReportDetails({ applyNo: this.searchForm.applyNo || '' })
              return
            }
            this.$toast({
              type: 'error',
              content: this.$t(res.msg || '删除失败')
            })
          })
          .catch((err) => {
            this.$toast({
              type: 'error',
              content: this.$t(err.msg || '操作失败')
            })
          })
          .finally(() => {
            this.$store.commit('endLoading')
          })
        return
      }
    },
    handleDeleteLocationInfo(list) {
      if (list.length > 0) {
        list.map((m) => {
          this.tableData.map((item, index) => {
            if (m === item.id) {
              this.tableData.splice(index, 1)
            }
          })
        })
      }
    },
    subApplicationForm(list) {
      const ids = list.map((item) => item.id).filter((n) => !!n)
      this.$store.commit('startLoading')
      this.$API.outsourcingNew
        .cancelApplicationForm({ ids })
        .then((res) => {
          if (res && res.code === 200) {
            this.$toast({
              type: 'success',
              content: this.$t(res.msg || '取消成功')
            })
            return
          }
          this.$toast({
            type: 'success',
            content: this.$t(res.msg || '取消失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'success',
            content: this.$t(err.msg || '操作失败')
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    statusTransformValue(val) {
      if (!val) return ''
      const _obj = statusOptions.filter((item) => item.value === val)[0]
      return _obj.text
    },
    getRow(data) {
      console.log(data)
    },
    // 获取共享组织列表
    getOrgCodeList() {
      this.$API.outsourcingNew.getOrgCodeList().then((res) => {
        const _list = res.data || []
        this.orgCodeOptions = addCodeNameKeyInList({
          firstKey: 'orgCode',
          secondKey: 'orgName',
          list: _list
        })
      })
    },
    currentChange(current) {
      this.pageSettings.current = current
    },
    sizeChange(currentPageSize) {
      this.pageSettings.pageSize = currentPageSize
    },
    getOrgCode() {},
    dateChange(e, flag) {
      if (e.startDate) {
        this.searchForm[flag + 'Start'] = dayjs(e.startDate).valueOf()
        this.searchForm[flag + 'End'] = dayjs(e.endDate).valueOf()
      } else {
        this.searchForm[flag + 'Start'] = null
        this.searchForm[flag + 'End'] = null
      }
    },
    supplierChange(e) {
      if (e.itemData) {
        this.searchForm['supplierCode'] = e.itemData.supplierCode
        this.searchForm['supplierName'] = e.itemData.supplierName
      } else {
        this.searchForm['supplierCode'] = null
        this.searchForm['supplierName'] = null
      }
    },
    searchPersons(args = { text: '', setSelectData: null }) {
      const { text: fuzzyName, setSelectData } = args
      this.$API.masterData.getCurrentTenantEmployees({ fuzzyName }).then((res) => {
        const tmp = res.data || []
        this.personList = addCodeNameKeyInList({
          firstKey: 'employeeName',
          list: tmp
        })
        console.log(this.personList, 'personList')
        if (setSelectData) {
          setSelectData()
        }
      })
    },
    handleCustomReset() {
      for (let key in this.searchForm) {
        this.searchForm[key] = null
      }
    },
    // 获取明细信息
    getApplicationReportDetails(obj) {
      this.$store.commit('startLoading')
      this.$API.outsourcingNew
        .getApplicationReportDetails(obj)
        .then((res) => {
          if (res && res.code === 200) {
            this.detailIsPush = true
            this.tableData = res.data || []
            return
          }
          this.$toast({
            type: 'error',
            content: this.$t(res.msg || '获取明细失败')
          })
        })
        .catch((err) => {
          this.$toast({
            type: 'error',
            content: this.$t(err.msg || '获取明细失败')
          })
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    },
    // 获取抬头信息
    handleCustomSearch(row) {
      const _this = this
      this.$store.commit('startLoading')
      this.$API.outsourcingNew
        .getApplicationFormDetails(row)
        .then((res) => {
          if (res && res.code === 200) {
            for (let key in this.searchForm) {
              if (res.data && res.data[key]) {
                this.searchForm[key] = res.data[key]
              }
            }
            this.searchPersons({
              text: _this.searchForm.createUserName,
              setSelectData: () => {
                _this.searchForm.createUserId = res.data.createUserId
              }
            })
          }
        })
        .finally(() => {
          this.$store.commit('endLoading')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
  & .btn-list {
    width: 20%;
    position: relative;
    left: 80%;
  }
  & .submit-form {
    padding-top: 1.5rem;
  }
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-table-multi-cell .vxe-cell {
  &.c--tooltip {
    padding: 0 1px 0 0;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  & .span-style {
    display: flex;
    flex-flow: column;
    & span:nth-child(1) {
      border-bottom: 1px solid #cfd4d9;
    }
  }
}
.predict-vxe-dropdown {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}
.predict-vxe-list-item {
  display: block;
  padding: 8px;
  cursor: pointer;
}
.predict-vxe-list-item:hover {
  background-color: #f5f7fa;
}
::v-deep .xTable-class .vxe-table .table-row-class td {
  height: 40px !important;
}
</style>
