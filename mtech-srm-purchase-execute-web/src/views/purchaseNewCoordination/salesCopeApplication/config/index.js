import { i18n } from '@/main.js'
import Vue from 'vue'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}

export const statusOptions = [
  {
    text: i18n.t('新建'),
    value: 1
  },
  {
    text: i18n.t('审批中'),
    value: 2
  },
  {
    text: i18n.t('已审结'),
    value: 3
  },
  {
    text: i18n.t('已取消'),
    value: 4
  }
]

export const toolbar = [
  {
    code: 'add',
    title: i18n.t('新增'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'edit',
    title: i18n.t('编辑'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'import',
    title: i18n.t('导入'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'export',
    title: i18n.t('导出'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'delete',
    title: i18n.t('删除'),
    icon: '',
    status: 'primary'
  },
  {
    code: 'save',
    title: i18n.t('保存'),
    icon: '',
    status: 'primary'
  }
]

export const toolbarDetail = [
  {
    code: 'export',
    title: i18n.t('导出'),
    icon: '',
    status: 'primary'
  }
]

export const columnData = [
  {
    type: 'checkbox',
    width: '70',
    showOverflow: true
  },
  {
    width: '70',
    type: 'seq',
    title: i18n.t('序号'),
    showOverflow: true
  },
  {
    width: 200,
    field: 'supplierCode',
    title: i18n.t('供应商编码'),
    showOverflow: true
  },
  {
    field: 'supplierName',
    title: i18n.t('供应商名称'),
    showOverflow: true
  },
  {
    width: 200,
    field: 'customerCode',
    title: i18n.t('客户编码'),
    showOverflow: true
  },
  {
    field: 'customerName',
    title: i18n.t('客户名称'),
    showOverflow: true
  },
  {
    width: 160,
    field: 'amt',
    title: i18n.t('倒挂额度(元)'),
    showOverflow: true
  }
]
