<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="headerText"
    :buttons="buttons"
    @close="handleClose"
  >
    <mt-form ref="addFormRef" :model="addForm" :rules="addRules">
      <!-- <mt-form-item prop="companyCode" :label="$t('公司')">
        <debounce-filter-select
          v-model="addForm.companyCode"
          :request="getCompany"
          :data-source="companyOptions"
          :fields="{ text: 'theCodeName', value: 'orgCode' }"
          :value-template="companyCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="companyCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item> -->
      <mt-form-item prop="supplierCode" :label="$t('供应商')">
        <debounce-filter-select
          v-model="addForm.supplierCode"
          :request="getSupplier"
          :data-source="supplierOptions"
          :fields="{ text: 'theCodeName', value: 'supplierCode' }"
          :value-template="supplierCodeValueTemplate"
          :show-clear-button="true"
          :allow-filtering="true"
          @change="supplierCodeChange"
          :placeholder="$t('请选择')"
        ></debounce-filter-select>
      </mt-form-item>
      <mt-form-item prop="customerCode" :label="$t('客户')">
        <mt-select
          allow-filtering="true"
          v-model="addForm.customerCode"
          :data-source="customerCodeOptions"
          :placeholder="$t('请选择')"
          :fields="{ text: 'theCodeName', value: 'customerCode' }"
          :filtering="getCustomer"
          @change="customerChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="amt" :label="$t('倒挂额度')">
        <mt-input v-model="addForm.amt"></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import DebounceFilterSelect from '@/components/debounceFilterSelect/index.vue'
import { utils } from '@mtech-common/utils'

import { codeNameColumn, addCodeNameKeyInList } from '@/utils/utils'
export default {
  components: {
    DebounceFilterSelect
  },
  props: {
    modalData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      headerText: this.$t('新增'),
      supplierOptions: [],
      buyerOrgOptions: [],
      customerCodeOptions: [],
      companyOptions: [], // 公司 下拉选项
      companyCodeValueTemplate: codeNameColumn({
        firstKey: 'orgCode',
        secondKey: 'orgName'
      }),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      supplierCodeValueTemplate: codeNameColumn({
        firstKey: 'supplierCode',
        secondKey: 'supplierName'
      }), // 供应商
      addForm: {
        customerCode: '',
        customerName: '',
        amt: '',
        companyCode: '',
        companyName: '',
        supplierCode: '',
        supplierName: ''
      },
      addRules: {
        companyCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        siteCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        customerCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        buyerOrgCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        supplierCode: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }]
      }
    }
  },
  mounted() {
    console.log('我是弹窗')
    this.$refs.dialog.ejsRef.show()
    this.getBuyerOrgList({ text: undefined })
    this.getSupplier = utils.debounce(this.getSupplier, 500)
    this.getCustomer = utils.debounce(this.getCustomer, 500)

    this.$nextTick(() => {
      this.$refs.addFormRef.resetFields()
      if (this.modalData.isEdit) {
        this.headerText = this.modalData?.title
        if (this.modalData?.row) {
          this.addForm = {
            ...this.modalData.row
          }
          this.getCustomer({ text: this.modalData.row.companyCode })
          this.getSupplier({ text: this.modalData.row.supplierCode })
          console.log(this.addForm)
        }
      } else {
        this.getCustomer({ text: '' })
        this.getSupplier({ text: '' })
      }
    })
  },
  methods: {
    // 获取主数据-获取指定组织下指定组织层级节点列表 业务公司
    getCompany(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        organizationLevelCodes: ['ORG02', 'ORG01'],
        orgType: 'ORG001PRO',
        includeItself: true,
        organizationIds: []
      }
      this.$API.masterData
        .OrgFindSpecifiedChildrenLevelOrgs(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.companyOptions = addCodeNameKeyInList({
              firstKey: 'orgCode',
              secondKey: 'orgName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.companyOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    getCustomer(e) {
      //客户
      let str = e?.text || ''
      let params = {
        fuzzyNameOrCode: str
      }
      this.$API.masterData.getCustomer(params).then((res) => {
        const list = res.data || []
        this.customerCodeOptions = addCodeNameKeyInList({
          firstKey: 'customerCode',
          secondKey: 'customerName',
          list
        })
        this.$nextTick(() => {
          if (e.updateData && typeof e.updateData == 'function') {
            e.updateData(this.customerCodeOptions)
          }
        })
        if (this.header?.customerCode) this.ruleForm.customerCode = this.header?.customerCode
      })
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    supplierCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.supplierCode = itemData.supplierCode
        this.addForm.supplierName = itemData.supplierName
      } else {
        this.addForm.supplierCode = ''
        this.addForm.supplierName = ''
      }
    },
    customerChange(e) {
      if (e.itemData) {
        this.addForm.customerCode = e.itemData.customerCode
        this.addForm.customerName = e.itemData.customerName
      } else {
        this.addForm.customerCode = null
        this.addForm.customerName = null
      }
    },
    buyerOrgCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.buyerOrgId = itemData.id
        this.addForm.buyerOrgCode = itemData.groupCode
        this.addForm.buyerOrgName = itemData.groupName
      } else {
        this.addForm.buyerOrgId = ''
        this.addForm.buyerOrgCode = ''
        this.addForm.buyerOrgName = ''
      }
    },
    // 公司 change
    companyCodeChange(e) {
      const { itemData } = e
      if (itemData) {
        this.addForm.companyId = itemData.id
        this.addForm.companyCode = itemData.orgCode
        this.addForm.companyName = itemData.orgName
      } else {
        this.addForm.companyId = ''
        this.addForm.companyCode = ''
        this.addForm.companyName = ''
      }
    },
    // 主数据 获取供应商
    getSupplier(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyNameOrCode: text
      }
      this.$API.masterData
        .getSupplier(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.supplierOptions = addCodeNameKeyInList({
              firstKey: 'supplierCode',
              secondKey: 'supplierName',
              list
            })
            if (updateData) {
              this.$nextTick(() => {
                updateData(this.supplierOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    // 获取主数据-采购组
    getBuyerOrgList(args) {
      const { text, updateData, setSelectData } = args
      const params = {
        fuzzyParam: text,
        groupTypeCode: 'BG001CG'
      }
      this.$API.masterData
        .getbussinessGroup(params)
        .then((res) => {
          if (res) {
            const list = res?.data || []
            this.buyerOrgOptions = addCodeNameKeyInList({
              firstKey: 'groupCode',
              secondKey: 'groupName',
              list
            })
            if (updateData && typeof updateData === 'function') {
              this.$nextTick(() => {
                updateData(this.buyerOrgOptions)
              })
            }
            if (setSelectData) {
              this.$nextTick(() => {
                setSelectData()
              })
            }
          }
        })
        .catch(() => {})
    },
    confirm() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          console.log(this.addForm)
          let params = {
            ...this.addForm
          }
          this.$emit('confirm-function', params)
        }
      })
    }
  }
}
</script>

<style></style>
