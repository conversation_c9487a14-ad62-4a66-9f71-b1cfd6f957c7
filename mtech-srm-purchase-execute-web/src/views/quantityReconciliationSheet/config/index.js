import Vue from 'vue'
import { i18n } from '@/main.js'
import { timeNumberToDate, timeStringToDate } from '@/utils/utils'
import { MasterDataSelect } from '@/utils/constant'

// 时间日期显示
export const timeDate = (args) => {
  const { dataKey, isDateTime, isDate, isTime } = args

  const template = () => {
    return {
      template: Vue.component('timeDateComponent', {
        template: `
        <div class="grid-edit-column mt-flex-direction-column">
          <div class="field-content">
            <div v-if="isDateTime || isTime">{{data[dataKey] | timeFormat}}</div>
            <div v-if="isDateTime || isDate">{{data[dataKey] | dateFormat}}</div>
          </div>
        </div>`,
        data: function () {
          return { data: {}, dataKey, isDateTime, isDate, isTime }
        },
        filters: {
          dateFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'YYYY-mm-dd', value })
            } else {
              str = timeNumberToDate({ formatString: 'YYYY-mm-dd', value })
            }

            return str
          },
          timeFormat(value) {
            let str = ''
            // 数据库时间戳默认值为 0，为 0 时不显示
            if (value == 0) {
              return str
            }
            if (isNaN(Number(value))) {
              str = timeStringToDate({ formatString: 'HH:MM:SS', value })
            } else {
              str = timeNumberToDate({ formatString: 'HH:MM:SS', value })
            }

            return str
          }
        }
      })
    }
  }

  return template
}
export const columnData = [
  {
    width: '50',
    type: 'checkbox',
    showInColumnChooser: false
  },
  {
    field: 'siteCode',
    headerText: i18n.t('工厂编码')
  },
  {
    field: 'siteName',
    headerText: i18n.t('工厂名称')
  },
  {
    width: '150',
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    width: '150',
    field: 'thirdPartyLogisticsCode',
    headerText: i18n.t('第三方物流编码')
  },
  {
    field: 'thirdPartyLogisticsName',
    headerText: i18n.t('第三方物流名称')
  },
  {
    field: 'startDate',
    headerText: i18n.t('起始时间'),
    template: timeDate({ dataKey: 'startDate', isDate: true }),
    searchOptions: {
      ...MasterDataSelect.timeStamp
    }
  },
  {
    field: 'endDate',
    headerText: i18n.t('结束时间'),
    template: timeDate({ dataKey: 'endDate', isDate: true }),
    searchOptions: {
      ...MasterDataSelect.timeStamp
    }
  },
  {
    width: '170',
    field: 'vmiWarehouseCode',
    headerText: i18n.t('VMI仓编码')
  },
  {
    field: 'vmiWarehouseName',
    headerText: i18n.t('VMI仓名称')
  },
  {
    width: '170',
    field: 'itemCode',
    headerText: i18n.t('物料编码')
  },
  {
    field: 'itemName',
    headerText: i18n.t('物料名称')
  },
  {
    width: '150',
    field: 'purchaseGroupCode',
    headerText: i18n.t('采购组编码')
  },
  {
    width: '150',
    field: 'purchaseGroupName',
    headerText: i18n.t('采购组名称')
  },
  {
    width: '150',
    field: 'startCount',
    headerText: i18n.t('初期库存')
  },
  {
    width: '150',
    field: 'stockCount',
    headerText: i18n.t('入库数量')
  },
  {
    width: '150',
    field: 'returnCount',
    headerText: i18n.t('退货数量')
  },
  {
    width: '150',
    field: 'replaceOutCount',
    headerText: i18n.t('替换出数量')
  },
  {
    width: '150',
    field: 'replaceInCount',
    headerText: i18n.t('替换入数量')
  },
  {
    width: '150',
    field: 'deliveryCount',
    headerText: i18n.t('送货（在途）数量')
  },
  {
    width: '150',
    field: 'receiveCount',
    headerText: i18n.t('送货（收货）数量')
  },
  {
    width: '150',
    field: 'endCount',
    headerText: i18n.t('期末库存')
  }
]
