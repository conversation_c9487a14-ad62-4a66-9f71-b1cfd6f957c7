<template>
  <!-- 数量对账报表 -->
  <div class="full-height pt20">
    <mt-template-page
      ref="templateRef"
      :template-config="templateConfig"
      @handleClickToolBar="handleClickToolBar"
    >
    </mt-template-page>
  </div>
</template>

<script>
import { columnData } from './config'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {},
  data() {
    return {
      templateConfig: [
        {
          useToolTemplate: false, // 不使用预置(新增、编辑、删除)
          useBaseConfig: true, // 使用组件中的toolbar配置
          activatedRefresh: false,

          toolbar: [
            {
              id: 'listExport',
              icon: 'icon_solid_export',

              title: this.$t('导出')
            }
          ],
          gridId: '23d34cc6-a19a-4fe4-90ef-616b2493110a',
          grid: {
            frozenColumns: 3,
            allowPaging: true, // 分页
            // lineIndex: 0, // 序号列
            columnData: columnData,
            dataSource: [],
            asyncConfig: {
              url:
                this.$route.name === 'purchase/reconciliation'
                  ? `/statistics/tenant/vmiStockCountReport/purchase/report`
                  : this.$route.name === 'supplier/reconciliation'
                  ? `/statistics/tenant/vmiStockCountReport/supplier/report`
                  : this.$route.name === 'logistics/reconciliation'
                  ? `/statistics/tenant/vmiStockCountReport/third/report`
                  : ''
            }
          }
        }
      ]
    }
  },
  mounted() {
    console.log(this.$route)
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'listExport') {
        // 配置-导出
        this.listExport()
      }
    },
    listExport() {
      this.$loading()
      const queryBuilderRules =
        this.$refs.templateRef.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
      const params = {
        page: { current: 1, size: 10000 },
        ...queryBuilderRules
      } // 筛选条件

      this.$route.name === 'purchase/reconciliation'
        ? this.reconciliation('purchase', params)
        : this.$route.name === 'supplier/reconciliation'
        ? this.reconciliation('supplier', params)
        : this.$route.name === 'logistics/reconciliation'
        ? this.reconciliation('logistics', params)
        : ''
    },
    reconciliation(type, params) {
      this.$API.report[`${type}Export`](params)
        .then((res) => {
          this.$toast({
            type: 'success',
            content: this.$t('正在导出，请稍后！')
          })
          const fileName = getHeadersFileName(res)
          download({ fileName: `${fileName}`, blob: res.data })
          this.$hloading()
        })
        .catch((error) => {
          this.$hloading()
          this.$toast({
            content: error.msg || this.$t('下载失败'),
            type: 'warning'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
