<template>
  <div id="purchase-execute">
    <mt-loading v-show="loading"></mt-loading>
    <!-- <router-view v-show="!loading"></router-view> -->
    <!-- <keep-alive>
      <router-view v-if="isKeepAlive($router)" v-show="!loading"></router-view>
    </keep-alive>
    <router-view v-if="!isKeepAlive($router)" v-show="!loading"></router-view> -->
    <!-- routerKey:{{ routerKey }} -->
    <keep-alive>
      <router-view :key="routerKey"></router-view>
    </keep-alive>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'App',
  data() {
    return {
      active: 0,
      routerKey: this.$route.fullPath
    }
  },
  computed: {
    ...mapState(['loading'])
  },
  watch: {
    $route: {
      handler(newVal) {
        console.log('app.vue', newVal)
        if (newVal?.meta?.random) {
          this.routerKey = this.$route.fullPath + '-' + this.$route.meta.random
        } else {
          this.routerKey = this.$route.fullPath
        }
      },
      deep: true
    }
  },

  created() {
    this.$nextTick(() => {
      this.getUserInfo()
    })
  },

  methods: {
    next() {
      if (this.active++ > 2) this.active = 0
    },
    getUserInfo() {
      // 获取当前登录的用户
      this.$API.masterData.getUserInfo().then((res) => {
        if (res?.data) {
          sessionStorage.setItem('userInfo', JSON.stringify(res.data))
          this.$store.commit('updateUserInfo', res.data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#purchase-execute {
  width: 100%;
  height: 100%;
  position: relative;
  /deep/ .mt-loading {
    z-index: 9999;
  }
}
</style>
