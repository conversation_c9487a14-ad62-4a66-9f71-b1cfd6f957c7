import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
const state = {
  loading: false,
  // 采购申请 准备提交/保存的指令(只要涉及到要判断编辑状态的都要在此加状态) .....保存或提交之后一定要修改回0
  prSubmitFlag: 0, // 0：默认状态；1：保存草稿；2：提交，3：批量编辑(需求明细), 4：切换顶部的公司数据(需求明细), 5: 新增一行(需求明细), 6: 切换tab,
  // 7: 从需求明细tab切出去且处于结束编辑状态
  prCompanyInfo: {
    companyId: null,
    companyName: null,
    companyCode: null
  }, // 寻源需求 - 采购申请 选中的业务公司id
  sourceCompanyInfo: {
    companyId: null,
    companyName: null,
    companyCode: null
  }, // 寻源需求 - 寻源申请 选中的业务公司id
  companyId: '', //采购订单 选择的公司id
  companyCode: '', //采购订单 选择的公司id
  pcBusinessCode: '', // 采购申请的业务类型编码
  reconTotal: {
    // 创建对账单页面 数据
    openUnTax: 0, // 高低开 未税
    openTax: 0, // 高低开 含税
    detailUnTax: 0, // 对账单 汇总 执行未税
    detailTax: 0 // 对账单 汇总 执行含税
  },
  userInfo: null, // 用户登录信息
  preCreateParams: null,
  reconSummaryTvSearch: null, // 往来对账汇总TV查询条件
  saleReconSearch: null, // 销售对账查询条件
  outReconSearch: null, // 外发对账查询条件
  vnReconStandardSearch: null, // 越南对账标准查询条件
  vnReconConsignmentSearch: null, // 越南对账寄售查询条件
  deliveryQuantityStatisticsRow: null // 交货数统计行信息
}
const mutations = {
  startLoading(state) {
    state.loading = true
  },
  endLoading(state) {
    state.loading = false
  },
  updatePrSubmitFlag(state, prSubmitFlag) {
    state.prSubmitFlag = prSubmitFlag
  },
  updatePrCompanyInfo(state, prCompanyInfo) {
    state.prCompanyInfo = prCompanyInfo
  },
  updateSourceCompanyInfo(state, sourceCompanyInfo) {
    state.sourceCompanyInfo = sourceCompanyInfo
  },
  updateCompanyId(state, companyId) {
    state.companyId = companyId
  },
  updateCompanyCode(state, companyCode) {
    state.companyCode = companyCode
  },
  updatePcBusinessCode(state, pcBusinessCode) {
    state.pcBusinessCode = pcBusinessCode
  },
  updateReconTotal(state, reconTotal) {
    state.reconTotal = reconTotal
  },
  updateUserInfo(state, userInfo) {
    state.userInfo = userInfo
  },
  setPreCreateParams(state, preCreateParams) {
    state.preCreateParams = preCreateParams
  },
  setReconSummaryTvSearch(state, reconSummaryTvSearch) {
    state.reconSummaryTvSearch = reconSummaryTvSearch
  },
  setSaleReconSearch(state, saleReconSearch) {
    state.saleReconSearch = saleReconSearch
  },
  setOutReconSearch(state, outReconSearch) {
    state.outReconSearch = outReconSearch
  },
  setVnReconStandardSearch(state, vnReconStandardSearch) {
    state.vnReconStandardSearch = vnReconStandardSearch
  },
  setVnReconConsignmentSearch(state, vnReconConsignmentSearch) {
    state.vnReconConsignmentSearch = vnReconConsignmentSearch
  },
  setDeliveryQuantityStatisticsRow(state, deliveryQuantityStatisticsRow) {
    state.deliveryQuantityStatisticsRow = deliveryQuantityStatisticsRow
  }
}
const actions = {
  endLoading({ commit, dispatch }) {
    console.log('dispatch', dispatch)
    setTimeout(() => {
      commit('endLoading')
    }, parseInt(Math.random() * 300 + 300))
  }
}
const store = new Vuex.Store({
  state,
  mutations,
  getters: {},
  actions
})

export default store
