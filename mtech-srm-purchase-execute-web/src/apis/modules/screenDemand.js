// 屏采协同  接口
import { API } from '@mtech-common/http'
export const NAME = 'screenDemand'
const PROXY_BASE = '/srm-purchase-execute/tenant'
const PROXY_INTERNAL = '/srm-purchase-execute/internal'

// 屏采交货计划
// 库位地点清单  导入
export const screenWarehouseImport = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/import`, data, {
    responseType: 'blob'
  })

// 库位地点清单  导入模板下载
export const screenWarehouseImportTemp = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/download`, data, {
    responseType: 'blob'
  })

// 库位地点清单  导出
export const screenWarehouseExport = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/export`, data, {
    responseType: 'blob'
  })

// 库位地点清单  删除
export const screenWarehouseDelete = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/delete`, data)

// 库位地点清单  新增
export const screenWarehouseAdd = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/add`, data)

// 库位地点清单  修改
export const screenWarehouseUpdate = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/update`, data)

// 在途周期  导入
export const inTransitCycleImport = (data = {}) =>
  API.post(`${PROXY_BASE}/srm-scm/screen/importTransit`, data, {
    responseType: 'blob'
  })

// 在途周期  导入模板下载
export const inTransitCycleImportTemp = (data = {}) =>
  API.post(`${PROXY_BASE}/srm-scm/screen/transitTemplate`, data, {
    responseType: 'blob'
  })

// 在途周期  导出
export const inTransitCycleExport = (data = {}) =>
  API.post(`${PROXY_BASE}/srm-scm/screen/exportTransit`, data, {
    responseType: 'blob'
  })

// 在途周期  删除
export const inTransitCycleDelete = (data = {}) =>
  API.post(`${PROXY_BASE}/srm-scm/screen/delete`, data)

// 采方-交货计划查询
export const screenScheduleQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/query`, data)

// 采方-交货计划新增
export const screenScheduleAdd = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/add`, data)

// 采方-交货计划更新
export const screenScheduleUpdate = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/update`, data)

// 采方-交货计划删除
export const screenScheduleDelete = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/delete`, data)

// 采方-交货计划发布
export const screenSchedulePublish = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/publish`, data)

// 采方-交货计划获取bg类型下拉及库位编码下拉
export const screenWarehouseList = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/query/list`, data)

// 采方-交货计划导入
export const screenScheduleImport = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/import`, data, {
    responseType: 'blob'
  })

// 采方-交货计划导入模板
export const screenScheduleImportTemp = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/export/template`, data, {
    responseType: 'blob'
  })

// 采方-交货计划导出
export const screenScheduleeExport = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/export`, data, {
    responseType: 'blob'
  })

// 采方-供应商物料编码查询
export const supplierMaterialQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/supplier/material/code/query`, data)

// 采方-交货地点查询
export const deliveryAddressQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/screen/warehouse/addr/delivery/address/query`, data)

// 采方-交货计划历史查询
export const screenScheduleHistoryQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/history/query`, data)

// 采方-交货计划历史版本下拉获取
export const screenScheduleVersionQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/version/query`, data)

// 供方-交货计划查询
export const screenScheduleSupQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/supplier/screen/query`, data)

// 供方-交货计划导出
export const screenScheduleeSupExport = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/supplier/screen/export`, data, {
    responseType: 'blob'
  })

// 采方-交货计划历史导出
export const screenScheduleeHistoryPurExport = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/buyer/screen/export/history`, data, {
    responseType: 'blob'
  })

// 供方-交货计划历史导出
export const screenScheduleeHistorySupExport = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/supplier/screen/export/history`, data, {
    responseType: 'blob'
  })

// 供方-交货计划历史查询
export const screenScheduleHistorySupQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/supplier/screen/history/query`, data)

// 供方-交货计划历史版本下拉获取
export const screenScheduleVersionSupQuery = (data = {}) =>
  API.post(`${PROXY_BASE}/demand/plan/supplier/screen/query/version`, data)

// 屏采预测与供应
// 同步SCM
export const syncScm = (data = {}) => API.get(`${PROXY_INTERNAL}/screen/demand/v1/sync/scm`, data)

// 采方-屏需求查询
export const screenDemandQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/demand/v1/query`, data)

// 采方-屏预测管理-获取规格列表
export const screenDemandForecastManagerQuerySpecAll = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/forecast/manager/v1/query/spec/all`, data)

// 采方-屏预测管理查询
export const screenDemandForecastManagerQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/manager/v1/query`, data)

// 采方-屏预测管理修改
export const screenDemandForecastManagerUpdate = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/manager/v1/update`, data)

// 采方-屏预测管理发布
export const screenDemandForecastManagerPublish = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/manager/v1/publish`, data)

// 采方-屏预测管理导入
export const screenDemandForecastManagerImport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/manager/v1/excel/import`, data, {
    responseType: 'blob'
  })

// 采方-屏预测管理导出
export const screenDemandForecastManagerExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/manager/v1/excel/export`, data, {
    responseType: 'blob'
  })

// 采方-屏预测查询
export const screenDemandForecastQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/buyer/v1/query`, data)

// 采方-屏预测管理导出
export const screenDemandForecastExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/buyer/v1/export`, data, {
    responseType: 'blob'
  })

// 供方-屏预测查询
export const screenDemandForecastSupQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/supplier/v1/query`, data)

// 供方-屏预测管理导出
export const screenDemandForecastSupExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/forecast/supplier/v1/export`, data, {
    responseType: 'blob'
  })

// 采方-屏供应 - 获取规格列表
export const screenDemandSupplyBuyerQuerySpecAll = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/supply/buyer/v1/query/spec/all`, data)

// 采方-屏供应查询
export const screenDemandSupplyBuyerQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/query`, data)

// 采方-屏供应新增
export const screenDemandSupplyBuyerSave = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/save/batch`, data)

// 采方-屏供应修改
export const screenDemandSupplyBuyerUpdate = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/update/batch`, data)

// 采方-屏供应确认
export const screenDemandSupplyBuyerConfirm = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/confirm`, data)

// 采方-屏供应导入
export const screenDemandSupplyBuyerImport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/excel/import`, data, {
    responseType: 'blob'
  })

// 采方-屏供应导出
export const screenDemandSupplyBuyerExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/excel/export`, data, {
    responseType: 'blob'
  })

// 供方-屏供应查询
export const screenDemandSupplySupplierQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/query`, data)

// 供方-屏供应新增
export const screenDemandSupplySupplierSave = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/save/batch`, data)

// 供方-屏供应修改
export const screenDemandSupplySupplierUpdate = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/update/batch`, data)

// 供方-屏供应确认
export const screenDemandSupplySupplierFeedback = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/feedback`, data)

// 供方-屏供应导入
export const screenDemandSupplySupplierImport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/excel/import`, data, {
    responseType: 'blob'
  })

// 供方-屏供应导入模板下载
export const screenDemandSupplySupplierImportTemplate = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/supply/supplier/v1/excel/import/template`, data, {
    responseType: 'blob'
  })

// 供方-屏供应导出
export const screenDemandSupplySupplierExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/excel/export`, data, {
    responseType: 'blob'
  })

// 采方-屏供应汇总查询
export const screenDemandSummaryQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/month/summary/query`, data)

// 采方-屏供应汇总导出
export const screenDemandSummaryExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/buyer/v1/month/summary/export`, data, {
    responseType: 'blob'
  })

// 供方-屏供应汇总查询
export const screenDemandSummarySupQuery = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/month/summary/query`, data)

// 供方-屏供应汇总导出
export const screenDemandSummarySupExport = (data = {}) =>
  API.post(`${PROXY_INTERNAL}/screen/supply/supplier/v1/month/summary/export`, data, {
    responseType: 'blob'
  })

// 采方-获取出货地点列表
export const getScreenAddrAll = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/supply/buyer/v1/query/deliver/addr/all`, data)

// 采方-获取最新的版本记录
export const syncLatestVersion = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/sync/record/v1/query/latest/record`, data)

// 采方-获取屏采供应商
export const getScreenSupplier = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/forecast/manager/v1/query/supplier`, data)

// 采方-获取屏采供应商
export const getScreenSupplySupplier = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/supply/buyer/v1/query/supplier`, data)

// 采方-获取屏采供应商物料编码
export const getScreenSupplierItemCode = (data = {}) =>
  API.get(`${PROXY_INTERNAL}/screen/forecast/manager/v1/query/supplier/item/code`, data)

// 采方-获取库存日期
export const getInvDateApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/inv/query/all/inv/date`, data)

// 采方、供方 - 寄售库存 -导出
export const exportConsignmentInv = (data = {}) =>
  API.post(`${PROXY_BASE}/sap/consignment/inv/export`, data, {
    responseType: 'blob'
  })
