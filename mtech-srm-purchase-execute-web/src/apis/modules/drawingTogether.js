import { API } from '@mtech-common/http'

// import { BASE_TENANT } from "@/utils/constant";
const PROXY_SOURCING_TENANT = '/sourcing/tenant'
const PROXY_PURCHASE_EXECUTE_TENANT = '/srm-purchase-execute/tenant'

export const NAME = 'drawingTogether'

// 根据物料编码查询图纸列表
export const drawingsListByItemCode = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/listByItemCode`, data)

// 采方创建发布图纸
export const createPublish = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/create/publish`, data)

// 物料发送-反馈记录查询
export const getFeedbackRecords = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/buyer/record/query`, data)

// 供方异常反馈
export const supFeedback = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/sup/feedback`, data)

// 供方查看图纸
export const supQueryDrawing = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/sup/view`, data)

// 采方查看图纸
export const purQueryDrawing = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/buyer/view`, data)

// 供方查看dwg图纸
export const supQueryDwgDrawing = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/sup/views`, data)

// 采方查看dwg图纸
export const purQueryDwgDrawing = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/buyer/views`, data)

// 采方查看BOM
export const purQueryDrawingBom = (data = {}) =>
  API.get(`${PROXY_PURCHASE_EXECUTE_TENANT}/bom`, data)

// 采方查看BOM
export const purQueryDrawingBomExport = (data = {}) =>
  API.get(`${PROXY_PURCHASE_EXECUTE_TENANT}/bom/export`, data, {
    responseType: 'blob'
  })

// 采方导出
export const purExportDrawing = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/buyer/export`, data, {
    responseType: 'blob'
  })
// 采方图纸更新导出
export const purExportDrawingUpdate = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawingsUpdate/buyer/export`, data, {
    responseType: 'blob'
  })

// 供方图纸查阅反馈
export const supViewFeedback = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawingsUpdate/supplier/reviewFeedback`, data)

// 供方导出
export const supExportDrawing = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/supplier/export`, data, {
    responseType: 'blob'
  })

// -------------------采方-印刷品序列号变量查询-----------------------------------
// 已发布的序列号查询 - 分页查询
export const pagePrintMaterialSerialNumberPushApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberPush/buyer/queryPage`, data)
// 已发布的序列号查询 - 导出
export const exportPrintMaterialSerialNumberPushApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberPush/buyer/export`, data, {
    responseType: 'blob'
  })

// PLM-SRM推送清单 - 分页查询
export const pagePrintMaterialSerialNumberApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumber/queryPage`, data)
// PLM-SRM推送清单 - 导出
export const exportPrintMaterialSerialNumberApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumber/export`, data, {
    responseType: 'blob'
  })
// PLM-SRM推送清单 - 创建发布
export const createPushPrintMaterialSerialNumberApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumber/createPush`, data)
// 操作/更新记录
export const queryOperateLogApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberLog/queryOperateLog`, data)
// 印刷品实时获取下载链接
export const getFileUrlApi = (data = {}) =>
  API.get(`/masterDataManagement/tenant/printMaterialSerialNumber/getFileUrl/${data.id}`)
// -------------------供方-印刷品序列号变量查询-----------------------------------
// 分页查询
export const pagePrintMaterialSerialNumberPushSupplierApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberPush/supplier/queryPage`, data)
// 导出
export const exportPrintMaterialSerialNumberPushSupplierApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberPush/supplier/export`, data, {
    responseType: 'blob'
  })
// 查看印刷品序列号变量
export const viewSerialNumberApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberPush/viewSerialNumber`, data)
// 导出
export const exportOperateLogApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/printMaterialSerialNumberLog/exportOperateLog`, data, {
    responseType: 'blob'
  })

// 查看DWG图纸
export const queryPlmCadDocApi = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/queryPlmCadDoc`, data)

// 供方-查看图纸
export const pageCheckSupApi = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/sup/queryDrawingLatestVersionDoc`, data)
// 采方-查看图纸
export const pageCheckApi = (data = {}) =>
  API.get(`${PROXY_SOURCING_TENANT}/drawings/queryDrawingLatestVersionDoc`, data)

// 查看图纸历史版本
export const pageHistoryCheckApi = (data = {}) =>
  API.post(`${PROXY_SOURCING_TENANT}/drawings/queryDrawingHistoryVersionDoc`, data)

// 查看图纸历史版本
export const getItemParentDosageApi = (data = {}) =>
  API.get(`/masterDataManagement/tenant/printMaterialSerialNumber/getItemParentDosage`, data)

// 供方查看BOM
export const queryBomSupApi = (data = {}) =>
  API.get(`/srm-purchase-execute/tenant/bom/supplier/query`, data)
// 供方导出BOM
export const exportBomSupApi = (data = {}) =>
  API.get(`/srm-purchase-execute/tenant/bom/supplier/export`, data, {
    responseType: 'blob'
  })
