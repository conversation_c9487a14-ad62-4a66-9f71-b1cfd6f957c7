import { API } from '@mtech-common/http'
import { BASE_TENANT, PROXY_MDM_TENANT } from '@/utils/constant'

export const NAME = 'contractPrint'
/*
 列表
*/
// 查询合同列表---采方
export const queryBuilder = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/queryBuilder`, data)

// 查询合同列表---供方
export const queryBuilderFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/queryBuilderFeedback`, data)
// 合同生成与发布配置---列表
export const contractPrintConfigquery = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/query`, data)
// 寄售合同打印---列表
export const reconciliationHeader = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/queryBuilder`, data)

// // 采方---寄售合同
// export const contractPrintConfigprint = (data = {}) =>
//   API.post(`${BASE_TENANT}/contractPrintConfig/print`, data);
// // 供方---寄售合同
// export const contractPrintConfigsupPrint = (data = {}) =>
//   API.post(`${BASE_TENANT}/contractPrintConfig/supPrint`, data);

// 合同生成与发布配置---导入
export const contractPrintConfigimport = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/import`, data, {
    responseType: 'blob'
  })
// 合同生成与发布配置---导出
export const contractPrintConfigexport = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/export`, data, {
    responseType: 'blob'
  })
// 合同生成与发布配置---删除
export const contractPrintConfigdelete = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/delete`, data)
// 合同生成与发布配置---新增----查找公司
export const findSpecifiedChildrenLevelOrgs = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/findSpecifiedChildrenLevelOrgs`, data)
// 合同生成与发布配置---新增----供应商
export const criteriaQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/supplier/criteria-query`, data)
// 合同生成与发布配置---保存
export const saveAndUpdateData = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/saveAndUpdateData`, data)

// 合同生成与发布配置---初始化合同模板
export const initContractPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/initContractPrint`, data)
// 合同生成与发布配置---根据id获取模板
export const queryInfo = (id = {}) => API.get(`${BASE_TENANT}/contractPrintConfig/queryInfo`, id)
// 合同生成与发布配置---修改状态
export const contractStatus = (id = {}) =>
  API.post(`${BASE_TENANT}/contractPrintConfig/contractStatus`, id)

/*
  合同打印
  */
// 采方---寄售合同打印
export const printConsignment = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrint/printConsignment`, data, {
    responseType: 'blob'
  })
// 供方---寄售合同打印
export const supPrintConsignment = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrint/supPrintConsignment`, data, {
    responseType: 'blob'
  })
// 采方---订单合同打印
export const printOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrint/printOrder`, data, {
    responseType: 'blob'
  })
// 供方---订单合同打印
export const supPrintOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrint/supPrintOrder`, data, {
    responseType: 'blob'
  })
// 供方---订单合同打印 new
export const supPrintOrderContract = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrint/supPrintOrderContract`, data, {
    responseType: 'blob'
  })

// 供方 --- 订单合同打印 --- 打印中英文合同
export const supPrintOrderEnZh = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/printTvStandardContractEn`, data, {
    responseType: 'blob'
  })
/*
  查询合同模板详情
  */
//根据id获取合同模板
export const contractPrintTemplateQueryInfo = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintTemplate/queryInfo`, data)
export const updateContractTemplate = (data = {}) =>
  API.post(`${BASE_TENANT}/contractPrintTemplate/updateContractTemplate`, data)

// 供方 --- 寄售合同打印
export const printTvConsignment = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/printTvConsignment`, data, {
    responseType: 'blob'
  })

// 供方 --- 港料合同打印
export const printHKOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/printHKOrder`, data, {
    responseType: 'blob'
  })

// 采方 --- 屏采合同重新生成pdf
export const screenGeneratePdf = (data = {}) =>
  API.post(`${BASE_TENANT}/screen/buyerOder/generatePdf`, data)

// 采方 --- 屏采合同批量下载
export const batchDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/screen/buyerOder/batchDownload`, data, {
    responseType: 'blob'
  })

// 供方 --- 屏采合同批量下载
export const batchDownloadSup = (data = {}) =>
  API.post(`${BASE_TENANT}/screen/supOrder/batchDownload`, data, {
    responseType: 'blob'
  })
