import { API } from '@mtech-common/http'

export const NAME = 'authorizationApplication'

export const saveAuthPage = (data = {}) =>
  // 供方创建供应商授权申请书
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supAuth/supplier/create`, data)

export const printAuthPage = (headerId, data = {}) =>
  // 供方打印供应商授权申请书
  API.post(
    `/srm-purchase-execute/tenant/deliver/auth/supAuth/supplier/print-html?headerId=${headerId}`,
    data,
    {
      responseType: 'blob'
    }
  )

export const submitAuthPage = (data = {}) =>
  // 供方提交供应商授权申请书
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supAuth/supplier/submit`, data)

export const updateAuthPage = (data = {}) =>
  // 供方更新供应商授权申请书
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supAuth/supplier/save`, data)

export const saveFile = (data = {}) =>
  // 供方保存供应商授权申请书附件
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supAuth/supplier/saveFile`, data)

export const savePurAuthPage = (data = {}) =>
  // 采方保存供应商授权申请书
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supAuth/buyer/update`, data)

export const exportAuthPage = (data = {}) =>
  // 采方供应商授权申请书
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supAuth/buyer/export`, data, {
    responseType: 'blob'
  })

export const exportSupPassPage = (data = {}) =>
  // 采方供应商出入证申请书导出
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supPass/buyer/export`, data, {
    responseType: 'blob'
  })

export const createSupPass = (data = {}) =>
  // 供方临时出入证创建
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supPass/supplier/create`, data)

export const submitSupPass = (data = {}) =>
  // 供方提交临时出入证申请
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supPass/supplier/submit`, data)

export const printSupPassPage = (headerId, data = {}) =>
  // 供方打印临时出入证创建
  API.post(
    `/srm-purchase-execute/tenant/deliver/auth/supPass/supplier/print-html?headerId=${headerId}`,
    data,
    {
      responseType: 'blob'
    }
  )

export const saveSupPassPage = (data = {}) =>
  // 供方保存临时出入证创建
  API.post(`/srm-purchase-execute/tenant/deliver/auth/supPass/supplier/save`, data)

export const batchDelByIds = (data = {}) =>
  // 供方入园申请批量删除
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/supplier/batch/del`, data)

export const exportParkApplySup = (data = {}) =>
  // 供方入园申请 导出
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/supplier/export`, data, {
    responseType: 'blob'
  })

export const saveParkApplySup = (data = {}) =>
  // 供方入园申请新增
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/supplier/save`, data)

export const updateParkApplySup = (data = {}) =>
  // 供方入园申请修改
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/supplier/update`, data)

export const submitParkApplySup = (data = {}) =>
  // 供方入园申请提交
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/supplier/submit`, data)

export const getParkApplyDetailSup = (data = {}) =>
  // 根据出入证Id查询详情
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/supplier/inOutId`, data)

export const queryTvFilesList = (data = {}) =>
  // 出入证详情查附件
  API.get(`/srm-purchase-execute/tenant/tv/files/query/list`, data)

export const delTvFilesList = (data = {}) =>
  // 出入证详情删除附件
  API.post(`/srm-purchase-execute/tenant/tv/files/delBySysFileId`, data)

export const exportParkApplyPur = (data = {}) =>
  // 采方导出入园申请 导出
  API.post(`/srm-purchase-execute/tenant/lcd/park/apply/buyer/export`, data, {
    responseType: 'blob'
  })
