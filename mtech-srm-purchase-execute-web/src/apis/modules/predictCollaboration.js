import { API } from '@mtech-common/http'

import { BASE_TENANT, BASE_INTERNAL } from '@/utils/constant'

// 预测协同
export const NAME = 'predictCollaboration'

// 采方-批量写入预测数据
export const postBuyerForecastBatchInsert = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/batch/insert`, data)

// 采方-批量写入预测数据 -Tv
export const postBuyerForecastBatchInsertTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/batch/save`, data)

// 获取有权限的计划员 只有code
export const getPlannerAll = (data = {}) =>
  API.post(`${BASE_INTERNAL}/planner/v1/relation/planner/all`, data)

// 获取有权限的计划员 code和Name
export const getPlannerAllName = (data = {}) =>
  API.post(`${BASE_INTERNAL}/planner/v1/relation/planner/all/name`, data)

// 供方获取有权限的计划员 code和Name
export const getPlannerAllNameSup = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/supplier/v1/query/planner`, data)

// 供方预测历史获取有权限的计划员 code和Name
export const getPlannerAllNameHistorySup = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/supplier/history/v1/query/planner`, data)

// 采方取消发布接口
export const postBuyerForecastCancel = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/cancel`, data)

// 采方预测信息-查询导入错误数据
export const getBuyerForecastCacheErrorList = (data = {}) =>
  API.get(`${BASE_TENANT}/buyer/forecast/cache/error/list`, data)

// 采方预测信息-修改导入数据
export const postBuyerForecastCacheList = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/cache/update`, data)

// 采方预测信息-删除导入数据
export const postBuyerForecastBatchCacheDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/batch/cache/delete`, data)

// 采方预测信息-导入数据提交
export const getBuyerForecastCacheSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/buyer/forecast/cache/submit`, data)

// 采方确认信息
export const postBuyerForecastConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/confirm`, data)

// 采方确认信息 - TV
export const postBuyerForecastConfirmTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/batch/confirm`, data)

// 采方删除预测信息
export const postBuyerForecastDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/delete`, data)

// 采方删除预测信息 - TV
export const postBuyerForecastDeleteTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/batch/delete`, data)

// 采方预测信息-导出
export const postBuyerForecastExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/exportByForecastType`, data, {
    responseType: 'blob'
  })

// 采方预测信息-new导出
export const postKfBuyerForecastExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/kt-export`, data, {
    responseType: 'blob'
  })

// 采方预测信息-new导出模板 -tv
export const postBuyerForecastExportTemplateTv = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/buyer/v1/excel/down/exportTemplate`, data, {
    responseType: 'blob'
  })

// 采方预测信息-new导出 -tv
export const postBuyerForecastExportTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/excel/export`, data, {
    // responseType: 'blob'
  })

// 采方预测历史-new导出 -tv
export const postBuyerForecastExportHistoryTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/history/v1/excel/export`, data, {
    // responseType: 'blob'
  })

// 采方预测历史-new导出 -tv
export const postSupplierForecastExportHistoryTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/supplier/history/v1/excel/export`, data, {
    // responseType: 'blob'
  })

// 采方预测信息-new导入 -tv
export const postBuyerForecastImportTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/excel/import`, data, {
    responseType: 'blob'
  })

// 采方预测信息-导入
export const postBuyerForecastImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/import`, data)

// 采方预测信息-查询导入数据
export const getBuyerForecastCacheList = (data = {}) =>
  API.get(`${BASE_TENANT}/buyer/forecast/cache/list`, data)

// 供方预测信息-查询导入数据
export const getSupForecastCacheList = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/forecast/cache/list`, data)

// 采方预测信息-查询导入错误数据
export const getSupForecastCacheErrorList = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/forecast/cache/error/list`, data)

// 采方预测信息-修改导入数据
export const postSupForecastCacheList = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/cache/update`, data)

// 采方预测信息-删除导入数据
export const postSupForecastBatchCacheDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/batch/cache/delete`, data)

// 供方预测信息-导入数据提交
export const getSupForecastCacheSubmit = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/forecast/cache/submit`, data)

// 采方发布预测信息接口
export const postBuyerForecastPublish = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/publish`, data)

// 采方发布预测信息接口 - Tv
export const postBuyerForecastPublishTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/batch/publish`, data)

// 采方-获取采方预测信息列表
export const postBuyerForecastQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/query`, data)

// 采方-根据ID获取采方预测信息
export const getBuyerForecastQueryId = (id = '') =>
  API.get(`${BASE_TENANT}/buyer/forecast/query/${id}`)

// 采方-new获取采方预测信息列表
export const postBuyerKtForecastQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/kt-query`, data)

// 采方-new获取采方预测信息列表 -TV
export const postBuyerTvForecastQuery = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/query`, data)

// 采方-new根据ID获取采方预测信息
export const getBuyerKtForecastQueryId = (id = '') =>
  API.get(`${BASE_TENANT}/buyer/forecast/kt-query/${id}`)

// 采方-修改预测信息
export const postBuyerForecastSaveForecast = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/save/forecast`, data)

// 采方-修改预测信息-tv
export const postBuyerForecastSaveForecastTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/v1/batch/update`, data)

// 供方-反馈
export const postSupForecastFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/feedback`, data)

// 供方-获取供方预测信息列表
export const postSupForecastQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/query`, data)

// 供方-new获取供方预测信息列表
export const postSupKtForecastQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/kt-query`, data)

// 供方-获取供方预测信息
export const getSupForecastQueryId = (id = '') => API.get(`${BASE_TENANT}/sup/forecast/query/${id}`)

// 供方预测信息-导出
export const postSupForecastExport = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/export`, data, {
    responseType: 'blob'
  })

// 供方预测信息-new导出
export const postSupKtForecastExport = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/kt-export`, data, {
    responseType: 'blob'
  })

// 供方-修改预测信息
export const postSupForecastSaveForecast = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/forecast/save/forecast`, data)

// 供方-获取供方预测信息历史
export const postForecastSupDataReceiveQueryHistory = (data = {}) =>
  API.post(`${BASE_TENANT}/forecast/supDataReceive/query/history`, data)

// 供方-new获取采方预测信息列表 -TV
export const postSupTvForecastQuery = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/supplier/v1/query`, data)

// 供方-反馈-tv
export const postSupForecastFeedbackTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/supplier/v1/batch/feedback`, data)

// 供方预测信息-new导出 -tv
export const postSupForecastExportTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/supplier/v1/excel/export`, data, {
    // responseType: 'blob'
  })

// 供方预测信息-new导入模板 -tv
export const postSupForecastImportTempTv = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/supplier/v1/excel/down/exportTemplate`, data, {
    responseType: 'blob'
  })

// 供方预测信息-new导入 -tv
export const postSupForecastImportTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/supplier/v1/excel/import`, data, {
    responseType: 'blob'
  })

// 采方-获取采方预测信息历史
export const postBuyerForecastDataReceiveQueryHistory = (data = {}) =>
  API.post(`${BASE_TENANT}/buyer/forecast/dataReceive/query/history`, data)

// 采方-获取采方预测信息历史 - tv
export const postBuyerForecastDataReceiveQueryHistoryTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/buyer/history/v1/query`, data)

// 采方-查询采方预测信息历史版本 - tv
export const postBuyerForecastDataReceiveQueryHistoryDetailTv = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/buyer/history/v1/query/detail`, data)

// 供方-获取供方预测信息历史 - tv
export const postSupForecastDataReceiveQueryHistoryTv = (data = {}) =>
  API.post(`${BASE_INTERNAL}/forecast/supplier/history/v1/query`, data)

// 供方-查询供方预测信息历史版本 - tv
export const postSupForecastDataReceiveQueryHistoryDetailTv = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/supplier/history/v1/query/detail`, data)

// 采方-获取采方预测信息历史版本下拉列表 - tv
export const postBuyerForecastDataReceiveQueryHistoryVersion = (data = {}) =>
  API.get(`${BASE_INTERNAL}/forecast/metadata/v1/version/all`, data)

// 采方预测配置添加
export const postBuyerForecastConfigureCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/create`, data)

// 采方预测配置删除
export const postBuyerForecastConfigureDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/delete`, data)

// 交货计划配置模板导出
export const buyerForecastConfigureDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/download-temp-new`, data, {
    responseType: 'blob'
  })

// 采方预测配置停用启用
export const postBuyerForecastConfigureStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/status`, data)

// 采方预测配置修改
export const postBuyerForecastConfigureUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/update`, data)

// 采方预测配置Excel导出
export const postBuyerForecastConfigureExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/export`, data, {
    responseType: 'blob'
  })

// 数据管理 - 计划员关系表 - 采方 - 查询
export const getPlannerRelationSchedule = (data = {}) => {
  return API.post(`${BASE_INTERNAL}/planner/v1/relation/fuzzy`, data)
}
// 根据工厂代码+物料编码带出计划员 - 供方 - 泛智屏
export const getPlannerByCode = (data = {}) => {
  return API.post(`${BASE_INTERNAL}/planner/v1/distribution/distinctQuery`, data)
}
// 数据管理 - 计划员关系表 - 采方 - 新增
export const addPlannerRelationSchedule = (data = {}) =>
  API.post(`${BASE_INTERNAL}/planner/v1/relation/batch/save`, data)

// 数据管理 - 计划员关系表 - 采方 - 编辑
export const updatePlannerRelationSchedule = (data = {}) =>
  API.post(`${BASE_INTERNAL}/planner/v1/relation/batch/update`, data)

// 数据管理 - 计划员关系表 - 采方 - 删除
export const deletePlannerRelationSchedule = (data = {}) =>
  API.post(`${BASE_INTERNAL}/planner/v1/relation/batch/delete`, data)

// 数据管理 - 计划员关系表 - 获取用户下拉
export const getPlannerRelationName = (data = {}) =>
  API.post(`${BASE_INTERNAL}/planner/v1/relation/query/all/name`, data)

// 采方-new获取采方预测信息列表 -kt
export const queryBuyerKTForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/buyer/page`, data)

// 采方预测信息- 获取头信息
export const queryBuyerKTForecastHeader = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/get/date/header`, data)
// 采方预测信息-保存 -kt
export const updateForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/buyer/update`, data, {})
// 采方预测信息-新增 -kt
export const addForecast = (data = {}) => API.post(`/contract/tenant/kt/forecast/v1/add`, data, {})
// 采方预测信息-确认 -kt
export const confirmForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/confirm`, data, {})
// 采方预测信息-确认v2 -kt
export const confirmV2Forecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/confirm/v2`, data, {})
// 采方预测信息-确认校验 -kt
export const confirmCheckForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/confirm/check`, data, {})
// 采方预测信息-发布 -kt
export const publishForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/publish`, data, {})
// 采方预测信息-删除 -kt
export const deleteForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/remove`, data, {})
// 采方预测信息-导出 -kt
export const exportForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/buyer/export`, data, {
    responseType: 'blob'
  })
// 采方预测信息-导出(可再导入) -kt
export const exportReImportForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/buyer/export/reImport`, data, {
    responseType: 'blob'
  })
// 采方预测信息-导入 -kt
export const importForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/buyer/import`, data, {
    responseType: 'blob'
  })
// 供方预测信息-查询 -kt
export const querySupplierForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/supplier/page`, data)
// 供方预测信息-修改 -kt
export const updateSupplierForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/supplier/update`, data)
// 供方预测信息-导出 -kt
export const exportSupplierForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/supplier/export`, data, {
    responseType: 'blob'
  })
// 供方预测信息-导出(可再导入) -kt
export const exportReImportSupplierForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/supplier/export/reImport`, data, {
    responseType: 'blob'
  })
// 供方预测信息-导入 -kt
export const importSupplierForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/supplier/import`, data, {
    responseType: 'blob'
  })
// 供方预测信息-反馈 -kt
export const feedBackSupplierForecast = (data = {}) =>
  API.post(`/contract/tenant/kt/forecast/v1/feedback`, data)
