import { API } from '@mtech-common/http'
import utils from '@/utils/utils'

import { BASE_TENANT, BASE_COMMON } from '@/utils/constant'

export const NAME = 'bgConfig'

// 业务类型列表
export const getBusinessConfig = (data = {}) => API.post(`${BASE_TENANT}/pe/business/configs`, data)
// API.post(`/sourcing/tenant/pe/business/configs`, data);

// 业务类型 - 保存
export const saveBusinessConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/pe/business/config/save`, data)

//获取基础模块
export const getBaseModules = (data = {}) => API.get(`${BASE_COMMON}/base/modules`, data)

//获取基础模块
export const queryFieldGroup = (data = {}) => API.get(`${BASE_COMMON}/base/fieldGroup`, data)

//新增业务字段
export const saveItemFileConfig = (data = {}) => API.post(`${BASE_COMMON}/config/files/save`, data)

//删除业务字段
export const deleteBaseField = (data = {}) => API.post(`${BASE_COMMON}/baseField/delete`, data)

//导出业务字段
export const exportBaseField = (data = {}) => API.post(`${BASE_COMMON}/export/baseField/json`, data)

//导入业务字段
export const importBaseField = (data = {}) => API.post(`${BASE_COMMON}/import-baseField/json`, data)

// 业务类型 - 获取详情
export const getBusinessConfigDetail = (data = {}) =>
  API.get(`${BASE_TENANT}/pe/business/config/detail`, data)
/*
    文件配置接口
  */
//根据ID删除
export const deleteFileConfig = (data = {}) =>
  API.put(`${BASE_TENANT}/sourcingFileConfig/deleteById`, data)

//获取文件配置
export const getFileConfig = (data = {}) => {
  const query = utils.toQueryParams(data)
  return API.get(`${BASE_TENANT}/sourcingFileConfig/queryByConfigId?${query}`)
}

//寻源文件配置保存
export const saveFileConfig = (data = {}) => API.put(`${BASE_TENANT}/sourcingFileConfig/save`, data)

// 寻源--供应商--后期删除
//获取基础模块
export const getBaseModulesSourcing = (params) => API.get(`/sourcing/common/base/modules`, params)

//配置详情
export const getBusinessConfigDetailSourcing = (data) =>
  API.get(`/sourcing/tenant/por/business/config/detail`, data)

/*
采购执行策略配置接口
*/
// 查询采购订单策略配置
export const getOrderStrategy = (data) => API.get(`${BASE_TENANT}/orderStrategy/query`, data)

// 获取采购执行方式
export const getPurExecutionMethod = (data) =>
  API.get(`${BASE_TENANT}/orderStrategy/getPurExecutionMethod`, data)

// 保存采购订单策略配置
export const saveOrderStrategy = (data) => API.post(`${BASE_TENANT}/orderStrategy/save`, data)

// 查询并单策略配置
export const getOrderConsolidationConfig = (data) =>
  API.get(`${BASE_TENANT}/orderConsolidationConfig/query`, data)

// 保存并单策略配置
export const saveOrderConsolidationConfig = (data) =>
  API.post(`${BASE_TENANT}/orderConsolidationConfig/save`, data)

// 查询订单预占库存配置
export const getOrderStockOccupyConfig = (data) =>
  API.get(`${BASE_TENANT}/orderStockOccupyConfig/query`, data)

// 保存订单预占库存配置
export const saveOrderStockOccupyConfig = (data) =>
  API.post(`${BASE_TENANT}/orderStockOccupyConfig/save`, data)

// 查询订单售后原因
export const getOrderAfterSalesReason = (data) =>
  API.get(`${BASE_TENANT}/orderAfterSalesReason/query`, data)

// 保存订单售后原因
export const saveOrderAfterSalesReason = (data) =>
  API.post(`${BASE_TENANT}/orderAfterSalesReason/save`, data)

// 查询采购申请配置
export const getOrderApplyStrategy = (data) =>
  API.get(`${BASE_TENANT}/orderApplyStrategy/query`, data)

// 保存采购申请配置
export const saveOrderApplyStrategy = (data) =>
  API.post(`${BASE_TENANT}/orderApplyStrategy/save`, data)

// 查询验收项配置
export const getOrderAcceptance = (data) =>
  API.get(`${BASE_TENANT}/orderAcceptanceStrategy/query`, data)

// 保存验收项配置
export const saveOrderAcceptance = (data) =>
  API.post(`${BASE_TENANT}/orderAcceptanceStrategy/save`, data)

// 查询自动化配置
export const getOrderAutomation = (data) =>
  API.get(`${BASE_TENANT}/orderAutomationStrategy/query`, data)

// 保存自动化配置
export const saveOrderAutomation = (data) =>
  API.post(`${BASE_TENANT}/orderAutomationStrategy/save`, data)

// 查询本地业务类型
export const getLocalBusinessTypeList = (data) =>
  API.post(`${BASE_TENANT}/pe/business/configs/local`, data)

// 查询定时同步配置
export const getOrderScheduleStrategy = (data) =>
  API.post(`${BASE_TENANT}/_order_schedule_strategy/query`, data)

// 新增定时同步配置
export const addOrderScheduleStrategy = (data) =>
  API.post(`${BASE_TENANT}/_order_schedule_strategy/add`, data)

// 编辑定时同步配置
export const editOrderScheduleStrategy = (data) =>
  API.post(`${BASE_TENANT}/_order_schedule_strategy/edit`, data)

// 删除定时同步配置
export const delOrderScheduleStrategy = (data) =>
  API.post(`${BASE_TENANT}/_order_schedule_strategy/delete`, data)

// 获取初始化同步名称
export const getOrderScheduleName = (data) =>
  API.post(`${BASE_TENANT}/_order_schedule_strategy/init`, data)

// 供应商预占库存配置接口 - 下载
export const downloadOrderStockOccupy = (data = {}) =>
  API.get(`${BASE_TENANT}/orderStockOccupyConfig/download`, data, {
    responseType: 'blob'
  })

// 供应商预占库存配置接口 - 上传
export const uploadOrderStockOccupy = (data = {}) =>
  API.post(`${BASE_TENANT}/orderStockOccupyConfig/upload`, data, {
    responseType: 'blob'
  })

// 自动化配置接口 - 下载
export const downloadAutomationStrategy = (data = {}) =>
  API.get(`${BASE_TENANT}/orderAutomationStrategy/download`, data, {
    responseType: 'blob'
  })

// 自动化配置接口 - 上传
export const uploadAutomationStrategy = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAutomationStrategy/upload`, data, {
    responseType: 'blob'
  })

// 订单售后原因
// 1. 编辑
export const editOrderAfterSalesReason = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAfterSalesReason/edit`, data)

//2. 删除
export const deleteOrderAfterSalesReason = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAfterSalesReason/delete`, data)

// 验收项配置
// 1. 编辑
export const editOrderAcceptance = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAcceptanceStrategy/edit`, data)

//2. 删除
export const deleteOrderAcceptance = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAcceptanceStrategy/delete`, data)

// 订单自动化
// 1. 编辑
export const editOrderAutomation = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAutomationStrategy/edit`, data)

//2. 删除
export const deleteOrderAutomation = (data = {}) =>
  API.post(`${BASE_TENANT}/orderAutomationStrategy/delete`, data)

// 物流明细字段 - 新增更新
export const logisticsFieldUpdate = (data = {}) =>
  API.post(`/contract/tenant/request/config/field/addOrUpdate`, data)
// 物流明细字段 - 删除
export const logisticsFieldDelete = (data = {}) =>
  API.post(`/contract/tenant/request/config/field/delete`, data)
// 物流明细字段 - 更新状态
export const logisticsFieldSateUpdate = (data = {}) =>
  API.post(`/contract/tenant/request/config/field/updateStatus`, data)

// 模板配置 - 新增更新
export const logisticsTempalteUpdate = (data = {}) =>
  API.post(`/contract/tenant/request/config/template/addOrUpdate`, data)
// 模板配置 - 删除
export const logisticsTempalteDelete = (data = {}) =>
  API.post(`/contract/tenant/request/config/template/delete`, data)
// 模板配置 - 复制
export const logisticsTempalteCopy = (data = {}) =>
  API.get(`/contract/tenant/request/config/copy/template`, data)
// 模板配置 - 更新
export const logisticsTempalteSateUpdate = (data = {}) =>
  API.post(`/contract/tenant/request/config/template/updateStatus`, data)

// 模板配置 - 查询模板关联的字段列表（勾选）
export const getLogisticsTempalteList = (data = {}) =>
  API.get(`/contract/tenant/request/config/rel/list`, data)

// 模板配置 - 获取列表信息
export const getLogisticsTemplateConfig = (data = {}) =>
  API.post(`/contract/tenant/request/config/template/list`, data)

// 模板配置 - 提交明细信息
export const setLogisticsTempalte = (data = {}) =>
  API.post(`/contract/tenant/request/config/template/rel`, data)
