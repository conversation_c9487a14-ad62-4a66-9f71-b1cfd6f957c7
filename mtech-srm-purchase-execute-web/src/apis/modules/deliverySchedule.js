// 要货计划
import { API } from '@mtech-common/http'
import { BASE_TENANT } from '@/utils/constant'
import { BASE_MESSAGE, PROXY_MDM_TENANT } from '@/utils/constant'

export const NAME = 'deliverySchedule'
// ************ 要货排期列表begin *******************
// 要货日程视图查询 采方
export const buyerGoodsDemandPlanInfoQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/query`, data)
// 要货日程视图查询 采方 - TV
export const buyerGoodsDemandPlanInfoQueryTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/query`, data)
// 要货日程视图查询获取版本下拉列表 采方 - TV
export const buyerGoodsDemandPlanInfoQueryVersionTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/query/version`, data)
// 要货日程视图查询获取版本下拉列表 供方 - TV
export const supplierGoodsDemandPlanInfoQueryVersionTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/query/version`, data)
// tv交货计划计划员下拉列表 供方 - TV
export const supplierGoodsDemandPlanInfoQueryPlannerTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/query/planner`, data)
// 保存要货计划
export const buyerGoodsDemandPlanInfoUpdateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/update`, data)
// 保存要货计划-供方
export const supplierGoodsDemandPlanInfoUpdateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/update`, data)
// 要货日程视图查询 供方
export const supplierGoodsDemandPlanInfoQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierGoodsDemandPlanInfo/query`, data)
// 要货日程视图查询 供方 - TV
export const supplierGoodsDemandPlanInfoQueryTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/query`, data)
// 保存要货计划
export const buyerGoodsDemandPlanInfoSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/save`, data)
// 保存要货计划 多行
export const buyerGoodsDemandPlanInfoSaveBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/copy`, data)
// 保存jit计划 多行
export const buyerJitInfoSaveBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/save-batch`, data)
// 删除要货计划
export const buyerGoodsDemandPlanInfoDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/delete`, data)
// 删除tv要货计划
export const buyerGoodsDemandPlanInfoDeleteTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/delete`, data)
// 修改要货计划
export const buyerGoodsDemandPlanInfoUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/update`, data)
// 批量修改要货计划
export const buyerGoodsDemandPlanInfoUpdateList = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/updateList`, data)
//发布要货计划
export const buyerGoodsDemandPlanInfoPublish = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/publish`, data)
// 采方取消发布Tv要货计划
export const buyerGoodsDemandPlanInfoPublishTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/publish`, data)
// 采方-发布校验
export const validPublishTvApi = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/valid/customs`, data)
// 采方-下载无报关要素清单
export const exportCustomsTvApi = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/customs/export`, data, {
    responseType: 'blob'
  })
// 采方取消发布要货计划
export const buyerGoodsDemandPlanInfoCancel = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/cancel`, data)
// 采方取消发布要货计划-Tv
export const buyerGoodsDemandPlanInfoCancelTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/cancel/publish`, data)
// 采方关闭要货计划
export const buyerGoodsDemandPlanInfoClose = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/close`, data)
// 采方关闭Tv要货计划
export const buyerGoodsDemandPlanInfoCloseTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/close`, data)
// 采方确认要货计划
export const buyerGoodsDemandPlanInfoConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/confirm`, data)
// 采方确认要货计划 - tv
export const buyerGoodsDemandPlanInfoConfirmTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/confirm`, data)
// 供方确认要货计划
export const supplierGoodsDemandPlanInfoConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/confirm`, data)
//采方下达要货计划
export const buyerGoodsDemandPlanInfoReleased = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/released`, data)
// 采方要货计划Excel导出
export const buyerGoodsDemandPlanInfoExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/export`, data, {
    responseType: 'blob'
  })
// 采方要货计划Excel导出 - Tv
export const buyerGoodsDemandPlanInfoExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/export`, data, {
    responseType: 'blob'
  })
// 采方要货计划Excel动态导出 - Tv
export const buyerGoodsDemandPlanInfoExportDynamicTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/exportDynamic`, data, {
    responseType: 'blob'
  })
// 采方要货计划Excel导出 - Tv
export const buyerGoodsDemandPlanInfoExportTempTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/export/template`, data, {
    responseType: 'blob'
  })

// 采方要货计划Excel导入 - Tv
export const buyerGoodsDemandPlanInfoImportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/buyer/tv/import`, data, {
    responseType: 'blob'
  })

// 供方要货计划Excel导入 - Tv
export const supplierGoodsDemandPlanInfoImportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/import`, data, {
    responseType: 'blob'
  })

// 供方要货计划Excel导出 - Tv
export const supplierGoodsDemandPlanInfoExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/export`, data, {
    responseType: 'blob'
  })

// 供方要货计划Excel导出 - Tv
export const supplierGoodsDemandPlanInfoExportDynamicTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/exportDynamic`, data, {
    responseType: 'blob'
  })

// 供方要货计划反馈 - Tv
export const supplierGoodsDemandPlanInfoFeedbackTv = (data = {}) =>
  API.post(`${BASE_TENANT}/demand/plan/supplier/tv/feedback`, data)

// 采方要货计划 JIT Excel 导出
export const buyerGoodsDemandPlanInfoExportJIT = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/exportJIT`, data, {
    responseType: 'blob'
  })

// 采方要货计划 JIT Excel 导出
export const buyerGoodsDemandPlanInfoTemplateExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/exports`, data, {
    responseType: 'blob'
  })

// 采方要货计划 excel 导入
export const buyerGoodsDemandPlanInfoImportBD = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/importBD`, data, {
    responseType: 'blob'
  })

// 采方要货计划 excel 导入模板下载
export const buyerGoodsDemandPlanInfoImportBDTemplate = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/getImportTemplateBD`, data, {
    responseType: 'blob'
  })
// wms送货地址配置-导入模板下载
export const getBdImportTemperateApi = (data = {}) =>
  API.get(`${BASE_TENANT}/siteTenantExtend/getBdImportTemperate`, data, {
    responseType: 'blob'
  })
// wms送货地址配置-导入
export const bdWmsImportApi = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/bdImport`, data, {
    responseType: 'blob'
  })
// wms送货地址配置-导出
export const bdWmsExportBDApi = (data = {}) =>
  API.post(`/statistics/tenant/siteTenantExtend/exportBD`, data, {
    responseType: 'blob'
  })

// 送货地址配置导出
export const siteTenantExtendexport = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/export`, data, {
    responseType: 'blob'
  })
// 送货地址配置导出 new 钢材
export const pickupOrderAddressExtendexport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/export`, data, {
    responseType: 'blob'
  })

// 送货地址配置批量保存
export const siteTenantExtendSaveBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/saveBatch`, data)

// 送货地址配置批量保存  钢材 new
export const pickupOrderAddressExtendSaveBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/saveBatch`, data)

// 送货地址配置导入
export const siteTenantExtendImport = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/import`, data, {
    responseType: 'blob'
  })
// 采方要货计划Excel导入
export const buyerGoodsDemandPlanInfoImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/import`, data)

// 供方反馈要货计划
export const supplierGoodsDemandPlanInfoFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierGoodsDemandPlanInfo/feedback`, data)
// 供方要货计划Excel导出
export const supplierGoodsDemandPlanInfoExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/supplierGoodsDemandPlanInfo/export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

export const getAsnCount = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/asn-count`, data)
// ************ 要货排期列表end *******************

// ************ 要货发布配置begin *******************
// 保存
export const buyerReleaseFrequencyConfiguresave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReleaseFrequencyConfigure/save`, data)
// 修改
export const buyerReleaseFrequencyConfigureupdate = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReleaseFrequencyConfigure/update`, data)
// 删除
export const buyerReleaseFrequencyConfiguredelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReleaseFrequencyConfigure/delete`, data)
// 启用
export const buyerReleaseFrequencyConfigureenable = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReleaseFrequencyConfigure/enable`, data)
//禁用
export const buyerReleaseFrequencyConfiguredisable = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReleaseFrequencyConfigure/disable`, data)
// ************ 要货发布配置end *******************

// ************ 要货计划校验begin *******************
// 保存
export const buyerVerifyConfiguresave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerVerifyConfigure/save`, data)
// 修改
export const buyerVerifyConfigureupdate = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerVerifyConfigure/update`, data)
// 删除
export const buyerVerifyConfiguredelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerVerifyConfigure/delete`, data)
// 启用
export const buyerVerifyConfigureenable = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerVerifyConfigure/enable`, data)
//禁用
export const buyerVerifyConfiguredisable = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerVerifyConfigure/disable`, data)
// ************ 要货计划校验end *******************

// ************ 要货计划送货地址begin *******************
// 保存
export const siteTenantExtendsave = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/save`, data)
// 保存 new 钢材
export const pickupOrderAddressExtendsave = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/save`, data)
// 修改
export const siteTenantExtendupdate = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/update`, data)

// 重试
export const buyerGoodsDemandPlanInfoRetry = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/push-sap/retry`, data)

// 钢材页面 new 修改
export const pickupOrderAddressExtendupdate = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/update`, data)

// 删除
export const siteTenantExtenddelete = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/delete`, data)
// 删除 new 钢材
export const pickupOrderAddressExtenddelete = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/delete`, data)

// 启用
export const siteTenantExtendenable = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/enable`, data)

// 同步
export const syncWmsApi = (data = {}) => API.post(`${BASE_TENANT}/siteTenantExtend/syncWms`, data)

export const pickupOrderAddressExtendenable = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/enable`, data)
// 禁用
export const siteTenantExtenddisable = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/disable`, data)
// 禁用 钢材 new
export const pickupOrderAddressExtenddisable = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/disable`, data)
//根据工厂库存地点或加工商查询送货地址
export const siteTenantExtendqueryBySite = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryBySite`, data)
//  new
export const siteNewTenantExtendqueryBySite = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/subSite/queryBySite`, data)

// 查询送货地址
export const getdeliveryAddressInfo = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/query`, data)
// ************ 要货计划送货地址end *******************

// ************ 工作中心和加工商配置begin *******************
// 工作中心和加工商配置查询
export const workCenterSupplierRelquery = (data = {}) =>
  API.post(`${BASE_TENANT}/workCenterSupplierRel/query`, data)
// 保存
export const workCenterSupplierRelsave = (data = {}) =>
  API.post(`${BASE_TENANT}/workCenterSupplierRel/save`, data)
// 修改
export const workCenterSupplierRelupdate = (data = {}) =>
  API.post(`${BASE_TENANT}/workCenterSupplierRel/update`, data)
// 删除
export const workCenterSupplierReldelete = (data = {}) =>
  API.post(`${BASE_TENANT}/workCenterSupplierRel/delete`, data)
// ************ 工作中心和加工商配置end *******************
// 获取当前租户所有版本
export const buyerGoodsDemandPlanInfogetVersions = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/getVersions`, data)
// 获取当前租户所有版本
export const supplierGoodsDemandPlanInfogetVersions = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierGoodsDemandPlanInfo/getVersions`, data)

// ************ 消息提醒配置begin *******************
// 消息提醒配置删除
export const noticeConfigDel = (data = {}) => API.post(`${BASE_MESSAGE}/notice/config/delete`, data)
//消息提醒配置导出
export const noticeConfigExport = (data = {}) =>
  API.post(`${BASE_MESSAGE}/notice/config/download`, data, {
    responseType: 'blob'
  })
// 消息提醒配置导入
export const noticeConfigImport = (data = {}) =>
  API.post(`${BASE_MESSAGE}/notice/config/import`, data, {
    responseType: 'blob'
  })
// 消息提醒配置保存
export const noticeConfigSave = (data = {}) => API.post(`${BASE_MESSAGE}/notice/config/save`, data)
// 消息提醒配置批量保存
export const noticeConfigSaveBatch = (data = {}) =>
  API.post(`${BASE_MESSAGE}/notice/config/saveBatch`, data)
// 消息提醒配置状态修改
export const noticeConfigStatus = (data = {}) =>
  API.post(`${BASE_MESSAGE}/notice/config/status`, data)
// 获取消息提醒场景
export const templateGrouplist = (data = {}) => API.get(`message/tenant/templateGroup/list`, data)
// 获取消息提醒场景
export const templateQuery = (data = {}) => API.post(`message/tenant/template/queryBuilder`, data)
// ************ 消息提醒配置begin *******************

/* JIT 计划 { */
// 采方jit-批量修改日期
export const batchUpdateBuyerJitInfoApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/batchUpdateDeliveryDate`, data)
// 采方jit-获取采方jit
export const buyerJitInfoQuery = (data = {}) => API.post(`${BASE_TENANT}/buyerJitInfo/query`, data)
// 采方jit-保存jit
export const buyerJitInfoSave = (data = {}) => API.post(`${BASE_TENANT}/buyerJitInfo/save`, data)
// 采方jit-保存jit - tv
export const buyerJitInfoSaveTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/add`, data)
// 采方jit-更新保存jit - tv
export const buyerJitInfoUpdateSaveTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/update`, data)
// 采方jit-采方发布jit - tv
export const buyerJitInfoPublishTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/publish`, data)
// 采方jit-采方确认jit - tv
export const buyerJitInfoConfirmTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/confirm`, data)
// 采方jit-采方取消发布jit - tv
export const buyerJitInfoCancelPublishTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/cancel/publish`, data)
// 采方jit-采方删除jit - tv
export const buyerJitInfoDeleteTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/delete`, data)
// 采方jit-采方关闭jit - tv
export const buyerJitInfoCloseTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/close`, data)
// 采方jit-采方强制关闭jit - tv
export const buyerJitInfoCompelCloseTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/compel/close`, data)
// 采方jit-采方 查询调度员下拉 - tv
export const queryDispatcher = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/dispatcher/query`, data)
// 采方jit-采方jitExcel导出 -tv
export const buyerJitInfoExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/export`, data, {
    responseType: 'blob'
  })
// 采方jit-采方jitExcel导入 -tv
export const buyerJitInfoImportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/import`, data, {
    responseType: 'blob'
  })
// 采方jit-采方jitExcel导入模板 -tv
export const buyerJitInfoImportTempTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/buyer/tv/download`, data, {
    responseType: 'blob'
  })
// 采方jit-获取库存地点 - tv
export const queryBySiteAndAddress = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryBySiteAndAddress`, data)
// 采方jit-供应商获取库存地点 - tv
export const queryScreenAddr = (data = {}) =>
  API.post(`${BASE_TENANT}/screen/warehouse/addr/query`, data)

// 采方jit-采方jitExcel导出
export const buyerJitInfoExport = (data = {}, field) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/export?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })
// 采方jit-采方jitExcel导出
export const buyerJitInfoExportNewKt = (data = {}, field) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/export/kt?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })
// 采方jit-采方jitExcel导出 kt
export const buyerJitInfoExportKt = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/exportKT`, data, {
    responseType: 'blob'
  })
// 采方jit-采方jitExcel导入
export const buyerJitInfoImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/import`, data, {
    responseType: 'blob'
  })
// 采方JIT - excel导入
export const buyerJitInfoImportBD = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/importExcelBD`, data, {
    responseType: 'blob'
  })
// 采方JIT - excel 模板下载
export const buyerJitInfoImportBDTemplate = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerJitInfo/getImportTemplateBD`, data, {
    responseType: 'blob'
  })
// 采方JIT - 导出失败的数据
export const exportBuyerJitInfoErrorKtApi = (data = {}, traceId) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/getImportDataExcelKTRedis?traceId=${traceId}`, data, {
    responseType: 'blob'
  })

// 采方jit-采方jitExcel导入 kt
export const buyerJitInfoImportKt = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/importKT`, data, {
    responseType: 'blob'
  })
// 采方jit-jit转交
export const buyerJitInfoChange = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/change/handler`, data)
// 采方jit-采方关闭jit
export const buyerJitInfoClose = (data = {}) => API.post(`${BASE_TENANT}/buyerJitInfo/close`, data)
// 采方jit-采方删除jit
export const buyerJitInfoDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/delete`, data)
// 采方jit-采方发布jit
export const buyerJitInfoPublish = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/publish`, data)
// 采方jit-采方取消发布jit
export const buyerJitInfoCancelPublish = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/withdraw/publish`, data)
// 采方jit-匹配供应商
export const buyerJitInfoMatchSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/match/supplier`, data)

// 供方jit-反馈
export const supplierJitInfoFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierJitInfo/feedback`, data)

// 供方jit-反馈 -TV
export const supplierJitInfoFeedbackTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/supplier/tv/feedback`, data)
// 供方jit-获取供方jit
export const supplierJitInfoQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierJitInfo/query`, data)
// 供方jit-修改备注
export const supplierJitInfoRemark = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierJitInfo/remark`, data)
// 供方jit-修改备注 -TV
export const supplierJitInfoUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/supplier/tv/update`, data)
// 供方jit-Excel导出
export const supplierJitInfoExport = (data = {}, field) =>
  API.post(`${BASE_TENANT}/supplierJitInfo/export?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })
// 供方jit-供方jitExcel导出 -tv
export const supplierJitInfoExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/plan/supplier/tv/export`, data, {
    responseType: 'blob'
  })
/* JIT 计划 } */

export const supplierJitItem = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-org-jit/paged-query`, data)

/* eslint-disable prettier/prettier */
/************* 净计划   *************/
// 获取工厂信息
export const getFactoryInfo = (data = {}) =>
  API.post(
    `/masterDataManagement/auth/site/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 净需求查询 - 净需求明细 - 导出
export const netRequirementExport = (data = {}) =>
  API.post(`${BASE_TENANT}/bdNetDemand/excelExport`, data, {
    responseType: 'blob'
  })

// 净需求查询 - 净需求明细 - 重置状态
export const netRequirementReset = (data = {}) => API.post(`${BASE_TENANT}/bdNetDemand/reset`, data)

// 品类下拉框
export const fuzzyQuery = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/category/fuzzy-query`, data)
}

// 交货计划时间限制参数设置 - 新增/编辑
export const addAndEditScheduleTimeConfigInfo = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/deliveryDateLimitationConfig/save`, data)
}
// 交货计划时间限制参数设置 - 删除
export const deleteScheduleTimeConfig = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/deliveryDateLimitationConfig/delete`, data)
}
// 交货计划时间限制参数设置 - 导入
export const importScheduleTimeConfig = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/deliveryDateLimitationConfig/importExcel`, data, {
    responseType: 'blob'
  })
}
// 交货计划时间限制参数设置 - 导入模板下载
export const importScheduleTimeConfigDown = (data = {}) => {
  return API.get(`/srm-purchase-execute/tenant/deliveryDateLimitationConfig/excel`, data, {
    responseType: 'blob'
  })
}

// 交货计划时间限制参数设置 - 导出
export const exportScheduleTimeConfig = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/deliveryDateLimitationConfig/excelExport`, data, {
    responseType: 'blob'
  })
}

// 采方jit-获取库存地点 - tv - 屏发货
export const siteAndAddressForScreen = (data = {}) =>
  API.post(`${BASE_TENANT}/siteTenantExtend/queryBySiteAndAddressForScreen`, data)

/** jit自动叫料 */
// jit自动叫料 - 分页查询
export const jitAllocationQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/page`, data)
// jit自动叫料 - 批量删除
export const jitAllocationBatchDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/batch/delete`, data)
// jit自动叫料 - 批量撤回
export const jitAllocationBatchRevoke = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/batch/revoke`, data)
// jit自动叫料 - 批量保存
export const jitAllocationBatchSave = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/batch/save`, data)
// jit自动叫料 - 批量提交
export const jitAllocationBatchSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/batch/submit`, data)
// jit自动叫料 - 复制行
export const jitAllocationBatchCopy = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/copy`, data)
// jit自动叫料 - 查询版本号
export const jitVersionListQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/auto/allocation/get/versionNo`, data)
// jit自动叫料 - 导入
export const importJitAllocation = (data = {}) => {
  return API.post(`${BASE_TENANT}/jit/auto/allocation/import`, data, {
    responseType: 'blob'
  })
}
// jit自动叫料 - 导入模板下载
export const importJitAllocationTemp = (data = {}) => {
  return API.post(`${BASE_TENANT}/jit/auto/allocation/exportTemplate`, data, {
    responseType: 'blob'
  })
}
// jit自动叫料 - 导出
export const exportJitAllocation = (data = {}) => {
  return API.post(`${BASE_TENANT}/jit/auto/allocation/export`, data, {
    responseType: 'blob'
  })
}

/** 批次来料物料属性 */
// 批次来料物料属性 - 新增
export const addBatchMaterialConfigApi = (data = {}) => {
  return API.post(`${PROXY_MDM_TENANT}/item-org-batch/add`, data)
}
// 批次来料物料属性 - 更新
export const editBatchMaterialConfigApi = (data = {}) => {
  return API.put(`${PROXY_MDM_TENANT}/item-org-batch/update`, data)
}
// 批次来料物料属性 - 删除
export const deleteBatchMaterialConfigApi = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-org-batch/batch-delete`, data)
// 批次来料物料属性 - 生效or失效
export const updateStatusBatchMaterialConfigApi = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-org-batch/batch-update-status`, data)
// 批次来料物料属性 - 导入模板下载
export const downloadTempBatchMaterialConfigApi = (data = {}) => {
  return API.get(`${PROXY_MDM_TENANT}/deliveryDateLimitationConfig/excel`, data, {
    responseType: 'blob'
  })
}
// 批次来料物料属性 - 导入
export const importBatchMaterialConfigApi = (data = {}) => {
  return API.post(`${PROXY_MDM_TENANT}/item-org-batch/import-data`, data, {
    responseType: 'blob'
  })
}
// 批次来料物料属性 - 导出
export const exportBatchMaterialConfigApi = (data = {}) => {
  return API.post(`${PROXY_MDM_TENANT}/item-org-batch/export-data`, data, {
    responseType: 'blob'
  })
}

// 采方 - 集散中心交货计划 - 分页查询
export const pageDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/query`, data)
// 采方 - 集散中心交货计划 - 推送Tms
export const pushTmsDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/send`, data)
// 采方 - 集散中心交货计划 - 导出
export const exportDistributionDemanPlandApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/distribution/demand/plan/export`, data, {
    responseType: 'blob'
  })
}
// 采方 - 集散中心交货计划 - 已下发Tms分页查询
export const pageNoDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/no/query`, data)
// 采方 - 集散中心交货计划 - 历史分页查询
export const pageHistoryDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/history/query`, data)
// 采方 - 集散中心交货计划 - 导出
export const exportHistoryDistributionDemanPlandApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/distribution/demand/plan/history/export`, data, {
    responseType: 'blob'
  })
}
// 采方 - 集散中心交货计划 - 获取需求日期
export const getDemandDateDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/query/demand/date`, data)
// 采方 - 集散中心交货计划 - 获取版本号
export const getVersionDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/query/version`, data)
// 采方 - 集散中心交货计划 - 删除
export const deleteDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/delete`, data)
// 采方 - 集散中心交货计划 - 回滚
export const rollbackDistributionDemanPlandApi = (data = {}) =>
  API.post(`${BASE_TENANT}/distribution/demand/plan/rollback`, data)

// 采方 - JIT物料配置 - 分页查询
export const pageJitMaterialConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/material/config/query`, data)
// 采方 - JIT物料配置 - 新增
export const addJitMaterialConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/material/config/add`, data)
// 采方 - JIT物料配置 - 修改
export const updateJitMaterialConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/material/config/update`, data)
// 采方 - JIT物料配置 - 删除
export const deleteJitMaterialConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/jit/material/config/delete`, data)
// 采方 - JIT物料配置 - 导入模板下载
export const downloadJitMaterialConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/jit/material/config/import/template`, data, {
    responseType: 'blob'
  })
}
// 采方 - JIT物料配置 - 导入
export const importJitMaterialConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/jit/material/config/import`, data, {
    responseType: 'blob'
  })
}
// 采方 - JIT物料配置 - 导出
export const exportJitMaterialConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/jit/material/config/export`, data, {
    responseType: 'blob'
  })
}

// 采方 - 调度员分工 - 分页查询
export const pageDispatcherDivideApi = (data = {}) =>
  API.post(`${BASE_TENANT}/dispatcher/division/query`, data)
// 采方 - 调度员分工 - 新增
export const addDispatcherDivideApi = (data = {}) =>
  API.post(`${BASE_TENANT}/dispatcher/division/add`, data)
// 采方 - 调度员分工 - 修改
export const updateDispatcherDivideApi = (data = {}) =>
  API.post(`${BASE_TENANT}/dispatcher/division/update`, data)
// 采方 - 调度员分工 - 删除
export const deleteDispatcherDivideApi = (data = {}) =>
  API.post(`${BASE_TENANT}/dispatcher/division/delete`, data)
// 采方 - 调度员分工 - 导入模板下载
export const downloadDispatcherDivideApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/dispatcher/division/import/template`, data, {
    responseType: 'blob'
  })
}
// 采方 - 调度员分工 - 导入
export const importDispatcherDivideApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/dispatcher/division/import`, data, {
    responseType: 'blob'
  })
}
// 采方 - 调度员分工 - 导出
export const exportDispatcherDivideApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/dispatcher/division/export`, data, {
    responseType: 'blob'
  })
}

// 采方 - 物料送货类型配置 - 分页查询
export const pageMaterialDeliveryTypeConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/material/delivery/type/configuration/query`, data)
// 采方 - 物料送货类型配置 - 新增
export const addMaterialDeliveryTypeConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/material/delivery/type/configuration/add`, data)
// 采方 - 物料送货类型配置 - 修改
export const updateMaterialDeliveryTypeConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/material/delivery/type/configuration/update`, data)
// 采方 - 物料送货类型配置 - 删除
export const deleteMaterialDeliveryTypeConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/material/delivery/type/configuration/delete`, data)
// 采方 - 物料送货类型配置 - 导入模板下载
export const downloadMaterialDeliveryTypeConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/material/delivery/type/configuration/import/template`, data, {
    responseType: 'blob'
  })
}
// 采方 - 物料送货类型配置 - 导入
export const importMaterialDeliveryTypeConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/material/delivery/type/configuration/import`, data, {
    responseType: 'blob'
  })
}
// 采方 - 物料送货类型配置 - 导出
export const exportMaterialDeliveryTypeConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/material/delivery/type/configuration/export`, data, {
    responseType: 'blob'
  })
}
// 采方 - 物料送货类型配置 - 同步SAP
export const syncSapMaterialDeliveryTypeConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/material/delivery/type/configuration/sync/sap`, data)
// 采方 - 物料送货类型配置 - 物料查询 - 导出
export const exportItemMaterialDeliveryTypeConfigApi = (data = {}) => {
  return API.post(`masterDataManagement/common/item/org/rel/export`, data, {
    responseType: 'blob'
  })
}

// 采方-交货计划-空调 导入
export const importPrintMaterialApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/importPrintMaterial`, data, {
    responseType: 'blob'
  })

// 采方-交货计划-空调 导入模板下载
export const getImportPrintMaterialTemplateApi = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/getImportPrintMaterialTemplate`, data, {
    responseType: 'blob'
  })

// 采方-停用自动补交货计划配置-空调-分页查询
export const pageDeliveryPlanConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/deliveryDisableAutoCreatePlan/queryPage`, data)
// 采方-停用自动补交货计划配置-空调-保存
export const saveDeliveryPlanConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/deliveryDisableAutoCreatePlan/save`, data)
// 采方-停用自动补交货计划配置-空调-删除
export const deleteDeliveryPlanConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/deliveryDisableAutoCreatePlan/delete`, data)
// 采方-停用自动补交货计划配置-空调-导入模板下载
export const downloadDeliveryPlanConfigApi = (data = {}) => {
  return API.get(`${BASE_TENANT}/deliveryDisableAutoCreatePlan/getImportTemplate`, data, {
    responseType: 'blob'
  })
}
// 采方-停用自动补交货计划配置-空调-导入
export const importDeliveryPlanConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/deliveryDisableAutoCreatePlan/import`, data, {
    responseType: 'blob'
  })
}
// 采方-停用自动补交货计划配置-空调-导出
export const exportDeliveryPlanConfigApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/deliveryDisableAutoCreatePlan/export`, data, {
    responseType: 'blob'
  })
}

// 采方 - PSI范围设置 - 分页查询
export const pagePsiConfigApi = (data = {}) => API.post(`/contract/tenant/psi/config/page`, data)
// 采方 - PSI范围设置 - 保存
export const savePsiConfigApi = (data = {}) => API.post(`/contract/tenant/psi/config/add`, data)
// 采方 - PSI范围设置 - 删除
export const deletePsiConfigApi = (data = {}) =>
  API.post(`/contract/tenant/psi/config/batchDelete`, data)
// 采方 - PSI范围设置 - 生效/失效 0-有效，1-无效
export const statusPsiConfigApi = (data = {}) =>
  API.post(`/contract/tenant/psi/config/status`, data)
// 采方 - PSI范围设置 - 导入模板下载
export const downloadPsiConfigApi = (data = {}) => {
  return API.get(`/contract/tenant/psi/config/importTemplate`, data, {
    responseType: 'blob'
  })
}
// 采方 - PSI范围设置 - 导入
export const importPsiConfigApi = (data = {}) => {
  return API.post(`/contract/tenant/psi/config/excel/import`, data, {
    responseType: 'blob'
  })
}
// 采方 - PSI范围设置 - 导出
export const exportPsiConfigApi = (data = {}) => {
  return API.post(`/contract/tenant/psi/config/export`, data, {
    responseType: 'blob'
  })
}

// 采方 - PSI交货计划/JIT - 分页查询
export const pagePsiDeliveryScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/psi/buyer/page`, data)

// 采方 - PSI交货计划/JIT - 导出
export const exportPsiDeliveryScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/psi/buyer/export`, data, {
    responseType: 'blob'
  })
// 采方 - PSI交货计划/JIT - 差异导出
export const exportDetailPsiDeliveryScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/psi/buyer/diff/export`, data, {
    responseType: 'blob'
  })

// 采方 - PSI交货计划 - 重新计算
export const calculatePsiDeliveryScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/psi/plan/calculate`, data)

// 采方 - PSI-JIT- 重新计算
export const calculatePsiJitApi = (data = {}) =>
  API.post(`/contract/tenant/psi/jit/calculate`, data)

// 供方 - PSI交货计划/JIT - 分页查询
export const pagePsiDeliveryScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/psi/supplier/page`, data)

// 供方 - PSI交货计划/JIT - 导出
export const exportPsiDeliveryScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/psi/supplier/export`, data, {
    responseType: 'blob'
  })
// 供方 - PSI交货计划/JIT - 差异导出
export const exportDetailPsiDeliveryScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/psi/supplier/diff/export`, data, {
    responseType: 'blob'
  })

// 采供 - PSI - 表头查询
export const headerPsApi = (data) => {
  return API.get(`/contract/tenant/psi/header`, data)
}

// 采方 - 叫料计划中山空调 - 分页查询
export const pageKtMaterialControlApi = (data = {}, original) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/ktMaterialControl/query?original=${original}`, data)
// 采方 - 叫料计划中山空调 - 删除
export const deleteKtMaterialControlApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/ktMaterialControl/delete`, data)
// 采方 - 叫料计划中山空调 - 批量修改交货日期
export const batchUpdateKtMaterialControlApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/ktMaterialControl/batchUpdateDeliveryDate`, data)
// 采方 - 叫料计划中山空调 - 导出
export const exportKtMaterialControlApi = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerJitInfo/ktMaterialControl/export/kt`, data, {
    responseType: 'blob'
  })

// 采方-供方数据对接监控表
export const pageSupplierDataMonitoringApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/syncLog/query`, data)

// 采方 - 供方数据对接监控表 - 导出
export const exportSupplierDataMonitoringApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/syncLog/buyer/export`, data, {
    responseType: 'blob'
  })
