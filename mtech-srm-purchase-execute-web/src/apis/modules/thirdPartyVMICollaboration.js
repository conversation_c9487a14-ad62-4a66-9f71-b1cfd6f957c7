// vmi-第三方接口
import { API } from '@mtech-common/http'
import { BASE_TENANT } from '@/utils/constant'
import utils from '@/utils/utils'

export const NAME = 'thirdPartyVMICollaboration'
// 入库管理
// 确认收货
export const postBuyerWarehousingConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-receive-order/confirm`, data)

// 库存管理
// 获取工厂 供应商  vmi仓
export const postthirdPartyFactorySelectList = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/listByQueryBuilderForLogistic`, data)
// 创建调拨单接口
export const postthirdPartyFactoryCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiAllocationOrder/create`, data)
// 提交-替换单  三方共用一个接口
export const postthirdPartyReplaceSubmit = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiReplaceOrder/submit`, data)
// 提交-调拨单  三方共用一个接口
export const postthirdPartyAllocationSubmit = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiAllocationOrder/submit`, data)
// 取消-替换单  三方共用一个接口
export const postthirdPartyReplaceCancel = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiReplaceOrder/cancel`, data)
// 取消-调拨单  三方共用一个接口
export const postthirdPartyAllocationCancel = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiAllocationOrder/cancel`, data)
// 删除-调拨单 采方供方没有用到
export const postthirdPartyAllocationDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiAllocationOrder/delete`, data)
// 删除-替换单 采方供方没有用到
export const postthirdPartyReplaceDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiReplaceOrder/delete`, data)
// 创建替换单
export const postthirdPartyReplaceCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReplaceOrder/create`, data)

//   // 详情接口
// export const postthirdPartyDetails= (data = {}) =>
//   API.post(`${BASE_TENANT}/vmi-order/detail`, data);

// 退货头视图列表
export const postVMIReturnLogisticQuery = `${BASE_TENANT}/vmi-order/logistic-page-query`
// 退货明细视图列表
export const postVMIReturnLogisticQueryDetail = `${BASE_TENANT}/vmi-order/logistic-page-query-detail`

// 退货管理  提交
export const postthirdReturnSubmit = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiReturnedOrder/submit`, data)

// 退货管理  取消
export const postthirdReturnCancel = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiReturnedOrder/cancel`, data)

// 退货管理  删除
export const postthirdReturnDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiReturnedOrder/delete`, data)

// 退货管理  导出
export const postthirdReturnExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiReturnedOrder/logistic-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 创建退货单
export const postthirdPartyLogistic = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/create`, data)
// 第三方退货 vmi库查询
export const postthirdPartyLogisticQuer = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_stock/logistic-page-query`, data)
// 退货-根据vmi仓查出地址   logistic/findByCode    findByCodeAndTenantId
export const postWarehouseAddressTenantId = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiWarehouse/logistic/findByCode`, data)
// 第三方退货 关联订单号
export const postthirdQueryThirdPartyLogisticsPO = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-order/queryThirdPartyLogisticsPO`, data)
//  创建发货单
export const createAsn = (data = {}) => API.put(`${BASE_TENANT}/vmi-pickup-order/create-asn`, data)
//  创建发货单 钢材
export const createNewAsn = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/create-asn`, data)

//  明细列表数据
export const logisticItemPageList = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-pickup-order/logistic-item-page-query`, data)

// 供方收发货供货计划-vmi订单供货计划列表Excel导出
export const postVmiOrderExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/export/query`, data, {
    responseType: 'blob'
  })

// 钣金调料申请-导出
export const postVmiSteelPickupExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/processor-export`, data, {
    responseType: 'blob'
  })

// 钣金调料申请-供方-导出
export const postVmiSteelPickupSupplierExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/supplier-export`, data, {
    responseType: 'blob'
  })

// 钣金调料申请-采方-导出
export const postVmiSteelPickupBuyerExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/buyer-export`, data, {
    responseType: 'blob'
  })

// 钣金调料申请-采方-导入
export const buyerExcelImport = (data) => {
  return API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/importExcel`, data, {
    responseType: 'blob'
  })
}

// 钣金调料申请-采方-导入模板
export const buyerExportOutInventoryItem = (data) => {
  return API.get(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/exportExcel`, data, {
    headers: { 'Content-Type': 'application/text' },
    responseType: 'blob'
  })
}

// 供方收发货供货计划-vmi交货计划供货计划列表Excel导出
export const postVmiPlanExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/plan/export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 供方收发货供货计划-vmi jit供货计划列表Excel导出
export const postVmiJitExport = (data = {}, query) =>
  API.post(
    `${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/jit/export?zeroFilter=${query.query}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 供方收发货供货计划-vmi订单创建送货单
export const postVmiOrderCreate = (data = {}) => {
  const query = utils.toQueryParams(data.query)
  return API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/order/create?${query}`, data.body)
}

// 供方收发货供货计划-vmi订单预创建送货单
export const postVmiOrderPreCreate = (data = {}) => {
  const query = utils.toQueryParams(data.query)
  return API.post(
    `${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/order/preCreate?${query}`,
    data.body
  )
}

// 供方收发货供货计划-vmi交货计划创建送货单
export const postVmiPlanCreate = (data = {}) => {
  return API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/plan/create`, data)
}
// 供方收发货供货计划-vmi交货计划预创建送货单
export const postVmiPlanPreCreate = (data = {}) => {
  return API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/plan/preCreate`, data)
}

// 供方收发货供货计划-vmi交货计划创建无订单送货单
export const postVmiPlanCreateNoOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/plan/create/noOrder`, data)

// 第三方物流-分页查询
export const postVmiWarehouseLogisticQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiWarehouse/logistic-page-query`, data)

// 第三方物流-分页查询 new
export const postNewVmiWarehouseLogisticQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/criteria-query-for-logistic`, data)

// 供方收发货供货计划-vmi jit创建送货单
export const postVmiJitCreate = (data = {}) => {
  const query = utils.toQueryParams(data.query)
  return API.post(`${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/jit/create?${query}`, data.body)
}
// 供方收发货供货计划-vmi jit预创建送货单
export const postVmiJitPreCreate = (data = {}) => {
  const query = utils.toQueryParams(data.query)
  return API.post(
    `${BASE_TENANT}/supplierDeliverySupplyPlan/vmi/jit/preCreates?${query}`,
    data.body
  )
}
// -----------------------------------------------------------------------------
// 钣金调料申请-采方-钢材需求明细-导出
export const exportSteelDemandDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/steelDemandDetail/buyerExport`, data, {
    responseType: 'blob'
  })
// 钣金调料申请-采方-钢材需求详情-抬头
export const getSteelDemandHeaderByIdApi = (data) => {
  return API.get(`${BASE_TENANT}/steelDemand/buyer/queryHeader/${data.id}`)
}
// 钣金调料申请-采方-钢材需求详情-列表
export const getSteelDemandListByIdApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/buyer/pageDetail`, data)
}
// 钣金调料申请-采方-钢材需求详情-通过
export const passSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/buyer/pass`, data)
}
// 钣金调料申请-采方-钢材需求详情-回退
export const backSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/buyer/back`, data)
}
// 钣金调料申请-采方-钢材需求和调料对应关系-删除
export const deleteSteelDemandRelApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/steelDemandRel/delete`, data)
}
// 钣金调料申请-采方-钢材需求和调料对应关系-导出
export const exportSteelDemandRelApi = (data = {}) =>
  API.post(`${BASE_TENANT}/steelDemand/steelDemandRel/export`, data, {
    responseType: 'blob'
  })
// 钢材需求管理-供方-钢材需求清单-取消
export const cancelSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/cancel`, data)
}
// 钢材需求管理-供方-钢材需求明细-导出
export const exportSupplierSteelDemandDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/steelDemandDetail/supplierExport`, data, {
    responseType: 'blob'
  })
// 钢材需求管理-供方-获取送货地址
export const queryBySupplierForSteelApi = (data) => {
  return API.post(`${BASE_TENANT}/siteTenantExtend/queryBySupplierForSteel`, data)
}
// 钢材需求管理-供方-获取抬头
export const queryHeaderSupplierSteelDemandApi = (data) => {
  return API.get(`${BASE_TENANT}/steelDemand/supplier/queryHeader/${data.id}`)
}
// 钢材需求管理-供方-抬头保存
export const saveSupplierSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/supplier/save`, data)
}
// 钢材需求管理-供方-抬头提交
export const submitSupplierSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/supplier/submit`, data)
}
// 调料申请-采方-详情明细分页
export const pageDetailBuyerSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/buyer/detailList`, data)
}
// 钢材需求管理-供方-详情明细分页
export const pageDetailSupplierSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemand/supplier/detailList`, data)
}
// 钢材需求管理-供方-详情明细保存
export const saveDetailSupplierSteelDemandApi = (data) => {
  return API.post(
    `${BASE_TENANT}/steelDemandDetail/supplier/save/${data.demandId}`,
    data.detailList
  )
}
// 钢材需求管理-供方-详情明细删除
export const deleteDetailSupplierSteelDemandApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemandDetail/supplier/deleteDetail`, data)
}
// 钢材需求管理-供方-删除所有系统分配状态为失败的数据
export const deleteAllocationFailDetailApi = (data) => {
  return API.post(`${BASE_TENANT}/steelDemandDetail/supplier/deleteAllocationFailDetail`, data)
}
// 钢材需求管理-供方-详情明细导入模板
export const tempDetailSupplierSteelDemandApi = (data = {}) =>
  API.get(`${BASE_TENANT}/steelDemandDetail/importDetailTemplate`, data, {
    responseType: 'blob'
  })
// 钢材需求管理-供方-详情明细导入
export const importDetailSupplierSteelDemandApi = (data = {}, queryParams = {}) =>
  API.post(`${BASE_TENANT}/steelDemandDetail/supplier/importDetail/${queryParams.id}`, data, {
    responseType: 'blob'
  })
// 钢材需求管理-供方-详情明细导出
export const exportDetailSupplierSteelDemandApi = (params = {}, data = {}) =>
  API.get(`${BASE_TENANT}/steelDemandDetail/supplier/exportDetail/${params.id}`, data, {
    responseType: 'blob'
  })
