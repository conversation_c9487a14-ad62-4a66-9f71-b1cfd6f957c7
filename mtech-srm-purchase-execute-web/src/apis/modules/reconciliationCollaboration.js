import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'reconciliationCollaboration'

// 对账 - 供方

// 根据ID批量反馈正常_批量接受
export const putReconciliationHeaderSupplierBatchFeedbackNormal = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/batchFeedbackNormal`, data)

// 根据ID批量反馈正常_批量拒绝
export const putReconciliationHeaderSupplierBatchRefuse = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/batchFeedbackAbnormalNew`, data)

// 根据ID取消发布
export const putReconciliationHeaderSupplierCancelPublishById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/cancelPublishById`, data)

// 根据ID关闭
export const putReconciliationHeaderSupplierCloseById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/closeById`, data)

// 保存对账单
export const putReconciliationHeaderSupplierCreate = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/create`, data)

// 根据ID反馈正常
export const putReconciliationHeaderSupplierFeedbackNormal = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/feedbackNormalNew`, data)

// 根据ID反馈异常
export const putReconciliationHeaderSupplierFeedbackAbnormal = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/feedbackAbnormalNew`, data)

// 根据ID发布
export const putReconciliationHeaderSupplierPublishById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/publishById`, data)

// 保存文件信息
export const putReconciliationHeaderSupplierSaveFile = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeaderSupplier/saveFile`, data)

// 查询对账发票信息
export const putReconciliationInvoiceSupplierQueryList = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliationInvoiceSupplier/queryList`, data)

// 提交对账发票信息
export const putReconciliationInvoiceSupplierCommitInvoice = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationInvoiceSupplier/commitInvoice`, data)

// 待对账接口（供方） 获取待对账类型字段
export const postReconciliationWaitSupplierGetFields = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationWaitSupplier/getFields`, data)

// 待对账接口（供方） 获取待对账类型字段-对账单明细
export const postReconciliationWaitSupplierGetFieldsHeaderInfo = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationWaitSupplier/getFieldsHeaderInfo`, data)

// 待对账接口（供方） 获取发票清单
export const getInvoiceListByHeader = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation-invoice-v2/list-by-header`, data)
// 清账发票处理（采方） 获取待对账类型字段-对账单明细
export const postReconciliationWaitGetFieldsHeaderInfo = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/config/field/query`, data)

// 供方发票下载（导出）（导入的文件模板）
export const postDownloadInvoice = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationInvoiceSupplier/downloadSupInvoice`, data, {
    responseType: 'blob'
  })

// 供方发票上传（导入）
export const postReconciliationInvoiceSupplierUploadSupInvoice = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationInvoiceSupplier/uploadSupInvoice`, data, {
    responseType: 'blob'
  })
// 采购对账单 打印
export const printRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/print?BU_CODE=${data.currentBu}`, data, {
    responseType: 'blob'
  })

/**
 * 销售对账
 * 供应商销售对账单主单接口
 */
// 根据id 获取详情
export const getSaleDetailById = (data = {}) =>
  API.get(`${BASE_TENANT}/sale/reconciliation/sup/${data}`)

// 反馈 销售对账
export const feedBackSaleRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/sup/feedback`, data)

// 租户级-往来对账单供应商控制器-分页查询
export const postTransactionReconciliationHeaderSupplierPageQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_header_supplier/pageQuery`, data)

// 租户级-往来对账单供应商控制器-提交反馈
export const postTransactionReconciliationHeaderSupplierFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_header_supplier/feedback`, data)

// 租户级-往来对账单供应商控制器-查询对账单详情
export const postTransactionReconciliationHeaderSupplierDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_header_supplier/detail`, data)

// TV/BD-供方-往来对账反馈-详情
export const postTransactionContactDetailSupplierApi = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transaction/supplier/detail`, data)

// 租户级-往来对账明细供应商控制器-新增或修改
export const postTransactionReconciliationItemSupplierSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_item_supplier/save`, data)
// 根据对账单id查询期初/期末
export const queryBalanceById = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/queryById`, data)

// 租户级-往来对账明细供应商控制器-批量删除
export const deleteTransactionReconciliationItemSupplierBatchDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/transaction_reconciliation_item_supplier/batch-delete`, data)

// 租户级-往来对账明细供应商控制器-上传
export const postTransactionReconciliationItemSupplierUpload = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_item_supplier/upload`, data, {
    responseType: 'blob'
  })

// 租户级-往来对账明细供应商控制器-下载
export const postTransactionReconciliationItemSupplierDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_item_supplier/download`, data, {
    responseType: 'blob'
  })
// 采方往来明细---供方
export const puchaseDetailExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/transaction_reconciliation_item_supplier/exportItem?headerId=${data.headerId}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 供方往来明细---供方
export const supplierDetailExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/transaction_reconciliation_item_supplier/exportItemSupplier?headerId=${data.headerId}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 采方往来明细----采方
export const PuPuchaseDetailExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/transaction_reconciliation_item/exportItem?headerId=${data.headerId}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 供方往来明细----采方
export const PuSupplierDetailExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/transaction_reconciliation_item_supplier/exportItemSupplierItem?headerId=${data.headerId}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 往来对账单打印
export const postTransactionReconciliationPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/supplierPrint`, data, {
    responseType: 'blob'
  })
/**
 * 数量对账
 */
//供方数量对账单反馈
export const reconciliationFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/quantity/reconciliation/feedback`, data)
// 根据id 获取详情
export const getquantityDetailById = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/quantity/reconciliation/${data}`)

// 供方详情页-获取头信息
export const headerReconciliationApi = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/quantity/reconciliation/header/${data.id}`)
// 供方详情页-明细列表
export const getDetailReconciliationApi = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/quantity/reconciliation/detail/itemList/${data.id}`)
// 供方详情页-数量对账单数量汇总
export const summaryQuantityApi = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/quantity/reconciliation/detail/summaryQuantity/${data.id}`)
// 供方详情页-相关附件
export const filesDetailReconciliationApi = (data = {}) =>
  API.get(`${BASE_TENANT}/sup/quantity/reconciliation/detail/files/${data.id}`)

// 根据ID标记冻结
export const putSupplierFrozenById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationWaitSupplier/frozenById`, data)

// 根据ID取消标记冻结
export const putSupplierCancelFrozenById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationWaitSupplier/cancelFrozenById`, data)
//供方数量对账单查询打印
export const reconciliationPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/quantity/reconciliation/print`, data, {
    responseType: 'blob'
  })

// 采购对账-对账单明细下载
export const postReconSupplierDownload = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliationHeaderSupplier/download?headerId=${data.query.headerId}&BU_CODE=${data.currentBu}`,
    data.body,
    {
      responseType: 'blob'
    }
  )

// 采购对账-数量明细下载
export const postNumSupplierDownload = (data = {}) =>
  API.post(
    `${BASE_TENANT}/sup/quantity/reconciliation/download?reconciliationId=${data.query.reconciliationId}`,
    data.body,
    {
      responseType: 'blob'
    }
  )
// 供方数量对账明细下载
export const postNumListSupplierDownload = (data = {}) =>
  API.post(
    `${BASE_TENANT}/sup/quantity/reconciliation/exportItem?headerId=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
// 供方数量对账明细汇总
export const postNumSummaryListSupplierDownload = (data = {}) =>
  API.post(
    `${BASE_TENANT}/sup/quantity/reconciliation/exportSummaryData?headerId=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
// 采购对账-对账单上传修改备注
export const reconSupplierImport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/import`, data, {
    responseType: 'blob'
  })

// 采购对账-对账明细-不分页
export const reconItemSupplierNoPageFlag = (data = {}) =>
  API.get(
    `${BASE_TENANT}/reconciliationItemSupplier/queryBuilderNoPageFlag?headerId=${data.headerId}`
  )

// 数量对账 - 导出
export const reconciliationQuantityExport = (data = {}) =>
  API.post(`${BASE_TENANT}/sup/quantity/reconciliation/export`, data, {
    responseType: 'blob'
  })
//销售对账 - 导出
export const reconciliationSaleExport = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/sup/export`, data, {
    responseType: 'blob'
  })
//销售对账-明细 - 导出
export const reconciliationSaleListExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/sale/reconciliation/sup/exportItem?headerId=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
//往来对账 - 导出
export const reconciliationTransactionExport = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_header_supplier/export`, data, {
    responseType: 'blob'
  })
//采购待对账 - 导出
export const reconciliationHeaderWaitExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/reconciliationWaitSupplier/export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
//采购对账 - 导出
export const reconciliationHeaderListExport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/download`, data, {
    responseType: 'blob'
  })
//采购对账 - 导出
export const reconciliationHeaderExport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/export`, data, {
    responseType: 'blob'
  })
// 采购对账获取金额
export const getAmount = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/summaryReconciliationWait`, data)
// 供方采购对账获取明细数据
export const getDetailAmount = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationItemSupplier/queryBuilder`, data)

// 采方/供方往来明细TV----采方详情页面
export const PuPuchaseDetailExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionItem/export`, data, {
    responseType: 'blob'
  })
// 采方/供方往来明细TV----供方详情页面
export const PuPuchaseDetailSupplierExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionItem/supplier/export`, data, {
    responseType: 'blob'
  })

// 租户级-TV往来对账单供应商控制器-提交反馈
export const postTransactionReconciliationHeaderSupplierFeedbackTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transaction/supplier/commit`, data)

// 租户级-TV往来对账单往来明细-供方新增
export const postTransactionReconciliationHeaderSupplierAddTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionItem/supplier/add`, data)

// 租户级-TV往来对账单往来明细-供方编辑
export const postTransactionReconciliationHeaderSupplierUpdateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionItem/supplier/update`, data)

// 租户级-TV往来对账单往来明细-供方删除
export const postTransactionReconciliationHeaderSupplierDeleteTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionItem/supplier/delete`, data)

// 采方/供方往来明细TV----供方打印
export const PuPuchaseDetailPrintTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transaction/print`, data, {
    responseType: 'blob'
  })

// 供方-获取对账单详情
export const getContactDetailSupplierNewApi = (data = {}) =>
  API.get(`${BASE_TENANT}/contact/reconciliation/header/detail`, data)
// 获取调整项
export const pageAdjustApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/adjust/list`, data)
// 供方-保存调整项
export const saveOrUpdataBatchAdjustApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/adjust/saveOrUpdataBatchAdjust`, data)
// 供方-删除调整项
export const deleteAdjustApi = (data = {}) =>
  API.get(`${BASE_TENANT}/contact/reconciliation/adjust/deleteAdjust`, data)
// 采供-往来对账明细-分页
export const pageReconciliationItemApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/item/list`, data)
// 采供-往来对账明细-导出
export const exportReconciliationItemApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/item/export`, data, {
    responseType: 'blob'
  })
// 供方-往来对账明细-导入模板下载
export const downloadReconciliationItemApi = (data = {}) =>
  API.get(`${BASE_TENANT}/contact/reconciliation/item/template/download`, data, {
    responseType: 'blob'
  })
// 供方-往来对账明细-导入
export const importReconciliationItemApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/item/import`, data, {
    responseType: 'blob'
  })
// 供方-往来对账明细-保存
export const saveReconciliationItemApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/item/saveOrUpdataBatch`, data)
// 供方-往来对账明细-删除
export const deleteReconciliationItemApi = (data = {}) =>
  API.get(`${BASE_TENANT}/contact/reconciliation/item/deleteItem`, data)
// 供方-往来对账-打印
export const printReconciliationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/print/reconciliation`, data, {
    responseType: 'blob'
  })
// 供方-往来对账-保存
export const saveReconciliationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/update/reconciliation`, data)
// 供方-往来对账-提交
export const submitReconciliationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/submit/reconciliation`, data)
