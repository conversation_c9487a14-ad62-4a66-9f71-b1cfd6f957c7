import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'shipmentCollaboration'

// 供方拣货发货
export const postSoPlanDelivery = (data = {}) => API.post(`${BASE_TENANT}/so/plan/delivery`, data)

// 供方拣货
export const postSoPlanPicking = (data = {}) => API.post(`${BASE_TENANT}/so/plan/picking`, data)

// 拣货后发货
export const postSoDeliverAfterPicking = (data = {}) =>
  API.post(`${BASE_TENANT}/so/delivery/afterPicking`, data)

// 供方妥投
export const postSoDeliveryProper = (data = {}) =>
  API.post(`${BASE_TENANT}/so/delivery/proper`, data)

// 供方发货客户
export const getSoPlanPurTenant = () => API.get(`${BASE_TENANT}/so/plan/purTenant`)

// 获取供应商发货记录（待发货-待妥投-发货记录）
export const postSoDeliveryQuery = (data = {}) => API.post(`${BASE_TENANT}/so/delivery/query`, data)

// 获取供应商待拣货主单列表
export const postSoPlanQuery = (data = {}) => API.post(`${BASE_TENANT}/so/plan/query`, data)

// 供方撤回发货
export const postSoDeliveryWithdraw = (data = {}) =>
  API.post(`${BASE_TENANT}/so/delivery/withdraw`, data)

// 供方根据发货单id获取发货单
export const getSoDeliveryId = (id = '') => API.get(`${BASE_TENANT}/so/delivery/${id}`)

// 供方发货计划主单
export const postSoPlanHeader = (data = {}) => API.post(`${BASE_TENANT}/so/plan/header`, data)

// 供方发货计划主单明细
export const postSoPlanItem = (data = {}) => API.post(`${BASE_TENANT}/so/plan/item`, data)

// 关闭发货单
export const postSoDeliveryClose = (data = {}) => API.post(`${BASE_TENANT}/so/delivery/close`, data)

// 获取供应商待拣货明细列表
export const postSoPlanItemQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/so/plan/item/query`, data)
