import { API } from '@mtech-common/http'

import {
  BASE_TENANT,
  PROXY_MDM_TENANT,
  PROXY_MDM_USER,
  PROXY_MDM_COMMON,
  PROXY_MDM_COMMON_TENANT,
  PROXY_MDM_AUTH
} from '@/utils/constant'

export const NAME = 'masterData'

// 获取字典 dictCode:  businessType-业务类型 贸易条款-TradeClause   物流方式-TransportMode
export const getDictCode = (data = {}) => API.post(`${PROXY_MDM_TENANT}/dict-item/dict-code`, data)

export const getDictCodeSupApi = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/supplier/dict-item/item-tree`, data)

// 获取需要的字典存入sessionStorage
export const getNumDictListAllByCode = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/dict-item/getByDictCodes`, data)

// 跨租户级-字典相关接口-获取客户数据字典列表
export const getAuthFindDictItemByCustomerCodeAndDictCode = (data = {}) =>
  API.get(`${PROXY_MDM_AUTH}/dict-item/findDictItemByCustomerCodeAndDictCode`, data)

// 跨租户级-业务组物料接口-根据物料编码和业务组类型获取客户业务组
export const getAuthCustomerBusinessGroup = (data = {}) =>
  API.get(`${PROXY_MDM_AUTH}/business-group-item-code/customer-business-group`, data)
// ?tenantId=${tenantId}&companyCode=${companyCode}
export const getEnterpriseId = (data = {}) =>
  API.get(`${PROXY_MDM_AUTH}/entity/enterprise-id`, data)
// 获取字典值目前用到合同协议类型
export const getDictItemTree = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/dict-item/item-tree`, data)
// 获取字典值目前用到合同协议类型
export const getCommonDictItemTree = (data = {}) =>
  API.post(`${PROXY_MDM_COMMON_TENANT}/dict-item/dict-code`, data)
// 获取用户信息
export const getUserInfo = (data = {}) => API.get(`/iam/common/account/userinfo`, data)

// 租户级-利润中心接口-条件查询
export const postProfitCenterCriteriaQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/profit-center/criteria-query`, data)

// 租户级-成本中心接口-条件查询
export const postCostCenterCriteriaQuery = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/cost-center/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// ---------------模块配置 - 采购申请分配-------------------------------------------------------------------------------------------------
// 获取公司  公司：租户级-组织机构接口 - 模糊查询获取公司树
export const getFuzzyCompanyTree = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/getFuzzyCompanyTree`, data)
//查询结算方 本级公司和上级公司
export const getParentCompanyOrgIncludeself = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/organization/getParentCompanyOrgIncludeself`, data)
//查询采购组
export const getbussinessGroup = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/business-group/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
export const getBusinessGroupCriteriaQueryUrl = `${PROXY_MDM_TENANT}/business-group/criteria-query?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`

// 租户级-品项组(物料组)接口-条件查询
export const postItemGroupCriteriaQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-group/criteria-query`, data)

// 获取品类（所有） 品类：租户级-品类接口 - 条件查询；
export const getCategory = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/category/criteria-query`, data)

// 获取品类（外部物料组）
export const getCategoryfuzzy = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/category/fuzzy-query`, data)

// 未用到 获取品类（根据公司）租户级-品类接口 -  获取指定组织下品类树
export const getCategoryByCompanyId = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/category/getCategoryTreeByOrganization`, data)

// 获取品项 租户级-品项接口 - 条件查询
export const getCategoryItem = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/item/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 获取品类（外部物料组）- 条件查询
export const pageCategoryApi = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/category/paged-query`, data)

// 获取品项 分页查询
export const getItemPage = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// 物料
export const getItemUrl = `${PROXY_MDM_TENANT}/item/paged-query?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`
// 物料
export const getItemAuthUrl = `${PROXY_MDM_TENANT}/item/paged-auth?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`

// 获取工厂 租户级-地点接口 - 条件查询
export const getSite = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/site/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 获取工厂 租户级-地点接口 - 模糊查询
export const postSiteFuzzyQuery = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 获取工厂 租户级-地点接口 - 模糊查询
export const siteQuery = (data = {}) =>
  API.post(`${PROXY_MDM_AUTH}/site/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`, data)
//
// 模糊查询(代码或名称) 库存地点
export const getLocationFuzzyQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/location/fuzzy-query`, data)
// 获取工厂 租户级-库位接口 - 条件查询
export const getLocation = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/location/criteria-query`, data)

// 未使用：获取采购组织 租户级-业务组织接口 - 条件查询
export const getOrganization = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/criteria-query`, data)

// 获取组织架构 租户级-组织机构接口 - 获取指定范围树结构数据
export const getOrganizateTree = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/getStatedLimitTree`, data)

// 租户级 - 业务组织接口 - 条件查询
export const getBusinessOrg = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/business-organization/criteria-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

// --------采购申请管理 - 我的申请----------------------------------------------------------------------------------------------------------
// 获取用户 - 租户级
export const getUser = (data = {}) => API.post(`${PROXY_MDM_TENANT}/user/criteria-query`, data)

// 未使用：租户级-用户接口-获取当前租户下用户列表
export const getCurrentTenantUsers = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/user/findCurrentTenantUsers`, data)

// 未使用：用户级-用户接口-根据用户id获取所属公司列表
export const getCompanysByUserId = (data = {}) =>
  API.get(`${PROXY_MDM_USER}/user/findCompanysByUserId`, data)

// 未使用：用户级-用户接口-根据用户id和公司组织id获取所属部门列表
export const getDepartmentsByUserId = (data = {}) =>
  API.get(`${PROXY_MDM_USER}/user/findDepartmentsByUserId`, data)

// 租户级 - 业务组织 - 获取当前租户下的公司的业务组织
export const getOrganizateByOrgId = (data = {}) =>
  API.get(
    `${PROXY_MDM_TENANT}/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
export const getBusinessOrganizationByOrgIdUrl = `${PROXY_MDM_TENANT}/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`

// 租户级-雇员接口-当前租户下员工列表
export const getCurrentTenantEmployees = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/employee/currentTenantEmployees`, data)

// 租户级-雇员接口-通过工号查员工信息
export const findByEmployeeCode = (data = {}) =>
  API.post(`/masterDataManagement/common/employee/findByEmployeeCode`, data)

export const pageEmployeeApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/employee/paged-query`, data)

// // 租户级-根据业务组织id获取指定类型组织节点
// export const findOrgByBuOrgIdAndOrgLevelTypeCode = (data = {}) =>
//   API.get(
//     `${PROXY_MDM_TENANT}/business-org-org-rel/findOrgByBuOrgIdAndOrgLevelTypeCode`,
//     data
//   );

// 租户级-组织机构接口-根据员工id获取公司级组织列表（包含公司-事业群-板块-集团）行政标签
export const getOrgCompanysByEmpId = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/organization/company-level-orgs`, data)

// 获取指定组织下指定组织层级节点列表 业务标签公司 (目前涉及 采购申请，采购订单，对账结算)
export const OrgFindSpecifiedChildrenLevelOrgs = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/findSpecifiedChildrenLevelOrgs`, data)

// 临时出入证供方获取公司列表接口
export const getCompanyBySup = (data = {}) =>
  API.post(`/masterDataManagement/auth/company/auth-fuzzy`, data)
// 公司下拉模糊搜索
export const OrgFindSpecifiedChildrenLevelBalance = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/findSpecifiedChildrenLevelBalance`, data)
// 租户级-组织机构接口-根据员工id获取部门级组织列表
export const getDepartmentsByEmpId = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/organization/department-level-orgs`, data)

// 租户级-全品类查询
export const categoryAll = (data = {}) => API.post(`${PROXY_MDM_TENANT}/category/all-query`, data)

// -----------采购申请--新增/编辑----需求明细-------------------------------------------------------------------
// 税率  租户级-税目税率接口-条件查询
export const getTaxItem = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/tax-item/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 币种  租户级-货币接口-条件查询
export const getCurrency = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/currency/criteria-query`, data)

// 币种 模糊搜索
export const getCurrencyByFilter = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/currency/fuzzy-query`, data)

// 币种 模糊搜索 往来对账
export const getCurrencyFuzzyQuery = (data = {}) =>
  API.get(`/masterDataManagement/common/currency/queryActiveCurrency`, data)
// 币种 模糊搜索
export const getCurrencyAll = (data = {}) => API.post(`${PROXY_MDM_TENANT}/currency/queryAll`, data)

// 收货人 租户级-雇员接口-条件查询/根据姓名模糊查询
export const getEmploee = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/employee/criteria-query`, data)

// 质量免检标识（主数据根据物料+工厂带出）  租户级 - 品项质量视图 - 查询详情  (用户没有配置的话，可能会没有：skipQualityControl 质量免检标识 ；1：是，0：否)
export const getQuantity = (data = {}) => API.post(`${PROXY_MDM_TENANT}/item-quality/detail`, data)

// 推荐供应商（输入带出内部供应商，也可以不选内部，直接输入）【已有】租户级-供应商信息接口-条件查询
export const getSupplier = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/supplier/criteria-query`, data)
// vmi 管理方 厂内厂外
export const conditionGetSupplier = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/supplier/condition-query`, data)
// 供方带出采方供应商
export const authGetSupplier = (data = {}) =>
  API.post(`${PROXY_MDM_AUTH}/supplier/criteria-query`, data)
// 供方带出采方采购组
export const businessGetGroup = (data = {}) =>
  API.post(
    `${PROXY_MDM_AUTH}/business-group/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// 获取采购组
export const getBusinessGroupBy = (data = {}) =>
  API.post(
    `${PROXY_MDM_AUTH}/business-group/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
export const getBusinessGroupAuthFuzzyUrl = `${PROXY_MDM_AUTH}/business-group/auth-fuzzy?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`
// 根据供应商编码获取供应商信息 采购订单用到
export const supplierPagedQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/supplier/paged-query`, data)
// 推荐供应商（输入带出内部供应商，也可以不选内部，直接输入）【已有】租户级-供应商信息接口-模糊查询
export const getFuzzySupplier = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/supplier/fuzzy-query`, data)

// 获取物料  租户级 - 客户接口 - 模糊查询
export const getCustomer = (data = {}) => API.post(`${PROXY_MDM_TENANT}/customer/fuzzy-query`, data)
// 根据供应商代码查询公司
export const getCompanyByCode = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/findCompanyBySupplierCodes`, data)
// 供应商查询公司
export const getCompanyByTenantId = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/supplier/findCompanyByTenantId`, data)

// 获取物料  租户级 - 品项接口 - 分页模糊查询
export const getItemByKeyword = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/item/fuzzy-paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 获取SKU 租户级-SKU管理-分页模糊查询
export const getSKU = (data = {}) => API.post(`${PROXY_MDM_TENANT}/sku/fuzzy-paged-query`, data)

// 根据物料 查询关联的工厂列表 租户级-品项组织关系
// itemId： 品项ID  orgLevelTypeCode： 固定值 "ORG06"
export const getFactoryList = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/item-org-rel/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 没有物料，根据公司获取 工厂列表 organizationId（所选公司的组织id）
export const getFactoryListByCompany = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/site/getSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`, data)

// 根据工厂及品项查询品项相关采购数据 租户级-品项采购视图
export const getBasicByFacItem = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-purchasing/basic-in-org`, data)

// 根据物料编码和业务组类型获取业务组
export const getBusinessGroup = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/business-group-item-code/business-group`, data)

//  租户级-品项采购详情
export const getBasicDetail = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-purchasing/detail`, data)

// 单位
export const getUnit = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/unit/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 单位 可模糊查询
export const getUnitByFilter = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/unit/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 单位
export const getUnitUrl = `${PROXY_MDM_TENANT}/unit/paged-query?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`
// 根据组织id获取本位币
export const getStandardCurrency = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/organization/standard-currency`, data)

// 租户级-业务组品类及组织关联关系接口 条件查询指定品类组织业务组
export const postCriteriaQueryBusinessGroup = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/business-group-category-rel/criteriaQueryBusinessGroup`, data)

// 根据组织id和业务组类型获取业务组
export const getBusinessGroupFindByOrgIdAndGroupType = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/business-group/findByOrgIdAndGroupType`, data)

// 供方拿物料
export const getOrgRel = (data = {}) =>
  API.post(
    `${PROXY_MDM_AUTH}/item-org-rel/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 获取工作中心
export const getWorkCenter = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/work-center/criteria-query`, data)
// 采方 获取工作中心
export const getWorkCenterBy = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/work-center/paged-query`, data)
//根据物料查询采购组
export const findBusinessGroupByCategoryId = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/business-group-category-rel/findBusinessGroupByCategoryId`, data)
// 获取工厂条码打印用到
export const findOrgSiteInfo = (data = { orgType: '' }) =>
  API.post(
    `${PROXY_MDM_TENANT}/site/findOrgSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

//获取当前登录企业在指定客户中的供应商基础信息
export const supplierFindInBuyingByCustomerCode = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/supplier/findInBuyingByCustomerCode`, data)

//查询独立或集中 采购订单明细用到
export const itemPlanningDetail = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/item-planning/detail`, data)
// 查询合作伙伴
export const businessPartnerQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/business-partner-function-rel/criteria-query`, data)

// 租户级-客户接口-分页查询
export const postCustomerPagedQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/customer/paged-query`, data)

// // 租户级-客户接口-分页查询 new
// export const postCustomerNewPagedQuery = (data = {}) =>
//   API.post(`${PROXY_MDM_COMMON_TENANT}/dict-item/dict-code`, data);

// 租户级-客户接口-分页查询 new
export const postCustomerNewPagedQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/findOrgsByFuzzyOrgName`, data)

// 租户级-货源信息-我的客户的货源工厂 FIXME: 这个API 已废弃
export const getSourceSitesInVendor = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/supply/source/source-sites-in-vendor`, data)

// 租户级-供方视角-获取可用货源关系工厂
export const getSourceAvailable = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/supply/source/available-source-sites-in-supplier-view`, data)

// 租户级-供方视角-获取可用货源关系工厂（新 货源清单）
export const getSourceAvailableView = (data = {}) =>
  API.get(
    `${PROXY_MDM_TENANT}/supply-source-list/available-source-list-sites-in-supplier-view`,
    data
  )

// 租户级 - 货源信息 - 我的客户的供货物料;
export const postSourceItemsInVendor = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/supply/source/available-source-items-in-supplier-view`, data)

// 租户级 - 货源信息 - 我的客户的供货物料;
export const postSourceItemnVendor = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/supply/source/available-source-items-in-supplier-view`, data)

// 租户级 - 货源清单 - 我的客户的供货物料;
export const postSourceItemnVendorava = (data = {}) =>
  API.get(
    `${PROXY_MDM_TENANT}/supply-source-list/available-source-list-items-in-supplier-view`,
    data
  )

// 通用级-租户接口-获取当前登录用户所属租户企业信息
export const getEnterpriseInfo = (data = {}) => API.get(`${PROXY_MDM_COMMON}/enterprise-info`, data)

//租户级地点接口根据编码获取地点 公司信息
export const siteFindByCode = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/site/findByCode?BU_CODE=${localStorage.getItem('currentBu')}`, data)
// 付款条件
export const paymentTerms = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/payment-terms/criteria-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

const PROXY_MASTER_DATA = '/masterDataManagement'
const DEFAULTPARAM = {
  condition: '',
  page: {
    current: 1,
    size: 1000
  },
  pageFlag: false
}

/**租户级-公司接口
 * @description 模糊查询 获取公司编码列表
 * @param {Object} data
 */
export const getCompanyList = (data = {}) =>
  API.post(`${PROXY_MASTER_DATA}/tenant/company/fuzzy-query`, data)

// 获取当前登录企业在指定客户中的供应商基础信息
export const findInBuyingByCustomerCode = (data = {}) =>
  API.get(`${PROXY_MASTER_DATA}/tenant/supplier/findInBuyingByCustomerCode`, data)

/*
    租户级-业务组织接口
  */
// 根据业务组织类型代码查询   采购组织列表：BUORG002ADM
export const purchaseOraginaze = (data = {}) =>
  API.post(
    `${PROXY_MASTER_DATA}/tenant/business-organization/query-by-orgcode?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )

/*
    租户级-字典相关接口
  */
/**
 * @description: 根据字典类型编码获取字典详情
 * 推荐合同类型:contractType  业务类型:businessType 项目策略:policyType
 * @param {*}
 * @return {itemCode、itemName、id}
 */
export const dictionaryGetList = (data = {}) =>
  API.post(`${PROXY_MASTER_DATA}/tenant/dict-item/dict-code`, data)

/*
    租户级-雇员接口
  */
// 根据姓名模糊查询
export const getUserListByTenantId = (data) =>
  API.get(`${PROXY_MASTER_DATA}/tenant/employee/fuzzy-query`, data)

/*
    租户级-部门接口
  */
// 模糊查询    获取部门列表
export const getDepartmentList = (data = {}) =>
  API.put(`${PROXY_MASTER_DATA}/tenant/department/fuzzy-query`, data)

/*
    租户级-部门接口
  */
// 模糊查询    获取部门列表
export const findSiteInfoByParentId = (data = {}) => {
  return API.post(
    `${PROXY_MASTER_DATA}/tenant/site/findSiteInfoByParentId?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
}

/*
    租户级-地点接口
  */
/**
 * @description:分页查询
 * @param {*}
 * @return {siteCode、siteName、id}
 */
export const getSiteList = (data = DEFAULTPARAM) =>
  API.post(
    `${PROXY_MASTER_DATA}/tenant/site/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// 工厂
export const getSiteListUrl = `${PROXY_MASTER_DATA}/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`
export const getSiteAuthFuzzyUrl = `${PROXY_MASTER_DATA}/auth/site/auth-fuzzy?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`

// 根据业务公司 查 对账科目  租户级-实体科目接口-根据组织编码获取实体科目列表
export const getAccountSubject = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/entity-account-subject/findByOrgCode`, data)

export const queryAllCurrency = (data = {}) => {
  return API.post(`${PROXY_MASTER_DATA}/tenant/currency/queryAll`, data)
}

// export const postCustomerPagedQuery = (data = {}) =>
//   API.post(`${PROXY_MDM_TENANT}/customer/paged-query`, data);
// 物流信息维护--客户公司名称下拉接口
export const postBuyerCriteriaQuery = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/customer/criteria-query`, data)

// 获取扩展
export const findBusinessOrgInfoByOrg = (data = {}) => {
  return API.get(
    `/sourcing/tenant/permission/purOrgWithSite?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
/*
    添加数据权限后，使用以下接口
  */
//获取允许操作的采购组织信息
export const permissionOrgList = ({ orgId }) => {
  return API.get(
    `/sourcing/tenant/permission/purOrgs?BU_CODE=${localStorage.getItem('currentBu')}`,
    {
      companyId: orgId
    }
  )
}
//获取允许操作的工厂信息
export const permissionSiteList = ({ buOrgId, companyId }) =>
  API.get(`/sourcing/tenant/permission/sites?BU_CODE=${localStorage.getItem('currentBu')}`, {
    purOrgId: buOrgId,
    companyId
  })
//通过工厂获取组织
export const getByOrgIdAndBgOrgTypeCode = ({ siteId, orgTypeCode }) =>
  API.get(`/sourcing/tenant/permission/purOrgs/site?BU_CODE=${localStorage.getItem('currentBu')}`, {
    siteId,
    orgTypeCode
  })
//只有sit环境交货计划用到
//查询jit标识
export const getBySiteItemCode = (data = {}) =>
  API.post(`${PROXY_MASTER_DATA}/tenant/item-org-jit/getBySiteItemCode`, data)
//直送销售标识
export const getBySiteLocationCode = (data = {}) =>
  API.post(`${PROXY_MASTER_DATA}/tenant/site-location-manage/getBySiteLocationCode`, data)
//只有sit环境交货计划用到
export const getComFuzzyQuerySupplier = (data = {}) =>
  API.post(`${PROXY_MDM_AUTH}/supplier/getComFuzzyQuery`, data)
//供方查询公司
export const getComListSupplier = (data = {}) =>
  API.get(`${PROXY_MDM_AUTH}/supplier/getComList`, data)
//供方公司下工厂
export const getSiteListSupplier = (data = {}) =>
  API.get(`${PROXY_MDM_AUTH}/supplier/getSiteList`, data)
//供方工厂下物料
export const getSiteItemListSupplier = (data = {}) =>
  API.get(`${PROXY_MDM_AUTH}/supplier/getSiteItemList`, data)
//供方工厂下物料 new
// codeOnly 为1 只搜物料编码  equalOnly为1的时候  全值匹配（精确）
export const getSiteItemFuzzyQuerySupplier = (data = {}) =>
  API.post(
    `${PROXY_MDM_AUTH}/supplier/getSiteItemFuzzyQuery?siteCode=${data.siteCode}&&codeOnly=1&&equalOnly=1`,
    data.fuzzyNameOrCode
  )
export const getItemCodeApi = (data = {}) =>
  API.post(
    `${PROXY_MDM_AUTH}/supplier/getSiteItemFuzzyQuery?siteCode=${data.siteCode}&&codeOnly=1&&equalOnly=0`,
    data.fuzzyNameOrCode
  )

// 采方获取工厂  数据权限
export const getSiteFindByPermission = (data = {}) =>
  API.get(
    `${PROXY_MDM_TENANT}/site/findByPermission?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

export const getSiteData = (data = {}) =>
  API.post(`/masterDataManagement/tenant/site/app/paged-query`, data)

export const getDictSpecified = (data = {}) =>
  API.post(`${PROXY_MDM_TENANT}/organization/specified-level-paged-query`, data)

// 获取采购组（主数据下拉）
export const getGroup = (data = {}) =>
  API.post(
    `${PROXY_MDM_TENANT}/business-group/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// 采购组
export const getBusinessGroupUrl = `${PROXY_MDM_TENANT}/business-group/paged-query?BU_CODE=${localStorage.getItem(
  'currentBu'
)}`
// 获取字典值目前用到合同协议类型
export const getCommonDictItemTreeTv = (data = {}) =>
  API.post(`${PROXY_MDM_COMMON_TENANT}/dict-item/dict-code`, data)

// 根据权限获取有权限的工厂 -- 不允许直调feign接口因此替换为该接口
export const getOrgListByCode = (data) =>
  API.get(`/analysis/tenant/buyer/assess/comprehensiveResult/getOrgListByCode`, data)

// 获取会计科目
export const getAccountSubjectApi = (data = {}) =>
  API.post(`${PROXY_MDM_USER}/account-subject/criteria-query`, data)

// 获取出货库位
export const getOutboundWarehouseListApi = (data = {}) =>
  API.get(`${PROXY_MDM_TENANT}/dict-item/getOutboundWarehouseList`, data)

// 获取税率
export const pageTaxItemApi = (data = {}) =>
  API.post(
    `/masterDataManagement/tenant/tax-item/paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
