// import { $http } from "@/utils/httpClient";
import { API } from '@mtech-common/http'

import { BASE_TENANT, BASE_COMMON } from '@/utils/constant'

export const NAME = 'moduleConfig'

// 申请规则列表------------------------------
// 新增
export const addDimension = (data = {}) => API.post(`${BASE_TENANT}/_distribute_rule/save`, data)

export const addDimensionValid = (data = {}) =>
  API.get(`${BASE_TENANT}/_distribute_rule/save-valid`, data)

// 删除
export const deleteDimension = (data = {}) =>
  API.delete(`${BASE_TENANT}/_distribute_rule/delete`, data)

// 修改
export const updateDimension = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule/update`, data)

export const updateDimensionValid = (data = {}) =>
  API.get(`${BASE_TENANT}/_distribute_rule/update-valid`, data)

// 获取列表
export const queryDimension = (data = {}) => API.post(`${BASE_TENANT}/_distribute_rule/list`, data)

// 查询分配规则
export const getDimension = (data = {}) =>
  API.get(`${BASE_TENANT}/_distribute_rule/queryDimension`, data)

// 启用、停用操作
export const updateStatusDimension = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule/updateStatus`, data)

// 申请规则详情-------------
// 新增
export const addDimensionDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule_detail/save`, data)

export const addDimensionDetailValid = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule_detail/save-valid`, data)

// 删除
export const deleteDimensionDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule_detail/delete`, data)

// 修改
export const updateDimensionDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule_detail/update`, data)

export const updateDimensionDetailValid = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule_detail/update-valid`, data)

// 获取列表
export const queryDimensionDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/_distribute_rule_detail/list`, data)

// 审批配置 ---------

// 获取审批流节点
export const getFlowOperationTypes = (data = {}) =>
  API.get(`${BASE_COMMON}/flow/operation-types`, data)

// 新增/保存
export const saveApprovalConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/process/config/save`, data)

// 删除
export const deleteApprovalConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/process/config/delete`, data)

// 修改状态:启用禁用
export const updateApprovalConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/process/config/enable`, data)

// 删除 ew
export const deleteNewApprovalConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/process/config/delete`, data)

// 修改状态:启用禁用
export const updateNewApprovalConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/process/config/enable`, data)
