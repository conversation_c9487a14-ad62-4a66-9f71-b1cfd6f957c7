/*
 * @Author: your name
 * @Date: 2021-09-03 17:39:36
 * @LastEditTime: 2021-09-09 13:46:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-master-data-web\src\apis\modules\roles.js
 */
import { API } from '@mtech-common/http'
//业务组织
export const NAME = 'Inventory'

const urlPath = {
  tree: '/masterDataManagement',
  srm: 'srm-purchase-execute'
}
// 供方--盘点单列表   --已使用
export const querySupHeader = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/querySupHeader`, data)
}
// 供方--创建盘点单--公司接口 --已使用
export const findSpecifiedChildrenLevelOrgs = (data) => {
  return API.post(`${urlPath.tree}/tenant/organization/findSpecifiedChildrenLevelOrgs`, data)
}
// 供方--创建盘点单--工厂接口  --已使用
export const getSiteInfo = (data) => {
  return API.get(
    `${urlPath.tree}/tenant/site/getSiteInfo?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
//单位 --已使用
export const fuzzyQuery = (data) => {
  return API.post(
    `${urlPath.tree}/tenant/unit/fuzzy-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}
//供方单位 --已使用
export const findByTenantId = (data) => {
  return API.post(`${urlPath.tree}/auth/unit/findByTenantId`, data)
}
// 供方--创建盘点单--查询相关附件
export const queryFileByDocId = (data = '') =>
  API.get(`/srm-purchase-execute/tenant/file/queryFileByDocId?docId=${data}&doctype=pd&parentId=0`)
// 供方--创建盘点单--保存--提交 --已使用
export const saveSubmit = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/saveSubmit`, data)
}
// 供方--创建盘点单列表--提交 --已使用
export const submit = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/submit`, data)
}
// 盘点单对比
export const contrast = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/contrast`, data)
}
// 盘点单导入
export const excelImport = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/supplierImport`, data, {
    responseType: 'blob'
  })
}
// 盘点单导出
export const excelExport = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/supplierExport`, data)
}
// 删除
export const inventoryDelete = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/supDelete`, data)
}
// 根据code查询详情
export const queryInventoryItem = (data) => {
  return API.get(`${urlPath.srm}/tenant/inventory/queryInventoryItem`, data)
}

// 盘点差异明细 -供 ---确认
export const batchAccept = (data) => {
  return API.put(`${urlPath.srm}/tenant/inventory/batchAccept`, data)
}
// 盘点差异明细 -供 ---拒绝
// export const batchReject = (data) => {
//   return API.post(`${urlPath.srm}/tenant/inventory/batchReject`, data);
// };
// 盘点差异明细 -供 ---导出
export const supExportInventoryVarianceItem = (data) => {
  return API.post(
    `${urlPath.srm}/tenant/inventoryVariance/supExportInventoryVarianceItem?inventoryCode=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
}
export const supplierInventoryConfirm = (data) => {
  return API.get(`${urlPath.srm}/tenant/inventoryVariance/supplierConfirm?inventoryCode=${data}`)
}
// 盘点单列表 ----------------------------------------- 采
//删除
export const buyerInventoryDelete = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/buyerDelete`, data)
}
//取消
export const buyerInventoryInactive = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/buyerCancel`, data)
}
//盘点单对比
export const buyerContrast = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/getSiteCompanySupplier`, data)
}
// 盘点单创建 - 采
// 盘点单创建
export const buyerInventorySave = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/buyerSave`, data)
}
export const buyerInventoryConfirm = (data) => {
  return API.get(`${urlPath.srm}/tenant/inventoryVariance/buyerConfirm?inventoryCode=${data}`)
}
export const buyerInventoryRefuse = (data) => {
  return API.get(`${urlPath.srm}/tenant/inventoryVariance/buyerRefuse?inventoryCode=${data}`)
}
// 获取工厂|公司|供应商数据
export const getSiteCompanySupplier = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/getSiteCompanySupplier?type=${data}`)
}
// 通过公司获取工厂
export const getSiteByCompany = (data) => {
  return API.get(
    `${urlPath.tree}/tenant/site/findByCompanyCode?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}?parentCode=${data}`
  )
}
// 导出盘点差异明细 - 采
export const buyerExportInventoryVarianceItem = (data) => {
  return API.post(
    `${urlPath.srm}/tenant/inventoryVariance/buyerExportInventoryVarianceItem?inventoryCode=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
}
// 拒绝盘点差异明细
export const buyerRefuse = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryVariance/buyerRefuse`, data)
}

// 根据盘点单号查询盘点差异明细
export const queryInventoryVarianceItem = (data) => {
  return API.get(
    `${urlPath.srm}/tenant/inventoryVariance/queryInventoryVarianceItem?inventoryCode=${data}`
  )
}

//导入委外盘点明细 - 采
export const buyerExcelImport = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/buyerImport`, data, {
    responseType: 'blob'
  })
}

// 导出委外盘点明细 - 采
export const buyerExportOutInventoryItem = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventory/buyerExportOutInventoryItem`, data, {
    headers: { 'Content-Type': 'application/text' },
    responseType: 'blob'
  })
}

// 导出委外盘点明细 - 供方
export const supplierExportOutInventoryItem = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventory/supplierExportOutInventoryItem`, data, {
    headers: { 'Content-Type': 'application/text' },
    responseType: 'blob'
  })
}

// 获取物料下拉数据 - 采方
export const getItemforSite = (data) => {
  return API.post(
    `/masterDataManagement/tenant/item-org-rel/paged-query-for-site?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
}

// 根据工厂代码获取物料带单位
export const getItemPageQuery = (data) => {
  return API.post(
    `/masterDataManagement/tenant/item/paged-query?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
}

// 同步盘点明细数据
export const syncInventoryItem = (data) => {
  return API.post(`${urlPath.srm}/tenant/inventoryStocktaking/supplierInventory`, data)
}

// 采方-PSI库存-分页查询
export const pagePsiInventoryApi = (data) => {
  return API.post(`/contract/tenant/supplier/inv/query`, data)
}

// 采方-PSI库存-导出
export const exportPsiInventoryApi = (data) => {
  return API.post(`/contract/tenant/supplier/inv/buyer/export`, data, {
    responseType: 'blob'
  })
}

// 供方-PSI库存-分页查询
export const pagePsiInventorySupApi = (data) => {
  return API.post(`/contract/tenant/supplier/inv/supplier/query`, data)
}

// 供方-PSI库存-导出
export const exportPsiInventorySupApi = (data) => {
  return API.post(`/contract/tenant/supplier/inv/supplier/export`, data, {
    responseType: 'blob'
  })
}
// 供方-PSI库存-导入
export const importtPsiInventorySupApi = (data) => {
  return API.post(`/contract/tenant/supplier/inv/excel/import`, data, {
    responseType: 'blob'
  })
}
// 供方-PSI库存-导入模板
export const downloadPsiInventorySupApi = (data) => {
  return API.get(`/contract/tenant/supplier/inv/importTemplate`, data, {
    responseType: 'blob'
  })
}

// 采方-PSI生产日计划-分页查询
export const pagePsiDailyPlanApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/buyer/page`, data)
}

// 采方-PSI生产日计划-导出
export const exportPsiDailyPlanApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/buyer/export`, data, {
    responseType: 'blob'
  })
}

// 供方-PSI生产日计划-分页查询
export const pagePsiDailyPlanSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/supplier/page`, data)
}

// 供方-PSI生产日计划-导出
export const exportPsiDailyPlanSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/supplier/export`, data, {
    responseType: 'blob'
  })
}

// 供方-PSI生产日计划-导入
export const importtPsiDailyPlanSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/excel/import`, data, {
    responseType: 'blob'
  })
}
// 供方-PSI生产日计划-导入模板
export const downloadPsiDailyPlanSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/supplier/template`, data, {
    responseType: 'blob'
  })
}

// 采供-PSI生产日计划-表头查询
export const headerPsiDailyPlanApi = (data) => {
  return API.get(`/contract/tenant/psi/supplierDayPlan/header`, data)
}

// 采方-日合格率&达成率-表头查询
export const headerPsiDailyPassApi = (data) => {
  return API.get(`/contract/tenant/psi/supplierDayPlan/rate/header`, data)
}

// 采方-日合格率&达成率-分页查询
export const pagePsiDailyPassApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/rate/buyer/page`, data)
}

// 采方-日合格率&达成率-图表查询
export const chartPsiDailyPassApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/rate/buyer/chart`, data)
}

// 采方-日合格率&达成率-导出
export const exportPsiDailyPassApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/rate/buyer/export`, data, {
    responseType: 'blob'
  })
}

// 供方-日合格率&达成率-分页查询
export const pagePsiDailyPassSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/rate/supplier/page`, data)
}

// 供方-日合格率&达成率-图表查询
export const chartPsiDailyPassSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/rate/supplier/chart`, data)
}

// 供方-日合格率&达成率-导出
export const exportPsiDailyPassSupApi = (data) => {
  return API.post(`/contract/tenant/psi/supplierDayPlan/rate/supplier/export`, data, {
    responseType: 'blob'
  })
}
