import { API } from '@mtech-common/http'
export const NAME = 'rfxList'
const PROXY_SOURCING = '/sourcing'
// 获取询价对象合集
export const getSourcingObjects = () => API.get(`${PROXY_SOURCING}/tenant/config/soucingobjs`)

// 添加询价单--表单验证
export const addRfxHeaderValid = (data = {}) =>
  API.get(`${PROXY_SOURCING}/tenant/rfxHeader/save-valid`, data)

// 生成RFX CODE
export const getRfxCode = (data = {}) =>
  API.get(`${PROXY_SOURCING}/tenant/rfxHeader/rfx-code`, data)

// 获取用户配置字段
export const getUserConfigFields = (data) =>
  API.get(`${PROXY_SOURCING}/tenant/rfx/header/config`, data)

export const getStrategyConfigFields = (data = {}) =>
  API.get(`${PROXY_SOURCING}/tenant/rfx/strategy/detail`, data)

// 添加询价单
export const addRfxHeader = (data = {}) => API.put(`${PROXY_SOURCING}/tenant/rfxHeader/save`, data)

// 添加询价单--轉尋源
export const createRfxByPr = (data = {}) =>
  API.put(`${PROXY_SOURCING}/tenant/rfxHeader/createRfxByPr`, data)

// 添加询价单--转寻源 物流
export const createLogisticsRfxByPr = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/header/toSourcing`, data)

//配置列表
export const getConfigList = (data = {}) =>
  API.post(`${PROXY_SOURCING}/tenant/business/configs`, data)
