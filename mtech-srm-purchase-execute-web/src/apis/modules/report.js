// vmi-第三方接口
import { API } from '@mtech-common/http'
import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'report'

// 备货达成率报表-导出
export const buyerStockRateReportReport = (data = {}) =>
  API.post(`/statistics/tenant/buyerStockRateReport/export`, data, {
    responseType: 'blob'
  })

// 回货价格失效-导出
export const failureReport = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/deliveryPriceInvalidWarn/exportExcel`, data, {
    responseType: 'blob'
  })

// 入库执行报表-导出
export const vmiInboundRateReport = (data = {}) =>
  API.post(`/statistics/tenant/vmiInboundRate/exportExcel`, data, {
    responseType: 'blob'
  })

// 入库执行报表-按月汇总-导出
export const vmiInboundRateMonthReport = (data = {}) =>
  API.post(`/statistics/tenant/vmiInboundRate/pageGroupDateExport`, data, {
    responseType: 'blob'
  })

// 入库执行报表-采购分类-导出
export const vmiInboundRatePurReport = (data = {}) =>
  API.post(`/statistics/tenant/vmiInboundRate/pageGroupCategoryExport`, data, {
    responseType: 'blob'
  })

// 交期回复及时-完整率报表导出
export const deliveryDateReplyRateReport = (data = {}) =>
  API.post(`/statistics/tenant/buyer/deliveryDateReplyRate/export`, data, {
    responseType: 'blob'
  })

// 下单执行比例报表导出
export const buyerOrderRateReport = (data = {}) =>
  API.post(`/statistics/tenant/buyerOrderRate/export`, data, {
    responseType: 'blob'
  })

// 下单执行比例报表-按月汇总-导出
export const buyerOrderRateMonthReport = (data = {}) =>
  API.post(`/statistics/tenant/buyerOrderRate/exportGroupMonth`, data, {
    responseType: 'blob'
  })

// 交期回复导出
export const planOrderExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/buyerGoodsDemandPlanInfo/report/export?reportType=${data.reportType}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 备货达成率配置新增
export const stockRateConfigCreate = (data = {}) =>
  API.post(`/statistics/tenant/stockRateConfig/create`, data)

// 备货达成率配置修改
export const stockRateConfigUpdate = (data = {}) =>
  API.post(`/statistics/tenant/stockRateConfig/update`, data)

// 备货达成率配置删除
export const stockRateConfigDelete = (data = {}) =>
  API.post(`/statistics/tenant/stockRateConfig/delete`, data)
// 采方数量对账报表导出
export const purchaseExport = (data = {}) =>
  API.post(`/statistics/tenant/vmiStockCountReport/purchase/report/export`, data, {
    responseType: 'blob'
  })
// 供方数量对账报表导出
export const supplierExport = (data = {}) =>
  API.post(`/statistics/tenant/vmiStockCountReport/supplier/report/export`, data, {
    responseType: 'blob'
  })
// vmi数量对账报表导出
export const logisticsExport = (data = {}) =>
  API.post(`/statistics/tenant/vmiStockCountReport/third/report/export`, data, {
    responseType: 'blob'
  })

// 交货计划 - 横向报表 - 查询列表 - 采方
export const getdemandPlanTransverseList = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/query', data)

// 交货计划 - 横向报表 - 查询历史版本 - 采方
export const historyQueryDemandPlanTransverseApi = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/buyer/historyQuery', data)

// 交货计划 - 横向报表 - 获取列表表头数据 - (供，采)
export const getdemandPlanTransverseHeaderInfo = () =>
  API.get('/statistics/tenant/goodsDemandPlanTransverse/getColumnList')
// 交货计划 - 横向报表 - 获取历史表头数据 - (供，采)
export const getHistoryColumnListApi = (data = {}) =>
  API.get('/statistics/tenant/goodsDemandPlanTransverse/getHistoryColumnList', data)

// 交货计划 - 横向报表 - 导出列表数据 - 采方
export const exportdemandPlanTransverseInfo = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/export', data, {
    responseType: 'blob'
  })

// 交货计划 - 横向报表 - 导出历史版本- 采方
export const historyExportDemandPlanTransverseApi = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/buyer/historyExport', data, {
    responseType: 'blob'
  })

// 交货计划 - 横向报表 - 查询列表 - 供方
export const getdemandPlanTransverseListSup = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/supplierQuery', data)

// 交货计划 - 横向报表 - 历史版本 - 供方
export const historyQueryDemandPlanTransverseListSupApi = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/supplier/historyQuery', data)

// 交货计划 - 横向报表 - 导出列表数据 - 供方
export const exportdemandPlanTransverseInfoSup = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/supplierExport', data, {
    responseType: 'blob'
  })

// 交货计划 - 横向报表 - 导出历史版本 - 供方
export const historyExportDemandPlanTransverseInfoSupApi = (data = {}) =>
  API.post('/statistics/tenant/goodsDemandPlanTransverse/supplier/historyExport', data, {
    responseType: 'blob'
  })

// 获取供应商列表 - 交货计划 - 横向报表 - 供方
export const getSupplierList = (data = {}) =>
  API.post('/masterDataManagement/auth/supplier/fuzzy-query', data)

// 获取采购组 - 交货计划 - 横向报表 - 供方
export const getBuyerOrgCodeList = (data = {}) =>
  API.post(
    `/masterDataManagement/auth/business-group/auth-fuzzy?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )

// 获取工厂列表 - 交货计划 - 横向报表 - 供方
export const getSiteCodeList = (data = {}) =>
  API.post(
    `/masterDataManagement/auth/site/auth-fuzzy?BU_CODE=${localStorage.getItem('currentBu')}`,
    data
  )
// 交货计划 - 大数据批次来料接口明细表 - 查询列表
export const getbatchLncomingMaterialDetails = (data = {}) =>
  API.post('/statistics/tenant/jitBatchCallMaterial/pageQuery', data)

// 交货计划 - 大数据批次来料接口明细表 - 导出列表数据
export const exportbatchLncomingMaterialDetails = (data = {}) =>
  API.post('/statistics/tenant/jitBatchCallMaterial/export', data, {
    responseType: 'blob'
  })

// 交货计划 - 大数据批次来料接口明细表 - 查询分厂下拉
export const getSubSiteCodeListOptions = (data = {}) =>
  API.post('/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteCode', data)

// 交货计划 - 大数据批次来料接口明细表 - 查询分厂库存地点下拉
export const getSubSiteAddressOptions = (data = {}) =>
  API.post('/srm-purchase-execute/tenant/buyerJitInfo/condition?conditionType=subSiteAddress', data)

// 交货计划 - 反馈率报表 - 导出列表数据 - 采方
export const exportFeedbackRateReport = (data = {}) =>
  API.post('/srm-purchase-execute/tenant/demand/plan/buyer/tv/report/export', data, {
    responseType: 'blob'
  })

// 预测管理 - 预测反馈率报表 - 查询列表数据 - 采方
export const queryFeedbackRateReport = (data = {}) =>
  API.post('/srm-purchase-execute/internal/forecast/buyer/response/rate/report/query', data)

// 预测管理 - 预测反馈率报表 - 导出列表数据 - 采方
export const exportForecastFeedbackRateReport = (data = {}) =>
  API.post('/srm-purchase-execute/internal/forecast/buyer/response/rate/report/export', data, {
    responseType: 'blob'
  })

// 采方 - 叫料计划送货跟踪报表 - 分页
export const pagePurchaseJitReportApi = (data = {}) =>
  API.post('/statistics/tenant/BuyerJitInfoTrail/query', data)

// 采方 - 叫料计划送货跟踪报表 - 导出
export const exportPurchaseJitReportApi = (data = {}) =>
  API.post('/statistics/tenant/BuyerJitInfoTrail/export', data, {
    responseType: 'blob'
  })
