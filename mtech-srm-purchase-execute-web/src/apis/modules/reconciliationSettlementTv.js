import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'reconciliationSettlementTv'

/**
 *
  对账单查询-tv
 */

// 对账单确认和退回
export const reconUpdateStatus = (data = {}, status) =>
  API.put(`${BASE_TENANT}/reconciliation/header/updateStatus?status=${status}`, data, {})
// 删除对账单
export const reconSoftDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/softDelete`, data, {})
// 重新发布对账单
export const reconRePublish = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliation/header/rePublish`, data, {})
// 作废对账单
export const reconDiscard = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliation/header/discard`, data, {})
// 标准对账单导出
export const reconExport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/export`, data, {
    responseType: 'blob'
  })
// 寄售对账单导出
export const reconConsignmentExport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/export/consignment`, data, {
    responseType: 'blob'
  })
// 寄售进出存报表导出
export const reconConsignmentItemExport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/consignmentItem/export/report`, data, {
    responseType: 'blob'
  })
// 注塑外发对账单导出
export const reconOutgoingExport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/export/outgoing`, data, {
    responseType: 'blob'
  })
// 查询对账单下工厂
export const queryFactoryCodes = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliation/header/query/factoryCodes`, data, {})
// 标准对账单打印
export const printStandardRecon = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliation/header/print/standard?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 标准追溯对账单打印
export const printStandardRetrospectRecon = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliation/header/print/standardRetrospect?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 寄售对账单打印
export const printConsignRecon = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliation/header/print/consignment?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 寄售进出存报表可打印数据ids
export const queryFactoryPrintCodes = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/query/headerIds`, data, {})
// 寄售进出存报表打印
export const printConsignSaveReportRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/header/print/saveReport`, data, {
    responseType: 'blob'
  })
// 寄售追溯对账单打印
export const printConsignRetrospectRecon = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliation/header/print/consignRetrospect?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 注塑外发对账单打印
export const printOutGoingtRecon = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliation/header/print/outgoing`, data, {
    responseType: 'blob'
  })

// 采购对账-标准对账单明细下载
export const exportStandardItem = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/standardItem/export`, data, {
    responseType: 'blob'
  })

// 采购对账-寄售对账单明细下载
export const exportConsignmentItem = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/consignmentItem/export`, data, {
    responseType: 'blob'
  })

// 采购对账-注塑外发对账单明细下载
export const exportOutgoingItem = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/outgoingItem/export`, data, {
    responseType: 'blob'
  })

// 销售对账-分页查询
export const pageSaleReconApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/page`, data)
// 销售对账-导出
export const exportSaleReconApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/export`, data, {
    responseType: 'blob'
  })
// 销售对账-详情
export const detailSaleReconApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/detail`, data)
// 销售对账-对账明细分页查询
export const pageSaleReconItemApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/item/page`, data)
// 销售对账-对账明细导出
export const exportSaleReconItemApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/item/export`, data, {
    responseType: 'blob'
  })
// 销售对账-供方-对账明细导出
export const exportSupSaleReconItemApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/item/supplier/export`, data, {
    responseType: 'blob'
  })
// 销售对账-作废
export const refuseSaleReconApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/refuse`, data)
// 销售对账-重新发布
export const publichSaleReconApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/publich`, data)
// 销售对账-删除
export const deleteSaleReconApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/remove`, data)
// 供方-销售对账-分页查询
export const pageSaleReconSupplierApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/supplier/page`, data)
// 供方-销售对账-导出
export const exportSaleReconSupplierApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/supplier/export`, data, {
    responseType: 'blob'
  })
// 供方-销售对账-反馈
export const feedbackSaleReconSupplierApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/feedback`, data)
// 采供-销售对账-获取对账单工厂列表
export const getFactoryListApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/getFactoryList`, data)
// 采供-销售对账-打印
export const printSaleReconSupplierApi = (data = {}) =>
  API.get(`/contract/tenant/sale/reconciliation/print`, data, {
    responseType: 'blob'
  })
// 供方-销售对账-对账明细分页查询
export const pageSaleReconItemSupplierApi = (data = {}) =>
  API.post(`/contract/tenant/sale/reconciliation/item/supplier/page`, data)
// 采供-销售对账-附件保存
export const saveFileSaleReconApi = (data = {}) =>
  API.post(`/contract/tenant/reconciliation/file/save`, data)

// 外发对账-分页查询
export const pageOutReconApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/page`, data)
// 外发对账-导出
export const exportOutReconApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/export`, data, {
    responseType: 'blob'
  })
// 外发对账-重新发布
export const publichOutReconApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/publich`, data)
// 外发对账-作废
export const refuseOutReconApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/refuse`, data)
// 外发对账-删除
export const deleteOutReconApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/remove`, data)
// 采供-外发对账-打印
export const printOutReconApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/print`, data, {
    responseType: 'blob'
  })
// 采供-销售对账-获取对账单工厂列表
export const getFactoryListOutReconApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/getFactoryList`, data)

// 供方-外发对账-分页查询
export const pageOutReconSupplierApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/supplier/page`, data)
// 供方-外发对账-导出
export const exportOutReconSupplierApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/supplier/export`, data, {
    responseType: 'blob'
  })
// 供方-外发对账-反馈
export const feedbackOutReconSupplierApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/feedback`, data)

// 外发对账-详情
export const detailOutReconApi = (data = {}) =>
  API.get(`/contract/tenant/external/reconciliation/detail`, data)
// 外发对账-对账明细分页查询
export const pageOutReconItemApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/item/page`, data)
// 外发对账-对账明细导出
export const exportOutReconItemApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/item/export`, data, {
    responseType: 'blob'
  })
// 供方-外发对账-对账明细分页查询
export const pageOutReconItemSupplierApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/item/supplier/page`, data)
// 外发对账-供方-对账明细导出
export const exportSupOutReconItemApi = (data = {}) =>
  API.post(`/contract/tenant/external/reconciliation/item/supplier/export`, data, {
    responseType: 'blob'
  })
