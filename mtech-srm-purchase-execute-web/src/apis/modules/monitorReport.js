import { API } from '@mtech-common/http'

export const NAME = 'monitorReport'

// 库存监控报表-分页查询
export const pageInventoryMonitorReportApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitor/queryReport`, data)

// 库存监控报表-导出
export const exportInventoryMonitorReportApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitor/exportReport`, data, {
    responseType: 'blob'
  })
// 库存监控报表-更新
export const calculateVmiStockMonitorApi = (data = {}) =>
  API.get(`/purchase/tenant/vmiStockMonitor/calculateVmiStockMonitor`, data)

// 综合报表-分页查询
export const pageComprehensiveReportApi = (data = {}) =>
  API.post(`/contract/tenant/app/comprehensive/queryReport`, data)
// 综合报表-导出
export const exportComprehensiveReportApi = (data = {}) =>
  API.post(`/contract/tenant/app/comprehensive/exportReport`, data, {
    responseType: 'blob'
  })
