import { API } from '@mtech-common/http'

export const NAME = 'appConfig'

// 交货计划监控配置-分页查询
export const pageDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/queryPage`, data)

// 交货计划监控配置-保存
export const saveDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/save`, data)

// 交货计划监控配置-删除
export const deleteDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/delete`, data)

// 交货计划监控配置-启用
export const enableDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/enable`, data)

// 交货计划监控配置-停用
export const disableDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/disable`, data)

// 交货计划监控配置-导出
export const exportDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/export`, data, {
    responseType: 'blob'
  })

// 交货计划监控配置-导入模板
export const downloadDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.get(`/contract/tenant/goodsPlanMonitorConfig/getImportTemplate`, data, {
    responseType: 'blob'
  })

// 交货计划监控配置-导入
export const importDeliveryScheduleMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/goodsPlanMonitorConfig/import`, data, {
    responseType: 'blob'
  })

// -----------------------------------------------------------------------------

// 叫料计划监控配置-分页查询
export const pageJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/queryPage`, data)

// 叫料计划监控配置-保存
export const saveJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/save`, data)

// 叫料计划监控配置-删除
export const deleteJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/delete`, data)

// 叫料计划监控配置-启用
export const enableJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/enable`, data)

// 叫料计划监控配置-停用
export const disableJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/disable`, data)

// 叫料计划监控配置-导出
export const exportJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/export`, data, {
    responseType: 'blob'
  })

// 叫料计划监控配置-导入模板
export const downloadJitMonitorConfigApi = (data = {}) =>
  API.get(`/contract/tenant/jitInfoMonitorConfig/getImportTemplate`, data, {
    responseType: 'blob'
  })

// 叫料计划监控配置-导入
export const importJitMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/jitInfoMonitorConfig/import`, data, {
    responseType: 'blob'
  })

// -----------------------------------------------------------------------------

// 送货单监控配置-分页查询
export const pageDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/queryPage`, data)

// 送货单监控配置-保存
export const saveDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/save`, data)

// 送货单监控配置-删除
export const deleteDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/delete`, data)

// 送货单监控配置-启用
export const enableDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/enable`, data)

// 送货单监控配置-停用
export const disableDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/disable`, data)

// 送货单监控配置-导出
export const exportDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/export`, data, {
    responseType: 'blob'
  })

// 送货单监控配置-导入模板
export const downloadDeliveryMonitorConfigApi = (data = {}) =>
  API.get(`/contract/tenant/deliveryMonitorConfig/getImportTemplate`, data, {
    responseType: 'blob'
  })

// 送货单监控配置-导入
export const importDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/import`, data, {
    responseType: 'blob'
  })

// 送货单监控配置-获取详情
export const detailDeliveryMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/deliveryMonitorConfig/queryDetail`, data)

// -----------------------------------------------------------------------------

// 原材质检监控配置-分页查询
export const pageQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/queryPage`, data)

// 原材质检监控配置-保存
export const saveQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/save`, data)

// 原材质检监控配置-删除
export const deleteQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/delete`, data)

// 原材质检监控配置-启用
export const enableQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/enable`, data)

// 原材质检监控配置-停用
export const disableQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/disable`, data)

// 原材质检监控配置-导出
export const exportQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/export`, data, {
    responseType: 'blob'
  })

// 原材质检监控配置-导入模板
export const downloadQualityInspectionMonitorConfigApi = (data = {}) =>
  API.get(`/contract/tenant/materialMonitorConfig/getImportTemplate`, data, {
    responseType: 'blob'
  })

// 原材质检监控配置-导入
export const importQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/import`, data, {
    responseType: 'blob'
  })

// 原材质检监控配置-获取详情
export const detailQualityInspectionMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/materialMonitorConfig/queryDetail`, data)

// -----------------------------------------------------------------------------

// 库存监控配置-分页查询
export const pageInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/pageQuery`, data)

// 库存监控配置-保存
export const saveInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/saveConfig`, data)

// 库存监控配置-删除
export const deleteInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/delete`, data)

// 库存监控配置-启用or停用
export const updateStatusInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/changeStatus`, data)

// 库存监控配置-导出
export const exportInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/exportData`, data, {
    responseType: 'blob'
  })

// 库存监控配置-导入模板
export const downloadInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/downloadImportTemplate`, data, {
    responseType: 'blob'
  })

// 库存监控配置-导入
export const importInventoryMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/supplierInventoryMonitorConfig/importData`, data, {
    responseType: 'blob'
  })

// -----------------------------------------------------------------------------

// 订单监控配置-分页查询
export const pageOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/pageQuery`, data)

// 订单监控配置-保存
export const saveOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/saveConfig`, data)

// 订单监控配置-删除
export const deleteOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/delete`, data)

// 订单监控配置-启用or停用
export const updateStatusOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/changeStatus`, data)

// 订单监控配置-导出
export const exportOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/exportData`, data, {
    responseType: 'blob'
  })

// 订单监控配置-导入模板
export const downloadOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/downloadImportTemplate`, data, {
    responseType: 'blob'
  })

// 订单监控配置-导入
export const importOrderMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/orderConfirmMonitorConfig/importData`, data, {
    responseType: 'blob'
  })

// -----------------------------------------------------------------------------

// 首页配置-分页查询
export const pageHomePageConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/config/homeConfigPageQuery`, data)

// 首页配置-保存
export const saveHomePageConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/config/saveConfig`, data)

// 首页配置-删除
export const deleteHomePageConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/config/removeConfig/${data.id}`)

// 首页配置-修改状态(1:停用 2:启用)
export const updateStatusHomePageConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/config/changeStatus`, data)

// 首页配置-复制
export const copyHomePageConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/app/config/copyConfig/${data.id}`)

// -----------------------------------------------------------------------------

// 物料新增需求监控配置-分页查询
export const pageNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/itemAddDemandMonitorConfig/queryPage`, data)

// 物料新增需求监控配置-保存
export const saveNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/itemAddDemandMonitorConfig/save`, data)

// 物料新增需求监控配置-删除
export const deleteNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/purchase/tenant/itemAddDemandMonitorConfig/delete`, data)

// 物料新增需求监控配置-启用
export const enableNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/itemAddDemandMonitorConfig/enable`, data)

// 物料新增需求监控配置-停用
export const disableNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/itemAddDemandMonitorConfig/disable`, data)

// 物料新增需求监控配置-导出
export const exportNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/itemAddDemandMonitorConfig/export`, data, {
    responseType: 'blob'
  })

// 物料新增需求监控配置-导入模板
export const downloadNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.get(`/contract/tenant/itemAddDemandMonitorConfig/getImportTemplate`, data, {
    responseType: 'blob'
  })

// 物料新增需求监控配置-导入
export const importNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/itemAddDemandMonitorConfig/import`, data, {
    responseType: 'blob'
  })

// 物料新增需求监控配置-获取详情
export const detailNewMaterialDemandMonitorConfigApi = (data = {}) =>
  API.post(`/contract/tenant/itemAddDemandMonitorConfig/queryDetail`, data)

// -----------------------------------------------------------------------------

// 采供-周计划排产-获取日期
export const headerSchedulingApi = (data = {}) =>
  API.get(`/contract/tenant/purchase/scheduling/header`, data)

// 采方-周计划排产-分页查询
export const pageWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/page`, data)

// 采方-周计划排产-发布
export const publishWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/publish`, data)

// 采方-周计划排产-取消发布
export const unpublishWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/cancel/publish`, data)

// 采方-周计划排产-保存
export const saveWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/update`, data)

// 采方-周计划排产-确认
export const confirmWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/confirm`, data)

// 采方-周计划排产-删除
export const deleteWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/delete`, data)

// 采方-周计划排产-计算校验
export const validCalWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/cal/valid`, data)

// 采方-周计划排产-计算
export const calWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/cal`, data)

// 采方-周计划排产-导入模板
export const downloadWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/export/template`, data, {
    responseType: 'blob'
  })

// 采方-周计划排产-导入
export const importWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/excel/import`, data, {
    responseType: 'blob'
  })

// 采方-周计划排产-导出
export const exportWeeklyProductionScheduleApi = (data = {}) =>
  API.post(`/contract/tenant/purchase/scheduling/export`, data, {
    responseType: 'blob'
  })

// -----------------------------------------------------------------------------

// 供方-周计划排产-分页查询
export const pageWeeklyProductionScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/scheduling/page`, data)

// 供方-周计划排产-反馈
export const feedbackWeeklyProductionScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/scheduling/feedback`, data)

// 供方-周计划排产-保存
export const saveWeeklyProductionScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/scheduling/update`, data)

// 供方-周计划排产-导入模板
export const downloadWeeklyProductionScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/scheduling/export/template`, data, {
    responseType: 'blob'
  })

// 供方-周计划排产-导入
export const importWeeklyProductionScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/scheduling/excel/import`, data, {
    responseType: 'blob'
  })

// 供方-周计划排产-导出
export const exportWeeklyProductionScheduleSupApi = (data = {}) =>
  API.post(`/contract/tenant/supplier/scheduling/export`, data, {
    responseType: 'blob'
  })
