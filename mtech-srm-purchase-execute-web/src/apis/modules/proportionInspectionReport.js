import { API } from '@mtech-common/http'

import { STAT_TENANT } from '@/utils/constant'

export const NAME = 'proportionInspectionReport'

// ----------------------------采方-比例稽查报表-类别设置------------------------------
export const pageCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/pageQuery`, data)

export const saveCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/saveOrUpdate`, data)

export const deleteCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/delete`, data)

export const enableCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/effective`, data)

export const disableCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/invalid`, data)

export const importCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/upload`, data, {
    responseType: 'blob'
  })
export const downloadCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/templateDownload`, data, {
    responseType: 'blob'
  })
export const exportCategorySettingsApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryConfig/export`, data, {
    responseType: 'blob'
  })

// ----------------------------采方-比例稽查报表-类别品类对照关系------------------------------
export const pageCategoryComparisonRelationshipApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryTypeConfig/pageQuery`, data)

export const saveCategoryComparisonRelationshipApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryTypeConfig/saveOrUpdate`, data)

export const deleteCategoryComparisonRelationshipApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryTypeConfig/delete`, data)

export const importCategoryComparisonRelationshipApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryTypeConfig/upload`, data, {
    responseType: 'blob'
  })
export const downloadCategoryComparisonRelationshipApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryTypeConfig/templateDownload`, data, {
    responseType: 'blob'
  })
export const exportCategoryComparisonRelationshipApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectCategoryTypeConfig/export`, data, {
    responseType: 'blob'
  })

// ----------------------------采方-比例稽查报表-配额报批清单------------------------------
export const pageQuotaApprovalListApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/pageQuery`, data)

export const saveQuotaApprovalListApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/saveOrUpdateHead`, data)

// ----------------------------采方-比例稽查报表-配额报批清单-详情--------------------------
export const pageQuotaApprovalDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/pageQueryDetail`, data)

export const saveQuotaApprovalDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/saveOrUpdateDetailList`, data)

export const deleteQuotaApprovalDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/batchDelete`, data)

export const importQuotaApprovalDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/upload`, data, {
    responseType: 'blob'
  })
export const downloadQuotaApprovalDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/templateDownload`, data, {
    responseType: 'blob'
  })
export const exportQuotaApprovalDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/export`, data, {
    responseType: 'blob'
  })

export const querySupplyOrgApi = (data = {}) =>
  API.post(`${STAT_TENANT}/proportionInspectApproval/querySupplyOrg`, data)

// ----------------------------采方-比例稽查报表-汇总------------------------------
export const pageAuditReportListApi = (data = {}) =>
  API.post(`${STAT_TENANT}/QuotaProportionInspect/pageQuery`, data)
export const exportAuditReportListApi = (data = {}) =>
  API.post(`${STAT_TENANT}/QuotaProportionInspect/headExport`, data, {
    responseType: 'blob'
  })
// ----------------------------采方-比例稽查报表-明细------------------------------
export const pageAuditReportDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/QuotaProportionInspect/pageQueryDetail`, data)
export const exportAuditReportDetailApi = (data = {}) =>
  API.post(`${STAT_TENANT}/QuotaProportionInspect/detailExport`, data, {
    responseType: 'blob'
  })
