import { API } from '@mtech-common/http'
import { BASE_CONTRACT, BASE_TENANT } from '@/utils/constant'

export const NAME = 'contract'

//从合同进入根据key查询合同列表
export const getOrderKey = (key = {}) => API.get(`${BASE_CONTRACT}/contract-item/order/key/${key}`)
// 查询合同列表
export const queryPurchasePage = (data = {}) =>
  API.post(`${BASE_CONTRACT}/contract/purchase/page`, data)

// 根据合同名称模糊查询合同列表
export const getContractByName = (data = {}) =>
  API.get(`${BASE_CONTRACT}/contract/purchase/order/query`, data)

// 标准合同打印查询
export const supplierContrantPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/printQuery`, data)

// 港料合同打印查询
export const supplierPrintHKOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/printQueryHK`, data)

// 根据合同单号获取合同信息
export const getByContractCodeApi = (data = {}) =>
  API.get(`${BASE_CONTRACT}/contract/purchase/getByContractCode`, data)

// 根据rfxCode查询询价单
export const findByRfxCodeApi = (data = {}) =>
  API.get(`/sourcing/tenant/rfxHeader/findByRfxCode`, data)

// 根据合同查询合同明细
export const getContractDetailApi = (data = {}) =>
  API.get(`/contract/contract/purchase/getContractDetail`, data)

// 根据合同编码,采购明细查询合同明细
export const getItemByRequestInfoApi = (data = {}) =>
  API.post(`/contract/contract-item/getItemByRequestInfo`, data)
