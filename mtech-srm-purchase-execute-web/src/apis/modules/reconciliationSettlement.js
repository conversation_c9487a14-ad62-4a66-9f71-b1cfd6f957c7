import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'
export const NAME = 'reconciliationSettlement' // 对账 - 采方

// 根据ID取消发布(采方创建)
export const cancelPublishReconciliation = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/cancelPublishById`, data)

// 根据ID关闭(采方创建)
export const closeReconciliation = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/closeById?BU_CODE=${data.currentBu}`, data)

// 根据ID发布(采方创建)
export const publishReconciliation = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/publishById`, data)

// 数量待对账明细
export const numberReconExcelExport = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/wait/download`, data, {
    responseType: 'blob'
  })

// 根据ID标记冻结
export const putReconciliationWaitFrozenById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationWait/frozenById?BU_CODE=${data.currentBu}`, data)

// 根据ID取消标记冻结
export const putReconciliationWaitCancelFrozenById = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationWait/cancelFrozenById?BU_CODE=${data.currentBu}`, data)

// 批量创建对账单
export const putReconciliationHeaderBatchCreate = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/batchCreate`, data)

// 根据docType和docI查询所有文件信息
export const getReconciliationHeaderQueryFileByDocIdAndDocType = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliationHeader/queryFileByDocIdAndDocType`, data)

/**
 *  对账单主单接口
 *  */
//  保存对账单(采方创建)
export const putReconciliationHeaderCreate = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/create?BU_CODE=${data.currentBu}`, data)

//  保存清账对账单(采方创建)
export const putReconciliationHeaderCreateSettled = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/createSettled`, data)

//  修改对账单(采方)
export const updateStatementStatus = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/updateStatus`, data)

// 创建过期清账
export const putCreateOverdue = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/createSettled`, data)

// 查询-待对账明细信息-不分页
export const postReconciliationwaitQueryreconciliationwaitlist = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationWait/queryReconciliationWaitList`, data)

// 根据ID批量反馈正常_批量接受(采方创建)
export const putBatchFeedbackNormal = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/batchFeedbackNormal`, data)

// 根据ID反馈正常_接受(采方创建)
export const putFeedbackNormal = (data = {}) =>
  // API.put(`${BASE_TENANT}/reconciliationHeader/feedbackNormal`, data);
  API.put(`${BASE_TENANT}/reconciliationHeader/feedbackNormalNew`, data) // 根据ID反馈正常_接受_新

// 根据ID反馈异常_拒绝(采方创建)
export const putFeedbackAbnormal = (data = {}) =>
  // API.put(`${BASE_TENANT}/reconciliationHeader/feedbackAbnormal`, data);
  API.put(`${BASE_TENANT}/reconciliationHeader/feedbackAbnormalNew`, data)
// 根据ID反馈异常_拒绝(采方创建)
export const putBatchFeedbackAbnormal = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationHeader/batchFeedbackAbnormalNew`, data)

// 同步待对账数据至供应商
export const getReconciliationWaitSyncToSup = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliationWait/syncToSup?BU_CODE=${data.currentBu}`, data)

/**
 * 对账类型接口
 */
// 采购对账类型列表
export const getReconTypeData = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliation/type/query`, data)

// 采购对账类型 保存 编辑
export const saveReconType = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/type/save`, data)

// 对账类型删除
export const deleteReconType = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/type/del`, data)

// 获取对账类型 （根据id获取详情）
export const getDetailReconType = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliation/type/${data}`)

/**
 * 往来对账科目汇总
 */

//  保存
export const saveCourseCollect = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationSummaryAccount/saveSummaryAccount`, data)

// 更新
export const updateCourseCollect = (data = {}) =>
  API.put(`${BASE_TENANT}/reconciliationSummaryAccount/updateSummaryAccount`, data)

// 删除
export const deleteCourseCollect = (data = {}) =>
  API.delete(`${BASE_TENANT}/reconciliationSummaryAccount/batchDeleteByIds`, data)

/**
 * 对象类型配置字段接口
 */
// 采购对账字段配置
export const postReconciliationConfigFieldQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/config/field/query`, data)

// 保存配置
export const saveReconConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/config/field/save`, data)

// 采购对账字段配置（缓存） -- tab使用
export const queryReconConfig = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliation/config/field/queryCache`, data)

/**
 * 对账类型接口
 */
// 采购对账类型（待对账tab使用）
export const getTypeTab = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliation/type/queryTab`, data)

// 租户级-往来对账单-采方-批量快速创建往来对账单并发布
export const postTransactionReconciliationBatchSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/batch-save`, data)

// 租户级-往来对账单-采方-获取期初余额
export const postTransactionReconciliationPreSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/preSave`, data)

// 租户级-往来对账单-采方-创建往来对账单并发布
export const postTransactionReconciliationSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/save`, data)

// 租户级-往来对账单-采方-采购确认往来对账单
export const postTransactionReconciliationFinish = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/finish`, data)
// 租户级-往来对账单-采方-财务确认往来对账单
export const postTransactionReconciliationFinace = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/finance`, data)
// 租户级-往来对账单-采方-采购退回往来对账单
export const postTransactionReconciliationBack = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/back`, data)
// 租户级-往来对账单-采方-财务退回往来对账单
export const postTransactionReconciliationFinanceBack = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/financeBack`, data)
// 租户级-往来对账单-采方-财务驳回往来对账单
export const postTransactionReconciliationFinanceReject = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/reject`, data)

// 租户级-往来对账单-采方-查询对账单详情
export const postTransactionReconciliationDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/detail`, data)

// 租户级-往来对账单-采方-关闭往来对账单
export const postTransactionReconciliationClose = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/close`, data)

// 租户级-往来待对账-根据公司供应商等查询列表
export const postTransactionReconciliationWaitFindList = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/wait/find-list`, data)

// 租户级-往来对账通知配置-保存往来对账配置
export const postTransactionReconciliationSupplierConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_supplier_config/save`, data)

// 租户级-往来对账通知配置-删除往来对账配置
export const postTransactionReconciliationSupplierConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_supplier_config/delete`, data)

// 租户级-往来对账通知配置-往来对账配置下载
export const postTransactionReconciliationSupplierConfigDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_supplier_config/download`, data, {
    responseType: 'blob'
  })

// 租户级-往来对账通知配置-往来对账配置上传
export const postTransactionReconciliationSupplierConfigUpload = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_supplier_config/upload`, data, {
    responseType: 'blob'
  })

// 租户级-往来对账单差异调整项接口-批量删除
export const deleteTransactionReconciliationAdjustBatchDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/transaction-reconciliation-adjust/batch-delete`, data)

// 租户级-往来对账单差异调整项接口-新增或修改
export const postTransactionReconciliationAdjustBatchSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction-reconciliation-adjust/save`, data)

// 租户级-往来对账单-采方-再次发布往来对账单
export const postTransactionReconciliationRePublish = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/re-publish`, data)

// 租户级-往来对账单-采方-再次发布往来对账单
export const putTransactionReconciliationSaveFile = (data = {}) =>
  API.put(`${BASE_TENANT}/transaction_reconciliation/saveFile`, data)

// 租户级-往来对账单-附件管理-保存文件信息
export const postTransactionReconciliationFilesSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/files/save`, data)

// 租户级-往来对账单-附件管理-保存文件信息 - 供方附件
export const postTransactionReconciliationFilesSupplierSave = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/files/saveSupplierFile`, data)

// 租户级-往来对账通知配置-发送往来对账通知
export const postTransactionReconciliationSupplierConfigSendNotice = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation_supplier_config/send-notice`, data)
// 往来对账单 打印
export const printContactRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/print`, data, {
    responseType: 'blob'
  })
/**
 * 销售对账
 */
// 创建销售对账单  销售对账单主单-创建销售对账单
export const createSateRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/create`, data)
// 创建销售对账单  销售对账单主单-批量创建销售对账单
export const createSaleByBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/batchCreate`, data)

// 根据包数据，获取销售对账单明细行  销售待对账明细接口-销售待对账明细
export const getSaleRowsByPackage = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/wait/package/detail`, data)

// 包 创建对账单  销售对账单主单-创建销售对账单
export const createSaleByPackage = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/createByPackage`, data)

// 关闭销售对账单 销售对账单主单 - 关闭
export const closeSaleRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/close`, data)

// 确认销售对账单 销售对账单主单 - 确认
export const confirmSaleRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/confirm`, data)

// 发布 销售对账单 销售对账单主单 - 发布
export const publishSaleRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/publish`, data)

// 根据销售对账单id，查明细  销售对账单主单 - 获取销售对账单
export const getSaleDetailById = (data = {}) =>
  API.get(`${BASE_TENANT}/sale/reconciliation/${data}`)

// 销售对账单 重新推送
export const rePushSaleRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/external/reconciliation/sale/push`, data)

// 销售对账单 冲销
export const writeOffSaleRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/external/reconciliation/sale/write_off`, data)

// 期初余额维护  租户级 - 往来对账 - 初期余额维护
// 新增
export const addInitBalance = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/balance/add`, data)
// 编辑
export const editInitBalance = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/balance/edit`, data)
// 删除
export const deleteInitBalance = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/balance/deleteBalance`, data)
// 导入
export const importInitBalance = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/balance/upload`, data, {
    responseType: 'blob'
  })

// 导出
export const exportInitBalance = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/balance/downloadBalance`, data, {
    responseType: 'blob'
  })

/**
 * 销售价格维护  销售价格主单接口
 */
// 保存草稿
export const saveSalePriceDraft = (data = {}) =>
  API.post(`${BASE_TENANT}/selling_price/draft`, data)

// 提交草稿
export const submitSalePriceDraft = (data = {}) =>
  API.post(`${BASE_TENANT}/selling_price/save`, data)

// 获取详情
export const getSalePriceDetail = (data = {}) => API.get(`${BASE_TENANT}/selling_price/${data}`)

// 删除
export const delSalePrice = (data = {}) => API.post(`${BASE_TENANT}/selling_price/del`, data)

// 销售价格维护 批量提交
export const batchSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/selling_price/batchStartProcess`, data)

// 销售价格维护空调明细 - 导出
export const salePriceExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/selling_price/item-excel-kt-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 销售价格维护重推sap
export const pushSAP = (data = {}) => API.post(`${BASE_TENANT}/selling_price/pushSAP`, data)

// 下载模板  销售价格维护
export const downloadTemp = (data = {}) =>
  API.get(`${BASE_TENANT}/selling_price/template`, data, {
    responseType: 'blob'
  })

// 下载模板  销售价格维护 空调
export const downloadKtTemp = (data = {}) =>
  API.get(`${BASE_TENANT}/selling_price/kt-template`, data, {
    responseType: 'blob'
  })

// 上传
export const uploadEx = (data = {}) =>
  API.post(`${BASE_TENANT}/selling_price/import`, data, {
    responseType: 'blob'
  })

// 上传 空调
export const uploadKtEx = (data = {}) =>
  API.post(`${BASE_TENANT}/selling_price/ktImport`, data, {
    responseType: 'blob'
  })

/**
 * 数量对账
 */
// 数量待对账 - 汇总查询明细
export const getNumberByPackage = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/wait/list-in-summary`, data)

// 数量待对账 - 获取数量汇总
export const getSummaryQuantity = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/wait/waitSummaryQuantity`, data)
// 数量对账单 打印
export const printNumRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/print`, data, {
    responseType: 'blob'
  })
// 创建明细
export const createNumberDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/save`, data)

// 按汇总创建对账单
export const createNumberAll = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/fast-create`, data)

// 对账单：根据id获取详情
export const getNumberReconDetail = (data = {}) =>
  API.get(`${BASE_TENANT}/quantity/reconciliation/${data}`)

// 采方-数量对账单获取头信息
export const detailReconciliationApi = (data = {}) =>
  API.get(`${BASE_TENANT}/quantity/reconciliation/header/${data.id}`)

// 采方-数量对账单详情页-明细列表
export const getDetailReconciliationApi = (data = {}) =>
  API.get(`${BASE_TENANT}/quantity/reconciliation/detail/itemList/${data.id}`)

// 采方-数量对账单详情页-明细分页列表
export const pageDetailReconciliationApi = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/detail/itemPage`, data)

// 采方-数量对账单数量汇总
export const summaryQuantityApi = (data = {}) =>
  API.get(`${BASE_TENANT}/quantity/reconciliation/detail/summaryQuantity/${data.id}`)

// 采方-数量对账单详情页-相关附件
export const filesDetailReconciliationApi = (data = {}) =>
  API.get(`${BASE_TENANT}/quantity/reconciliation/detail/files/${data.id}`)

// 对账单：发布
export const publishNumberRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/publish`, data)

// 对账单：取消发布
export const cancelPublishNumberRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/cancel`, data)

// 对账单：确认
export const confirmNumberRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/confirm`, data)

// 对账单：关闭
export const closeNumberRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/close`, data)

// 采购对账单 打印
export const printRecon = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/print?BU_CODE=${data.currentBu}`, data, {
    responseType: 'blob'
  })

// 采购对账单 人力外包打印
export const printHRORecon = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/hro/print`, data, {
    responseType: 'blob'
  })

// 采购对账单 人力外包对账单同步SAP
export const syncReconciliationHroInfoToSap = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/hro/syncReconciliationHroInfoToSap`, data)

// 采购对账单 人力外包打印 - 供方
export const printHROReconSup = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/hro/print`, data, {
    responseType: 'blob'
  })

// 采购对账-对账单明细下载
export const postReconDownload = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliationHeader/download?headerId=${data.query.headerId}&BU_CODE=${data.currentBu}`,
    data.body,
    {
      responseType: 'blob'
    }
  )

// 采购对账-对账单上传修改备注
export const reconImport = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/import`, data, {
    responseType: 'blob'
  })

// 采购对账-对账明细-不分页
export const reconItemNoPageFlag = (data = {}) =>
  API.get(`${BASE_TENANT}/reconciliationItem/queryBuilderNoPageFlag?headerId=${data.headerId}`)

// 异常确认
export const abnormalConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/abnormalConfirm`, data)
// 异常处理
export const abnormalHandle = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/abnormalDeal`, data)

// 采购待对账 - 导出
export const reconciliationHeaderExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/reconciliationWait/export?includeColumnFiledNames=${field}&BU_CODE=${data.currentBu}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 采购对账单明细 - 导出
export const reconciliationHeaderListDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/download`, data, {
    responseType: 'blob'
  })
// 采购对账单 - 导出
export const reconciliationHeaderDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/export `, data, {
    responseType: 'blob'
  })
// 采购对账单查询 - 导出
export const reconciliationHeaderQueryDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/exportForCondition `, data, {
    responseType: 'blob'
  })

// 数量对账单 - 导出
export const reconciliationQuantityExport = (data = {}) =>
  API.post(`${BASE_TENANT}/quantity/reconciliation/export`, data, {
    responseType: 'blob'
  })
// 数量对账单-明细 - 导出
export const reconciliationQuantityListExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/quantity/reconciliation/exportItem?headerId=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
// 数量对账单-汇总 - 导出
export const reconciliationQuantitySummaryExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/quantity/reconciliation/exportSummaryData?headerId=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
// 销售对账单 - 导出
export const reconciliationSaleExport = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/export`, data, {
    responseType: 'blob'
  })
// 销售对账单明细 - 导出
export const reconciliationDetailxport = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/item/export`, data, {
    responseType: 'blob'
  })
// 销售待对账 - 导出
export const reconciliationSaleDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/sale/reconciliation/wait/export`, data, {
    responseType: 'blob'
  })
// -明细 - 导出
export const reconciliationSaleListExport = (data = {}) =>
  API.post(
    `${BASE_TENANT}/sale/reconciliation/exportItem?headerId=${data}`,
    {},
    {
      responseType: 'blob'
    }
  )
// 往来对账单 - 导出
export const reconciliationTransactionExport = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/export`, data, {
    responseType: 'blob'
  })
// 往来待对账 - 导出
export const reconciliationTransactionDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/wait/export`, data, {
    responseType: 'blob'
  })
// 采购对账获取金额
export const getAmount = (data = {}) =>
  API.post(
    `${BASE_TENANT}/reconciliationHeader/summaryReconciliationWait?BU_CODE=${data.currentBu}`,
    data
  )
// 过期清账获取金额
export const getClearAmount = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/summarySettledReconciliationWait`, data)
//根据id查询对账明细
export const getPurReconciliationItemById = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationItem/queryBuilder`, data)
// 人力外包对账单-汇总对账单
export const reconciliationSummaryStatement = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/hro/reconciliationSummaryStatement`, data)
// 人力外包对账单-租赁费对账单
export const rentalFeeSummaryStatement = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeader/hro/rentalFeeSummaryStatement`, data)
// 人力外包对账单-汇总对账单-供方
export const reconciliationSummaryStatementSup = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/hro/reconciliationSummaryStatement`, data)
// 人力外包对账单-租赁费对账单-供方
export const rentalFeeSummaryStatementSup = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationHeaderSupplier/hro/rentalFeeSummaryStatement`, data)

// 历史查询-标准-导出
export const exoprtHistoryStander = (data = {}) =>
  API.post(`${BASE_TENANT}/reconciliationItem/exportItemHistory`, data, {
    responseType: 'blob'
  })

// TV往来待对账 - 导出
export const reconciliationTransactionDownloadTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionWait/export`, data, {
    responseType: 'blob'
  })

// TV往来待对账 - 可生产往来对账单供应商 - 导出
export const reconciliationTransactionDownloadSupplierTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionWait/payableSum/export`, data, {
    responseType: 'blob'
  })

// TV往来待对账 - 创建对账单
export const reconciliationTransactionBatchCreateTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transaction/batchCreate`, data)

// 租户级-TV往来对账单-采方-财务确认往来对账单
export const postTransactionReconciliationFinaceTv = (data = {}, remark = '') =>
  API.post(`${BASE_TENANT}/tv/transaction/fiance/confirm?remark=${remark}`, data)

// 租户级-TV往来对账单-采方-财务退回往来对账单
export const postTransactionReconciliationFinanceBackTv = (data = {}, reason = '') =>
  API.post(`${BASE_TENANT}/tv/transaction/fiance/reject?reason=${reason}`, data)

// TV往来对账单 - 导出
export const reconciliationTransactionExportTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transaction/header/export`, data, {
    responseType: 'blob'
  })

// 租户级-TV往来对账单-采方-查询对账单详情
export const postTransactionReconciliationDetailTv = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transaction/detail`, data)

// 租户级-TV往来对账单差异调整项接口-批量删除
export const deleteTransactionReconciliationAdjustBatchDeleteTv = (data = {}) =>
  API.delete(`${BASE_TENANT}/tv/transactionAdjust/common/delete`, data)

// 租户级-TV往来对账单差异调整项接口-新增
export const postTransactionReconciliationAdjustAdd = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionAdjust/common/add`, data)

// 租户级-TV往来对账单差异调整项接口-编辑
export const postTransactionReconciliationAdjustUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/tv/transactionAdjust/common/update`, data)

// 往来待对账 - 批量删除
export const patchDeleteApi = (data = {}) =>
  API.post(`${BASE_TENANT}/transaction_reconciliation/batchClose`, data)

// -------------------------------采方-往来对账通知------------------------------
export const importReconNoticeTVApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/notice/import`, data, {
    responseType: 'blob'
  })
export const downloadReconNoticeTVApi = (data = {}) =>
  API.get(`${BASE_TENANT}/contact/reconciliation/notice/download`, data, {
    responseType: 'blob'
  })
export const createReconNoticeTVApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/create/reconciliation`, data)
// -------------------------------采方-往来对账汇总------------------------------
export const pageReconSummaryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/header/list`, data)
export const createReconSummaryApi = (data = {}) =>
  API.get(`${BASE_TENANT}/contact/reconciliation/createItem`, data)
export const feedbackReconSummaryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/feedback`, data)
export const exportReconSummaryApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/header/export`, data, {
    responseType: 'blob'
  })
// -------------------------------采方-可生成往来对账单------------------------------
export const pageContactReconciliationSupplierApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/supplier/list`, data)
export const exportContactReconciliationSupplierApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/export/supplier/list`, data, {
    responseType: 'blob'
  })
// -------------------------------采方-往来对账明细------------------------------
export const pageReconDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/query/wait/list`, data)
export const exportReconDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/export/wait/list`, data, {
    responseType: 'blob'
  })
// -------------------------------供方-往来对账反馈------------------------------
export const pageContactReconciledSupplierNewApi = (data = {}) =>
  API.post(`${BASE_TENANT}/contact/reconciliation/supplier/header/list`, data)

// ------------------------------采方-越南日结对账-标准---------------------------
export const pageVietnamReconStandardApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/standard/list`, data)
// 明细
export const detailVietnamReconApi = (data = {}) =>
  API.get(`/contract/tenant/daily/reconciliation/header/detail`, data)
// 重新发布
export const rePublishVietnamReconApi = (data = {}) =>
  API.put(`/contract/tenant/daily/reconciliation/rePublish`, data)
// 作废
export const discardVietnamReconApi = (data = {}) =>
  API.put(`/contract/tenant/daily/reconciliation/discard`, data)
// 删除
export const deleteVietnamReconApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/delete`, data)
// 确认前校验
export const validVietnamReconApi = (data = {}) =>
  API.post(`/contract/tenant/external/invoice/checkAmount`, data)
// 2确认/3退回
export const updateStatusVietnamReconApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/updateStatus`, data)
// 导出
export const exportVietnamReconStandardApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/standard/export`, data, {
    responseType: 'blob'
  })
// 查询对账单下工厂
export const queryFactoryCodesVietnamReconApi = (data = {}) =>
  API.get(`/contract/tenant/daily/reconciliation/query/factoryCodes`, data)
// 打印（追溯）
export const printVietnamReconStandardRetrospectApi = (data = {}) =>
  API.post(
    `/contract/tenant/daily/reconciliation/print/standardRetrospect?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 打印
export const printVietnamReconStandardApi = (data = {}) =>
  API.post(
    `/contract/tenant/daily/reconciliation/print/standard?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 生成月结对账单
export const createMonthReconciliationApi = (data = {}) =>
  API.get(`/contract/tenant/daily/reconciliation/createMonthReconciliation`, data)
// ------------------------------采方-越南日结对账-寄售---------------------------
export const pageVietnamReconConsignmentApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/consignment/list`, data)
// 导出
export const exportVietnamReconConsignmentApi = (data = {}) =>
  API.post(`contract/tenant/daily/reconciliation/consignment/export`, data, {
    responseType: 'blob'
  })
// 打印（追溯）
export const printVietnamReconConsignRetrospectApi = (data = {}) =>
  API.post(
    `/contract/tenant/daily/reconciliation/print/consignRetrospect?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 打印
export const printVietnamReconConsignmentApi = (data = {}) =>
  API.post(
    `/contract/tenant/daily/reconciliation/print/consignment?headerId=${data.headerId}&factoryCode=${data.factoryCode}`,
    data,
    {
      responseType: 'blob'
    }
  )
// ------------------------------采方-越南日结对账-寄售进出存报表---------------------------
export const pageVietnamReconReportApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/query/report`, data)
// 导出
export const exportVietnamReconReportApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/export/report`, data, {
    responseType: 'blob'
  })
// 寄售进出存报表可打印数据ids
export const headerIdsVietnamReconReportApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/query/headerIds`, data)
// 打印
export const printVietnamReconReportApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/print/saveReport`, data, {
    responseType: 'blob'
  })

// ------------------------------采供-越南日结对账-明细---------------------------
// 查询标准对账明细
export const pageVietnamReconDetailStandardApi = (data = {}) =>
  API.post(`/contract/tenant/daily/standard/reconciliation/item/query`, data)
// 标准对账明细导出
export const exportVietnamReconDetailStandardApi = (data = {}) =>
  API.post(`/contract/tenant/daily/standard/reconciliation/item/export`, data, {
    responseType: 'blob'
  })
// 查询寄售对账明细
export const pageVietnamReconDetailConsignmentApi = (data = {}) =>
  API.post(`/contract/tenant/daily/consignment/reconciliation/item/query`, data)
// 寄售对账明细导出
export const exportVietnamReconDetailConsignmentApi = (data = {}) =>
  API.post(`/contract/tenant/daily/consignment/reconciliation/item/export`, data, {
    responseType: 'blob'
  })

// ------------------------------采方-越南日结对账-待对账明细---------------------------
export const pageVietnamReconDetailApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/wait/query`, data)
// 导出
export const exportVietnamReconDetailApi = (data = {}) =>
  API.post(`/contract/tenant/daily/reconciliation/wait/export`, data, {
    responseType: 'blob'
  })
