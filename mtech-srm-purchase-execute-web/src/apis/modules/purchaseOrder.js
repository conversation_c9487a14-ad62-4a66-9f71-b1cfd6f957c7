import { API } from '@mtech-common/http'

import { BASE_TENANT, IAM_TENANT, PRICE_TENANT } from '@/utils/constant'

export const NAME = 'purchaseOrder'
//  -------供方采购start-------
//供方查询明细
export const soDetailQuery = (data = {}) => API.post(`${BASE_TENANT}/so/detail/query`, data)

//供方导出明细
export const soDetailDownload = (data = {}, params) =>
  API.post(`${BASE_TENANT}/so/detail/download`, data, {
    responseType: 'blob',
    params
  })

//供方导出明细 - 新模式
export const newModeSoDetailDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/so/detail/downloadNewType`, data, {
    responseType: 'blob'
  })

//采方导出明细
export const poDetailDownload = (data = {}, params) =>
  API.post(`${BASE_TENANT}/po/detail/download`, data, {
    responseType: 'blob',
    params
  })
// 供方采购订单查询
export const supOrderQuery = (data = {}) => API.post(`${BASE_TENANT}/supOrder/query`, data)
// 明细视图获取表头
export const getbusinessModuleFields = (data = {}) =>
  API.get(`${BASE_TENANT}/business/module/fields`, data)
//业务类型查询供方
export const getSupOrderQueryTab = (data = {}) => API.get(`${BASE_TENANT}/supOrder/queryTab`, data)
//业务类型查询
export const getBusinessType = (data = {}) =>
  API.post(`${IAM_TENANT}/user/permission/summary/queryPermissionList`, data)
// 供方采购订单根据ID获取顶部详情
export const getSupOrder = (id = {}) => API.get(`${BASE_TENANT}/supOrder/${id}`)

// 供方采购订单上传送货单
export const uploadDeliverOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/upLoadDeliveryFile`, data)

// 供方 - 采购订单 - 新模式 - 根据ID获取顶部详情
export const getnewModeSupOrder = (id = {}) => API.get(`${BASE_TENANT}/supOrder/${id}`)

// 获取模块配置
export const getModuleConfig = (data = {}) => API.get(`${BASE_TENANT}/supOrder/module/config`, data)
// 获取订单历史版本号
export const getVersion = (data = {}) => API.post(`${BASE_TENANT}/supOrder/version`, data)
// 根据订单编号及版本获取订单
export const getSupOrderHistory = (data) => API.post(`${BASE_TENANT}/supOrder/history`, data)

// 根据订单编号及版本获取订单 - 新模式
export const getNewModeSupOrderHistory = (data) =>
  API.post(`${BASE_TENANT}/supOrder/historyNewType`, data)

// 获取模块配置详情
export const getModuleData = (data = {}) => API.post(`${BASE_TENANT}/supOrder/module/data`, data)
//批量反馈订单
export const supOrderbatchFeedback = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/batchFeedback`, data)
// 批量确认
export const supOrderConfirm = (data = {}) => API.post(`${BASE_TENANT}/supOrder/confirm`, data)
// 批量拒绝
export const supOrderRefuse = (data = {}) => API.post(`${BASE_TENANT}/supOrder/refuse`, data)
// 供方反馈采方订单接受或者拒绝右上角
// /tenant/supOrder/feedBack
export const supOrderFeedBack = (data = {}) => API.post(`${BASE_TENANT}/supOrder/feedBack`, data)
// export const getSupOrderFeedBack = (id = {}) =>
//   API.get(`${BASE_TENANT}/supOrder/feedBack/${id}`);

//  -------供方采购end-------

// -------采购订单start-------
// 批量保存订单配置
export const poTimeConfigSaveBatch = (data = {}) =>
  API.post(`${BASE_TENANT}/po/time/config/saveBatch`, data)
//上传订单配置
export const poTimeConfigUpload = (data = {}) =>
  API.post(`${BASE_TENANT}/po/time/config/upload`, data, {
    responseType: 'blob'
  })
// 订单配置下载
export const poTimeConfigDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/po/time/config/download`, data, {
    responseType: 'blob'
  })
// 保存采方订单配置
export const poTimeConfigSave = (data = {}) => API.post(`${BASE_TENANT}/po/time/config/save`, data)
// 删除采方订单配置
export const poTimeConfigDel = (data = {}) => API.post(`${BASE_TENANT}/po/time/config/del`, data)
// 采方订单配置启用禁用
export const poTimeConfigSwitch = (data = {}) =>
  API.post(`${BASE_TENANT}/po/time/config/switch`, data)
// 模糊查询订单号
export const purOrderQueryOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/queryOrder`, data)
// 模糊查询订单号(非关闭)
export const getPurOrderQueryOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/queryOrderByCompany?enterpriseId=` + data.enterpriseId, data)
// 根据采购订单号获取委外方式
export const getOutsourcedType = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/queryOrderByCompanyNew?enterpriseId=` + data.enterpriseId, data)
// 通过订单号查询订单明细数据
export const purOrderGetByOrder = (data = {}) => API.get(`${BASE_TENANT}/supOrder/getByOrder`, data)
// 订单主单加急
export const purOrderUrgentOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/urgentOrder`, data)

// 订单确认归档
export const orderCommitArchive = (data = {}) =>
  API.post(`/statistics/tenant/po/archive/commitArchive`, data)
// 订单取消归档
export const orderCancelArchive = (data = {}) =>
  API.post(`/statistics/tenant/po/archive/cancelArchive`, data)
// 订单主单加急
export const purOrderUrgent = (data = {}) => API.post(`${BASE_TENANT}/purOrder/urgent`, data)
// 订单主单取消加急
export const purOrderCancelUrgent = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/cancelUrgent`, data)
// 订单明细加急
export const poDetailUrgent = (data = {}) => API.post(`${BASE_TENANT}/po/detail/urgent`, data)
// 订单明细取消加急
export const poDetailCancleUrgent = (data = {}) =>
  API.post(`${BASE_TENANT}/po/detail/cancelUrgent`, data)
// 根据采购商城ID和业务类型查询采购订单数据
export const queryRequestModelPo = (data = {}) =>
  API.post(`${BASE_TENANT}/mall/queryRequestModelPo`, data)
//自定义参数规则并单校验 新增订单和从合同过来新增订单 从合同过来编辑订单使用
export const purchaseToOrderConsolidationMatch = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/purchaseToOrderConsolidationMatch`, data)
// 下载采购订单明细
export const purOrderDownload = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/download`, data, {
    responseType: 'blob'
  })
//上传采购订单
export const purOrderUpload = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/upload`, data, {
    responseType: 'blob'
  })
// 下载订单模板
export const purOrderTemplateDownload = (data = {}) =>
  API.get(`${BASE_TENANT}/purOrder/template/download`, data, {
    responseType: 'blob'
  })
// 根据价格协议查询含税单价 未税单价 税率
export const priceCalc = (data = {}) => API.post(`${BASE_TENANT}/order/price/calc`, data)
// 按照物料 和 工厂地点 供应商 查询价格协议
export const queryPrice = (data = {}) => API.post(`${BASE_TENANT}/order/price/query`, data)
//申请和合同进入批量查询价格
export const queryAndCompute = (data = {}) =>
  API.post(`${PRICE_TENANT}/compute/order/queryAndCompute`, data)
// 根据采购申请idlist 查询采购申请数据
export const requestItemBatchQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/batchQuery`, data)
// 明细列表获取动态表头
export const getPeBuinessConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/pe/business/config/detail`, data)
// 采购订单验收计划 验收类型   验收项配置
export const getOrderAcceptanceStrategy = (data = {}) =>
  API.get(`${BASE_TENANT}/orderAcceptanceStrategy/query`, data)
// 验收计划推送第三方挂账报销
export const pushReimbursement = (data = {}) =>
  API.post(`${BASE_TENANT}/external/acceptance/pushReimbursement`, data)
// 验收计划推送第三方共享财务
export const pushSharedFinance = (data = {}) =>
  API.post(`${BASE_TENANT}/external/acceptance/pushSharedFinance`, data)
//提前开票
export const acceptanceAdvance = (data = {}) =>
  API.post(`${BASE_TENANT}/po/acceptance/advance`, data)
// 验收
export const acceptanceAccept = (data = {}) => API.post(`${BASE_TENANT}/po/acceptance/accept`, data)
// 撤销验收
export const acceptanceUnAccept = (data = {}) =>
  API.post(`${BASE_TENANT}/po/acceptance/unAccept`, data)
// 确认驳回验收
export const acceptanceUnConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/po/acceptance/confirm?acceptanceStatus=${data.acceptanceStatus}`, data)
// 取消提前开票
export const acceptanceUnAdvance = (data = {}) =>
  API.post(`${BASE_TENANT}/po/acceptance/unAdvance`, data)
// 采方根据订单id获取订单
export const getTenantPurOrder = (id = {}) => API.get(`${BASE_TENANT}/purOrder/${id}`)
// 采方根据订单code获取订单
export const getTenantPurOrderCode = (id = {}) => API.get(`${BASE_TENANT}/purOrder/code/${id}`)
// 关闭订单
export const poDetailClose = (data = {}) => API.post(`${BASE_TENANT}/po/detail/close`, data)
//发布订单
export const purOrderPublish = (data = {}) => API.post(`${BASE_TENANT}/purOrder/publish`, data)
// 订单审批
export const purOrderApprove = (data = {}) => API.post(`${BASE_TENANT}/purOrder/approve`, data)
// 完成订单
export const purOrderDone = (data = {}) => API.post(`${BASE_TENANT}/purOrder/done`, data)
// /采方订单模块配置编辑的时候 获取采购订单单据模块定义
export const getPurOrderModuleConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/purOrder/module/config`, data)
// 获取采购订单单据模块定义
export const getBuyerSaleServiceOrderModuleConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerSaleServiceOrder/module/config`, data)
// 采方订单模块配置新增的时候
export const getPebusinessConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/pe/business/config/detail`, data)
// 获取采方模块配置详情
export const getPurOrderModuleData = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/module/data`, data)
//编辑进入需要查询附件左边节点
export const getFileNodeByDocId = (data = {}) =>
  API.get(`${BASE_TENANT}/file/queryFileNodeByDocId`, data)
//编辑进入需要查询附件列表
export const queryFileByDocId = (data = {}) => API.get(`${BASE_TENANT}/file/queryFileByDocId`, data)
// 采方订单明细分摊信息暂未用到
export const getOrderCostId = (id = '', detailId = '') =>
  API.get(`${BASE_TENANT}/orderCost/${id}/${detailId}`)
// 保存草稿订单
export const purOrderDraft = (data = {}) => API.post(`${BASE_TENANT}/purOrder/draft`, data)
// 保存订单
export const purOrderSave = (data = {}) => API.post(`${BASE_TENANT}/purOrder/save`, data)
// 模糊搜索订单数组
export const getPurOrderQuery = (data = {}) =>
  API.get(`${BASE_TENANT}/purOrder/query/orderId`, data)
// 采方根据订单编号获取订单
export const getPurOrderCode = (code = '', currentBu = '') =>
  API.get(`${BASE_TENANT}/purOrder/code/${code}?currentBu=${currentBu}`)
// 采方根据订单id获取售后订单
export const getBuyerSaleServiceOrderId = (id = '') =>
  API.get(`${BASE_TENANT}/buyerSaleServiceOrder/${id}`)
// 售后订单明细列表 init
export const postBuyerSaleServiceOrderDetailInitDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrderDetail/init/detail`, data)
// 售后订单明细列表
export const postBuyerSaleServiceOrderDetailDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrderDetail/detail`, data)
// 保存售后订单
export const postBuyerSaleServiceOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/save`, data)
// 保存草稿售后订单
export const postBuyerSaleServiceOrderDraft = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/draft`, data)
// 系统同步订单
export const getPurOrderSync = (data = {}) => API.get(`${BASE_TENANT}/purOrder/sync`, data)
// -------采购订单end-------

//采方 售后订单 begin
// 保存草稿订单
export const buyerSaleOrderDraft = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/draft`, data)
// 保存订单
export const buyerSaleOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/save`, data)
// 关闭订单
export const buyerSaleOrderClose = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/close`, data)
//根据订单id获取售后订单
export const getbuyerSaleOrder = (id = {}) => API.get(`${BASE_TENANT}/buyerSaleServiceOrder/${id}`)
// 编辑进入获取模块配置
export const getbuyerSaleOrderConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerSaleServiceOrder/module/config`, data)
// 查询编辑进入订单明细行
export const buyerSaleOrderDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrderDetail/detail`, data)
// 订单审批
export const buyerSaleOrderApprove = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/approve`, data)
// /发布订单
export const buyerSaleOrderPublish = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/publish`, data)
// 完成订单
export const buyerSaleOrderDone = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrder/done`, data)
//发货方式保存
export const buyerSaleOrderDeliverySave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerSaleServiceOrderDelivery/save`, data)
//采方 售后订单 end

// -------供方售后订单start-------
// 供方根据订单id获取售后订单
export const getSupplierSaleServiceOrder = (id = {}) =>
  API.get(`${BASE_TENANT}/supplierSaleServiceOrder/${id}`)
// 获取模块配置
export const getSupSaleServiceModuleConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierSaleServiceOrder/module/config`, data)
// 获取模块配置详情
export const getSupSaleServiceModuleData = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSaleServiceOrderDetail/module/data`, data)
// 获取订单历史版本号
export const getSupSaleServiceVersion = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSaleServiceOrder/version`, data)
// 根据订单编号及版本获取订单
export const getSupSaleServiceOrderHistory = (data) =>
  API.post(`${BASE_TENANT}/supplierSaleServiceOrder/history`, data)
// 批量确认
export const supSaleServiceOrderConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSaleServiceOrder/confirm`, data)
// 批量拒绝
export const supSaleServiceOrderRefuse = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSaleServiceOrder/refuse`, data)
// 供方反馈采方订单接受或者拒绝右上角
export const getSupSaleServiceOrderFeedBack = (id = {}) =>
  API.get(`${BASE_TENANT}/supplierSaleServiceOrder/feedBack/${id}`)
//添加相关文件
export const saveRelativeFile = (data = {}) => API.post(`${BASE_TENANT}/file/save`, data)
// -------供方售后订单end-------

// 采购订单历史查询 - 查询接口
export const getPurchaseOrderHistoryList = (data = {}) =>
  API.post(`${BASE_TENANT}/HistoryOrder/query`, data)

// 采购订单历史查询 - 详情信息查询
export const getPurchaseOrderHistoryDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/HistoryOrder/query/data`, data)

// ----------------------------采方-供方销售订单查询------------------------------
export const pageSupplierSalesOrderListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrder/buyer/queryPage`, data)

export const exportSupplierSalesOrderListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrder/buyer/export`, data, {
    responseType: 'blob'
  })

export const pageSupplierSalesOrderDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrderDetail/buyer/queryPage`, data)

export const exportSupplierSalesOrderDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrderDetail/buyer/export`, data, {
    responseType: 'blob'
  })

// 供方销售订单 - 手动获取
export const pullSupplierSalesOrderApi = (data = {}) =>
  API.get(`${BASE_TENANT}/supplierSalesOrder/handPull`, data)

// ----------------------------供方-本方销售订单查询------------------------------
export const pageSalesOrderListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrder/supplier/queryPage`, data)

export const exportSalesOrderListApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrder/supplier/export`, data, {
    responseType: 'blob'
  })

export const pageSalesOrderDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrderDetail/supplier/queryPage`, data)

export const exportSalesOrderDetailApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierSalesOrderDetail/supplier/export`, data, {
    responseType: 'blob'
  })

// ----------------------------采方-采购订单协同------------------------------
// 同步供方系统
export const syncSupplierSystemApi = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/syncSupplierSystem`, data)
