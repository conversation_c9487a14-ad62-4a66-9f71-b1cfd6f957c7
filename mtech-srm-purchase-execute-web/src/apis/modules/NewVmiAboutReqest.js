/*
 * @Author: zhaoriyang3 <EMAIL>
 * @Date: 2022-05-18 09:54:49
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @LastEditTime: 2022-05-24 15:46:51
 * @FilePath: \mtech-srm-purchase-execute-web\src\apis\modules\vmiAboutReqest.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'newVmi'
//  -------供方采购start-------

// 供方采购订单根据ID获取顶部详情
export const getByOrder = (data = {}) => API.get(`${BASE_TENANT}/supOrder/getByOrder`, data)
export const findByCode = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmiWarehouse/findByCode?code=${data.code}`)

export const getVmiHouse = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmiWarehouse/query`, data)

export const getVmiHouse2 = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/criteria-query-for-supplier`, data)

//新增VMI仓与供应商关联关系
export const addwarehoseAndSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi_warehouse_supplier_rel/save`, data)
//修改VMI仓与供应商关联关系
// export const updatewarehoseAndSupplier = (data = {}) =>
//   API.put(`${BASE_TENANT}/vmiSteel/vmi_warehouse_supplier_rel/update`, data);

//删除VMI仓与供应商关联关系
export const deletewarehoseAndSupplier = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiSteel/vmi_warehouse_supplier_rel/delete`, data)

//新增VMI仓库
export const addWarehose = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmiWarehouse/save`, data)

//新增
export const addWarehoseRel = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_item_rel/add`, data)
//批量新增
export const addBatchWarehose = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_item_rel/batch-add`, data)
//修改
export const updateBatchWarehose = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi_warehouse_item_rel/update`, data)

//删除VMI仓库
export const deleteWarehose = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmiWarehouse/delete`, data)
//导入
export const importWarehose = (data = {}) => API.post(`${BASE_TENANT}/vmiWarehouse/import`, data)
//导出
export const downloadWarehose = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmiWarehouse/download`, data, {
    responseType: 'blob'
  })
//导入
export const importWarehoseAnd = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/data-import`, data)
//导出
export const downloadWarehoseAnd = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/export`, data, {
    responseType: 'blob'
  })
//列表
export const getLastList = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_item_rel/find-by-supplier-relid`, data)

export const batchDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmi_warehouse_item_rel/batch-delete`, data)

export const submitAsn = (data = {}) => API.put(`${BASE_TENANT}/vmi-pickup-order/submit-asn`, data)

export const submitNewAsn = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/submit-asn`, data)

export const addmitAsn = (data = {}) => API.put(`${BASE_TENANT}/vmi-pickup-order/create-asn`, data)
