import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'demandPool'

// 转订单：tenant/request/query 条件：业务类型  有价格（priceStatus=1）
// 转寻源：tenant/request/query 条件：业务类型  无价格（priceStatus=0）
export const getQuery = (data = {}) => API.post(`${BASE_TENANT}/request/query`, data)

// 我认领的-转订单 tenant/request/queryOwn 条件：业务类型  有价格（priceStatus=1）
// 我认领的-转寻源 tenant/request/queryOwn 条件：业务类型  有价格（priceStatus=0）
export const getOwnQuery = (data = {}) => API.post(`${BASE_TENANT}/request/queryOwn`, data)

// 获取关联合同
export const getRelatedContractsApi = (data = {}) =>
  API.get(`/contract/contract/purchase/query`, data)
export const getPrviewApi = (param = {}) =>
  API.get(`/contract/contract/purchase/operation-view/${param}`)

// 获取供应商联系人
export const getSupplierContactApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/business_partner_contact/findBusinessPartnerContact`, data)
// 获取合同模板
export const getContractTemplateApi = (param = {}) =>
  API.get(`/contract/form/selectable/contractTemplate`, param)
// 获取是否立项
export const getEstablishedApi = (param = {}) =>
  API.get(`/contract/form/selectable/established`, param)

// 合同附件上传
export const uploadContractApi = (data = {}) =>
  API.post(`/contract/attachment/upload`, data, {
    'Content-Type': 'application/form-data'
  })
// 合同附件删除
export const delContractApi = (data = {}) => API.delete(`/contract/attachment/${data}`)
// 合同附件下载
export const downloadContractApi = (data = {}) =>
  API.get(`/contract/attachment/download/${data}`, { responseType: 'blob' })

// 采购申请创建合同草稿
export const createDraftContractDirectlyApi = (data = {}) =>
  API.post(`/contract/contract/purchase/createDraftContractDirectly`, data)
// 采购申请直接发布合同
export const publishContractDirectlyApi = (data = {}) =>
  API.post(`/contract/contract/purchase/publishContractDirectly`, data)
// 根据供应商id查询信息
export const getCountryCodeByIdApi = (data = {}) =>
  API.post(`/masterDataManagement/tenant/enterprise/getByEnterpriseId/${data}`)
