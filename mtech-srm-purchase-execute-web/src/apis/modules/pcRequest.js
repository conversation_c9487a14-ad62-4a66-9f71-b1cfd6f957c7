import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'purchaseRequest'

// 采购申请 - 我的申请-----------------------------------------------------------------------------
// 并单校验-采购申请明细单接口 采购申请转订单并单策略校验
export const poConsolidationConfigMatch = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/poConsolidationConfigMatch`, data)

// 并单寻源校验-采购申请明细单接口 采购申请转订单并单策略校验
export const porConsolidationConfigMatch = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/porConsolidationConfigMatch`, data)

// 新增
export const addRequest = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/add`, data)

// 编辑
export const editRequest = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/edit`, data)

// 列表
export const getRequestList = (data = {}) =>
  API.post(`${BASE_TENANT}/requestHeader/query/own`, data)

// 关闭
export const closeRequest = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/close`, data)

// 取消
export const cancleRequest = (data = {}) =>
  API.post(`${BASE_TENANT}/requestHeader/strengthenClose`, data)

// 提交
export const submitRequest = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/submitAll`, data)

// 查询是否创建寻源需求 1是0否
export const sourceStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/requestHeader/query/sourceStatus`, data)

// 明细----------------------------------------
// 我认领的 列表
export const getOwnRequest = (data = {}) => API.post(`${BASE_TENANT}/requestItem/queryOwn`, data)

// 转办 采购申请明细单 - 分配采购明细
export const allotRequest = (data = {}) => API.post(`${BASE_TENANT}/requestItem/allot`, data)

// 取消认领
export const cancelClaim = (data = {}) => API.post(`${BASE_TENANT}/requestItem/claim/cancel`, data)

// 采购申请据模块 - 获取业务类型的所有字段
export const getModuleByBusinessId = (data = {}) =>
  API.get(`${BASE_TENANT}/business/module/field`, data)

// 采购申请明细 - 获取价格记录  SkuId   ItemId  SiteId CategoryId
export const getPriceRecord = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/price/record`, data)

// 详情(获取所有tab和对应字段)：采购申请单据模块接口 - 获取采购申请单据模块定义
export const getModuleByDocId = (data = {}) => API.get(`${BASE_TENANT}/pr/module/config`, data)

// 获取详情数据： 采购申请单据模块接口 - 获取采购申请单据模块数据
export const getModuleData = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/data`, data)
// API.post(`${BASE_TENANT}/pr/module/data`, data);

// 商城转申请 获取详情 采购商城对接采购执行 - 根据商城ID和业务类型查询采购申请
export const getModuleDataFromMall = (data = {}) =>
  API.post(`${BASE_TENANT}/mall/queryRequestModelPr`, data)

// 商城转申请 获取业务类型
export const queryMallBusinessType = (data = {}) =>
  API.get(`${BASE_TENANT}/mall/queryMallBusinessType`, data)

// 保存草稿 ：  采购申请单据模块接口 - 保存采购申请单据模块数据
export const saveModuleData = (data = {}) =>
  // API.post(`${BASE_TENANT}/pr/module/save`, data);
  API.post(`${BASE_TENANT}/requestHeader/save`, data)

// 提交：采购申请主单接口 - 提交采购申请
export const submitModuleData = (data = {}) =>
  // API.post(`${BASE_TENANT}/requestHeader/submit`, data);
  API.post(`${BASE_TENANT}/requestHeader/save/submit`, data)

// 提交明细：采购明细单接口 - 提交采购申请明细
// export const submitDetailData = (data = {}) =>
//   API.post(`${BASE_TENANT}/requestItem/submit`, data);

// 获取主单信息：采购申请主单接口 - 根据ID获取主单信息
export const getHeaderInfo = (data = {}) => API.get(`${BASE_TENANT}/requestHeader/get`, data)

// 获取主单信息：采购申请主单接口 - 根据ID获取主单信息
export const getLogisticsHeaderInfo = (data = {}) =>
  API.get(`/contract/tenant/logistics/auto/header/detail`, data)

// 获取主单信息：采购申请主单接口 - 根据templateId获取明细字段
export const getLogisticsFields = (data = {}) =>
  API.get(`/contract/tenant/request/config/rel/list`, data)

// 相关文件： 获取左侧文件节点  采购申请相关文件 - 根据申请主单查询所有文件节点信息
export const getFileNodeByDocId = (data = {}) =>
  API.get(`${BASE_TENANT}/requestFile/queryFileNodeByDocId`, data)

// 根据左侧节点获取文件列表   采购申请相关文件 -根据rfxId-节点id查询所有文件信息
export const getFileByDocId = (data = {}) =>
  API.get(`${BASE_TENANT}/requestFile/queryFileByDocId`, data)

// 上传后保存文件  采购申请相关文件 - 添加相关文件
export const saveHeaderFile = (data = {}) => API.post(`${BASE_TENANT}/requestFile/save`, data)

// 相关说明：  采购申请主单接口-根据主单ID查询说明信息
export const getHeaderState = (data = {}) =>
  API.get(`${BASE_TENANT}/requestHeader/queryClobByHeaderId`, data)

// 相关说明：保存  采购申请主单接口-根据主单ID查询说明信息
export const saveHeaderState = (data = {}) => API.put(`${BASE_TENANT}/requestHeader/saveClob`, data)

// 采购申请 主单接口 删除采购申请
export const deleteHeader = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/delete`, data)

// 整单分摊：
// 采购申请费用分摊接口 - 获取费用分摊
export const getCostShareStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/requestCostSharing/query`, data)

// 采购申请费用分摊接口 - 设置是否整单分摊
export const setCostShareStatus = (data = {}) =>
  API.get(`${BASE_TENANT}/requestCostSharing/costSharingStatus`, data)

// 采购申请费用分摊接口 - 添加费用分摊
export const addCostShare = (data = {}) => API.post(`${BASE_TENANT}/requestCostSharing/add`, data)

// 采购申请费用分摊接口 - 编辑费用分摊
export const editCostShare = (data = {}) => API.post(`${BASE_TENANT}/requestCostSharing/edit`, data)

// 采购申请费用分摊接口 - 删除整单分摊
export const deleteCostShare = (data = {}) =>
  API.post(`${BASE_TENANT}/requestCostSharing/delete`, data)

// 创建寻源需求
export const createSourcingRequire = (data = {}) =>
  API.post(`${BASE_TENANT}/requestHeader/createPor`, data)

// 推送聚采 寻源需求
export const createGatherPurchasePor = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/createGatherPurchasePor`, data)

// 查询寻源需求并单策略配置
export const getPorConsolidationConfig = (data = {}) =>
  API.get(`${BASE_TENANT}/requestItem/queryPorConsolidationConfig`, data)

// 匹配创建寻源需求并单策略配置
export const porSouringConfigMatch = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/porConsolidationConfigMatch`, data)

// 匹配创建订单并单策略配置
export const porCreateOrderConfigMatch = (data = {}) =>
  API.post(`${BASE_TENANT}/purOrder/orderConsolidation`, data)

// 采购申请明细单接口 - 上传采购申请明细
export const uploadRequestItem = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/upload`, data, {
    responseType: 'blob'
  })

// 采购申请明细单接口 - 下载采购申请明细
export const downloadRequestItem = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/download`, data, {
    responseType: 'blob'
  })

// 业务类型配置接口 - 采购申请明细模板下载
export const downloadItemTemplate = (data = {}) =>
  API.get(`${BASE_TENANT}/pe/template/download`, data, {
    responseType: 'blob'
  })

// 采购池--------
// 获取采购申请主单列表
export const getSummaryRequestList = (data = {}) =>
  API.post(`${BASE_TENANT}/requestHeader/query`, data)

// 认领(明细)
export const claimDetail = (data = {}) => API.post(`${BASE_TENANT}/requestItem/claim`, data)

// 分配采购(明细)
export const allotDetail = (data = {}) => API.post(`${BASE_TENANT}/requestItem/allot`, data)

// 物流（年约需求） 转办
export const logisticsTransfer = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/header/transfer`, data)

// 认领(主单)
export const claimMain = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/claim`, data)

// 分配采购(主单)
export const allotMain = (data = {}) => API.post(`${BASE_TENANT}/requestHeader/allot`, data)

// 商城转采购申请，根据传过来的code，换取id
export const getIdByCode = (data = {}) => API.get(`${BASE_TENANT}/requestHeader/findIdByCode`, data)
// 物流类 通过code查询id
export const getLogisticsCodeById = (data = {}) =>
  API.post(`/contract/tenant/logistics/request/header/getOneDetail`, data)
// 需求池 获取业务类型上的数字
export const getBusinessNum = (data = {}) =>
  API.post(`${BASE_TENANT}/requestItem/queryAmount`, data)

// 需求池 获取业务类型物流类的计数
export const getLogisticsBusinessNum = (data = {}) =>
  API.get(`/contract/tenant/logistics/auto/count/handle`, data)

// 需求池 - 非采台账导出
export const standingBookExport = (data = {}) =>
  API.post(`${BASE_TENANT}/standingBook/exportResult`, data, {
    responseType: 'blob'
  })

// 采购申请-物流 删除
export const deleteLogisticsList = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/request/delete`, data)

// 采购申请-物流 关闭
export const closeLogisticsList = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/request/close`, data)

// 采购申请-物流 取消
export const cancelLogisticsList = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/request/cancel`, data)

// 采购申请-物流 提交
export const commitLogisticsList = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/request/commit`, data)

// 采购申请-物流 保存提交
export const saveAndCommitLogisticsList = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/request/saveAndCommit`, data)

// 采购申请-物流 保存草稿
export const addOrUpdateLogisticsList = (data = {}) =>
  API.post(`/contract/tenant/logistics/auto/request/addOrUpdate`, data)
