import { API } from '@mtech-common/http'

import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'receiptCollaboration'

// 根据条件查询运货单
export const postBuyerDeliveryBillQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryBill/query`, data)

// 根据条件获取运货单明细
export const postBuyerDeliveryBillItemQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryBillItem/query`, data)

// 根据条件获取收货单
export const postBuyerReceivingBillQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReceivingBill/query`, data)

// 根据条件获取收货单明细
export const postBuyerReceivingBillItemQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReceivingBillItem/query`, data)

// 根据条件获取收货退回单
export const postBuyerReceivingCancelBillQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReceivingCancelBill/query`, data)

// 根据条件获取收货退回单明细
export const postBuyerReceivingCancelBillItemQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReceivingCancelBillItem/query`, data)

// 退回操作
export const postBuyerReceivingBillItemCancel = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerReceivingBillItem/cancel`, data)

// 收货操作
export const postBuyerDeliveryBillItemReceive = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryBillItem/receive`, data)

// 根据单据ID获取运货单详情
export const getBuyerDeliveryBillGet = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerDeliveryBill/get`, data)

// 根据ID获取收货单详情
export const getBuyerReceivingBillGet = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerReceivingBill/get`, data)

// 根据ID获取收货退回单详情
export const getBuyerReceivingCancelBillGet = (data = {}) =>
  API.get(`${BASE_TENANT}/buyerReceivingCancelBill/get`, data)

// 获取收货协同单据模块定义
export const getReModuleConfig = (data = {}) => API.get(`${BASE_TENANT}/re/module/config`, data)
