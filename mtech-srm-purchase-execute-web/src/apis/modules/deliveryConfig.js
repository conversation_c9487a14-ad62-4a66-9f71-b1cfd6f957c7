import { API } from '@mtech-common/http'

import { BASE_TENANT, BASE_COMMON } from '@/utils/constant'

export const NAME = 'deliveryConfig'

// 修改司机状态
export const postBuyerDriverBlacklistStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDriverBlacklist/status`, data)

// 删除司机
export const postBuyerDriverBlacklistDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDriverBlacklist/delete`, data)

// 采方送货司机黑名单-司机Excel导出
export const postBuyerDriverBlacklistExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDriverBlacklist/export`, data, {
    responseType: 'blob'
  })

// 采方出入库记录导出
export const poExport = (data = {}, params) =>
  API.post(`${BASE_TENANT}/po/in_out_record/export`, data, {
    responseType: 'blob',
    params
  })

// 供方出入库记录导出
export const poSupplierExport = (data = {}, params) =>
  API.post(`${BASE_TENANT}/po/in_out_record/supplier-export?BU_CODE=${data.currentBu}`, data, {
    responseType: 'blob',
    params
  })

// 采方送货司机黑名单-Excel导入
export const postBuyerDriverBlacklistImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDriverBlacklist/import`, data, {
    responseType: 'blob'
  })

// 获取司机列表
export const postBuyerDriverBlacklistQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDriverBlacklist/query`, data)

// 保存司机
export const postBuyerDriverBlacklistSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDriverBlacklist/save`, data)

// 保存超时自动取消配置
export const postBuyerDeliveryOvertimeCancelSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOvertimeCancel/save`, data)

// 超时自动取消配置Excel导出
export const postBuyerDeliveryOvertimeCancelExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOvertimeCancel/export`, data, {
    responseType: 'blob'
  })

// 超时自动取消配置Excel导入
export const postBuyerDeliveryOvertimeCancelImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOvertimeCancel/import`, data, {
    responseType: 'blob'
  })

// 修改超时自动取消配置状态
export const postBuyerDeliveryOvertimeCancelStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOvertimeCancel/status`, data)

// 删除超时自动取消配置
export const postBuyerDeliveryOvertimeCancelDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOvertimeCancel/delete`, data)

// 删除卸货地点
export const postBuyerUnloadLocationDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerUnloadLocation/delete`, data)

// 卸货地点Excel导出
export const postBuyerUnloadLocationExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerUnloadLocation/export`, data, {
    responseType: 'blob'
  })

// 卸货地点Excel导入
export const postBuyerUnloadLocationImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerUnloadLocation/import`, data, {
    responseType: 'blob'
  })

// 保存卸货地点
export const postBuyerUnloadLocationSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerUnloadLocation/save`, data)

// 卸货地点-修改状态
export const postBuyerUnloadLocationStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerUnloadLocation/status`, data)

// 收发货自动补单配置、无订单送货配置-保存配置
export const postBuyerDeliveryOrderConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderConfig/save`, data)

// 收发货自动补单配置、无订单送货配置-配置Excel导出
export const postBuyerDeliveryOrderConfigExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderConfig/export`, data, {
    responseType: 'blob'
  })

// 收发货自动补单配置、无订单送货配置-Excel导入
export const postBuyerDeliveryOrderConfigImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderConfig/import`, data, {
    responseType: 'blob'
  })

// 收发货自动补单配置、无订单送货配置-删除配置
export const postBuyerDeliveryOrderConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderConfig/delete`, data)

// 收发货自动补单配置、无订单送货配置-修改配置状态
export const postBuyerDeliveryOrderConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderConfig/status`, data)

// 关联项目文本批次物料配置-修改状态
export const postBuyerProjectTextBatchConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerProjectTextBatchConfig/status`, data)

// 关联项目文本批次物料配置-保存
export const postBuyerProjectTextBatchConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerProjectTextBatchConfig/save`, data)

// 关联项目文本批次物料配置-Excel导出
export const postBuyerProjectTextBatchConfigExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerProjectTextBatchConfig/export`, data, {
    responseType: 'blob'
  })

// 采方要货计划Excel导出 new
export const buyerGoodsDemandPlanInfoKtExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/buyerGoodsDemandPlanInfo/exportKT?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 关联项目文本批次物料配置-Excel导入
export const postBuyerProjectTextBatchConfigImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerProjectTextBatchConfig/import`, data, {
    responseType: 'blob'
  })

// 关联项目文本批次物料配置-Excel导入
export const buyerGoodsDemandPlanInfoImportExcelKT = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerGoodsDemandPlanInfo/importExcelKT`, data, {
    responseType: 'blob'
  })

// 关联项目文本批次物料配置-删除
export const postBuyerProjectTextBatchConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerProjectTextBatchConfig/delete`, data)

// 送货提前期维护-删除
export const postBuyerDeliveryPeriodConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryPeriodConfig/delete`, data)

// 送货提前期维护-Excel导出
export const postBuyerDeliveryPeriodConfigExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryPeriodConfig/export`, data, {
    responseType: 'blob'
  })

// 送货提前期维护-Excel导入
export const postBuyerDeliveryPeriodConfigImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryPeriodConfig/import`, data, {
    responseType: 'blob'
  })

// 送货提前期维护-保存
export const postBuyerDeliveryPeriodConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryPeriodConfig/save`, data)

// 送货提前期维护-修改状态
export const postBuyerDeliveryPeriodConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryPeriodConfig/status`, data)

// 送货关联配置-保存
export const postBuyerDeliveryOrderAssociationConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderAssociationConfig/save`, data)

// 送货关联配置-Excel导出
export const postBuyerDeliveryOrderAssociationConfigExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderAssociationConfig/export`, data, {
    responseType: 'blob'
  })

// 送货关联配置-Excel导入
export const postBuyerDeliveryOrderAssociationConfigImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderAssociationConfig/import`, data, {
    responseType: 'blob'
  })

// 送货关联配置-删除
export const postBuyerDeliveryOrderAssociationConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderAssociationConfig/delete`, data)

// 送货关联配置-修改状态
export const postBuyerDeliveryOrderAssociationConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderAssociationConfig/status`, data)

// 按采购订单送货物料配置-删除
export const postBuyerDeliveryOrderMaterialConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderMaterialConfig/delete`, data)

// 按采购订单送货物料配置-Excel导出
export const postBuyerDeliveryOrderMaterialConfigExport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderMaterialConfig/export`, data, {
    responseType: 'blob'
  })

// 预测空调-Excel模板导出
export const buyerJitInfoexport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerForecastConfigure/download-temp-kt`, data, {
    responseType: 'blob'
  })

// 按采购订单送货物料配置-Excel导入
export const postBuyerDeliveryOrderMaterialConfigImport = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderMaterialConfig/import`, data, {
    responseType: 'blob'
  })

// 按采购订单送货物料配置-保存
export const postBuyerDeliveryOrderMaterialConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderMaterialConfig/save`, data)

// 按采购订单送货物料配置-修改状态
export const postBuyerDeliveryOrderMaterialConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryOrderMaterialConfig/status`, data)

// 送货单并单校验-保存
export const postBuyerDeliveryCombineConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryCombineConfig/save`, data)

// 送货单并单校验-删除
export const postBuyerDeliveryCombineConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryCombineConfig/delete`, data)

// 送货单并单校验-修改状态
export const postBuyerDeliveryCombineConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryCombineConfig/status`, data)

//删除发货匹配订单配置
export const postBuyerDeliveryMatchConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryMatchConfig/delete`, data)
//保存发货匹配订单配置
export const postBuyerDeliveryMatchConfigSave = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryMatchConfig/save`, data)
//修改发货匹配订单配置状态
export const postBuyerDeliveryMatchConfigStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerDeliveryMatchConfig/status`, data)

// wms仓库-删除
export const postWmsAddressConfigConfigDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/locationWmsAddressConfig/batch-delete`, data)

// wms仓库-新增
export const postWmsAddressConfigConfigAdd = (data = {}) =>
  API.post(`${BASE_TENANT}/locationWmsAddressConfig/add`, data)

// wms仓库-更新
export const postWmsAddressConfigConfigUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/locationWmsAddressConfig/update`, data)

// 呆料送货单-新增
export const idleMaterialAdd = (data = {}) => API.post(`${BASE_TENANT}/idle/material/add`, data)

// 呆料送货单-冻结
export const idleMaterialOn = (data = {}) => API.post(`${BASE_TENANT}/idle/material/on`, data)

// 呆料送货单-解冻
export const idleMaterialOff = (data = {}) => API.post(`${BASE_TENANT}/idle/material/off`, data)

// 机芯限制关系维护-新增
export const movementAdd = (data = {}) =>
  API.post(`${BASE_TENANT}/movement/astrict/relation/add`, data)

// 机芯限制关系维护-修改
export const movementUpdate = (data = {}) =>
  API.post(`${BASE_TENANT}/movement/astrict/relation/update`, data)

// 机芯限制关系维护-删除
export const movementDelete = (data = {}) =>
  API.post(`${BASE_TENANT}/movement/astrict/relation/delete`, data)

// 机芯限制关系维护-导出
export const movementExport = (data = {}) =>
  API.post(`${BASE_TENANT}/movement/astrict/relation/export`, data, {
    responseType: 'blob'
  })

// 送货黑名单配置 - 查看脱敏信息
export const checkDeliveryConfigInfo = (data = {}) =>
  API.get(`${BASE_COMMON}/dataDesensitize?desensitize=${data.key}`)

// 供方库存余量限制送货配置 - 查询
export const pageDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/query`, data)

// 供方库存余量限制送货配置 - 保存
export const saveDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/save`, data)

// 供方库存余量限制送货配置 - 删除
export const deleteDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/delete`, data)

// 供方库存余量限制送货配置 - 生效
export const effectiveDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/effective`, data)

// 供方库存余量限制送货配置 - 失效
export const expireDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/expire`, data)

// 供方库存余量限制送货配置 - 导入
export const importDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/importData`, data, {
    responseType: 'blob'
  })

// 供方库存余量限制送货配置 - 导入模板下载
export const downTempDeliveryLimitConfigApi = (data = {}) =>
  API.get(`${BASE_TENANT}/supplier/inventory/config/excelImportTemplate`, data, {
    responseType: 'blob'
  })

// 供方库存余量限制送货配置 - 导出
export const exportDeliveryLimitConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/supplier/inventory/config/excelExport`, data, {
    responseType: 'blob'
  })

// ----------------------------采方-系统设置-送货预约强控名单------------------------------
export const pageDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/queryPage`, data)

export const saveDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/saveOrUpdate`, data)

export const deleteDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/delete`, data)

export const importDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/importData`, data, {
    responseType: 'blob'
  })
export const downloadDeliveryAppointmentConfigApi = (data = {}) =>
  API.get(`${BASE_TENANT}/ReservationControlName/excelImportTemplate`, data, {
    responseType: 'blob'
  })
export const exportDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/excelExport`, data, {
    responseType: 'blob'
  })

export const enableDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/enable`, data)

export const disableDeliveryAppointmentConfigApi = (data = {}) =>
  API.post(`${BASE_TENANT}/ReservationControlName/disable`, data)
