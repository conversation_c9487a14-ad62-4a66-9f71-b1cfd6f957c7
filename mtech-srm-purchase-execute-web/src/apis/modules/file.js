import { API } from '@mtech-common/http'
import { PROXY_FILE, BASE_TENANT } from '@/utils/constant'

export const NAME = 'fileService'

//文件上传-私密类型
export const uploadPrivateFile = (data = {}) =>
  API.post(`${PROXY_FILE}/user/file/uploadPrivate?useType=2`, data)

//文件上传-公有类型
export const uploadPublicFile = (data = {}) =>
  API.post(`${PROXY_FILE}/user/file/publicUpload?useType=2`, data)

//文件下载-私密文件
export const downloadPrivateFile = (data = {}) =>
  API.get(`${PROXY_FILE}/user/file/downloadPrivateFile?useType=2`, data, {
    responseType: 'blob'
  })
// 文件下载--公开文件
export const downloadPublicFile = (data = {}) =>
  API.get(`/file/user/file/downloadPublicFile`, data, {
    responseType: 'blob'
  })
// 文件预览
export const getMtPreview = (data = {}) => API.get(`${PROXY_FILE}/user/file/mtPreview`, data)
// 文件预览
export const filePreview = (data = {}) => API.get(`/file/user/file/mtPreview`, data)
export const filePreviewApi = (data = {}) => API.get(`/file/user/file/mtPreview2`, data)
// 公有文件预览
export const getMtPreviewPub = (data = {}) => API.get(`${PROXY_FILE}/user/file/mtPreview2`, data)
// 删除文件  模块文件接口 - 根据ID删除
export const deleteFile = (data = {}) => API.put(`${BASE_TENANT}/file/deleteById`, data)

// 文件上传 - 采方可查看
export const fileUploadSitHaz = (data = []) => {
  return API.post(`${PROXY_FILE}/user/file/uploadPrivate?useType=2`, data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 文件下载 - 采方可查看
export const fileDownload = (data = {}) =>
  API.get(`/file/user/file/downloadPrivateFile?useType=2&id=${data}`, '', {
    responseType: 'blob'
  })
