import { API } from '@mtech-common/http'

import { BASE_TENANT, STAT_TENANT } from '@/utils/constant'

export const NAME = 'reconciliationReport'

/**
 * 
  对账协同报表
 */

// 供应商应付款报表 - 导出
export const supplierAccountExport = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierPayment/exportExcel`, data, {
    responseType: 'blob'
  })
// 供应商应付款报表 - 查询
export const supplierAccountQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierPayment/queryBuilder`, data, {})

// 供应商供货金额汇总(供应商维度) - 导出
export const supplierSummaryExport = (data = {}) =>
  API.post(`${STAT_TENANT}/reconciliationSupplierSummary/supplierSummaryExport`, data, {
    responseType: 'blob'
  })
// 供应商供货金额汇总(供应商维度) - 查询
export const supplierSummaryQuery = (data = {}) =>
  API.post(`${STAT_TENANT}/reconciliationSupplierSummary/supplierSummaryQuery`, data, {})

// 供应商供货金额汇总(物料维度) - 导出
export const supplierSummaryItemExport = (data = {}) =>
  API.post(`${STAT_TENANT}/reconciliationSupplierSummary/supplierSummaryItemExport`, data, {
    responseType: 'blob'
  })
// 供应商供货金额汇总(物料维度) - 查询
export const supplierSummaryItemQuery = (data = {}) =>
  API.post(`${STAT_TENANT}/reconciliationSupplierSummary/supplierSummaryItemQuery`, data, {})
// 根据权限获取公司列表 - 查询
export const getCompanyList = (data = {}) =>
  API.post(`masterDataManagement/tenant/permission/company`, data, {})
