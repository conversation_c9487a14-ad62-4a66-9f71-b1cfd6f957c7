// vmi-采方接口
import { API } from '@mtech-common/http'
import { BASE_TENANT, STAT_TENANT } from '@/utils/constant'
export const NAME = 'purchaseCoordination'
const PROXY_BASE = '/srm-purchase-execute'

// 采方入库管理 ====
// 同步WMS - 入库
export const synchronousWms = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/syncWms`, data)
// 同步WMS - 送货 - 采
export const deliveryPurSynchronousWms = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/syncWms`, data)
// 同步WMS - 送货 - 供
export const deliverySupSynchronousWms = (data = {}) =>
  API.post(`${BASE_TENANT}/supplierOrderDelivery/syncWms`, data)
// 同步 - 送货单清单-泛智屏 - 采供
export const deliverySynchronousWms = (data = {}) =>
  API.post(`${BASE_TENANT}/delivery/header/buyer/synOrderDelivery`, data)
// 同步WMS - 退货
export const returnSynchronousWms = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/syncWms`, data)
// 同步WMS - 替换
export const replaceSynchronousWms = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReplaceOrder/syncWms`, data)
// 详情接口
export const postBuyerWarehousingDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/detail`, data)
export const postPurchaseLimitQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/purchase-limit-query`, data)
// 提交校验
export const postBuyerWarehousingPreservation = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-receive-order/qc`, data)
// 改判
export const postBuyerWarehousingChange = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-receive-order/qc-change`, data)
// 退货头视图列表
export const postBuyerWarehousingReturnGoods = `${BASE_TENANT}/vmi-order/buyer-page-query`
// 退货明细视图列表
export const postReturnQueryDetail = `${BASE_TENANT}/vmi-order/buyer-page-query-detail`
// 退货详情列表
export const postBuyerWarehousingReturnDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-order/detail`, data)
// 退货详情列表 new
export const postNewBuyerWarehousingReturnDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-order/detail`, data)

// 退货 采方接收
export const postBuyerWarehousingReturnConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/confirm`, data)

// vmi退货明细导出 -采方
export const postBuyerWarehousingReturnDetailExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiReturnedOrder/buyer-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// vmi冲销明细导出 -采方
export const postBuyerWarehousingReturnExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiSteel/vmiReturnedOrder/buyer-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// vmi冲销明细导出 -供方
export const postSupplierWarehousingReturnExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiSteel/vmiReturnedOrder/supplier-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 确认退货单（带实收数量）
export const postVmiReturnedOrderConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/confirmReturnedOrder`, data)
// 退货 采方退回
export const postBuyerWarehousingReturnRejected = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/rejected`, data)

// 库存调整管理====
// 替换单接收
export const purchaseReplaceReceive = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReplaceOrder/confirm`, data)
// 调拨单接收
export const purchaseAllocationReceive = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiAllocationOrder/confirm`, data)
// 替换单回退
export const purchaseReplaceBackOff = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReplaceOrder/rejected`, data)
// 调拨单回退
export const purchaseAllocationBackOff = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiAllocationOrder/rejected`, data)
// 创建供应商库存导入-采方确认接口
export const postPurchaseStockImportConfirm = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSupStockOrder/confirm`, data)
// ====【VMI】领料管理=====
// VMI领料单详情==查询
export const VMIPickupOrderDetailQuery = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmi-pickup-order/detail`, data)
}

// 加了个参数：resultType，可选值为：site(工厂)/warehouse(VMI仓库)/supplier(（原材料）供应商);
// 你需要查工厂的时候就传site，需要查仓库就传warehouse，需要查供应商就传supplier；
// 查VMI仓要根据工厂过滤就将选择的工厂编码作为查询VMI仓的条件一起传进去。
// 采方-VMI领料单详情==查询工厂、查询（原材料）供应商、查询VMI仓（传入工厂和原材料供应商）下拉数据
export const VMIQueryCriteriaByResultType = (data = {}) => {
  // API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/criteria-query-for-supplier`, data);
  return API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/criteria-query`, data)
}
// 采方-VMI领料单详情==根据VMI仓查出送货地址 可修改
export const postWarehouseAddress = (data) => {
  return API.post(`${PROXY_BASE}/tenant/siteTenantExtend/queryBySite`, data)
}
// 采方-VMI领料单详情-选择VMI库存行（弹窗）==根据 “批次/卷号” 查询 “可领料数量”、“采购订单号”、“采购订单行号”（批量）
export const queryItemLimit = (data) => {
  return API.post(`${PROXY_BASE}/tenant/vmi-pickup-order/item-limit-query`, data)
}
//
export const batchViewViewExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/report/buyer/batchView/export`, data, {
    responseType: 'blob'
  })
// 钢材库龄报表导出
export const batchAgeViewExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/report/buyer/stock-day-export`, data, {
    responseType: 'blob'
  })

// 采方vmi退货打印
export const vmiReturnedOrderPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/print`, data, {
    responseType: 'blob'
  })

// 采方、第三方vmi退货打印
export const thirdVmiReturnedOrderPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiReturnedOrder/print-html`, data, {
    responseType: 'blob'
  })

// 钢材总数库龄报表导出
export const batchTENANTAgeViewExport = (data = {}) =>
  API.post(`${STAT_TENANT}/steelStock/buyer/stock-day-export`, data, {
    responseType: 'blob'
  })

// vmi库龄报表导出
export const batchVmiTENANTAgeViewExport = (data = {}, field) =>
  API.post(
    `${STAT_TENANT}/vmiStock/buyer/stock-day-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// vmi入库头视图导出
export const buyerExcelAgeViewExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmi-receive-order/buyer-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// vmi入库明细视图导出
export const buyerItemAgeViewExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmi-receive-order/buyer-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 库存查询采方导出
export const vmiStockExport = (data = {}, field) =>
  API.post(`${BASE_TENANT}/vmi_stock/buyer/export?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })

// 库存查询供方导出
export const vmiSupStockExport = (data = {}, field) =>
  API.post(`${BASE_TENANT}/vmi_stock/supplier/export?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })

// 库存查询第三方导出
export const vmiLogStockExport = (data = {}, field) =>
  API.post(`${BASE_TENANT}/vmi_stock/logistic/export?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })

// 库存查询调拨
export const stockAllocationKtApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/vmi_stock/stockAllocationKt`, data)
//
export const listConfigByParam = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/pickupOrderAddress/listConfigByParam`, data)
// 获取账款
export const getAmmountData = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/summarize-ammount`, data)
//
export const orderViewViewExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/report/buyer/orderView/export`, data, {
    responseType: 'blob'
  })

// VMI领料单详情==保存||提交
export const VMIPickupOrderCreate = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmi-pickup-order/create`, data)
}

// VMI领料单列表==提交（列表-快速提交）
export const VMIPickupOrderFastSubmit = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmi-pickup-order/fast-submit`, data)
}

// VMI领料单列表==删除（列表-删除）
export const VMIPickupOrderDelete = (data = {}) => {
  return API.delete(`${PROXY_BASE}/tenant/vmi-pickup-order/delete`, data)
}

// VMI领料单列表==取消（列表-取消）
export const VMIPickupOrderCancel = (data = {}) => {
  return API.put(`${PROXY_BASE}/tenant/vmi-pickup-order/cancel`, data)
}
// 根据物料编码和业务组类型获取业务组
export const VMIPickupOrderQueryBusiness = (data = {}) => {
  return API.get(`/masterDataManagement/tenant/business-group-item-code/business-group`, data)
}
// 采方获取采购订单 单据视图
export const purOrderQuery = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/purOrder/query`, data)
}
// 采方获取采购订单 明细视图
export const purOrderDetailQuery = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/po/detail/pageQuery`, data)
}
// 采方获取采购订单 验收计划汇总
export const purAcceptanceQuery = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/po/acceptance/query`, data)
}
// 采方获取订单号
export const purOrderQueryOrder = (data = {}) => {
  return API.post(`/srm-purchase-execute/tenant/purOrder/queryOrder`, data)
}
// 订单行号
export const getByOrder = (data = {}) => {
  return API.get(`/srm-purchase-execute/tenant/purOrder/getByOrder`, data)
}
// 配置相关
// 请求物流公司接口
export const postLogistics = (data = {}) =>
  API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)

// new VMI领料单列表==删除（列表-删除）
export const VMINewPickupOrderDelete = (data = {}) => {
  return API.delete(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/delete`, data)
}

// new VMI领料单列表 创建弹窗钢材库存导出
export const VMINewPickupExport = (data = {}, field) => {
  return API.post(
    `${BASE_TENANT}/vmiSteel/vmi_stock/buyer-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
}

// new 钢材入库单采方明细导出
export const VMIWarehousingIndexpExport = (data = {}, field) => {
  return API.post(
    `${BASE_TENANT}/vmiSteel/vmi-receive-order/buyer-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
}

// new VMI领料单列表==提交（列表-快速提交）
export const VMINewPickupOrderFastSubmit = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/fast-submit`, data)
}

// new VMI领料单列表==提交（列表-批量快速提交）
export const VMINewPickupOrderBatchFastSubmit = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/batch-fast-submit`, data)
}

// new VMI领料单列表==取消（列表-取消）
export const VMINewPickupOrderCancel = (data = {}) => {
  return API.put(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/cancel`, data)
}
// new VMI领料单详情==保存||提交
export const VmiSteelPickupOrderCreate = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/create`, data)
}
// new 获取金额

export const VmiSteelGetPickupAmount = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/getPickupAmount`, data)
}
// new 冲销 退货头视图列表
export const VMINewBuyerWarehousingReturnGoods = `${BASE_TENANT}/vmiSteel/vmi-order/buyer-page-query`

// new 冲销创建
export const VmiSteelReturnedOrder = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmiReturnedOrder/batch-create`, data)
}

// new 供方 冲销创建
export const VmiSteelSupReturnedOrder = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmiReturnedOrder/sup/batch-create`, data)
}
// new采方入库管理 ====
// new 详情接口
export const VmiPostBuyerWarehousingDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/detail`, data)

// new 退货明细视图列表
export const postNewReturnQueryDetail = `${BASE_TENANT}/vmiSteel/vmiReturnedOrder/item-page-query`

// 8.19 new
// 领料单删除
export const postReceiveRemoveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/removeDetail`, data)
// new VMI领料单详情==查询
export const VMINewPickupOrderDetailQuery = (data = {}) => {
  return API.post(`${PROXY_BASE}/tenant/vmiSteel/vmi-pickup-order/detail`, data)
}
// new 查询送货地址配置
export const siteTenantExtendQuery = (data = {}) => {
  return API.post(`${BASE_TENANT}/siteTenantExtend/queryBySupplierForSteel`, data)
}

// 启用禁用
export const activeNewStockSetting = (data = {}) =>
  API.patch(`${BASE_TENANT}/saleOutsourceAmountConfig/turnState`, data)
// 删除
export const deleteNewStockSetting = (data = {}) =>
  API.delete(`${BASE_TENANT}/saleOutsourceAmountConfig/delete`, data)
// 新增
export const saveNewStockSetting = (data = {}) =>
  API.post(`${BASE_TENANT}/saleOutsourceAmountConfig/save`, data)

// 库存查询 - 查看变更记录
export const checkEditInfo = (data = {}) =>
  API.get(`${BASE_TENANT}/inventoryStockUpdateRecord`, data)

// 库存查询 - 导出
export const supSystemCallInfo = (data = {}) =>
  API.post(
    `${BASE_TENANT}/inventoryStockUpdateRecord/export?vmiStockId=${data.vmiStockId}&sourceType=${data.sourceType}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 供方库存系统调用日志查询 - 列表视图 - 导出
export const supWarechaseSystemCallMainExport = (data = {}) =>
  API.post(`${BASE_TENANT}/inventorySync/record/downLoad`, data, {
    responseType: 'blob'
  })

// 供方库存系统调用日志查询 - 明细列表 - 导出
export const supWarechaseSystemCallDetailExport = (data = {}) =>
  API.post(`${BASE_TENANT}/inventorySync/recordDetail/downLoad`, data, {
    responseType: 'blob'
  })

// 送货单列表 - 创建SAP交货单 - 采,供方
export const createSapdeliveryNote = (data = {}) =>
  API.post(`${BASE_TENANT}/buyerOrderDelivery/syncSapGetDeliveryNo`, data)
