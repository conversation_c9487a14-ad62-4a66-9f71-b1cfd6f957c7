/*
 * @Author: zhaoriyang3 <EMAIL>
 * @Date: 2022-05-18 09:53:59
 * @LastEditors: zhaoriyang3 <EMAIL>
 * @LastEditTime: 2022-06-21 10:26:52
 * @FilePath: \mtech-srm-purchase-execute-web\src\apis\modules\supplierCoordination.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// vmi-供方接口
import { API } from '@mtech-common/http'
import { BASE_TENANT } from '@/utils/constant'
export const NAME = 'supplierCoordination'
import { PROXY_MDM_COMMON_TENANT } from '@/utils/constant'
// 采方入库管理=====
// export const postBuyerWarehousingList = (data = {}) =>
//   API.post(`${BASE_TENANT}/vmi-receive-order/buyer-page-query`, data);\

// 8.16   new
// 保存入库单数据
export const postBuyerWarehousingSave = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/save`, data)

// 8.16   new
// 获取详情入库单数据
export const postReceiveDetail = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/detail `, data)

// 打印入库单
export const postOrderPrintNew = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/print `, data, {
    responseType: 'blob'
  })
export const postOrderPrintHtml = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/print-html `, data, {
    responseType: 'blob'
  })
// 入库单打印供方头视图
export const postSupplierOrderPrintNew = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmi-receive-order/supplier-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 入库单打印供方明细视图
export const postSupplierItemOrderPrintNew = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmi-receive-order/supplier-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 入库单打印第三方明细视图
export const postLogisticOrderPrintNew = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmi-receive-order/logistic-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 入库单明细批量确认
export const postLogisticOrderConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-receive-order/batchConfirm`, data)

// 入库单打印第三方明细视图
export const postLogisticItemOrderPrintNew = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmi-receive-order/logistic-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 8.16   new
// 打印入库单
export const postOrderPrint = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/print `, data, {
    responseType: 'blob'
  })

// 入库单导出明细视图
export const vmiReceiveOrderExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiSteel/vmi-receive-order/supplier-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 8.16 new
// 删除入库单
export const postReceiveDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiSteel/vmi-receive-order/delete`, data)

// 9.24 new
// 提交入库单批量
export const postReceiveSubmitEvery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/submitList`, data)

// 8.16 new
// 取消入库单详情
export const postReceiveCancel = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-receive-order/cancel`, data)

// 8.16 new
// 打印领料
export const postReceivePrint = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/print`, data, {
    responseType: 'blob'
  })

// 8.16 new
// 提交入库单详情
export const postReceiveSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/submit`, data)

// 8.16 new
// 接收入库单详情
export const postReceiveConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-receive-order/confirm`, data)

//8.18 new
export const purNewOrderQueryBatchConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/batch-confirm`, data)

// 8.18 new
//供方退回
export const purNewOrderQueryBatchReject = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/batch-reject`, data)

// 8.18 new
//供方确认
export const purNewOrderQueryConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/confirm`, data)

// 删除
export const postBuyerWarehousingDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmi-receive-order/delete`, data)

//   取消
export const postBuyerWarehousingCancel = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-receive-order/cancel`, data)

// 页面的提交接口
export const postBuyerWarehousingSubmit = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/fast-submit`, data)

export const postSupplierPageQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiWarehouse/supplier-page-query`, data)
// 创建接口
export const postBuyerWarehousingCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/create`, data)
// 请求物流公司接口
export const postLogisticsCompany = (data = {}) =>
  API.post(`${PROXY_MDM_COMMON_TENANT}/dict-item/dict-code`, data)
// 查询出vmi仓接口
export const postWarehouse = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/criteria-query-for-supplier`, data)
// 查询出vmi仓接口 - 新
export const getVmiWarehouse = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/listByQueryBuilder`, data)

// 查询工厂接口 - 新
export const getFactoryList = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/listByQueryBuilder`, data)

// 查询出vmi仓接口 - 新 第三方物流
export const getVmiWarehouseThird = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/listByQueryBuilderForLogistic`, data)

// 查询出vmi仓接口 - 新 供方
export const getVmiWarehouseSupplier = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi_warehouse_supplier_rel/listByQueryBuilderForSupplier`, data)

// 根据vmi仓查出地址
export const postWarehouseAddress = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiWarehouse/supplier/findByCode`, data)
// 根据订单编号及行号查询该订单可发货数量
export const postReceiveLimitQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-receive-order/receive-limit-query`, data)
// 库存调整管理====
// 调用客户接口
export const postSupplierStockCustomer = (data = {}) =>
  API.post(`masterDataManagement/tenant/customer/criteria-query`, data)
// 创建供应商库存导入-新建
export const postSupplierStockImportCreate = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSupStockOrder/create`, data)
// 创建供应商库存导入-单独提交
export const postSupplierStockImportSubmit = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSupStockOrder/submit`, data)
// 删除供应商导入入库单
export const postSupplierStockImportDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiSupStockOrder/delete`, data)

//送货单 /tenant/vmi-receive-order/delete  1
export const postSupplierStockImportDelete1 = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmi-receive-order/delete`, data)
//领料单 /tenant/vmi-pickup-order/delete 2
export const postSupplierStockImportDelete2 = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmi-pickup-order/delete`, data)
//// 退货单 /tenant/vmiReturnedOrder/delete 3
export const postSupplierStockImportDelete3 = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiReturnedOrder/delete`, data)
// 库存导入 /tenant/vmiSupStockOrder/delete 4
export const postSupplierStockImportDelete4 = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiSupStockOrder/delete`, data)
// 调拨单 /tenant/vmiAllocationOrder/delete 5
export const postSupplierStockImportDelete5 = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiAllocationOrder/delete`, data)
// 替换单 /tenant/vmiReplaceOrder/delete 6
export const postSupplierStockImportDelete6 = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiReplaceOrder/delete`, data)

// 模糊查询订单号
export const purOrderQueryOrder = (data = {}) =>
  API.post(`${BASE_TENANT}/supOrder/queryOrder`, data)
// 库存管理导出
export const supplierStockExport = (data = {}, field) =>
  API.post(`${BASE_TENANT}/vmiSupStockOrder/export?includeColumnFiledNames=${field}`, data)
// 库存管理导出 - 供应商
export const supplierHeaderStockExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSupStockOrder/supplier-export`, data, {
    responseType: 'blob'
  })
// 库存管理导出 - 第三方物流
export const logisticsHeaderStockExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSupStockOrder/logistics-export`, data, {
    responseType: 'blob'
  })
// 采方明细导出
export const buyerExcelExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiAllocationOrder/buyer-ext-detail-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 采方汇总导出
export const buyerSumExcelExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSupStockOrder/buyer-export`, data, {
    responseType: 'blob'
  })
// 供方明细导出
export const supplierExcelExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiAllocationOrder/supplier-ext-detail-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

// 供方库龄导出
export const supplierStockExcelExport = (data = {}) =>
  API.post(`/statistics/tenant/vmiStock/supplier/stock-day-export`, data, {
    responseType: 'blob'
  })

// 第三方物料库龄导出
export const supplierLogisticsExcelExport = (data = {}, field) =>
  API.post(
    `/statistics/tenant/vmiStock/logistics/stock-day-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
// 第三方明细导出

export const logisticExcelExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiAllocationOrder/logistic-ext-detail-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )

//
export const supplierBatchViewExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/report/supplier/batchView/export`, data, {
    responseType: 'blob'
  })

//
export const supplierOrderViewExport = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/report/supplier/orderView/export`, data, {
    responseType: 'blob'
  })

export const supplierSoOrderViewExport = (data = {}) =>
  API.post(`${BASE_TENANT}/so/detail/supplier-available-excel-export`, data, {
    responseType: 'blob'
  })
//==  领料  ==
//供方确认
export const purOrderQueryConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-pickup-order/confirm`, data)
//供方确认 new
export const purOrderNewQueryConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/confirm`, data)
export const purOrderQueryBatchConfirm = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-pickup-order/batch-confirm`, data)
//供方退回
export const purOrderQueryBatchReject = (data = {}) =>
  API.put(`${BASE_TENANT}/vmi-pickup-order/batch-reject`, data)

//供方退回 new
export const purOrderNewQueryBatchReject = (data = {}) =>
  API.put(`${BASE_TENANT}/vmiSteel/vmi-pickup-order/batch-reject`, data)

// 退货头视图列表
export const postVMIReturnQuery = `${BASE_TENANT}/vmi-order/supplier-page-query`
// 退货头视图列表 new
export const postNewVMIReturnQuery = `${BASE_TENANT}/vmiSteel/vmi-order/supplier-page-query`
// 退货明细视图列表
export const postVMIReturnQueryDetail = `${BASE_TENANT}/vmi-order/supplier-page-query-detail`

// 退货明细视图列表
export const postNewVMIReturnQueryDetail = `${BASE_TENANT}/vmiSteel/vmi-order/supplier-page-query-detail`

// 物流信息维护--列表
export const postVMIPagedQuery = `${BASE_TENANT}/vmi-logistics-company/paged-query`

// new 物流信息维护--列表
export const postNewVMIPagedQuery = `${BASE_TENANT}/vmiSteel/vmi-logistics-company/paged-query`

//物流信息维护--新增/编辑
export const purOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-logistics-company/save`, data)
//new 物流信息维护--新增/编辑
export const NewPurOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-logistics-company/save`, data)

// new 物流信息维护
export const purNewOrderSave = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-logistics-company/paged-query`, data)

// 物流信息维护--删除
export const postBuyerBatchDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmi-logistics-company/batch-delete`, data)

// new 物流信息维护--删除
export const postNewBuyerBatchDelete = (data = {}) =>
  API.delete(`${BASE_TENANT}/vmiSteel/vmi-logistics-company/batch-delete`, data)

// 物流信息维护--启用/禁用
export const postBuyerBatchSwitchStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-logistics-company/batch-switch-status`, data)
// new 物流信息维护--启用/禁用
export const postNewBuyerBatchSwitchStatus = (data = {}) =>
  API.post(`${BASE_TENANT}/vmiSteel/vmi-logistics-company/batch-switch-status`, data)

// 物流信息维护--客户公司名称下拉接口
export const postBuyerCriteriaQuery = (data = {}) =>
  API.post(`/masterDataManagement/tenant/customer/criteria-query`, data)

// 物流信息维护--客户公司名称下拉接口
export const postNewBuyerCriteriaQuery = (data = {}) =>
  API.post(`/masterDataManagement/tenant/customer/criteria-query`, data)

// 库存调整管理Excel模板导出
export const exportExcel = (data = {}) => {
  return API.get(`${BASE_TENANT}/vmiSupStockOrder/exportExcelModel`, data, {
    responseType: 'blob'
  })
}

// 库存调整管理Excel导入
export const importExcel = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmiSupStockOrder/importExcel`, data, {
    responseType: 'blob'
  })
}

// 库存调整管理Excel导入
export const importExcelNew = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmi-receive-order/importExcel`, data, {
    responseType: 'blob'
  })
}

// 第三方物流退货导入模板
export const vmiReturnedOrderExcelNew = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmiReturnedOrder/exportTemplate`, data, {
    responseType: 'blob'
  })
}

// VMI调拨单导入模板下载
export const vmiAllocationOrderTemplateDownload = (data = {}) => {
  return API.get(`${BASE_TENANT}/vmiAllocationOrder/templateDownload`, data, {
    responseType: 'blob'
  })
}

// VMI调拨单导入
export const vmiAllocationOrderImport = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmiAllocationOrder/import`, data, {
    responseType: 'blob'
  })
}

// VMI替换导入模板下载
export const vmiReplaceOrderTemplateDownload = (data = {}) => {
  return API.get(`${BASE_TENANT}/vmiReplaceOrder/templateDownload`, data, {
    responseType: 'blob'
  })
}

// VMI替换单导入
export const vmiReplaceOrderImport = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmiReplaceOrder/import`, data, {
    responseType: 'blob'
  })
}

// 第三方物流退货导入
export const vmiReturnedOrderImportNew = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmiReturnedOrder/import`, data, {
    responseType: 'blob'
  })
}

// 钢材vmi入库 导入
export const vmiSteelImportExcel = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmiSteel/vmi-receive-order/importExcel`, data, {
    responseType: 'blob'
  })
}
// 钢材vmi入库 导入模板
export const vmiSteelExportExcel = (data = {}) => {
  return API.get(`${BASE_TENANT}/vmiSteel/vmi-receive-order/exportExcel`, data, {
    responseType: 'blob'
  })
}
// vmi入库 导入
export const vmiImportExcel = (data = {}) => {
  return API.post(`${BASE_TENANT}/vmi-receive-order/importExcel`, data, {
    responseType: 'blob'
  })
}
// vmi入库 导入模板
export const vmiExportExcel = (data = {}) => {
  return API.get(`${BASE_TENANT}/vmi-receive-order/exportExcel`, data, {
    responseType: 'blob'
  })
}

// VMI物流公司信息维护--客户公司名称下拉接口
export const postNewSupCriteriaQuery = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-logistics-company/criteria-query`, data)

// 供方退货管理导入
export const supplierOutImportExcel = (data = {}) =>
  API.post(`${BASE_TENANT}/vmi-order/importExcel`, data, {
    responseType: 'blob'
  })

// 供方退货管理导入模板
export const supplierOutTemplateDownload = (data = {}) =>
  API.get(`${BASE_TENANT}/vmi-order/templateDownload`, data, {
    responseType: 'blob'
  })

// 供方退货管理导出
export const supplierOutExportExcel = (data = {}, field) =>
  API.post(`${BASE_TENANT}/vmi-order/excelExport?includeColumnFiledNames=${field}`, data, {
    responseType: 'blob'
  })

// vmi退货明细导出 -供方
export const postSupplierWarehousingReturnDetailExport = (data = {}, field) =>
  API.post(
    `${BASE_TENANT}/vmiReturnedOrder/supplier-item-excel-export?includeColumnFiledNames=${field}`,
    data,
    {
      responseType: 'blob'
    }
  )
