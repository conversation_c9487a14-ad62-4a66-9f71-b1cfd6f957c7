import { API } from '@mtech-common/http'
import { BASE_TENANT } from '@/utils/constant'

export const NAME = 'capacityManagement'
const prefix = BASE_TENANT + '/mold/config'

// 模具产能配置 - 查询
export const getcapacityManagementList = (data = {}) => {
  return API.post(`${prefix}/query`, data)
}

// 模具产能配置 - 新增
export const addCapacityManagementInfo = (data = {}) => {
  return API.post(`${prefix}`, data)
}

// 模具产能配置 - 编辑
export const editCapacityManagementInfo = (data = {}) => {
  return API.put(`${prefix}`, data)
}

// 模具产能配置 - 删除
export const deleteCapacityManagementInfo = (data = []) => {
  return API.delete(`${prefix}?ids=${data.join()}`)
}

// 模具产能配置 - 启用
export const enableCapacityManagementInfo = (data = {}) => {
  return API.put(`${prefix}/onLineAndOffLine?ids=${data.ids}&status=${data.status}`)
}

// 模具产能配置 - 停用
export const deactivateCapacityManagementInfo = (data = {}) => {
  return API.put(`${prefix}/onLineAndOffLine?ids=${data.ids}&status=${data.status}`)
}

// 模具产能配置 - 刷新产能收集
export const refreshCapacityManagementApi = (data = {}) => {
  return API.get(`${prefix}/refreshMoldCapacity`, data)
}

// 导入
export const importCapacityManagementList = (data = {}) => {
  return API.post(`${prefix}/import`, data, {
    responseType: 'blob'
  })
}

// 导入模板下载
export const downloadImportTemplate = (data = {}) => {
  return API.get(`${prefix}/template`, data, {
    responseType: 'blob'
  })
}

// 导出
export const exportCapacityManagementList = (data = {}) => {
  return API.post(`${prefix}/export`, data, {
    responseType: 'blob'
  })
}

// 模具产能收集 - 分页
export const pageCapacityCollectionApi = (data = {}) => {
  return API.post(
    `${BASE_TENANT}/mold/capacityArchive/query?versionNo=${data.versionNo}&archiveStatus=${data.archiveStatus}`,
    data.page
  )
}

// 模具产能收集 - 归档
export const fileCapacityCollectionApi = (data = {}) => {
  return API.put(
    `${BASE_TENANT}/mold/capacityArchive?ids=${data.ids}&archiveStatus=${data.archiveStatus}`
  )
}

// 模具产能收集 - 物料明细 - 分页
export const pageCapacityCollectionListApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/mold/collect/query`, data)
}

// 模具产能收集 - 物料明细 - 保存
export const saveCapacityCollectionListApi = (data = {}) => {
  return API.put(`${BASE_TENANT}/mold/collect/update`, data)
}

// 模具产能回复 - 物料明细 - 保存
export const saveSupplierCapacityCollectionListApi = (data = {}) => {
  return API.put(`${BASE_TENANT}/mold/collect?supplier=${data.supplier}`, data.data)
}

// 模具产能回复 - 更新状态
export const updateStatusCapacityCollectionListApi = (data = {}) => {
  return API.put(
    `${BASE_TENANT}/mold/collect/update/viewStatus?supplierCode=${data.supplierCode}&versionNo=${data.versionNo}`
  )
}

// 模具产能收集 - 物料明细 - 导出
export const exportCapacityCollectionListApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/mold/collect/export`, data, {
    responseType: 'blob'
  })
}

// 模具产能收集 - 物料明细 - 导入模板
export const dowmloadTempCapacityCollectionListApi = (data = {}) => {
  return API.get(`${BASE_TENANT}/mold/collect/template`, data, {
    responseType: 'blob'
  })
}

// 模具产能收集 - 物料明细 - 采方导入
export const importCapacityCollectionListApi = (data = {}, queryParams = {}) => {
  return API.post(
    `${BASE_TENANT}/mold/collect/purchase/import?dayCapacityLevel=` + queryParams.dayCapacityLevel,
    data,
    {
      responseType: 'blob'
    }
  )
}

// 模具产能收集 - 物料明细 - 供方导入
export const importSupplierCapacityCollectionListApi = (data = {}, queryParams = {}) => {
  return API.post(
    `${BASE_TENANT}/mold/collect/supplier/import?dayCapacityLevel=` + queryParams.dayCapacityLevel,
    data,
    {
      responseType: 'blob'
    }
  )
}

// 模具库存报表 - 分页
export const pageInventoryReportApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/mold/capacityReportForms/query`, data)
}

// 模具库存报表  - 导出
export const exportInventoryReportApi = (data = {}) => {
  return API.post(`${BASE_TENANT}/mold/capacityReportForms/export`, data, {
    responseType: 'blob'
  })
}

// 供方 - 获取物料信息
export const getSupplierItemCode = (data = {}) =>
  API.post(
    `/masterDataManagement/tenant/item/fuzzy-paged-query?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
