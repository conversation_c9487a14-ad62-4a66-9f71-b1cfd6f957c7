<template>
  <!-- 获取验证码按钮 -->
  <button :disabled="!complete" :class="['code-btn', !complete && 'btn-disabled']" @click.prevent>
    {{ $t(countDownText) }}
  </button>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { useTCaptcha } from '@/utils/index'
@Component({})
export default class PfVerifyButton extends Vue {
  /** data -start */
  countDownText: string = '获取验证码' // 获取验证码
  last: number = 60
  complete: boolean = true
  /** data -end */

  /** method --start */
  // open() {}
  countDown() {
    if (this.last <= 0 || !this.complete) return
    this.complete = false
    let step = 1000
    let timmer = setInterval(() => {
      this.last--
      this.countDownText = `${this.last}s`
      if (this.last <= 0) {
        clearInterval(timmer)
        this.countDownText = '再次发送' // 再次发送
        this.complete = true
        this.last = 20
      }
    }, step)
  }

  openCaptcha() {
    if (this.last <= 0 || !this.complete) return
    useTCaptcha().then((res: any) => {
      this.$emit('finish', res)
    })
  }
  /** method --end */
}
</script>

<style lang="less" scoped>
.code-btn {
  border: none;
  color: #2783fe;
  background: transparent;
  font-size: 14px;
  padding: 0 10px;
  cursor: pointer;
  white-space: nowrap;
  margin-bottom: -5px;
}
.btn-disabled {
  border: 1px solid #ccc;
  color: #ccc;
}
</style>
