<template>
  <div class="input-other-tips">
    <span
      :class="[
        'strong-level',
        strengths >= 0 && 'strong-level-0',
        strengths === 0 && 'level-actived'
      ]"
    ></span>
    <span
      :class="[
        'strong-level',
        strengths >= 1 && 'strong-level-1',
        strengths === 1 && 'level-actived'
      ]"
    ></span>
    <span
      :class="[
        'strong-level',
        strengths >= 2 && 'strong-level-2',
        strengths === 2 && 'level-actived'
      ]"
    ></span>
    <span>
      {{ $t('安全等级') }} ：{{
        strengths === 0 ? $t('低') : strengths === 1 ? $t('中') : $t('高')
      }}</span
    >
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
@Component
export default class MtPwdStrength extends Vue {
  @Prop({ type: Number, required: false, default: 0 }) readonly strengths!: number
}
</script>

<style lang="less" scoped>
.input-other-tips {
  position: absolute;
  top: 0;
  right: 0;
  height: 48px;
  line-height: 48px;
  // width: 180px;
  font-size: 12px;
  color: #606266;
  // border-bottom: 1px solid #3e3e3e;
  display: flex;
  align-items: center;
  justify-content: space-around;
  // margin-left: 8px;
  .strong-level {
    width: 17px;
    height: 6px;
    margin-right: 6px;
    display: inline-block;
    background: #dbd9dc;
    &-0 {
      background: #d31616;
    }
    &-1 {
      background: #f07c2c;
    }
    &-2 {
      background: #52bd17;
    }
  }
  .level-actived {
    color: #f44336;
  }
}
</style>
