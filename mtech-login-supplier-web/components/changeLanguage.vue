<template>
  <div class="change-language">
    <mt-select
      :width="120"
      :popupWidth="120"
      cssClass="lang-select e-outline"
      v-model="lang"
      :dataSource="langList"
      :fields="{ text: 'name', value: 'code' }"
      :open-dispatch-change="false"
      @change="changeLang"
    ></mt-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
@Component
export default class ChangeLanguage extends Vue {
  langList: any = []
  lang: string = 'zh-CN'

  created() {
    if (this.$cookies.get('locale')) {
      this.lang = this.$cookies.get('locale')
      this.$store.commit('setLocale', this.lang)
    } else {
      this.$cookies.set('locale', this.$store.state.locale)
    }
    this.getLanguage()
  }

  async getLanguage() {
    const { code, data } = await this.$api.login.getLanguage()
    if (code === 200) {
      const item = data.find((el: any) => el.code === 'zh')
      if (item) item.code = 'zh-CN'
      this.langList = data
    }
  }

  changeLang(val: any) {
    this.$store.commit('setLocale', val.value)
    this.$cookies.set('locale', val.value)
    this.$changeL(val.value)
    if (process.client) {
      localStorage.setItem('internationlization', val.value)
      // location.reload()
    }
  }
}
</script>

<style lang="less" scoped>
.change-language {
  margin-right: 0 !important;
  height: 32px;
  /deep/ .select-container {
    height: 100%;
    .e-input-group {
      border: none;
      margin: 0;
      color: #999;
      .e-input {
        padding: 0 0 0 8px;
        min-height: 32px;
      }
      .e-input-group-icon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }
  /deep/ .lang-select {
    color: #999;
    font-size: 16px;
    background-color: #fff;
    height: 32px;
    font-size: 14px;
    line-height: 32px;
    .e-float-line {
      display: none;
    }
    .e-input-group-icon {
      color: #999;
    }
  }
}
</style>
