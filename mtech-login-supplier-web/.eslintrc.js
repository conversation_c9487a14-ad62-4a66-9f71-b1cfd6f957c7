module.exports = {
  root: true,
  env: {
    browser: true,
    node: true
  },
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2017,
    sourceType: 'module'
  },
  extends: [
    '@mtech/eslint-config-vue',
    'eslint:recommended',
    'plugin:vue/essential',
    'plugin:@typescript-eslint/recommended'
  ],
  // 'vue-typescript',
  ecmaFeatures: {
    legacyDecorators: true
  },
  // plugins: ['prettier'],
  // add your custom rules here
  rules: {
    '@typescript-eslint/no-inferrable-types': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-this-alias': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'no-redeclare': 'off',
    'vue/attribute-hyphenation': 'off',
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/no-extra-semi': 0,
    'prettier/prettier': [
      'error',
      {
        semi: false,
        endOfLine: 'auto'
      }
    ]
    // 'semi': [2, 'never'],
    // 'eqeqeq': 'off',
    // 'no-lonely-if': 'off',
    // 'no-console': process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'devCheck' ? 'error' : 'off',
    // 'no-debugger': process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'devCheck' ? 'error' : 'off',
    // 'require-atomic-updates': 'off',
    // 'vue/max-attributes-per-line': 'off',
    // 'vue/singleline-html-element-content-newline': 'off',
    // 'vue/html-self-closing': 'off',
    // 'vue/no-v-html': 'off',
    // 'nuxt/no-cjs-in-config': 'off',
    // 'prefer-const': 'off',
    // 'no-unused-expressions': 'off',
    // 'vue/html-quotes': ['error', 'double'],
    // 'vue/html-indent': [
    //   'error',
    //   2,
    //   {
    //     attribute: 1,
    //     baseIndent: 1,
    //     closeBracket: 0,
    //     alignAttributesVertically: true,
    //     ignores: []
    //   }
    // ],
    // 'prettier/prettier': [
    //   'error',
    //   {
    //     semi: false,
    //     singleQuote: true,
    //     quoteProps: 'consistent',
    //     printWidth: 150,
    //     tabWidth: 2,
    //     proseWrap: 'always',
    //     endOfLine: 'auto',
    //     trailingComma: 'none' // 对象最后一项默认格式化会加逗号, 这里取消
    //   }
    // ]
  }
}
