<template>
  <div class="share-wrapper">
    <img :src="imgUrl" />
  </div>
</template>
<script>
export default {
  layout: 'h5',
  data() {
    return {
      formData: {},
      imgUrl: ''
    }
  },
  mounted() {
    this.getImgUrl()
  },
  methods: {
    // 获取图片信息
    getImgUrl() {
      const params = {
        rfxId: this.$route.query?.rfxId
      }
      this.$api.register.getSharePicture(params).then((res) => {
        if (res.code === 200) {
          this.imgUrl = res.data
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.share-wrapper {
  width: 100%;
  img {
    width: 100%;
  }
}
</style>
