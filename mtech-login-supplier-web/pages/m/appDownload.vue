<template>
  <div class="down-wrapper">
    <div class="logo"></div>
    <p>TCL-SRM</p>
    <div class="desc">
      <span class="line"></span>
      <span class="text">当责|创新|卓越</span>
      <span class="line"></span>
    </div>
    <p class="tips">
      如果您在点击按钮后没有看到安装提示，请确保您正在使用<b>苹果设备</b>并在<b>默认浏览器</b>中访问此页面。
    </p>
    <a class="down-btn" @click="down" href="javascript:void(0)">下载安装</a>
  </div>
</template>
<script>
export default {
  layout: 'h5',
  data() {
    return {}
  },

  methods: {
    down() {
      if (this.isWeChatBrowser) {
        alert('请在默认浏览器中打开')
      } else {
        const _host = window.location.host
        window.location.href = `itms-services:///?action=download-manifest&url=${_host}/api/purchase/app/public/pc/srm.plist`
      }
    },
    isWeChatBrowser() {
      const userAgent = navigator.userAgent.toLowerCase()
      return /micromessenger/.test(userAgent)
    }
  }
}
</script>
<style lang="less" scoped>
.down-wrapper {
  position: fixed;
  width: 100%;
  height: 100vh;
  text-align: center;
  background-image: url(../../assets/images/bg-download.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  .logo {
    width: 99px;
    height: 99px;
    margin: 110px auto 0;
    background: url(../../assets/images/icon-logo.png) no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
  }
  p {
    margin-top: 16px;
    font-family: PingFangSC-SNaNpxibold;
    font-weight: 600;
    font-size: 21px;
    color: #000000;
    letter-spacing: 1px;
  }
  .desc {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #347ffe;
    letter-spacing: 3px;
    .line {
      display: inline-block;
      align-self: center;
      width: 15px;
      height: 1px;
      background: #347ffe;
    }
    .text {
      padding-left: 25px;
      padding-right: 25px;
    }
  }
  .tips {
    margin-top: 35px;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    font-weight: normal;
    padding: 0 30px;
  }
  .down-btn {
    display: inline-block;
    position: fixed;
    left: 0;
    bottom: 50px;
    width: 90%;
    margin-left: 5%;
    height: 50px;
    line-height: 50px;
    background: #4e5a70;
    border-radius: 15px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    text-decoration: none;
  }
}
</style>
