<template>
  <div class="register-wrapper">
    <div class="top">
      <div class="btn" @click="handleBack">{{ $t('返回') }}</div>
      <div class="btn" @click="handleNextStep">{{ $t('下一步') }}</div>
    </div>
    <div class="content">
      <div class="form-title">
        <svg-icon class="icon-style" icon-class="icon_logo" />
        <span>TCL实业数智采购平台(SRM)</span>
      </div>
      <div class="form-content">
        <div class="step-nav">
          <div :class="{ 'step-item': true, active: currentStep >= 0 }">
            <span>{{ $t('注册须知') }}</span>
          </div>
          <span></span>
          <div :class="{ 'step-item': true, active: currentStep >= 1 }">
            <span>{{ $t('填写基本信息') }}</span>
          </div>
          <span></span>
          <div :class="{ 'step-item': true, active: currentStep >= 2 }">
            <span>{{ $t('完成') }}</span>
          </div>
        </div>
        <div class="step-wrapper">
          <p class="tips">
            {{
              $t(
                '请在注册前认真阅读协议，廉洁是与TCL合作的第一原则，让我们共同维护阳光的合作环境！'
              )
            }}
          </p>
        </div>
        <dl>
          <dt>
            <p>{{ $t('《TCL实业供应商协同平台服务条款及声明》') }}</p>
            <span>{{ $t('附件') }}</span>
          </dt>
          <dd>
            <p>{{ $t('中文版') }}</p>
            <div class="download-wrapper">
              <a :href="baseUrl + '/docs/Chinese.pdf'" target="_blank">{{ $t('下载附件') }}</a>
            </div>
          </dd>
          <dd>
            <p>{{ $t('英文版') }}</p>
            <div class="download-wrapper">
              <a :href="baseUrl + '/docs/English.pdf'" target="_blank">{{ $t('下载附件') }}</a>
            </div>
          </dd>
          <dd>
            <p>{{ $t('越南语版') }}</p>
            <div class="download-wrapper">
              <a :href="baseUrl + '/docs/Vietnamese.pdf'" target="_blank">{{ $t('下载附件') }}</a>
            </div>
          </dd>
          <dd>
            <p>{{ $t('西班牙语版') }}</p>
            <div class="download-wrapper">
              <a :href="baseUrl + '/docs/Spanish.pdf'" target="_blank">{{ $t('下载附件') }}</a>
            </div>
          </dd>
          <dd>
            <p>{{ $t('葡萄牙语版') }}</p>
            <div class="download-wrapper">
              <a :href="baseUrl + '/docs/Portuguese.pdf'" target="_blank">{{ $t('下载附件') }}</a>
            </div>
          </dd>
        </dl>
      </div>
      <div class="thirdPartyLink">
        <div class="languageSelect">
          <span>{{ $t('请选择语言') }}：</span>
          <mt-select
            :width="150"
            :popupWidth="150"
            cssClass="lang-select e-outline"
            v-model="lang"
            :dataSource="languages"
            @change="languagesSelect"
          ></mt-select>
        </div>
        <a @click="isViewed = true" :href="jumpUrl" target="_blank">{{ language }}</a>
      </div>
      <div class="clause">
        <mt-checkbox
          :key="checkboxKey"
          :checked="isChecked"
          ref="checkbox"
          @change="onChange"
        ></mt-checkbox>
        <span>{{ $t('我已阅读并同意《TCL实业供应商协同平台服务条款及声明》') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'h5',
  data() {
    return {
      currentStep: 0,
      baseUrl: '/login',
      language: this.$t('我们将根据《隐私声明》来处理您的信息'),
      lang: 'zh',
      languages: [
        { text: '中文', value: 'zh' },
        { text: 'English', value: 'en' },
        { text: 'VietNam', value: 'vi' },
        { text: 'Español', value: 'es' },
        { text: 'Português', value: 'pt' }
      ],
      isChecked: false,
      checkboxKey: 1
    }
  },
  methods: {
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 下一步
    handleNextStep() {
      if (!this.isChecked) {
        return this.$tips.warning({
          title: this.$t('提示'),
          content: this.$t('请同意《TCL实业供应商协同平台服务条款及声明》')
        })
      }
      this.$router.push({
        name: 'baseInfo'
      })
    },
    languagesSelect(e) {
      if (e.value === 'zh') {
        this.language = '我们将根据《隐私声明》来处理您的信息'
      } else {
        this.language = 'We will process your information in accordance with our Privacy Notice'
      }
    },
    onChange(e) {
      this.isChecked = e.checked
    }
  }
}
</script>

<style lang="less" scoped>
.register-wrapper {
  background-color: #fff;
  .top {
    height: 40px;
    line-height: 40px;
    background-color: #4e5a70;
    color: #fff;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    font-size: 14px;
    .btn {
      cursor: pointer;
    }
  }
  .form-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    margin-bottom: 16px;
    .icon-style {
      width: 20px;
      height: 20px;
    }
    span {
      font-weight: 500;
      font-size: 16px;
      color: #4e5a70;
      margin-left: 10px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 1px;
        height: 20px;
        margin-right: 10px;
        background-color: #4a556b;
        opacity: 0.2;
      }
    }
  }
  .form-content {
    padding: 0 16px;
    .step-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .step-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        color: #999;
        span {
          flex: 1;
        }
        &::before {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #ddd;
          color: #fff;
          text-align: center;
          margin-right: 8px;
        }
        &:nth-of-type(1) {
          &::before {
            content: '1';
          }
        }
        &:nth-of-type(2) {
          &::before {
            content: '2';
          }
        }
        &:nth-of-type(3) {
          &::before {
            content: '3';
          }
        }
        &.active {
          color: #333;
          &::before {
            background-color: #2783fe;
          }
        }
      }
      > span {
        margin: 0 8px;
        flex: 1;
        height: 1px;
        background-color: #979797;
      }
    }
  }
}
.step-wrapper {
  a {
    color: #3979f9;
    cursor: pointer;
    user-select: none;
  }
  .tips {
    margin-top: 16px;
    margin-bottom: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    text-align: center;
  }
}
dl {
  dt {
    background-color: #f5f6f7;
    display: flex;
    padding-left: 12px;
    color: #333;
    line-height: 40px;
    p {
      flex: 0 0 80%;
      font-size: 13px;
    }
    span {
      font-size: 13px;
    }
  }
  dd {
    padding-left: 20px;
    display: flex;
    padding-left: 12px;
    color: #333;
    line-height: 40px;
    &:nth-child(odd) {
      background-color: #f5f6f7;
    }
    p {
      flex: 0 0 78%;
      font-size: 13px;
    }
    .download-wrapper {
      font-size: 13px;
    }
    &:last-child {
      border-bottom: 1px solid #eff0f2;
    }
  }
}
.thirdPartyLink {
  margin-top: 24px;
  margin-bottom: 24px;
  .languageSelect {
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-size: 13px;
    }
    /deep/ .lang-select {
      color: #999;
      font-size: 16px;
      background-color: #fff;
      height: 32px;
      font-size: 14px;
      line-height: 32px;
      margin: 0;
      input {
        min-height: 32px !important;
      }
      .e-float-line {
        display: none;
      }
      .e-input-group-icon {
        font-size: 12px;
        color: #999;
        margin-top: 3px;
        margin-bottom: 3px;
      }
    }
  }
  a {
    display: block;
    text-align: center;
    font-size: 13px;
    margin-top: 8px;
    padding: 0 15px;
  }
}
.clause {
  text-align: center;
  color: #666;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding-left: 18px;
  span {
    margin-top: -2px;
  }
}
</style>
