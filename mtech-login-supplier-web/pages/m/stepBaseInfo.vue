<template>
  <div class="register-wrapper">
    <div class="top">
      <div class="btn" @click="handleBack">{{ $t('返回') }}</div>
      <div class="btn" @click="handleNextStep">{{ $t('下一步') }}</div>
    </div>
    <div class="content">
      <div class="form-title">
        <svg-icon class="icon-style" icon-class="icon_logo" />
        <span>TCL实业数智采购平台(SRM)</span>
      </div>
      <div class="form-content">
        <div class="step-nav">
          <div :class="{ 'step-item': true, active: currentStep >= 0 }">
            <span>{{ $t('注册须知') }}</span>
          </div>
          <span></span>
          <div :class="{ 'step-item': true, active: currentStep >= 1 }">
            <span>{{ $t('填写基本信息') }}</span>
          </div>
          <span></span>
          <div :class="{ 'step-item': true, active: currentStep >= 2 }">
            <span>{{ $t('完成') }}</span>
          </div>
        </div>
        <div class="form-wrapper">
          <mt-form ref="ruleForm" :model="formData" :rules="rules" class="mt-form--wrap">
            <mt-form-item
              prop="registerAddressCountryCode"
              class="form-item form-item-prev"
              :label="$t('注册国家/地区')"
              label-style="left"
            >
              <mt-select
                cssClass="e-outline"
                @change="changeCountry"
                v-model="formData.registerAddressCountryCode"
                :dataSource="countryList"
                :placeholder="$t('请选择注册国家/地区')"
              ></mt-select>
            </mt-form-item>
            <mt-form-item
              prop="identityCode"
              class="form-item"
              label-style="left"
              :label="
                $t(
                  formData.registerAddressCountryCode === 'CN' ? '统一社会信用代码' : '相关经营证书'
                )
              "
            >
              <mt-input
                v-model.trim="formData.identityCode"
                type="text"
                cssClass="e-outline"
                :placeholder="
                  $t(
                    formData.registerAddressCountryCode === 'CN'
                      ? '请输入统一社会信用代码'
                      : '请输入相关经营证书'
                  )
                "
                @change="checkIdentityCode"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="enterpriseName"
              class="form-item"
              :label="$t('企业名称')"
              label-style="left"
            >
              <mt-input
                v-model.trim="formData.enterpriseName"
                type="text"
                cssClass="e-outline"
                :placeholder="$t('请输入企业名称')"
                @change="changeEnterpriseName"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="enterpriseEnglishName"
              class="form-item"
              :label="$t('企业英文名称')"
              label-style="left"
            >
              <mt-input
                v-model.trim="formData.enterpriseEnglishName"
                type="text"
                cssClass="e-outline"
                :placeholder="$t('请输入企业英文名称')"
                @change="changeEnterpriseName"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="email"
              class="form-item form-item-prev"
              :label="$t('邮箱')"
              label-style="left"
            >
              <mt-input
                v-model="formData.email"
                cssClass="e-outline"
                type="text"
                :placeholder="$t('请输入邮箱')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="mailCode"
              class="form-item form-item-mail"
              :label="$t('邮箱认证')"
              label-style="left"
            >
              <mt-input
                v-model="formData.mailCode"
                type="text"
                cssClass="e-outline"
                :placeholder="$t('请输入邮箱认证')"
              ></mt-input>
              <span class="form-button">
                <mt-button
                  cssClass="verifica--button"
                  :disabled="mailCodeCountDown >= 0"
                  @click="getMailCode"
                >
                  {{ mailCodeCountDown >= 0 ? `${mailCodeCountDown}s` : $t('获取验证码') }}
                </mt-button>
              </span>
            </mt-form-item>
            <!-- <input type="password" hidden autocomplete="new-password" /> -->
            <mt-form-item
              prop="phoneNumber"
              class="form-item form-item-prev"
              :label="$t('手机号')"
              label-style="left"
            >
              <mt-input
                v-model="formData.phoneNumber"
                :disabled="false"
                :showClearButton="true"
                type="text"
                cssClass="e-outline"
                :placeholder="$t('请输入手机号')"
              ></mt-input>
              <p class="mobile--error" v-if="hasMobileError">
                {{ $t('您的手机号已注册') }}
              </p>
            </mt-form-item>
            <mt-form-item
              prop="telephone"
              class="form-item form-item-next"
              :label="$t('固定电话')"
              label-style="left"
            >
              <mt-input
                v-model="formData.telephone"
                type="text"
                cssClass="e-outline"
                :placeholder="$t('请输入固定电话')"
              ></mt-input>
            </mt-form-item>
            <mt-form-item
              prop="password"
              class="form-item form-item-prev"
              :label="$t('密码')"
              label-style="left"
            >
              <div class="password-wrapper">
                <mt-input
                  v-model="formData.password"
                  :type="passwordInputType"
                  :placeholder="$t('请输入密码')"
                  cssClass="e-outline"
                ></mt-input>
                <svg-icon
                  class="icon-style"
                  @click="switchPasswordDisplay('passwordInputType')"
                  icon-class="eye"
                />
              </div>
            </mt-form-item>
            <mt-form-item
              prop="confirmPassword"
              class="form-item form-item-next"
              :label="$t('确认密码')"
              label-style="left"
            >
              <div class="password-wrapper">
                <mt-input
                  v-model="formData.confirmPassword"
                  :type="passwordConfirmInputType"
                  :placeholder="$t('请输入密码')"
                  cssClass="e-outline"
                ></mt-input>
                <svg-icon
                  class="icon-style"
                  @click="switchPasswordDisplay('passwordConfirmInputType')"
                  icon-class="eye"
                />
              </div>
            </mt-form-item>
            <mt-form-item
              prop="lastName"
              class="form-item form-item-prev"
              :label="$t('姓')"
              label-style="left"
            >
              <mt-input
                v-model="formData.lastName"
                type="text"
                :placeholder="$t('请输入姓氏')"
                cssClass="e-outline"
              ></mt-input>
            </mt-form-item>
            <mt-form-item prop="firstName" class="form-item" :label="$t('名')" label-style="left">
              <mt-input
                v-model="formData.firstName"
                type="text"
                :placeholder="$t('请输入名称')"
                cssClass="e-outline"
              ></mt-input>
            </mt-form-item>
          </mt-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { phoneRegex, pswRegex, emailRegex } from '@/utils/my.validators'
export default {
  layout: 'h5',
  data() {
    return {
      currentStep: 1,
      language: this.$t('我们将根据《隐私声明》来处理您的信息'),
      lang: 'zh',
      languages: [
        { text: '中文', value: 'zh' },
        { text: 'English', value: 'en' },
        { text: 'VietNam', value: 'vi' },
        { text: 'Español', value: 'es' },
        { text: 'Português', value: 'pt' }
      ],
      countryList: [
        {
          value: 'CN',
          text: this.$t('中国大陆')
        },
        {
          value: 'US',
          text: '中国大陆外（含港澳台）'
        }
      ],
      formData: {
        registerAddressCountryCode: 'CN',
        identityCode: null
      },
      rules: {
        phoneNumber: [
          { required: true, message: this.$t('请输入手机号'), trigger: 'blur' },
          { required: true, validator: this.validateMobile, trigger: 'blur' }
        ],
        telephone: [{ required: false, message: this.$t('请输入固定电话'), trigger: 'blur' }],
        identityCode: [
          {
            required: true,
            message: this.$t('请输入统一社会信用代码'),
            trigger: 'blur'
          }
        ],
        registerAddressCountryCode: [
          {
            required: true,
            message: this.$t('请选择注册国家/地区'),
            trigger: 'blur'
          }
        ],
        email: [{ required: true, validator: this.validateMail, trigger: 'blur' }],
        enterpriseName: [{ required: true, message: this.$t('请输入企业名称'), trigger: 'blur' }],
        enterpriseEnglishName: [
          { required: true, message: this.$t('请输入企业英文名称'), trigger: 'blur' },
          {
            max: 70,
            message: this.$t(`企业英文名称长度不能超过70个字符`),
            trigger: 'blur'
          },
          { required: true, validator: this.validateEnglishName, trigger: 'blur' }
        ],
        mailCode: [{ required: true, message: this.$t('请输入邮箱认证'), trigger: 'blur' }],
        password: [{ required: true, validator: this.validatePwd, trigger: 'blur' }],
        confirmPassword: [{ required: true, validator: this.validateConfirmPwd, trigger: 'blur' }],
        firstName: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
        lastName: [{ required: true, message: this.$t('请输入姓氏'), trigger: 'blur' }]
      },
      mailCodeTimer: null,
      mailCodeCountDown: -1,
      hasMobileError: false,
      loading: false,
      passwordInputType: 'password',
      passwordConfirmInputType: 'password'
    }
  },
  methods: {
    switchPasswordDisplay(field) {
      this.$set(this, field, this[field] === 'password' ? 'text' : 'password')
    },

    validateMail(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('请输入邮箱')))
      } else if (!emailRegex.test(value)) {
        callback(new Error(this.$t('邮箱地址格式不正确')))
      } else {
        callback()
      }
    },

    validateMobile(rule, value, callback) {
      this.hasMobileError = false
      if (this.formData.registerAddressCountryCode === 'CN') {
        if (!value) {
          callback(new Error(this.$t('请输入手机号')))
        } else if (!phoneRegex.test(value)) {
          callback(new Error(this.$t('手机号格式不正确')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    validateEnglishName(rule, value, callback) {
      const EnglishNameRegex = /^[a-zA-Z0-9,.\s\-_@!#$%^&*()+={}\[\]|\\:;"'<>.?/`~]+$/ // eslint-disable-line
      if (!value) {
        callback(new Error(this.$t('请输入企业英文名称')))
      } else if (!EnglishNameRegex.test(value)) {
        callback(new Error(this.$t('企业英文名称格式不正确')))
      } else {
        callback()
      }
    },

    validatePwd(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('请输入密码')))
      } else if (!pswRegex.test(value)) {
        callback(new Error(this.$t('需包含字母、数字、特殊符号且长度不少于6位')))
      } else {
        callback()
      }
    },
    validateConfirmPwd(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('请再次输入密码')))
      } else if (value !== this.formData.password) {
        callback(new Error(this.$t('两次密码输入内容不一致')))
      } else {
        callback()
      }
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 下一步
    async handleNextStep() {
      try {
        let valid = await this.$refs.ruleForm.validate()
        if (valid) {
          await this.checkIdentityCode()
          await this.handleRegister()
          this.$router.push({ name: 'success' })
        }
      } catch (error) {
        console.error(error)
      }
    },

    // 注册国家change
    changeCountry(arg) {
      this.$set(this.rules.identityCode[0], 'required', arg.value === 'CN')
      this.$set(this.rules.identityCode[0], 'required', arg.value === 'CN')
      this.$set(this.rules.phoneNumber[0], 'required', arg.value === 'CN')
      this.$set(this.rules.phoneNumber[1], 'required', arg.value === 'CN')
      this.$set(this.rules.telephone[0], 'required', arg.value !== 'CN')
      if (arg.value == 'CN' && this.formData.identityCode) {
        this.$api.register.checkIdentityCodeRepeat(this.formData.identityCode).then((res) => {
          if (res.code == 200 && res.data) {
            this.formData.email = res.data
          }
        })
      }
      if (arg.value == 'US' && this.formData.enterpriseName) {
        this.$api.register.checkNameRepeat(this.formData.enterpriseName).then((res) => {
          if (res.code == 200 && res.data) {
            this.formData.email = res.data
          }
        })
      }
    },
    // 统一社会信用代码
    checkIdentityCode() {
      return new Promise((resolve, reject) => {
        this.$api.register.checkIdentityCode(this.formData.identityCode).then((res) => {
          if (res.code === 200) {
            resolve(res)
          } else {
            if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
              this.$refs.formData.addErrorLabels(res.data.errorLabels)
            }
            reject(res)
          }
        })
      })
    },
    // 企业名称校验
    changeEnterpriseName(e) {
      if (!e) return
      if (this.formData.registerAddressCountryCode !== 'US') return
      this.$api.register.checkNameRepeat(this.formData.enterpriseName).then((res) => {
        if (res.code == 200 && res.data) {
          this.formData.email = res.data
          this.fixed()
        }
      })
    },
    // 获取邮箱
    getMailCode(emailParams = { randstr: '', ticket: '' }) {
      const email = this.formData.email
      if (!email || !emailRegex.test(email) || this.mailCodeCountDown >= 0) return

      this.$api.register
        .getMailSmsCode({
          mail: email,
          rand: emailParams.randstr,
          ticket: emailParams.ticket
        })
        .then((res) => {
          if (res.code === 200) {
            this.mailCodeCountDown = 60
            this.mailCodeTimer = setInterval(() => {
              if (this.mailCodeCountDown > 0) {
                this.mailCodeCountDown--
              } else {
                this.mailCodeCountDown = -1
                clearInterval(this.mailCodeTimer)
              }
            }, 1000)
          } else if (res.code === 601) {
            this.$toast({
              content: this.$t('网络异常，请稍后重试！'),
              type: 'warning'
            })
          }
        })
    },

    // 注册提交
    handleRegister() {
      return new Promise((resolve, reject) => {
        const params = {
          status: 0,
          ...this.formData
        }
        if (this.enterpriseId) {
          params.enterpriseId = this.enterpriseId
        }
        this.$api.register
          .register(params)
          .then((res) => {
            if (res.code === 200) {
              resolve({
                ...this.formData,
                userId: res.data?.userId || ''
              })
            } else {
              if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
                this.$refs.formData.addErrorLabels(res.data.errorLabels)
              }
              reject(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    }
  }
}
</script>

<style lang="less">
.register-wrapper {
  background-color: #fff;
  .top {
    height: 40px;
    line-height: 40px;
    background-color: #4e5a70;
    color: #fff;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    font-size: 14px;
    .btn {
      cursor: pointer;
    }
  }
  .form-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    margin-bottom: 16px;
    .icon-style {
      width: 20px;
      height: 20px;
    }
    span {
      font-weight: 500;
      font-size: 16px;
      color: #4e5a70;
      margin-left: 10px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 1px;
        height: 20px;
        margin-right: 10px;
        background-color: #4a556b;
        opacity: 0.2;
      }
    }
  }
  .form-content {
    padding: 0 16px;
    .step-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .step-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        color: #999;
        span {
          flex: 1;
        }
        &::before {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #ddd;
          color: #fff;
          text-align: center;
          margin-right: 8px;
        }
        &:nth-of-type(1) {
          &::before {
            content: '1';
          }
        }
        &:nth-of-type(2) {
          &::before {
            content: '2';
          }
        }
        &:nth-of-type(3) {
          &::before {
            content: '3';
          }
        }
        &.active {
          color: #333;
          &::before {
            background-color: #2783fe;
          }
        }
      }
      > span {
        margin: 0 8px;
        flex: 1;
        height: 1px;
        background-color: #979797;
      }
    }
  }
}
.step-wrapper {
  a {
    color: #3979f9;
    cursor: pointer;
    user-select: none;
  }
  .tips {
    margin-top: 16px;
    margin-bottom: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    text-align: center;
  }
}
.form-wrapper {
  margin-top: 24px;
  padding-bottom: 40px;
  .label {
    // width: 160px;
    flex: 1;
    font-weight: 500;
    text-align: left !important;
  }
  .e-input-group {
    border: none !important;
    font-size: 3px;
    input {
      padding: 8px 12px;
    }
  }
  .e-input-focus {
    border: none !important;
    box-shadow: none !important;
  }
  .mt-form-item {
    position: relative;
    margin-bottom: 8px;
    border-bottom: 1px solid rgb(221, 221, 221);
    .select-container,
    .mt-input {
      width: 60% !important;
    }
  }
  .verifica--button {
    position: absolute;
    box-shadow: none;
    padding: 0;
    font-size: 14px;
    color: #2783fe;
    background-color: transparent;
    left: -80px;
    top: -18px;
  }
  .password-wrapper {
    width: 60%;
    .mt-input {
      width: 100% !important;
    }
  }
  .svg-icon {
    position: absolute;
    right: 2px;
    top: 10px;
  }
  .error-label-label {
    top: -5px;
    left: 40%;
  }
}
</style>
