<template>
  <div class="register-wrapper">
    <div class="top">
      <div class="btn" @click="handleBack">{{ $t('返回') }}</div>
      <div class="btn" @click="handleNextStep">{{ $t('下一步') }}</div>
    </div>
    <div class="content">
      <div class="form-title">
        <svg-icon class="icon-style" icon-class="icon_logo" />
        <span>数智采购平台(SRM)</span>
      </div>
      <div class="form-content">
        <div class="step-nav">
          <div :class="{ 'step-item': true, active: currentStep >= 0 }">
            <span>{{ $t('注册须知') }}</span>
          </div>
          <span></span>
          <div :class="{ 'step-item': true, active: currentStep >= 1 }">
            <span>{{ $t('填写基本信息') }}</span>
          </div>
          <span></span>
          <div :class="{ 'step-item': true, active: currentStep >= 2 }">
            <span>{{ $t('完成') }}</span>
          </div>
        </div>
        <div class="success-wrapper">
          <div class="success-icon"></div>
          <div class="tips">{{ $t('恭喜你完成 TCL-SRM 注册流程') }}</div>
          <div class="tips">{{ $t('审批完成会以邮件方式通知') }}</div>
          <div class="btn" @click="goHomePage">{{ $t('返回首页') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'h5',
  data() {
    return {
      currentStep: 3,
      username: '邓工测试',
      supplierCode: 'SRM53001500'
    }
  },
  methods: {
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 下一步
    handleNextStep() {
      this.$router.push({
        name: 'success'
      })
    },
    goHomePage() {
      this.$router.push('/m/sourcingDetail')
    }
  }
}
</script>

<style lang="less" scoped>
.register-wrapper {
  background-color: #fff;
  .top {
    height: 40px;
    line-height: 40px;
    background-color: #4e5a70;
    color: #fff;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    font-size: 14px;
    .btn {
      cursor: pointer;
    }
  }
  .form-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    margin-bottom: 16px;
    .icon-style {
      width: 20px;
      height: 20px;
    }
    span {
      font-weight: 500;
      font-size: 16px;
      color: #4e5a70;
      margin-left: 10px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        width: 1px;
        height: 20px;
        margin-right: 10px;
        background-color: #4a556b;
        opacity: 0.2;
      }
    }
  }
  .form-content {
    padding: 0 16px;
    .step-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .step-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 20px;
        color: #999;
        span {
          flex: 1;
        }
        &::before {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #ddd;
          color: #fff;
          text-align: center;
          margin-right: 8px;
        }
        &:nth-of-type(1) {
          &::before {
            content: '1';
          }
        }
        &:nth-of-type(2) {
          &::before {
            content: '2';
          }
        }
        &:nth-of-type(3) {
          &::before {
            content: '3';
          }
        }
        &.active {
          color: #333;
          &::before {
            background-color: #2783fe;
          }
        }
      }
      > span {
        margin: 0 8px;
        flex: 1;
        height: 1px;
        background-color: #979797;
      }
    }
  }
}
.step-wrapper {
  a {
    color: #3979f9;
    cursor: pointer;
    user-select: none;
  }
  .tips {
    margin-top: 16px;
    margin-bottom: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    text-align: center;
  }
}
.success-wrapper {
  .success-icon {
    width: 40px;
    height: 40px;
    background-color: #3ebf17;
    border-radius: 50%;
    position: relative;
    margin: 0 auto;
    margin-top: 40px;
    margin-bottom: 20px;
    &::before {
      content: '';
      width: 20px;
      height: 10px;
      border-left: 5px solid #fff;
      border-bottom: 5px solid #fff;
      position: absolute;
      left: 12px;
      top: 14px;
      margin: auto;
      transform: rotate(-45deg);
    }
  }
  .tips {
    text-align: center;
    color: #333;
    font-size: 14px;
  }
  .btn {
    margin-top: 50px;
    width: 88%;
    margin-left: 6%;
    text-align: center;
    padding: 8px;
    background: #4e5a70;
    border-radius: 4px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    border: none;
  }
}
</style>
