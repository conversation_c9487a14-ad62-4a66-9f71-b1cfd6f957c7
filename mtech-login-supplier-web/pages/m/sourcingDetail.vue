<template>
  <div>
    <div class="detail-wrapper">
      <div class="base-info section">
        <div class="title"><span></span>{{ $t('寻源基本信息') }}</div>
        <div class="cnt">
          <div class="list-item">
            <span class="list-item-label">{{ $t('寻源标题') }}</span>
            <span class="list-item-text">{{ formData.rfxName }}</span>
          </div>
          <div class="list-item">
            <span class="list-item-label">{{ $t('寻源品类') }}</span>
            <span class="list-item-text">{{ formData.categoryName }}</span>
          </div>
          <div class="list-item">
            <span class="list-item-label">{{ $t('交货地址') }}</span>
            <span class="list-item-text">{{ formData.deliveryPlaceName }}</span>
          </div>
          <div class="list-item">
            <span class="list-item-label">{{ $t('报名截止日期') }}</span>
            <span class="list-item-text">{{ formData.deadlineDate }}</span>
          </div>
          <div class="list-item">
            <span class="list-item-label">{{ $t('年度采购需求') }}</span>
            <span class="list-item-text">{{ formData.totalDemandQty }}</span>
          </div>
        </div>
      </div>
      <div class="item section">
        <div class="title"><span></span>{{ $t('典型物料') }}</div>
        <div class="cnt">
          <div class="list-body" v-for="(item, key) in formData.publicSourcingItemDTOS" :key="key">
            <div class="list-item">
              <span class="list-item-label">{{ $t('物料名称') }}</span>
              <span class="list-item-text">{{ item.itemName }}</span>
            </div>
            <div class="list-item">
              <span class="list-item-label">{{ $t('预计年需求') }}</span>
              <span class="list-item-text">{{ item.demandQty }}</span>
            </div>
            <div class="list-item">
              <span class="list-item-label">{{ $t('单位') }}</span>
              <span class="list-item-text">{{ item.unitCode }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="contact section">
        <div class="title"><span></span>{{ $t('联系人') }}</div>
        <div class="cnt">
          <div class="list-item">
            <span class="list-item-label">{{ $t('联系人姓名：') }}</span>
            <span class="list-item-text">{{ formData.contactUserName }}</span>
          </div>
          <div class="list-item">
            <span class="list-item-label">{{ $t('联系人手机：') }}</span>
            <span class="list-item-text">{{ formData.contactMobile }}</span>
          </div>
          <div class="list-item">
            <span class="list-item-label">{{ $t('联系人邮箱：') }}</span>
            <span class="list-item-text">{{ formData.contactUserEmail }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="btn" @click="toRegister">{{ $t('注册') }}</div>
      <p @click="copyToClipboard">
        {{ $t('pc端报名，') }}<span>{{ $t('请点击此复制链接') }}</span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'h5',
  data() {
    return {
      formData: {}
    }
  },
  mounted() {
    this.getSourcingInfo()
  },
  methods: {
    toRegister() {
      this.$router.push({
        name: 'mRegister'
      })
    },
    copyToClipboard() {
      const input = document.createElement('input')
      input.value = window.location.host
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$tips.success({
        content: this.$t('复制成功')
      })
    },
    // 获取寻源信息
    getSourcingInfo() {
      const params = {
        rfxId: this.$route.query?.rfxId
      }
      this.$api.register.querySourcingInfo(params).then((res) => {
        if (res.code === 200) {
          this.formData = res.data
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.detail-wrapper {
  background: #f1f5f8;
  padding: 4% 4% 110px;
  min-height: 100vh;
  .section {
    background: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .title {
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      border-bottom: 0.5px solid #e6e6e6;
      padding: 12px 4%;
      span {
        display: inline-block;
        width: 3px;
        height: 14px;
        background: #e64c3d;
        margin-right: 8px;
        margin-top: 2px;
        border-bottom-right-radius: 10px;
      }
    }
    .cnt {
      padding: 12px 4% 2px;
      .list-body {
        padding-top: 12px;
        border-top: 0.5px dashed #cecece;
        &:first-child {
          padding-top: 0;
          border-top: none;
        }
      }
      .list-item {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 12px;
        .list-item-label {
          width: 100px;
          font-size: 14px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          color: #999999;
        }
        .list-item-text {
          flex: 1;
          text-align: right;
          font-size: 14px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          color: #333333;
        }
      }
    }
  }
  .base-info.section {
    background: url('~assets/images/bg_section.png') no-repeat center center;
    background-size: 100%;
  }
}
.footer {
  background: #ffffff;
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 16px 4%;
  .btn {
    text-align: center;
    background: #4e5a70;
    height: 40px;
    line-height: 40px;
    border-radius: 40px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    border: none;
  }
  p {
    text-align: center;
    margin-top: 12px;
    span {
      color: #3678fe;
    }
  }
}
</style>
