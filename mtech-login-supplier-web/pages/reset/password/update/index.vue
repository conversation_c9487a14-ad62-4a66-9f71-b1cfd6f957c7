<template>
  <div class="pf-update-password">
    <!-- <pf-header title="修改密码"></pf-header> -->
    <div class="update-password-box">
      <div class="update-password-body">
        <pf-step-bar :step-list="stepList" :active-index="stepIndex"></pf-step-bar>
        <div class="form-box">
          <template v-if="stepIndex === 0">
            <mt-form id="passwordForm" ref="passwordForm" :model="passwordForm" :rules="rules">
              <mt-form-item prop="oldPassword" :show-required-icon="false" class="input-item">
                <mt-input
                  v-model="passwordForm.oldPassword"
                  field="oldPassword"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('原密码')"
                >
                  <img slot="preIcon" src="../../../../assets/images/icon_psw.png" />
                </mt-input>
              </mt-form-item>
              <mt-form-item
                prop="password"
                :show-required-icon="false"
                class="form-group input-item"
              >
                <mt-input
                  v-model="passwordForm.password"
                  :maxlength="10"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('6-10个字符，支持数字、字母和符号')"
                  @blur="handlePswInput"
                >
                  <img slot="preIcon" src="../../../../assets/images/icon_psw.png" />
                </mt-input>
                <div v-if="isShowPswLevel" class="input-other-tips">
                  <span>{{ $t('密码强度') }}</span>
                  <span :class="['strong-level', passwordStrength === 0 && 'level-actived']">{{
                    $t('低')
                  }}</span>
                  <span :class="['strong-level', passwordStrength === 1 && 'level-actived']">{{
                    $t('中')
                  }}</span>
                  <span :class="['strong-level', passwordStrength === 2 && 'level-actived']">{{
                    $t('高')
                  }}</span>
                </div>
              </mt-form-item>
              <mt-form-item prop="confirmPsw" :show-required-icon="false" class="input-item">
                <mt-input
                  v-model="passwordForm.confirmPsw"
                  :maxlength="10"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('确认密码')"
                >
                  <img slot="preIcon" src="../../../../assets/images/icon_psw.png" />
                </mt-input>
              </mt-form-item>
            </mt-form>
            <button class="submit-btn" @click="handleConfirm">{{ $t('确认') }}</button>
          </template>
          <template v-if="stepIndex === 1">
            <div class="finish-img-box">
              <img src="../../../../assets/images/finish.png" />
            </div>
            <p class="finish-tips">{{ $t('恭喜！密码修改成功！') }}</p>
            <button class="submit-btn" @click="$router.push('/login')">{{ $t('立即登录') }}</button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { pswRegex } from '@/utils/my.validators'
import MtInput from '@/mtComponents/mtInput/index.vue'
// import PfHeader from '@/components/pfHeader.vue'
import PfStepBar from '@/components/pfStepBar.vue'
import PfVerifyButton from '@/components/pfVerifyButton.vue'

@Component({
  components: {
    MtInput,
    // PfHeader,
    PfStepBar,
    PfVerifyButton
  },
  middleware: ['auth', 'config'],
  async asyncData({ app, error }) {
    let validConfirmData = {}
    try {
      const res = await app.$api.reset.validPassword()
      if (res.code == 200) {
        validConfirmData = res.data || {}
      }
    } catch (err) {
      error({ statusCode: 500 })
    }
    return {
      validConfirmData
    }
  }
})
export default class ResetPasword extends Vue {
  /** data -start */
  validateCheckPsw = (rule: object, value: string, callback: Function) => {
    rule
    const { password } = this.passwordForm as any
    if (value !== password) {
      callback(new Error(this.$t('两次输入的密码不一致')))
    } else {
      callback()
    }
  }

  stepList = [this.$t('修改密码'), this.$t('修改完成')]
  stepIndex = 0
  passwordStrength: number = 0 // 密码强度
  isShowPswLevel: boolean = false

  passwordForm: object = {
    oldPassword: '',
    password: '',
    confirmPsw: ''
  }

  rules: object = {
    oldPassword: [{ required: true, message: this.$t('必填'), trigger: 'submit' }],
    password: [
      { required: true, message: this.$t('必填'), trigger: 'submit' },
      { pattern: pswRegex, message: this.$t('密码必须为6-20位数字/字母/特殊字符'), trigger: 'blur' }
    ],
    confirmPsw: [
      { required: true, message: this.$t('必填'), trigger: 'submit' },
      { validator: this.validateCheckPsw, trigger: 'blur' }
    ]
  }

  /** data -end */
  get formRef() {
    return this.$refs.passwordForm && (this.$refs.passwordForm as any)
  }

  /** method --start */
  handleConfirm() {
    this.formRef.validate(async (valid: boolean) => {
      if (valid) {
        const { oldPassword, password } = this.passwordForm as any
        let params = {
          oldPassword,
          password,
          passwordStrength: this.passwordStrength
        }
        const { code, data } = await this.$api.reset.updatePassword(params)
        if (code == 200) {
          this.stepIndex = 1
        } else {
          if (data && data.errorLabels && data.errorLabels.length) {
            this.formRef.addErrorLabels(data.errorLabels)
          }
        }
      }
    })
  }

  handlePswInput(val: any) {
    if (!val) return
    if (val.trim().length < 6) return false
    this.isShowPswLevel = true
    const high = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[_!@#$%^&*`~()-+=]).*$/
    const middle1 = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Za-z]).*$/
    const middle2 = /^.*(?=.{6,})(?=.*\d)(?=.*[_!@#$%^&*`~()-+=]).*$/
    if (val) {
      if (high.test(val)) {
        this.passwordStrength = 2
      } else if (middle1.test(val) || middle2.test(val)) {
        this.passwordStrength = 1
      } else {
        this.passwordStrength = 0
      }
    }
  }
  /** method --end */
}
</script>

<style lang="less" scoped>
.pf-update-password {
  height: 100%;
  overflow: hidden;
}
.update-password-box {
  height: calc(100% - 90px);
  display: flex;
  align-items: center;
}
.update-password-body {
  width: 800px;
  height: 480px;
  overflow: hidden;
  margin: 0 auto;
  background-color: #fff;
  padding: 40px 160px 30px 160px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  .form-box {
    width: 450px;
    margin: 40px auto 0 auto;
    text-align: center;
    // overflow: hidden;
    .input-item {
      margin-bottom: 20px;
    }
    .form-group {
      position: relative;
    }
    .input-other-tips {
      display: inline-block;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      min-width: 130px;
      font-size: 12px;
      color: #606266;
      margin-left: 8px;
      .strong-level {
        margin-left: 10px;
      }
      .level-actived {
        color: #f44336;
      }
    }
  }
  .clause-box {
    display: flex;
    align-items: center;
    position: relative;
    .clause-content {
      font-size: 14px;
      color: #999;
      margin-left: 5px;
    }
    .custom-error {
      position: absolute;
      top: 20px;
      left: 0;
    }
  }
  .btn-back {
    display: inline-block;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    margin-top: 20px;
  }
  .finish-img-box {
    width: 200px;
    height: 160px;
    margin: 50px auto 30px auto;
    > img {
      width: 100%;
      height: 100%;
    }
  }
  .finish-tips {
    font-size: 16px;
    color: #005da9;
    margin-bottom: 20px;
  }
}
.submit-btn {
  cursor: pointer;
  text-align: center;
  border-radius: 5px;
  background-color: #005ca9;
  color: #fff;
  padding: 0;
  border: none;
  width: 100%;
  font-size: 18px;
  color: #fff;
  margin-top: 20px;
  padding: 12px 0;
}

.pf-register-success {
  overflow: hidden;
  background-color: #f5f5f5;
  .pf-register-box {
    width: 100%;
    min-width: 960px;
    max-width: 1600px;
    margin: 0 auto;
    padding: 40px 50px;
    overflow: hidden;
    .pf-register-content {
      width: 80%;
      margin: 0 auto;
      .form-box {
        margin-top: 50px;
        padding: 60px 80px 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        .form-group {
          display: flex;
          align-items: center;
          margin-bottom: 30px;
          position: relative;
          .input-title {
            flex-shrink: 0;
            font-size: 16px;
            color: #333;
            display: inline-block;
            width: 120px;
            text-align: right;
            margin-right: 2px;
          }
          .input-other-tips {
            min-width: 130px;
            font-size: 12px;
            color: #606266;
            margin-left: 8px;
            .strong-level {
              margin-left: 10px;
            }
            .level-actived {
              color: #f44336;
            }
          }
        }
        .success-words {
          font-size: 24px;
          color: #333;
          margin-bottom: 60px;
        }
        .icon-box {
          margin-bottom: 20px;
          .icon-success {
            font-size: 70px;
            color: #005ca9;
          }
        }
      }
      .submit-btn {
        width: 260px;
        cursor: pointer;
        text-align: center;
        border-radius: 5px;
        background-color: #005ca9;
        color: #fff;
        height: 40px;
        padding: 0;
        border: none;
        margin-top: 20px;
      }
    }
  }
}
</style>
