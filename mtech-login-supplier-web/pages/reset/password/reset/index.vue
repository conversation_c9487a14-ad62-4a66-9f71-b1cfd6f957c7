<template>
  <div class="pf-password">
    <!-- <pf-header title="重置密码"></pf-header> -->
    <div class="password-box">
      <div class="password-body">
        <pf-step-bar :step-list="stepList" :active-index="stepIndex"></pf-step-bar>
        <div class="form-box">
          <template v-if="stepIndex === 0">
            <div class="pf-title">
              <div :class="['pf-title-item', pfType === 0 && 'title-actived']" @click="pfType = 0">
                <span> {{ $t('邮箱验证') }} </span>
                <div :class="['line', pfType === 0 && 'line-actived']"></div>
              </div>
              <!-- <div :class="['pf-title-item', pfType === 1 && 'title-actived']" @click="pfType = 1">
              <span>{{$t('手机验证')}}</span>
              <div :class="['line', pfType === 1 && 'line-actived']"></div>
            </div> -->
            </div>
            <mt-form
              ref="phoneForm"
              v-if="pfType == 1"
              :model="phoneForm"
              :rules="validNextData"
              class="form-inner"
            >
              <mt-form-item prop="registeredMobile" :show-required-icon="false" class="input-item">
                <mt-input
                  ref="registeredMobile"
                  key="phone"
                  v-model="phoneForm.phone"
                  :country-phone="true"
                  css-class="e-outline"
                  type="number"
                  :placeholder="$t('请输入手机号')"
                  @select="handleSelect"
                  @keyup.enter="phoneNumberKeyup"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_tel" />
                  <!-- <img slot="preIcon" src="../../../../assets/images/icon_phone.png" /> -->
                  <!-- <svg slot="preIcon" t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3634" width="24" height="24"><path d="M224 0h576a64 64 0 0 1 64 64v896a64 64 0 0 1-64 64H224a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64z" fill="#6386C1" p-id="3635"></path><path d="M288 128m32 0l384 0q32 0 32 32l0 512q0 32-32 32l-384 0q-32 0-32-32l0-512q0-32 32-32Z" fill="#B0C1DF" p-id="3636"></path><path d="M864 0v960a64 64 0 0 1-64 64H160L864 0z" fill="#000000" fill-opacity=".1" p-id="3637"></path><path d="M416 832m32 0l128 0q32 0 32 32l0 0q0 32-32 32l-128 0q-32 0-32-32l0 0q0-32 32-32Z" fill="#EDA133" p-id="3638"></path></svg> -->
                </mt-input>
              </mt-form-item>
              <mt-form-item prop="validateCode" :show-required-icon="false" class="input-item">
                <mt-input
                  ref="verifyCodeRef"
                  key="verificationCode"
                  v-model="phoneForm.verificationCode"
                  type="number"
                  css-class="e-outline"
                  :placeholder="$t('请输入验证码')"
                  @keyup.enter="handleNext"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_Comple" />
                  <!-- <img slot="/preIcon" src="../../../../assets/images/icon_psw.png" /> -->
                  <!-- <svg slot="preIcon" t="1626076180979" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3768" width="24" height="24"><path d="M512 0a256 256 0 0 1 256 256v256H256V256a256 256 0 0 1 256-256z m0 128a128 128 0 0 0-127.68 118.4L384 256v128h256V256a128 128 0 0 0-118.4-127.68L512 128z" fill="#EDA133" p-id="3769"></path><path d="M0 384m128 0l768 0q128 0 128 128l0 384q0 128-128 128l-768 0q-128 0-128-128l0-384q0-128 128-128Z" fill="#6386C1" p-id="3770"></path><path d="M448 576m64 0l0 0q64 0 64 64l0 128q0 64-64 64l0 0q-64 0-64-64l0-128q0-64 64-64Z" fill="#B0C1DF" p-id="3771"></path><path d="M768 384h128a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H256l512-640z" fill="#000000" fill-opacity=".1" p-id="3772"></path></svg> -->
                  <pf-verify-button
                    ref="verifyBtnRef"
                    slot="back"
                    @click.native="openCaptcha"
                    @finish="getVerifyCode"
                  ></pf-verify-button>
                </mt-input>
              </mt-form-item>
            </mt-form>

            <mt-form
              ref="emailForm"
              v-else
              :model="emailForm"
              :rules="validNextData"
              class="form-inner"
            >
              <mt-form-item prop="RegisteredAccount" :show-required-icon="false" class="input-item">
                <mt-input
                  ref="userNameNumberRef"
                  key="userName"
                  v-model="emailForm.userName"
                  css-class="e-outline"
                  type="text"
                  :placeholder="$t('请输入账号')"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_name" />
                  <!-- <svg-icon slot="preIcon" class="icon" icon-class="icon_login_mail" /> -->
                  <!-- <img slot="preIcon" src="../../../../assets/images/icon_email.png" /> -->
                  <!-- <svg slot="preIcon" t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3634" width="24" height="24"><path d="M224 0h576a64 64 0 0 1 64 64v896a64 64 0 0 1-64 64H224a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64z" fill="#6386C1" p-id="3635"></path><path d="M288 128m32 0l384 0q32 0 32 32l0 512q0 32-32 32l-384 0q-32 0-32-32l0-512q0-32 32-32Z" fill="#B0C1DF" p-id="3636"></path><path d="M864 0v960a64 64 0 0 1-64 64H160L864 0z" fill="#000000" fill-opacity=".1" p-id="3637"></path><path d="M416 832m32 0l128 0q32 0 32 32l0 0q0 32-32 32l-128 0q-32 0-32-32l0 0q0-32 32-32Z" fill="#EDA133" p-id="3638"></path></svg> -->
                </mt-input>
              </mt-form-item>
              <mt-form-item prop="registeredMail" :show-required-icon="false" class="input-item">
                <mt-input
                  ref="emailNumberRef"
                  key="email"
                  v-model="emailForm.email"
                  :country-email="true"
                  css-class="e-outline"
                  type="text"
                  :placeholder="$t('请输入邮箱')"
                  @select="handleSelect"
                  @keyup.enter="emailNumberKeyup"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_mail" />
                  <!-- <img slot="preIcon" src="../../../../assets/images/icon_email.png" /> -->
                  <!-- <svg slot="preIcon" t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3634" width="24" height="24"><path d="M224 0h576a64 64 0 0 1 64 64v896a64 64 0 0 1-64 64H224a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64z" fill="#6386C1" p-id="3635"></path><path d="M288 128m32 0l384 0q32 0 32 32l0 512q0 32-32 32l-384 0q-32 0-32-32l0-512q0-32 32-32Z" fill="#B0C1DF" p-id="3636"></path><path d="M864 0v960a64 64 0 0 1-64 64H160L864 0z" fill="#000000" fill-opacity=".1" p-id="3637"></path><path d="M416 832m32 0l128 0q32 0 32 32l0 0q0 32-32 32l-128 0q-32 0-32-32l0 0q0-32 32-32Z" fill="#EDA133" p-id="3638"></path></svg> -->
                </mt-input>
              </mt-form-item>
              <mt-form-item prop="validateCode" :show-required-icon="false" class="input-item">
                <mt-input
                  ref="verifyCodeRef"
                  key="verificationCode"
                  v-model="emailForm.verificationCodeEmail"
                  type="number"
                  css-class="e-outline"
                  :placeholder="$t('请输入验证码')"
                  @keyup.enter="handleNext"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_Comple" />
                  <!-- <img slot="/preIcon" src="../../../../assets/images/icon_psw.png" /> -->
                  <!-- <svg slot="preIcon" t="1626076180979" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3768" width="24" height="24"><path d="M512 0a256 256 0 0 1 256 256v256H256V256a256 256 0 0 1 256-256z m0 128a128 128 0 0 0-127.68 118.4L384 256v128h256V256a128 128 0 0 0-118.4-127.68L512 128z" fill="#EDA133" p-id="3769"></path><path d="M0 384m128 0l768 0q128 0 128 128l0 384q0 128-128 128l-768 0q-128 0-128-128l0-384q0-128 128-128Z" fill="#6386C1" p-id="3770"></path><path d="M448 576m64 0l0 0q64 0 64 64l0 128q0 64-64 64l0 0q-64 0-64-64l0-128q0-64 64-64Z" fill="#B0C1DF" p-id="3771"></path><path d="M768 384h128a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H256l512-640z" fill="#000000" fill-opacity=".1" p-id="3772"></path></svg> -->
                  <pf-verify-button
                    ref="verifyBtnRef"
                    slot="back"
                    @click.native="openCaptchaEmail"
                    @finish="getVerifyCodeEmail"
                  ></pf-verify-button>
                </mt-input>
              </mt-form-item>
            </mt-form>
            <div class="btn-div">
              <button class="submit-btn" @click="$router.push('/login')">
                {{ $t('返回登录') }}
              </button>
              <button class="submit-btn" @click="handleNext">{{ $t('下一步') }}</button>
            </div>
          </template>
          <template v-if="stepIndex === 1">
            <mt-form
              ref="passwordForm"
              :model="passwordForm"
              :rules="validConfirmData"
              class="form-inner"
            >
              <mt-form-item
                prop="password"
                :show-required-icon="false"
                class="form-group input-item"
              >
                <mt-input
                  ref="passwordRef"
                  key="password"
                  v-model="passwordForm.password"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('请输入新密码')"
                  @input="handlePswInput"
                  @keyup.enter="passwordKeyup"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_password" />
                  <!-- <img slot="preIcon" src="../../../../assets/images/icon_psw.png" /> -->
                  <!-- <svg slot="preIcon" t="1626076180979" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3768" width="24" height="24"><path d="M512 0a256 256 0 0 1 256 256v256H256V256a256 256 0 0 1 256-256z m0 128a128 128 0 0 0-127.68 118.4L384 256v128h256V256a128 128 0 0 0-118.4-127.68L512 128z" fill="#EDA133" p-id="3769"></path><path d="M0 384m128 0l768 0q128 0 128 128l0 384q0 128-128 128l-768 0q-128 0-128-128l0-384q0-128 128-128Z" fill="#6386C1" p-id="3770"></path><path d="M448 576m64 0l0 0q64 0 64 64l0 128q0 64-64 64l0 0q-64 0-64-64l0-128q0-64 64-64Z" fill="#B0C1DF" p-id="3771"></path><path d="M768 384h128a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H256l512-640z" fill="#000000" fill-opacity=".1" p-id="3772"></path></svg> -->
                </mt-input>

                <pwd-strength v-if="isShowPswLevel" :strengths="passwordStrength"></pwd-strength>
              </mt-form-item>
              <mt-form-item prop="confirmPsw" :show-required-icon="false" class="input-item">
                <mt-input
                  ref="confirmPswRef"
                  key="confirmPsw"
                  v-model="passwordForm.confirmPsw"
                  type="password"
                  css-class="e-outline"
                  :placeholder="$t('请确认密码')"
                  @keyup.enter="handleConfirm"
                >
                  <svg-icon slot="preIcon" class="icon" icon-class="icon_login_password" />
                  <!-- <img slot="preIcon" src="../../../../assets/images/icon_psw.png" /> -->
                  <!-- <svg slot="preIcon" t="1626076180979" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3768" width="24" height="24"><path d="M512 0a256 256 0 0 1 256 256v256H256V256a256 256 0 0 1 256-256z m0 128a128 128 0 0 0-127.68 118.4L384 256v128h256V256a128 128 0 0 0-118.4-127.68L512 128z" fill="#EDA133" p-id="3769"></path><path d="M0 384m128 0l768 0q128 0 128 128l0 384q0 128-128 128l-768 0q-128 0-128-128l0-384q0-128 128-128Z" fill="#6386C1" p-id="3770"></path><path d="M448 576m64 0l0 0q64 0 64 64l0 128q0 64-64 64l0 0q-64 0-64-64l0-128q0-64 64-64Z" fill="#B0C1DF" p-id="3771"></path><path d="M768 384h128a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H256l512-640z" fill="#000000" fill-opacity=".1" p-id="3772"></path></svg> -->
                </mt-input>
              </mt-form-item>
            </mt-form>
            <button class="submit-span-btn" @click="handleConfirm">{{ $t('确定重置') }}</button>
            <!-- <span class="btn-back" @click="goBack"> {{$t('previousStep')}} </span> -->
          </template>
          <template v-if="stepIndex === 2">
            <div class="finish-img-box">
              <img src="../../../../assets/images/reset_success.png" />
            </div>
            <p class="finish-tips">
              {{ $t('重置完成') }}""
              <span class="page3-span" @click="$router.push('/login')"> {{ $t('立即登录') }} </span>
            </p>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import MtInput from '@/mtComponents/mtInput/index.vue'
// import PfHeader from '@/components/pfHeader.vue'
import PfStepBar from '@/components/pfStepBar.vue'
import PfVerifyButton from '@/components/pfVerifyButton.vue'
import PwdStrength from '@/components/pwdStrength.vue'
import { phoneRegex, pswRegex } from '@/utils/my.validators'
// import { formatRules, useTCaptcha } from '@/utils/index'

@Component({
  components: {
    MtInput,
    // PfHeader,
    PfStepBar,
    PfVerifyButton,
    PwdStrength
  },
  middleware: ['config', 'country']
  // async asyncData({ app, error }) {
  //   let validRs = {
  //     validNextData: {},
  //     validConfirmData: {}
  //   }
  //   try {
  //     const validNextRs = await app.$api.reset.validForPasswordNext()
  //     const validConfirmRs = await app.$api.reset.validForPasswordConfirm()
  //     if (validNextRs.code == 200) {
  //       validRs.validNextData = validNextRs.data ? formatRules(validNextRs.data) : {}
  //     }
  //     if (validConfirmRs.code == 200) {
  //       validRs.validConfirmData = validConfirmRs.data ? formatRules(validConfirmRs.data) : {}
  //     }
  //     // console.log(JSON.stringify(validRs))
  //   } catch (err) {
  //     // error({ statusCode: 500 })
  //   }
  //   return {
  //     validNextData: validRs.validNextData,
  //     validConfirmData: validRs.validConfirmData
  //   }
  // }
})
export default class ResetPasword extends Vue {
  /** data -start */
  validateCheckPsw = (rule: object, value: string, callback: Function) => {
    rule
    const { password } = this.passwordForm as any
    if (value !== password) {
      callback(new Error(this.$t('两次输入的密码不一致')))
    } else {
      callback()
    }
  }

  stepList = [this.$t('短信验证'), this.$t('重置密码'), this.$t('修改完成')]
  stepIndex = 0
  passwordStrength: number = 0 // 密码强度
  pfType: number = 0 // 密码强度
  isShowPswLevel: boolean = false
  countryRegionParam: object = {
    countryRegionId: 1,
    countryRegionCode: '0086'
  }

  phoneForm: object = {
    phone: '',
    verificationCode: ''
  }
  emailForm: object = {
    userName: '',
    email: '',
    verificationCodeEmail: ''
  }
  validNextData: object = {}

  passwordForm: object = {
    password: '',
    confirmPsw: ''
  }

  validConfirmData: object = {}

  rules: object = {
    registeredMobile: [
      { required: true, message: this.$t('不能为空'), trigger: 'blur' },
      { pattern: phoneRegex, message: this.$t('手机号格式不正确'), trigger: 'submit' }
    ],
    RegisteredAccount: [{ required: true, message: this.$t('不能为空'), trigger: 'blur' }],
    registeredMail: [{ required: true, message: this.$t('不能为空'), trigger: 'blur' }],
    validateCode: [{ required: true, message: this.$t('不能为空'), trigger: 'blur' }],
    password: [
      { required: true, message: this.$t('必填'), trigger: 'submit' },
      { pattern: pswRegex, message: this.$t('密码必须为6-20位数字/字母/特殊字符'), trigger: 'blur' }
    ],
    confirmPsw: [
      { required: true, message: this.$t('必填'), trigger: 'submit' },
      { validator: this.validateCheckPsw, trigger: 'blur' }
    ]
  }
  /** data -end */

  get formRef() {
    const name = this.stepIndex === 0 ? (this.pfType === 1 ? 'phone' : 'email') : 'password'
    return this.stepIndex !== 2
      ? this.$refs[`${name}Form`] && (this.$refs[`${name}Form`] as any)
      : null
  }

  created() {
    this.validConfirmData['confirmPsw'] = [
      { required: true, message: this.$t('必填'), trigger: 'submit' },
      { validator: this.validateCheckPsw, trigger: 'blur' }
    ]
  }

  /** method --start */
  openCaptcha() {
    ;(this.$refs.verifyBtnRef as any).openCaptcha()
  }

  openCaptchaEmail() {
    const checkEmail = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/

    if (!this.emailForm['userName']) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('请输入账号')
      })
      return
    }
    if (!this.emailForm['email']) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('请输入邮箱')
      })
      return
    }
    if (!checkEmail.test(this.emailForm['email'])) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('邮箱格式错误')
      })
      return
    }
    if (checkEmail.test(this.emailForm['email'])) {
      ;(this.$refs.verifyBtnRef as any).openCaptcha()
    }
    // console.log(this.emailForm["email"], checkEmail.test(this.emailForm["email"]),"emailForm.email");
    // console.log(this.$refs.verifyBtnRef,"this.$refs.verifyBtnRef");
  }
  async getVerifyCode() {
    const { phone } = this.phoneForm as any
    let params = {
      mobile: phone
      // rand: res.randstr,
      // ticket: res.ticket,
      // ...this.countryRegionParam
    }
    const { code, data } = await this.$api.reset.getVerifyCodeForPassword(params)
    if (code == 200) {
      ;(this.$refs.verifyBtnRef as any).countDown()
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
      }
    }
  }

  async getVerifyCodeEmail(res: any) {
    // useTCaptcha().then(async (resCaptcha: any) => {
    const { userName, email } = this.emailForm as any
    const params: any = {
      userName: userName,
      mail: email
    }
    params.rand = res.randstr
    params.ticket = res.ticket
    const { code, data } = await this.$api.reset.getVerifyCodeForPasswordMail(params)
    if (code == 200) {
      ;(this.$refs.verifyBtnRef as any).countDown()
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
        let msg = data.errorLabels[0].message
        this.$tips.error({
          title: this.$t('提示'),
          content: this.$t(msg)
        })
        return
      }
    }
    // })
  }
  handleNext() {
    const checkEmail = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/
    console.log(this.emailForm, 'this.emailForm')

    if (!this.emailForm['userName']) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('请输入账号')
      })
      return
    }
    if (!this.emailForm['email']) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('请输入邮箱')
      })
      return
    }
    if (!this.emailForm['verificationCodeEmail']) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('请输入验证码')
      })
      return
    }
    if (!checkEmail.test(this.emailForm['email'])) {
      this.$tips.error({
        title: this.$t('提示'),
        content: this.$t('邮箱格式错误')
      })
      return
    }
    this.formRef.validate(async (valid: boolean) => {
      if (valid) {
        console.log('this.phoneForm', this.phoneForm)
        const { phone, verificationCode } = this.phoneForm as any
        const { userName, email, verificationCodeEmail } = this.emailForm as any
        let params: any = {}

        if (this.pfType === 1) {
          params.mobile = phone
          params.validateCode = verificationCode
        } else {
          params.userName = userName
          params.mail = email
          params.validateCode = verificationCodeEmail
        }

        const { code, data } = email
          ? await this.$api.reset.updatePasswordNextMail(params)
          : await this.$api.reset.updatePasswordNext(params)
        if (code == 200) {
          this.stepIndex = 1
        } else {
          if (data && data.errorLabels && data.errorLabels.length) {
            this.formRef.addErrorLabels(data.errorLabels)
          }
        }
      }
    })

    // this.stepIndex = 1
  }

  handleConfirm() {
    console.log(this.formRef)
    this.formRef.validate(async (valid: boolean) => {
      if (valid) {
        const { phone, verificationCode } = this.phoneForm as any
        const { userName, email, verificationCodeEmail } = this.emailForm as any
        // emailForm
        const { password } = this.passwordForm as any
        const params: any = {
          validateCode: verificationCode,
          modifiedPassword: password,
          confirmPassword: password
        }

        if (this.pfType === 1) {
          params.mobile = phone
          params.validateCode = verificationCode
        } else {
          params.userName = userName
          params.mail = email
          params.validateCode = verificationCodeEmail
        }

        const { code, data } = await this.$api.reset.updatePasswordConfirm(params)
        if (code == 200) {
          this.stepIndex = 2
        } else {
          if (data && data.errorLabels && data.errorLabels.length) {
            this.formRef.addErrorLabels(data.errorLabels)
          }
        }
      }
    })
  }

  toLogin() {
    this.$router.push({ path: '/login' })
  }

  handleSelect(event: any): void {
    const ruleCN = { pattern: phoneRegex, message: this.$t('手机号格式不正确'), trigger: 'submit' }
    const { itemData } = event || {}
    const { phone } = this.validNextData as any
    if (itemData.code === '0086') {
      phone[1] = ruleCN
    } else {
      const { length, maxLength, minLength } = itemData
      let ruleForeign =
        maxLength && minLength
          ? { max: maxLength, min: minLength, message: this.$t('errPhone'), trigger: 'submit' }
          : length
          ? { len: length, message: this.$t('errPhone'), trigger: 'submit' }
          : {}
      phone[1] = ruleForeign
    }
    const { phone: phoneNumber } = this.phoneForm as any
    phoneNumber && this.formRef.validateField('phone')
  }

  goBack() {
    this.formRef.resetFields()
    this.stepIndex = 0
  }

  handlePswInput(val: any) {
    if (!val) {
      this.passwordStrength = 0
      this.isShowPswLevel = false
      return
    }
    if (val.trim().length < 6) {
      this.passwordStrength = 0
      this.isShowPswLevel = false
      return
    }
    this.isShowPswLevel = true
    const high = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Za-z])(?=.*[_!@#$%^&*`~()-+=]).*$/
    const middle1 = /^.*(?=.{6,})(?=.*\d)(?=.*[A-Za-z]).*$/
    const middle2 = /^.*(?=.{6,})(?=.*\d)(?=.*[_!@#$%^&*`~()-+=]).*$/
    const middle3 = /^.*(?=.{6,})(?=.*[A-Za-z])(?=.*[_!@#$%^&*`~()-+=]).*$/
    if (val) {
      if (high.test(val)) {
        this.passwordStrength = 2
      } else if (middle1.test(val) || middle2.test(val) || middle3.test(val)) {
        this.passwordStrength = 1
      } else {
        this.passwordStrength = 0
      }
    }
  }

  phoneNumberKeyup() {
    this.$refs.verifyCodeRef && (this.$refs.verifyCodeRef as any).focusIn()
  }

  passwordKeyup() {
    this.$refs.confirmPswRef && (this.$refs.confirmPswRef as any).focusIn()
  }
  /** method --end */
}
</script>
<style lang="less">
.pf-password {
  margin-left: 0 !important;
  .e-ddl.e-input-group:not(.e-disabled) {
    margin: 0;
    border: none;
  }
}
</style>
<style lang="less" scoped>
.pf-password {
  height: 100%;
  overflow: hidden;
  margin-left: 80px;
}
.password-box {
  // height: calc(100% - 90px);
  // height: 100%;
  display: flex;
  align-items: center;
}
.password-body {
  width: 80vw;
  // height: 460px;
  // overflow: hidden;
  margin: 0 auto;
  background-color: #fff;
  padding: 50px 60px 30px 60px;
  // box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  // border-radius: 5px;
  .form-box {
    width: 500px;
    margin: 75px auto 0 auto;
    text-align: center;
    // overflow: hidden;

    .pf-title {
      // display: flex;
      padding: 0;
      margin-bottom: 30px;
      line-height: 40px;
      .pf-title-item {
        width: 100%;
        font-size: 16px;
        text-align: center;
        cursor: pointer;
        position: relative;

        &:first-child {
          margin-right: 20%;
        }

        .line {
          width: 100%;
          height: 4px;
          background: #fff;
          border-radius: 3px;
          margin: 8px auto auto;
        }
        .line-actived {
          background: #6386c1;
          position: absolute;
          bottom: -15px;
        }
      }
      .title-actived {
        font-size: 24px;
        height: 40px;
        color: #00469c;
      }
    }
    .form-inner {
      margin: 15px 0;
      .input-item {
        margin-bottom: 40px;
      }
      .form-group {
        position: relative;
        align-items: center;
      }
    }
  }
  .finish-img-box {
    // width: 200px;
    // height: 200px;
    margin: 0 auto 20px auto;
    > img {
      width: 340px;
      // height: 100%;
    }
  }
  .finish-tips {
    font-size: 13px;
    color: rgba(156, 156, 156, 1);
    margin-bottom: 10px;
    .page3-span {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      cursor: pointer;
      color: #00469c;
    }
  }
  .btn-back {
    display: inline-block;
    font-size: 14px;
    color: #999;
    cursor: pointer;
    margin-top: 20px;
  }
}
.btn-div {
  display: flex;
  justify-content: space-between;
}
.submit-btn {
  cursor: pointer;
  text-align: center;
  background-color: #005ca9;
  color: #fff;
  padding: 0;
  border: none;
  height: 44px;
  width: 45%;
  border-radius: 4px;
  font-size: 14px;
  color: #fff;
  margin-top: 20px;
  padding: 12px 0;
}
.submit-span-btn {
  cursor: pointer;
  text-align: center;
  background-color: #005ca9;
  padding: 0;
  border: none;
  border-radius: 4px;
  width: 300px;
  height: 44px;
  font-size: 14px;
  color: #fff;
  margin-top: 20px;
  padding: 12px 0;
}
</style>
