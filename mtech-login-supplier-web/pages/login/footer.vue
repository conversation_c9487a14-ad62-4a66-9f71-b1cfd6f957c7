<template>
  <div class="footer-wrapper">
    <dl>
      <dt>{{ $t('关于我们') }}</dt>
      <dd>{{ $t('TCL电子官网') }}</dd>
    </dl>
    <dl>
      <dt>{{ $t('友情链接') }}</dt>
      <dd>{{ $t('TCL商城') }}</dd>
    </dl>
    <dl>
      <dt>{{ $t('关注我们') }}</dt>
      <dd>{{ $t('TCL集团官方微博') }}</dd>
    </dl>
    <dl>
      <dt>{{ $t('推荐浏览器下载') }}</dt>
      <dd class="primary">Chrome Win7-Win10</dd>
      <dd>Chrome-XP</dd>
    </dl>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
@Component({})
export default class Footer extends Vue {}
</script>

<style lang="less" scoped>
.footer-wrapper {
  display: flex;
  justify-content: space-between;
  padding: 24px 0 32px;
  dl {
    line-height: 22px;
    dt {
      font-size: 18px;
      color: #666;
    }
    dd {
      font-size: 16px;
      color: #999;
      margin-top: 16px;
      cursor: pointer;
      &.primary {
        color: #3678fe;
      }
    }
  }
}
</style>
