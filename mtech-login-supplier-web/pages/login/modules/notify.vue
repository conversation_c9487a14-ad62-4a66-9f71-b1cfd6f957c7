<template>
  <div class="wrapper">
    <div class="wrapper-header">
      <svg-icon icon-class="icon_notice" />
      <span>{{ $t('系统公告') }}</span>
    </div>
    <div class="wrapper-body">
      <dl>
        <dt>
          <div class="title">{{ $t('寻源公告') }}</div>
          <span>{{ $t('更多') }}</span>
        </dt>
        <dd>TCL拟对xx进行采购，现对供应商进行公开寻源。</dd>
        <dd>TCL拟对xx进行采购，现对供应商进行公开寻源。</dd>
        <dd>TCL拟对xx进行采购，现对供应商进行公开寻源。</dd>
        <dd>TCL拟对xx进行采购，现对供应商进行公开寻源。</dd>
      </dl>
      <dl>
        <dt>
          <div class="title">{{ $t('中标结果公告') }}</div>
          <span>{{ $t('更多') }}</span>
        </dt>
        <dd>[2018-09-04] TCL空调新获“2018中国冷医智造大奖”</dd>
      </dl>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component({
  layout: 'blank'
})
export default class Notice extends Vue {}
</script>

<style lang="less" scoped>
.wrapper {
  width: 330px;
  height: 100%;
  margin-right: 16px;
  background: #ffffff;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  .wrapper-header {
    background-image: url('~assets/images/notice-bg.jpeg');
    height: 90px;
    background-size: 100%;
    padding: 58px 0 16px 24px;
    span {
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 22px;
    }
  }
  .wrapper-body {
    padding: 8px 26px 8px 24px;
    dl {
      dt {
        display: flex;
        justify-content: space-between;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
        }
        span {
          font-size: 12px;
          color: #3678fe;
        }
      }
      dd {
        display: flex;
        font-size: 14px;
        color: #666666;
        line-height: 22px;
        &::before {
          content: '';
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #3678fe;
          margin-top: 9px;
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
