<template>
  <div class="wrapper">
    <div class="wrapper-header">
      <svg-icon icon-class="icon_follow_us"></svg-icon>
      <span>{{ $t('关注我们') }}</span>
    </div>
    <ul class="wrapper-body">
      <li>TCL电子官网</li>
      <li>TCL集团官方微博</li>
      <li>铁粉社区</li>
    </ul>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component({})
export default class FollowUs extends Vue {}
</script>

<style lang="less" scoped>
.wrapper {
  background: #ffffff;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  padding: 16px 24px;
  .wrapper-header {
    span {
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 22px;
    }
  }
  ul {
    li {
      margin-top: 16px;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
      line-height: 25px;
    }
  }
}
</style>
