<template>
  <div class="wrapper">
    <div class="text-wrapper">
      <div class="wrapper-title">{{ $t('企业介绍') }}</div>
      <p>
        1981年，TCL诞生于中国惠州，从一个生产磁带的小公司起步，开始了全球化征程。
        从中国惠州生产磁带的合资企业，逐步将产品拓展到电话、电视、手机、冰箱、洗衣机、空调、智能健康电器、液晶面板，业务覆盖金融服务、互联网应用服务、销售及物流服务、投资与创投领域。TCL在全球共有8万多名员工，拥有28个研发中心，10余家联合实验室，22个制造加工基地，在80多个国家和地区设有销售机构，业务遍及全球160多个国家和地区。
      </p>
    </div>
    <img src="~assets/images/company.png" alt="" />
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component({})
export default class Introduce extends Vue {}
</script>

<style lang="less" scoped>
.wrapper {
  background: #ffffff;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  padding: 16px 24px;
  display: flex;
  .wrapper-title {
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 22px;
  }
  p {
    font-size: 14px;
    color: #666666;
    text-indent: 2em;
    margin-top: 16px;
  }
  img {
    border-radius: 5px;
    margin-left: 24px;
    width: 188px;
    height: 188px;
  }
}
</style>
