<template>
  <div class="wrapper">
    <div class="wrapper-header">
      <svg-icon icon-class="icon_guide" />
      <span>{{ $t('帮助中心') }}</span>
    </div>
    <dl class="wrapper-body">
      <!-- :class="[isActive == item.code ?'aPoints':'']" -->
      <a
        v-for="(item, index) in HelpcenterArr"
        :href="baseUrl + `${item.href}`"
        target="_blank"
        :key="index"
        >{{ item.name }}
      </a>
    </dl>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component({})
export default class Guide extends Vue {
  baseUrl: string = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/'
  isActive = '1' //before前缀
  HelpcenterArr = [
    {
      code: '1',
      name: 'TCL王牌电器(惠州)有限公司-开票信息',
      href: 'billing/TCL王牌电器（惠州）有限公司-开票信息.pdf'
    },
    {
      code: '2',
      name: '深圳TCL数字技术有限公司-开票信息',
      href: 'billing/深圳TCL数字技术有限公司-开票信息.pdf'
    },
    {
      code: '3',
      name: '深圳TCL新技术有限公司-开票信息',
      href: 'billing/深圳TCL新技术有限公司-开票信息.pdf'
    }
  ]
}
</script>

<style lang="less" scoped>
.wrapper {
  background: #ffffff;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  padding: 16px 0;
  .wrapper-header {
    padding-left: 16px;
    span {
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 22px;
    }
  }
  .wrapper-body {
    padding: 8px 26px 8px 24px;
    a {
      cursor: pointer;
      text-decoration-line: none;
      display: flex;
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      &::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #3678fe;
        margin-top: 9px;
        margin-right: 10px;
      }
    }
  }
}
</style>
