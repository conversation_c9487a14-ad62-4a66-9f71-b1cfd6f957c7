<template>
  <div class="wrapper">
    <div class="wrapper-header">
      <svg-icon icon-class="icon_news" />
      <span>{{ $t('新闻动态') }}</span>
    </div>
    <dl class="wrapper-body">
      <dd>[2018-09-04] TCL空调新获“2018中国冷医智造大奖”</dd>
      <dd>[12018-08-29] 李东生谈40年行业户变:技术创断是制造的这里是一个多…</dd>
      <dd>[2018-08-26] 央视《新闻联播》为TCL点赞，大国品牌</dd>
    </dl>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component({})
export default class News extends Vue {}
</script>

<style lang="less" scoped>
.wrapper {
  background: #ffffff;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px 0;
  .wrapper-header {
    padding-left: 16px;
    span {
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 22px;
    }
  }
  .wrapper-body {
    padding: 8px 26px 8px 24px;
    dd {
      display: flex;
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      &::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #3678fe;
        margin-top: 9px;
        margin-right: 10px;
      }
    }
  }
}
</style>
