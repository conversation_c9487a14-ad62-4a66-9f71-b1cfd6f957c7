<template>
  <div class="wrapper">
    <div class="wrapper-header">{{ $t('关于TCL-SRM平台') }}</div>
    <div class="wrapper-body">
      <img src="~assets/images/login-platform.jpeg" alt="" />
      <p>
        TCL
        T-SRM平台，是一个集多效的企业采购协同工作平台。通过这个平台可以发布采购信息，SRM系统是通过数字化、信息化、可视化的管控方式为复杂的供应商关系管理提供的高性能解决方案。包含预测、排期寻源采购、送货、付款、返利、对账及供应商生命周期、绩效等全程……
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
@Component({})
export default class SrmPlatform extends Vue {}
</script>

<style lang="less" scoped>
.wrapper {
  background: #ffffff;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.07);
  border-radius: 8px;
  padding: 16px 24px;
  .wrapper-header {
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 22px;
    margin-bottom: 16px;
  }
  .wrapper-body {
    img {
      display: block;
      height: 288px;
      border-radius: 5px;
    }
    p {
      font-size: 14px;
      color: #666666;
      margin-top: 16px;
      text-indent: 2em;
    }
  }
}
</style>
