<template>
  <div>
    <div class="login-container">
      <div class="login-form-wrapper">
        <div class="login-form-title">
          <svg-icon class="icon-style" icon-class="icon_logo" />
          <span>TCL实业数智采购平台(SRM)</span>
        </div>
        <div class="login-form-header">
          <!-- <div
            :class="{ 'tab-item': true, active: loginType === 0 }"
            @click="loginType = 0"
          >
            {{ $t('免密登录') }}
          </div> -->
          <!-- <div
            :class="{ 'tab-item': true, active: loginType === 1 }"
            @click="loginType = 1"
          >
            {{ $t('密码登录') }}
          </div> -->
          <div :class="{ 'tab-item': true, active: loginType === 1 }">
            {{ $t('密码登录') }}
          </div>
        </div>
        <!-- 免密登录 -->
        <!-- <mt-form
          :key="$store.state.locale"
          v-show="loginType === 0"
          ref="phoneForm"
          :model="phoneForm"
          :rules="validateByPhone"
          class="form-box"
        >
          <mt-form-item
            prop="phone"
            :show-required-icon="false"
            class="input-item"
            :label="$t('账号')"
          >
            <mt-input
              key="phone-phone"
              v-model="phoneForm.phone"
              :showClearButton="true"
              type="number"
              cssClass="e-outline"
              :placeholder="$t('请输入手机号')"
              @keyup.enter="phoneNumberKeyup"
            >
            </mt-input>
          </mt-form-item>
          <mt-form-item
            prop="validateCode"
            :show-required-icon="false"
            class="input-item"
            :label="$t('验证码')"
          >
            <mt-input
              key="phone-code"
              ref="verifyCodeRef"
              :showClearButton="true"
              v-model="phoneForm.validateCode"
              type="number"
              cssClass="e-outline"
              :placeholder="$t('请输入验证码')"
              @keyup.enter="loginSubmit(0)"
            >
            </mt-input>
            <span class="form-button">
              <pf-verify-button
                :key="$store.state.locale"
                ref="verifyBtnRef"
                @click.native="getVerifyCode"
                @finish="getVerifyCode"
              ></pf-verify-button>
            </span>
          </mt-form-item>
        </mt-form> -->
        <!-- 账号密码登录 -->
        <mt-form
          :key="$store.state.locale"
          v-show="loginType === 1"
          ref="accountForm"
          :model="accountForm"
          :rules="validateByName"
          class="form-box"
        >
          <mt-form-item
            prop="username"
            :show-required-icon="false"
            class="input-item"
            :label="$t('账号')"
          >
            <mt-input
              ref="usernameRef"
              key="account-name"
              :show-clear-button="true"
              v-model="accountForm.username"
              type="text"
              cssClass="e-outline"
              :placeholder="$t('请输入用户名/邮箱/手机号')"
              @keyup.enter="usernameKeyup"
            >
              <svg-icon slot="preIcon" class="icon" icon-class="icon_login_name" />
            </mt-input>
          </mt-form-item>
          <mt-form-item
            prop="password"
            :show-required-icon="false"
            class="input-item"
            :label="$t('密码')"
          >
            <mt-input
              ref="accountPwdRef"
              key="account-psw"
              :showClearButton="true"
              v-model="accountForm.password"
              type="password"
              cssClass="e-outline"
              :placeholder="$t('请输入密码')"
              @keyup.enter="loginSubmit(0)"
            >
              <svg-icon slot="preIcon" class="icon" icon-class="icon_login_password" />
            </mt-input>
          </mt-form-item>
        </mt-form>

        <button class="login-btn" @click="loginSubmit(0)">
          {{ $t('登录') }}
        </button>
      </div>
    </div>
    <div class="dialog" v-if="dialogType">
      <div class="dialogMain">
        <div class="main-hander">{{ $t('信息提示') }}</div>
        <div class="main-text">{{ $t(`首次登录，建议立即修改密码`) }}。</div>
        <div class="main-button">
          <button @click="cruelRefusal">{{ $t('下次再说') }}</button>
          <button @click="confirmModification">{{ $t('立即修改') }}</button>
        </div>
      </div>
    </div>
    <div class="dialog" v-if="countdownShow">
      <div class="dialogMain">
        <div class="main-hander">{{ $t('信息提示') }}</div>
        <div class="main-text">
          {{ $t(`即将为您跳转到新门户，请确认是否跳转?`) }} （ {{ countdown }}s）
        </div>
        <div class="main-button">
          <button @click="cancelRedirect">{{ $t('取消') }}</button>
          <button @click="confirmRedirect">{{ $t('立即跳转') }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { useTCaptcha } from '@/utils/index'
import PfVerifyButton from '@/components/pfVerifyButton.vue'
import News from './modules/news.vue'
import SrmPlatform from './modules/srm-platform.vue'
import Guide from './modules/guide.vue'
import FollowUs from './modules/follow-us.vue'
import Introduce from './modules/introduce.vue'
import Notify from './modules/notify.vue'
import Footer from './footer.vue'
@Component({
  components: {
    PfVerifyButton,
    SrmPlatform,
    Guide,
    News,
    FollowUs,
    Introduce,
    Notify,
    Footer
  },
  middleware: []
})
export default class TCLLogin extends Vue {
  loginType: number = 1 // 0: 免密登录 1：密码登录
  dialogType: boolean = false
  dialogData: object = {}
  className = 'modalOpen'
  phoneNumber: number | null = null
  phoneForm: object = {
    phone: '',
    validateCode: ''
  }
  accountForm: object = {
    username: '',
    password: ''
  }
  validateByPhone: object = {}
  validateByName: object = {}
  countdownShow: boolean = false
  countdown = 30
  isMobile: boolean = false
  timer: any = null
  get formRef() {
    const name = this.loginType ? 'account' : 'phone'
    return this.$refs[`${name}Form`] && (this.$refs[`${name}Form`] as any)
  }

  async mounted() {
    const _this: any = this
    const hasAuth = (this as any).$hasAuth()
    if (hasAuth) {
      await this.isClearCoolie()
    }
    _this.$clearParentAuth()
    _this.checkIsMobile()
    if (!_this.isMobile && location.hostname.indexOf('localhost') <= -1) {
      this.countdownShow = true
      _this.countdownTimer()
    }
    _this.setCooperativeHeight()
    window.addEventListener('resize', () => {
      _this.setCooperativeHeight()
    })
    // 直接跳转到新官网
    // if (location.hostname.indexOf('localhost') <= -1) {
    //   window.location.href = '/'
    // }
    // this.isClearCallBack()
  }
  beforedestroy() {
    clearTimeout(this.timer)
  }
  setCooperativeHeight() {
    let _dom = document.querySelector('.login-container') as HTMLElement
    const _domWidth = _dom.offsetWidth
    const boxHeight = _domWidth * 0.3
    _dom.style.height = `${boxHeight}px`
  }
  // countdown 定时器
  countdownTimer() {
    const _this: any = this
    if (_this.countdown > 0) {
      _this.countdown--
      _this.timer = setTimeout(() => {
        _this.countdownTimer()
      }, 1000)
    } else {
      _this.countdownShow = false
      window.location.href = '/'
    }
  }
  checkIsMobile() {
    const userAgentInfo = navigator.userAgent
    const mobileAgents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']
    for (let i = 0; i < mobileAgents.length; i++) {
      if (userAgentInfo.indexOf(mobileAgents[i]) > 0) {
        console.log(userAgentInfo)
        this.isMobile = true
        return
      }
    }
    this.isMobile = false
  }
  /** methods --start */
  // 发短信开启防水墙
  openCaptcha() {
    this.formRef.validateField('phone', (valid: boolean) => {
      if (valid) {
        ;(this.$refs.verifyBtnRef as any).openCaptcha()
      }
    })
  }

  // 短信登录获取验证码
  async getVerifyCode(res: any) {
    this.formRef.validateField('phone', (valid: boolean) => {
      if (valid) {
        console.log(valid)
      } else {
        return
      }
    })
    const { phone } = this.phoneForm as any
    let params = {
      mobile: phone || '',
      rand: res.randstr,
      ticket: res.ticket
    }
    const { code, data } = await this.$api.login.loginVerifyCode(params)
    if (code == 200) {
      if (this.loginType === 0) {
        ;(this.$refs.verifyBtnRef as any).countDown()
      }
    } else if (code == 601) {
      this.openCaptcha()
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
      }
    }
  }
  phoneNumberKeyup() {
    this.$refs.verifyCodeRef && (this.$refs.verifyCodeRef as any).focusIn()
  }
  loginSubmit(data = 0) {
    this.formRef.validate(async (valid: boolean) => {
      if (valid) {
        let params: object = { username: this.accountForm['username'] }
        if (this.loginType === 0) {
          params = { phone: this.phoneNumber || '' }
          console.log(params)
        }
        // const { code, data } = await this.$api.login.ifOpenCaptcha(params)
        const { code } = { code: 200 }
        if (code == 200) {
          let loginParams: object = {
            rand: '',
            ticket: ''
          }
          if (this.loginType === 1) {
            let { username, password } = this.accountForm as any
            loginParams = Object.assign(loginParams, {
              username: username,
              password: (this as any).$$encrypt(password)
            })
          } else {
            const { phone, validateCode } = this.phoneForm as any
            loginParams = Object.assign(loginParams, {
              mobile: phone,
              validateCode: validateCode
            })
          }
          if (data == 0) {
            this.loginApi(loginParams)
          }
          if (data == 1) {
            useTCaptcha().then((resCaptcha: any) => {
              ;(loginParams as any).rand = resCaptcha.randstr
              ;(loginParams as any).ticket = resCaptcha.ticket
              this.loginApi(loginParams)
            })
          }
        }
      }
    })
  }
  async loginApi(params: object) {
    if (this.loginType === 1) {
      var { code, data } = await this.$api.login.login(params)
    } else {
      var { code, data } = await this.$api.login.loginVerify(params)
    }
    if (code == 200) {
      this.dialogResetPassword(data)
    } else if (code == 601) {
      this.loginSubmit(1)
    } else if (code === 604) {
      this.$router.push({
        path: '/resetPassword'
      })
    } else {
      if (data && data.errorLabels && data.errorLabels.length) {
        this.formRef.addErrorLabels(data.errorLabels)
      }
    }
  }
  dialogResetPassword(data: any) {
    if (data.status == 0) {
      //不提示
      const { username, password } = this.accountForm as any
      window.localStorage.setItem(
        'userLoginInfo',
        JSON.stringify({
          loginType: this.loginType,
          username,
          password
        })
      )
      this.redirectUrl(data)
    } else if (data.status == 1) {
      //提示
      this.fixed()
      this.dialogType = true
      this.dialogData = data
    }
  }
  // 下次再说
  cruelRefusal() {
    this.fixed_cancel()
    this.redirectUrl(this.dialogData)
  }
  //确认修改
  confirmModification() {
    this.fixed_cancel()
    this.$router.push({
      path: `resetPassword`
    })
  }
  // 取消跳转
  cancelRedirect() {
    clearTimeout(this.timer)
    this.countdownShow = false
  }
  // 确认跳转
  confirmRedirect() {
    this.countdownShow = false
    window.location.href = '/'
  }
  //禁止body滚动
  fixed() {
    this.dialogType = true
    document.body.classList.add(this.className)
    document.body.style.height = '100%'
    document.body.style.overflow = 'hidden'
  }
  //同意body滚动
  fixed_cancel() {
    this.dialogType = false
    document.body.classList.remove(this.className)
    document.body.removeAttribute('style')
  }
  redirectUrl(data: any) {
    let href = ''
    // const { query } = this.$route
    // let nowHref = window.location.href
    // if (query && query.callback) {
    //   href = window.atob(query.callback as string)
    // } else if (data.callbackUrl) {
    //   href = data.callbackUrl
    // } else {
    //   if (nowHref.indexOf('dev') > 0) {
    //     href = 'http://srm.dev.qeweb.com/'
    //   } else if (nowHref.indexOf('test') > 0) {
    //     href = 'http://srm.test.qeweb.com/'
    //   } else if (nowHref.indexOf('.qeweb.com') > 0) {
    //     let a = nowHref.replace(/login/, 'srm')
    //     href = a.substring(0, a.lastIndexOf('/'))
    //   } else {
    //     href = 'http://localhost:9012/'
    //   }
    // }
    href = data.callbackUrl
    if (location.hostname.indexOf('localhost') > -1) {
      href = 'http://localhost:9012'
    }
    sessionStorage.removeItem('currentSideMenu')
    window.location.href = href
  }
  //是否需要清楚callback
  // isClearCallBack () {
  //    const callback:any = this.$route.query.callback
  //    if(callback){
  //       const callbackUrl = new URL(window.atob(callback))
  //           const mainUrlHostName = location.hostname
  //       if(callbackUrl.hostname !== mainUrlHostName){
  //           this.$router.replace({query: {}})
  //       }
  //    }
  // }
  async isClearCoolie() {
    await this.$api.login.isClearToken()
  }
  /** methods --end */
}
</script>

<style lang="less" scoped>
.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-image: url('~assets/images/download.jpg');
  background-repeat: no-repeat;
  background-position: center top;
  // background-size: auto 100%;
  min-height: 600px;
  background-size: auto 100%;
  .login-form-wrapper {
    width: 490px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px;
    margin-right: 3%;
    box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.06);
    padding: 50px 48px;
    .login-form-title {
      display: flex;
      align-items: center;
      margin-bottom: 40px;
      .icon-style {
        width: 30px;
        height: 30px;
        background-image: url('~assets/images/login.png');
      }
      span {
        font-weight: 500;
        font-size: 24px;
        color: #4e5a70;
        margin-left: 20px;
        display: flex;
        align-items: center;
        &::before {
          content: '';
          width: 1px;
          height: 24px;
          margin-right: 20px;
          background-color: #4a556b;
          opacity: 0.2;
        }
      }
    }
    .login-form-header {
      line-height: 46px;
      font-size: 14px;
      display: flex;
      margin-bottom: 24px;
      .tab-item {
        border-bottom: 2px solid #fff;
        margin-right: 24px;
        color: #c6c6c6;
        cursor: pointer;
        &.active {
          border-color: #f55448;
          color: #333;
        }
      }
    }
    .form-box {
      /deep/ .label {
        line-height: 30px;
        font-weight: normal;
        color: #333;
      }
    }
    .form-button {
      position: absolute;
      right: 0;
      top: 38px;
    }
    .check-box {
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;
      cursor: pointer;
      > span {
        margin-left: 5px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
      .checkbox:checked {
        background: #00469c;
      }
      .checkbox {
        width: 15px;
        height: 15px;
        background-color: #ffffff;
        border: solid 2px #00469c;
        -webkit-border-radius: 50%;
        border-radius: 50%;
        font-size: 0.8rem;
        margin: 0;
        padding: 0;
        position: relative;
        display: inline-block;
        vertical-align: top;
        cursor: default;
        -webkit-appearance: none;
        -webkit-user-select: none;
        user-select: none;
        -webkit-transition: background-color ease 0.1s;
        transition: background-color ease 0.1s;
      }
      .checkbox:checked::after {
        content: '';
        top: 2px;
        left: 2px;
        position: absolute;
        background: transparent;
        border: #fff solid 2px;
        border-top: none;
        border-right: none;
        height: 5px;
        width: 8px;
        -moz-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
      }
    }
    .to-register {
      margin-top: 15px;
      text-align: center;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        color: #00469c;
        cursor: pointer;
      }
    }
  }
}
.login-btn {
  cursor: pointer;
  text-align: center;
  background-color: #00469c;
  color: #fff;
  padding: 0;
  border: none;
  width: 100%;
  font-size: 18px;
  color: #fff;
  margin-top: 40px;
  padding: 12px 0;
  border-radius: 8px;
  background-color: #4e5a70;
}
.content-width {
  width: 1200px;
  margin: 0 auto;
}
.login-main {
  margin: 80px auto;
  .top {
    display: flex;
    margin-bottom: 16px;
    .notify,
    .right-container {
      width: 330px;
    }
    .srm-platform {
      flex: 1;
      margin: 0 16px;
    }
  }
  .bottom {
    display: flex;
    .follow-us {
      width: 330px;
    }
    .introduce {
      flex: 1;
      margin-left: 16px;
    }
  }
}
.footer {
  background-color: #ededed;
}
.dialog {
  z-index: 1001;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  .dialogMain {
    width: 500px;
    height: 300px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main-hander {
      width: 100%;
      height: 70px;
      padding: 20px;
      background-color: #31374e;
      text-align: center;
      color: #fff;
    }
    .main-text {
      width: 100%;
      padding: 20px;
      text-align: center;
      flex: 1;
      line-height: 150px;
    }
    .main-button {
      width: 100%;
      height: 44px;
      padding: 8px;
      display: flex;
      justify-content: right;
      button {
        border: none;
        outline: none;
        background-color: #fff;
        margin: 0 5px;
        padding: 0 5px;
      }
      button:hover {
        background-color: #f6f6f6;
        border-radius: 2px;
      }
    }
  }
  .noticeMain {
    width: 1000px;
    height: 476px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-radius: 5px;
    .main-text {
      position: relative;
      padding: 0;
    }
    img {
      width: 100%;
      height: 100%;
    }
    i {
      position: absolute;
      display: inline-block;
      width: 28px;
      height: 28px;
      background: url('~assets/images/close.png');
      background-size: 28px 28px;
      top: 12px;
      right: 12px;
      cursor: pointer;
    }
  }
}
</style>
