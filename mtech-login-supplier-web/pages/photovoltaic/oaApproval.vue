<!-- OA-预审调查表-详情 -->
<template>
  <div class="detail">
    <div class="header">
      <div class="title">{{ $t('供应商资格预审调查表') }}</div>
    </div>
    <div class="content">
      <div class="title" @click="handleShow('showOne')">一、基本信息</div>
      <div v-if="showOne" class="base-info">
        <div class="base-info-content">
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('供应商名称') }}</div>
            <div class="info-content-item">
              {{ baseInfo.supplierName || '--' }}
            </div>
            <div class="info-title">{{ $t('法人代表') }}</div>
            <div class="info-content-item">
              {{ baseInfo.legalRepresentative || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('拟入围物资名称') }}</div>
            <div class="info-content-item" style="width: 88% !important; display: block !important">
              <div v-for="e in 2" :key="e" style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in materialList" :key="key">
                  <mt-checkbox
                    v-if="e === 1 && key < 9"
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'materialName')"
                    :disabled="true"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                  <mt-checkbox
                    v-if="e === 2 && key >= 9"
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'materialName')"
                    :disabled="true"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('营业执照注册号') }}</div>
            <div class="info-content-item">
              {{ baseInfo.taxNumber || '--' }}
            </div>
            <div class="info-title">{{ $t('注册资本（万元）') }}</div>
            <div class="info-content-item">
              {{ baseInfo.registeredCapital || '--' }}
            </div>
            <div class="info-title">{{ $t('企业成立日期') }}</div>
            <div class="info-content-item">
              {{ baseInfo.establishmentTime | formatTime }}
            </div>
            <div class="info-title">{{ $t('注册日期') }}</div>
            <div class="info-content-item">
              {{ baseInfo.registrationDate | formatTime }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('公司注册地址') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              {{ baseInfo.detailAddress || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('公司经营地址') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              {{ baseInfo.businessAddress || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('生产工厂详细地址') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              {{ baseInfo.factoryAddress || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('公司电话') }}</div>
            <div class="info-content-item">
              {{ baseInfo.enterprisePhone || '--' }}
            </div>
            <div class="info-title">{{ $t('公司网址') }}</div>
            <div class="info-content-item">
              {{ baseInfo.website || '--' }}
            </div>
            <div class="info-title">{{ $t('开户行') }}</div>
            <div class="info-content-item">
              {{ baseInfo.bankName || '--' }}
            </div>
            <div class="info-title">{{ $t('开户账号') }}</div>
            <div class="info-content-item">
              {{ baseInfo.bankAccount || '--' }}
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('企业性质') }}</div>
            <div class="info-content-item" style="width: 88% !important; display: block !important">
              <div style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in enterpriseNatureList" :key="key">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'enterpriseNature')"
                    :disabled="true"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
              <div style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in isListedCompanyList" :key="key">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'isListedCompany')"
                    :disabled="true"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
              <div style="display: flex">
                <div class="checkbox-wrap" v-for="(item, key) in enterpriseTypeList" :key="key">
                  <mt-checkbox
                    :value="item.value"
                    :checked="item.checked"
                    :label="item.text"
                    :fields="{ label: 'text', id: 'value' }"
                    @change="checkChange(item, 'enterpriseType')"
                    :disabled="true"
                    style="margin-right: 20px"
                  ></mt-checkbox>
                </div>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('厂区面积（平方米）') }}</div>
            <div class="info-content-item">
              {{ baseInfo.factoryArea || '--' }}
            </div>
            <div class="info-title">{{ $t('建筑面积（平方米）') }}</div>
            <div class="info-content-item">
              {{ baseInfo.buildingArea || '--' }}
            </div>
            <div class="info-title">{{ $t('已使用生产能力') }}</div>
            <div class="info-content-item">
              {{ baseInfo.usedProduceAbility || '--' }}
            </div>
          </div>
        </div>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('baseInfoFileList')"
          >附营业执照</span
        >
      </div>
      <div class="title" @click="handleShow('showTwo')">二、主要客户</div>
      <div v-if="showTwo" style="width: 100%">
        <sc-table
          ref="clientTableRef"
          grid-id="faed14f6-8706-44ed-9a75-6c790b0d662e"
          :loading="loading"
          :columns="customerColumns"
          :table-data="clientList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('clientFileList')"
          >业绩表</span
        >
      </div>
      <div class="title" @click="handleShow('showThree')">三、主要竞争对手</div>
      <div v-if="showThree" style="width: 100%">
        <sc-table
          ref="competitorTableRef"
          grid-id="6d59ce4e-a7de-4c82-9d3e-d820aec6e7c0"
          :loading="loading"
          :columns="competitorColumns"
          :table-data="competitorList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div class="title" @click="handleShow('showFour')">四、二级供应商</div>
      <div v-if="showFour" style="width: 100%">
        <sc-table
          ref="levelTwoSupplierTableRef"
          grid-id="8f4aafab-42e7-4159-96c3-d1751f52158d"
          :loading="loading"
          :columns="levelTwoSupplierColumns"
          :table-data="levelTwoSupplierList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div class="title" @click="handleShow('showFive')">五、组织机构</div>
      <div v-if="showFive" style="width: 100%">
        <sc-table
          ref="organizationTableRef"
          grid-id="e8ae01f7-0b6d-45fd-a0df-957e301e3f4a"
          :loading="loading"
          :columns="organizationColumns"
          :table-data="organizationList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('organizationFileList')"
          >附组织结构图</span
        >
      </div>
      <div class="title" @click="handleShow('showSix')">六、生产设备</div>
      <div v-if="showSix" style="width: 100%">
        <sc-table
          ref="produceDeviceTableRef"
          grid-id="e809ffeb-7ca5-427a-b682-332f75ee4d2c"
          :loading="loading"
          :columns="produceDeviceColumns"
          :table-data="produceDeviceList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('produceDeviceFileList')"
          >附设备照片</span
        >
      </div>
      <div class="title" @click="handleShow('showSeven')">七、检验设备</div>
      <div v-if="showSeven" style="width: 100%">
        <sc-table
          ref="testDeviceTableRef"
          grid-id="32fd717f-a465-46d0-b105-c6de54353b5e"
          :loading="loading"
          :columns="testDeviceColumns"
          :table-data="testDeviceList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('testDeviceFileList')"
          >附设备照片</span
        >
      </div>
      <div class="title" @click="handleShow('showEight')">八、持证情况</div>
      <div v-if="showEight" style="width: 100%">
        <sc-table
          ref="certificateTableRef"
          grid-id="b983ee65-4bfa-4215-aec5-284ab772fb5d"
          :loading="loading"
          :columns="certificateColumns"
          :table-data="certificateList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('certificateFileList')"
          >附证书扫描件</span
        >
      </div>
      <div class="title" @click="handleShow('showNine')">九、资信情况</div>
      <div v-if="showNine" class="base-info">
        <div class="base-info-content">
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('银行信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div class="checkbox-wrap" v-for="(item, key) in bankCreditLevelOptions" :key="key">
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'bankCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('工商企业信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div
                class="checkbox-wrap"
                v-for="(item, key) in businessCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'businessCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('纳税信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div class="checkbox-wrap" v-for="(item, key) in taxCreditLevelOptions" :key="key">
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'taxCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('财务会计信用等级') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div
                class="checkbox-wrap"
                v-for="(item, key) in financeCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'financeCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('企业产品质量信用') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div style="margin-right: 20px">最近 1-3 年是否出现重大安全事故投诉记录</div>
              <div
                class="checkbox-wrap"
                v-for="(item, key) in productQualityCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'productQualityCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('重大项目投标信用') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div style="margin-right: 20px">
                最近 1-3 年间在投标项目中有无重大窜标围标等恶性竞标记录
              </div>
              <div
                class="checkbox-wrap"
                v-for="(item, key) in projectBidCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'projectBidCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
          <div class="base-info-line fbox">
            <div class="info-title">{{ $t('法定代表人个人信用') }}</div>
            <div class="info-content-item" style="width: 88% !important">
              <div style="margin-right: 20px">最近 1-3 年间法定代表人有无不良信用记录</div>
              <div
                class="checkbox-wrap"
                v-for="(item, key) in legalPersonCreditLevelOptions"
                :key="key"
              >
                <mt-checkbox
                  :value="item.value"
                  :checked="item.checked"
                  :label="item.text"
                  :fields="{ label: 'text', id: 'value' }"
                  @change="checkChange(item, 'legalPersonCreditLevel')"
                  :disabled="true"
                  style="margin-right: 20px"
                ></mt-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="padding: 20px 0">
        注：<span
          style="color: #2783fe; cursor: pointer"
          @click="handleClick('certificateCreditFileList')"
          >附支持材料扫描件、信用中国查询截图</span
        >
      </div>
      <div class="title" @click="handleShow('showTen')">十、正在进行及即将开始的法律纠纷</div>
      <div v-if="showTen" style="width: 100%">
        <sc-table
          ref="legalDisputesTableRef"
          grid-id="26e8b112-fb8f-45bf-acaf-ee67a14dcdfa"
          :loading="loading"
          :columns="legalDisputesColumns"
          :table-data="legalDisputesList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div class="title" @click="handleShow('showEleven')">十一、股权结构</div>
      <div v-if="showEleven" style="width: 100%">
        <sc-table
          ref="shareholdTableRef"
          grid-id="fab8af82-25a2-4d8b-ac33-ff1e9283b9de"
          :loading="loading"
          :columns="shareholdColumns"
          :table-data="shareholdList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">注：<span>提供前 5 大股东的名称和比例</span></div>
      <div class="title" @click="handleShow('showTwelve')">十二、财务报表</div>
      <div v-if="showTwelve" style="width: 100%">
        <sc-table
          ref="financeTableRef"
          grid-id="cad32847-55f2-4ad3-a5d3-f4dcf3e20efd"
          :loading="loading"
          :columns="financeColumns"
          :table-data="financeList"
          :fix-height="200"
          keep-source
          :sortable="false"
          :is-show-right-btn="false"
          :is-show-refresh-bth="false"
        >
        </sc-table>
      </div>
      <div style="padding: 20px 0">
        注：<span style="color: #2783fe; cursor: pointer" @click="handleClick('financeFileList')"
          >提供近三年资产负债表、利润表、现金流量表</span
        >
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import ScTable from '@/components/ScTable/src/index'
import { formatDate } from '@/utils/utils.ts'
export default {
  layout: 'h5',
  components: { ScTable },
  data() {
    return {
      baseInfo: {},
      loading: false,
      tableData: [],

      materialList: [
        { text: this.$t('组件'), value: '组件', checked: false },
        { text: this.$t('逆变器'), value: '逆变器', checked: false },
        { text: this.$t('并网箱'), value: '并网箱', checked: false },
        { text: this.$t('支架'), value: '支架', checked: false },
        { text: this.$t('彩钢瓦支架'), value: '彩钢瓦支架', checked: false },
        { text: this.$t('电缆'), value: '电缆', checked: false },
        { text: this.$t('储能'), value: '储能', checked: false },
        { text: this.$t('一次二次设备'), value: '一次二次设备', checked: false },
        { text: this.$t('充电桩'), value: '充电桩', checked: false },
        { text: this.$t('风电'), value: '风电', checked: false },
        { text: this.$t('H钢（钢结构）'), value: 'H钢（钢结构）', checked: false },
        { text: this.$t('辅材'), value: '辅材', checked: false },
        { text: this.$t('其他'), value: '其他', checked: false },
        { text: this.$t('钢材'), value: '钢材', checked: false },
        { text: this.$t('铝材'), value: '铝材', checked: false },
        { text: this.$t('硅片'), value: '硅片', checked: false },
        { text: this.$t('硅料'), value: '硅料', checked: false },
        { text: this.$t('元器件'), value: '元器件', checked: false }
      ],
      enterpriseNatureList: [
        { text: this.$t('国有/集体'), value: '1', checked: false, disabled: false },
        { text: this.$t('私营'), value: '2', checked: false, disabled: false },
        { text: this.$t('港/澳/台独资'), value: '3', checked: false, disabled: false },
        { text: this.$t('其他外商独资'), value: '4', checked: false, disabled: false },
        { text: this.$t('港/澳/台合资'), value: '5', checked: false, disabled: false },
        { text: this.$t('其他外商合资'), value: '6', checked: false, disabled: false }
      ],
      isListedCompanyList: [
        { text: this.$t('上市公司'), value: 1, checked: false, disabled: false },
        { text: this.$t('非上市公司'), value: 2, checked: false, disabled: false }
      ],
      enterpriseTypeList: [
        { text: this.$t('制造商'), value: 1, checked: false, disabled: false },
        { text: this.$t('贸易商'), value: 2, checked: false, disabled: false },
        { text: this.$t('代理商'), value: 3, checked: false, disabled: false }
      ],
      clientList: [],
      competitorList: [],
      levelTwoSupplierList: [],
      organizationList: [],
      produceDeviceList: [],
      testDeviceList: [],
      certificateList: [],
      legalDisputesList: [],
      shareholdList: [],
      financeList: [],

      employeeTypeOptions: [
        { text: this.$t('EHS人数'), value: 1 },
        { text: this.$t('研发人数'), value: 2 },
        { text: this.$t('生产人数'), value: 3 },
        { text: this.$t('质检人数'), value: 4 },
        { text: this.$t('售后人数'), value: 5 }
      ],
      financeTypeOptions: [
        { text: this.$t('固定资产'), value: 1 },
        { text: this.$t('流动资产'), value: 2 },
        { text: this.$t('资产负债率'), value: 3 },
        { text: this.$t('主营业务收入'), value: 4 },
        { text: this.$t('净利润'), value: 5 },
        { text: this.$t('净利润率'), value: 6 }
      ],
      bankCreditLevelOptions: [
        { text: this.$t('AAA级'), value: 1, checked: false, disabled: false },
        { text: this.$t('AA级'), value: 2, checked: false, disabled: false },
        { text: this.$t('A级'), value: 3, checked: false, disabled: false },
        { text: this.$t('其他'), value: 4, checked: false, disabled: false }
      ],
      businessCreditLevelOptions: [
        { text: this.$t('AAA级'), value: 1, checked: false, disabled: false },
        { text: this.$t('AA级'), value: 2, checked: false, disabled: false },
        { text: this.$t('A级'), value: 3, checked: false, disabled: false },
        { text: this.$t('其他'), value: 4, checked: false, disabled: false }
      ],
      taxCreditLevelOptions: [
        { text: this.$t('A级'), value: 1, checked: false, disabled: false },
        { text: this.$t('B级'), value: 2, checked: false, disabled: false },
        { text: this.$t('C级'), value: 3, checked: false, disabled: false },
        { text: this.$t('D级'), value: 4, checked: false, disabled: false },
        { text: this.$t('其他'), value: 5, checked: false, disabled: false }
      ],
      financeCreditLevelOptions: [
        { text: this.$t('A级'), value: 1, checked: false, disabled: false },
        { text: this.$t('B级'), value: 2, checked: false, disabled: false },
        { text: this.$t('C级'), value: 3, checked: false, disabled: false },
        { text: this.$t('D级'), value: 4, checked: false, disabled: false },
        { text: this.$t('其他'), value: 5, checked: false, disabled: false }
      ],
      productQualityCreditLevelOptions: [
        { text: this.$t('有'), value: 1, checked: false, disabled: false },
        { text: this.$t('无'), value: 0, checked: false, disabled: false }
      ],
      projectBidCreditLevelOptions: [
        { text: this.$t('有'), value: 1, checked: false, disabled: false },
        { text: this.$t('无'), value: 0, checked: false, disabled: false }
      ],
      legalPersonCreditLevelOptions: [
        { text: this.$t('有'), value: 1, checked: false, disabled: false },
        { text: this.$t('无'), value: 0, checked: false, disabled: false }
      ],

      showOne: true,
      showTwo: true,
      showThree: true,
      showFour: true,
      showFive: true,
      showSix: true,
      showSeven: true,
      showEight: true,
      showNine: true,
      showTen: true,
      showEleven: true,
      showTwelve: true
    }
  },
  filters: {
    formatTime: (e) => {
      if (e && !isNaN(e) && e.length == 13) {
        e = Number(e)
        return formatDate(e)
      } else {
        return '-'
      }
    }
  },
  computed: {
    currentId() {
      return this.$route.query?.id
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    },
    editRules() {
      return {
        companyCode: [{ required: true, message: this.$t('必填') }]
      }
    },
    toolbar() {
      return [
        { code: 'add', name: this.$t('新增'), status: 'info', loading: false },
        { code: 'delete', name: this.$t('删除'), status: 'info', loading: false }
      ]
    },
    customerColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'clientName',
          title: this.$t('客户名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.clientName} />]
            }
          }
        },
        {
          field: 'material',
          title: this.$t('所供物料及规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.material} />]
            }
          }
        },
        {
          field: 'goodsRate',
          title: this.$t('占自身出货率'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.goodsRate} type='integer' clearable min={0} max={100} />
              ]
            }
          }
        }
      ]
    },
    competitorColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'product',
          title: this.$t('产品'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.product} />]
            }
          }
        },
        {
          field: 'competitorName',
          title: this.$t('行业龙头/竞争对手名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.competitorName} />]
            }
          }
        },
        {
          field: 'marketShareRate',
          title: this.$t('市场份额百分比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input
                  v-model={row.marketShareRate}
                  type='integer'
                  clearable
                  min={0}
                  max={100}
                />
              ]
            }
          }
        },
        {
          field: 'advantage',
          title: this.$t('优势'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.advantage} />]
            }
          }
        },
        {
          field: 'disadvantage',
          title: this.$t('劣势'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.disadvantage} />]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} />]
            }
          }
        }
      ]
    },
    levelTwoSupplierColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'materialName',
          title: this.$t('材料名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.materialName} />]
            }
          }
        },
        {
          field: 'supplierName',
          title: this.$t('供应商名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.supplierName} />]
            }
          }
        },
        {
          field: 'unit',
          title: this.$t('单位'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.unit} />]
            }
          }
        },
        {
          field: 'yearPurchaseQuantity',
          title: this.$t('年采购量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-input v-model={row.yearPurchaseQuantity} type='integer' clearable min={0} />
              ]
            }
          }
        },
        {
          field: 'ratio',
          title: this.$t('占比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.ratio} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} />]
            }
          }
        }
      ]
    },
    organizationColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'employeeType',
          title: this.$t('类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text = this.employeeTypeOptions.find(
                (item) => item.value === row.employeeType
              )?.text
              return [<div>{text || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.employeeType}
                  options={this.employeeTypeOptions}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'employeeQuantity',
          title: this.$t('人数'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.employeeQuantity} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'ratio',
          title: this.$t('占比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.ratio} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'contactPerson',
          title: this.$t('对接人'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.contactPerson} />]
            }
          }
        },
        {
          field: 'contactPhone',
          title: this.$t('对接电话'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.contactPhone} />]
            }
          }
        }
      ]
    },
    produceDeviceColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'deviceName',
          title: this.$t('主要设备名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.deviceName} />]
            }
          }
        },
        {
          field: 'model',
          title: this.$t('规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.model} />]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.quantity} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'brand',
          title: this.$t('生产厂家或品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.brand} />]
            }
          }
        },
        {
          field: 'monthCapacity',
          title: this.$t('设备月产能'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.monthCapacity} />]
            }
          }
        },
        {
          field: 'startUserDate',
          title: this.$t('开始使用年月'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.startUserDate?.length === 13
                  ? formatDate(Number(row.startUserDate))
                  : row.startUserDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.startUserDate} type={'date'} transfer clearable />]
            }
          }
        }
      ]
    },
    testDeviceColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'deviceName',
          title: this.$t('主要设备名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.deviceName} />]
            }
          }
        },
        {
          field: 'model',
          title: this.$t('规格型号'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.model} />]
            }
          }
        },
        {
          field: 'quantity',
          title: this.$t('数量'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.quantity} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'brand',
          title: this.$t('生产厂家或品牌'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.brand} />]
            }
          }
        },
        {
          field: 'testItem',
          title: this.$t('测试项目'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.testItem} />]
            }
          }
        },
        {
          field: 'startUserDate',
          title: this.$t('开始使用年月'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.startUserDate?.length === 13
                  ? formatDate(Number(row.startUserDate))
                  : row.startUserDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.startUserDate} type={'date'} transfer clearable />]
            }
          }
        }
      ]
    },
    certificateColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'certificateType',
          title: this.$t('认证类型'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.certificateType} />]
            }
          }
        },
        {
          field: 'issueUnit',
          title: this.$t('发证单位'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.issueUnit} />]
            }
          }
        },
        {
          field: 'issueDate',
          title: this.$t('发证日期'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.issueDate?.length === 13 ? formatDate(Number(row.issueDate)) : row.issueDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.issueDate} type={'date'} transfer clearable />]
            }
          }
        },
        {
          field: 'validEndDate',
          title: this.$t('有效期至'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text =
                row.validEndDate?.length === 13
                  ? formatDate(Number(row.validEndDate))
                  : row.validEndDate
              return text
            },
            edit: ({ row }) => {
              return [<vxe-input v-model={row.validEndDate} type={'date'} transfer clearable />]
            }
          }
        }
      ]
    },
    legalDisputesColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'legalRole',
          title: this.$t('公司一审角色（原告/被告）'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.legalRole} />]
            }
          }
        },
        {
          field: 'currentStage',
          title: this.$t('目前所在阶段'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.currentStage} />]
            }
          }
        },
        {
          field: 'counterpart',
          title: this.$t('相对方'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.counterpart} />]
            }
          }
        },
        {
          field: 'amount',
          title: this.$t('金额'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.amount} type='integer' clearable min={0} />]
            }
          }
        }
      ]
    },
    shareholdColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'shareholder',
          title: this.$t('股东名称'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.shareholder} />]
            }
          }
        },
        {
          field: 'ratio',
          title: this.$t('占比'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.ratio} type='integer' clearable min={0} max={100} />]
            }
          }
        }
      ]
    },
    financeColumns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'financeType',
          title: this.$t('财务类型'),
          editRender: {},
          slots: {
            default: ({ row }) => {
              let text = this.financeTypeOptions.find(
                (item) => item.value === row.financeType
              )?.text
              return [<div>{text || ''}</div>]
            },
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.financeType}
                  options={this.financeTypeOptions}
                  option-props={{ label: 'text', value: 'value' }}
                  placeholder={this.$t('请选择')}
                  clearable
                  transfer
                />
              ]
            }
          }
        },
        {
          field: 'beforeLastYear',
          title: this.$t('前年'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.beforeLastYear} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'lastYear',
          title: this.$t('去年'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.lastYear} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'currentYear',
          title: this.$t('今年预期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.currentYear} type='integer' clearable min={0} />]
            }
          }
        },
        {
          field: 'nextYear',
          title: this.$t('明年预期'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.nextYear} type='integer' clearable min={0} />]
            }
          }
        }
      ]
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'invoiceNo',
          title: this.$t('客户名称')
        },
        {
          field: 'invoiceNo',
          title: this.$t('所供物料及规格型号')
        },
        {
          field: 'invoiceNo',
          title: this.$t('占自身出货率')
        }
      ]
    }
  },
  created() {
    if (this.currentId) {
      this.getEnterpriseNatureList()
      this.getDetail()
    }
  },
  methods: {
    handleShow(flag) {
      this[flag] = !this[flag]
    },
    getEnterpriseNatureList() {
      this.$api.gf['queryDict']({
        dictCode: 'EnterpriseType'
      }).then((res) => {
        if (res.code === 200) {
          this.enterpriseNatureList = res.data.map((item) => {
            return {
              text: item.itemName,
              value: item.itemCode,
              checked: false
            }
          })
        }
      })
    },
    getDetail() {
      let params = { id: this.currentId }
      this.$api.gf.queryOaViewApi(params).then((res) => {
        if (res.code === 200) {
          this.baseInfo = res.data
          this.clientList = res.data.clientList
          this.competitorList = res.data.competitorList
          this.levelTwoSupplierList = res.data.levelTwoSupplierList
          this.organizationList = res.data.organizationList
          this.produceDeviceList = res.data.produceDeviceList
          this.testDeviceList = res.data.testDeviceList
          this.certificateList = res.data.certificateList
          this.legalDisputesList = res.data.legalDisputesList
          this.shareholdList = res.data.shareholdList
          this.financeList = res.data.financeList
          this.baseInfo.factoryAddress = res.data.factoryAddress
            ? JSON.parse(res.data.factoryAddress)
                .map((v) => JSON.parse(v)?.address)
                .join(',')
            : ''
          this.setCheckbox()
        }
      })
    },
    setCheckbox() {
      let materials = this.baseInfo.materialName?.split(',')
      this.materialList.forEach((item) => {
        if (materials.includes(item.value)) {
          item.checked = !item.checked
        }
      })
      let enterpriseNature = this.baseInfo.enterpriseNature
      this.enterpriseNatureList.forEach((item) => {
        if (enterpriseNature) {
          if (item.value === enterpriseNature) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let isListedCompany = this.baseInfo.listedCompanyFlag
      this.isListedCompanyList.forEach((item) => {
        if (isListedCompany) {
          if (item.value === isListedCompany) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let enterpriseType = this.baseInfo.enterpriseType
      this.enterpriseTypeList.forEach((item) => {
        if (enterpriseType) {
          if (item.value === enterpriseType) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let bankCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].bankCreditLevel
          : null
      this.bankCreditLevelOptions.forEach((item) => {
        if (bankCreditLevel) {
          if (item.value === bankCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let businessCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].businessCreditLevel
          : null
      this.businessCreditLevelOptions.forEach((item) => {
        if (businessCreditLevel) {
          if (item.value === businessCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let taxCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].taxCreditLevel
          : null
      this.taxCreditLevelOptions.forEach((item) => {
        if (taxCreditLevel) {
          if (item.value === taxCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let financeCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].financeCreditLevel
          : null
      this.financeCreditLevelOptions.forEach((item) => {
        if (financeCreditLevel) {
          if (item.value === financeCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let productQualityCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].productQualityCreditLevel
          : null
      this.productQualityCreditLevelOptions.forEach((item) => {
        if (productQualityCreditLevel !== null) {
          if (item.value === productQualityCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let projectBidCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].projectBidCreditLevel
          : null
      this.projectBidCreditLevelOptions.forEach((item) => {
        if (projectBidCreditLevel !== null) {
          if (item.value === projectBidCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
      let legalPersonCreditLevel =
        this.baseInfo.certificateCreditList.length !== 0
          ? this.baseInfo.certificateCreditList[0].legalPersonCreditLevel
          : null
      this.legalPersonCreditLevelOptions.forEach((item) => {
        if (legalPersonCreditLevel !== null) {
          if (item.value === legalPersonCreditLevel) {
            item.checked = !item.checked
          } else {
            item.disabled = !item.disabled
          }
        }
      })
    },
    checkChange(e, key) {
      let materials,
        enterpriseNature,
        isListedCompany,
        enterpriseType,
        bankCreditLevel,
        businessCreditLevel,
        taxCreditLevel,
        financeCreditLevel,
        productQualityCreditLevel,
        projectBidCreditLevel,
        legalPersonCreditLevel = null
      switch (key) {
        case 'materialName':
          this.materialList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            }
          })
          materials = this.materialList.filter((item) => item.checked).map((item) => item.value)
          this.$set(this.baseInfo, 'materialName', materials.join(','))
          break
        case 'enterpriseNature':
          this.enterpriseNatureList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          enterpriseNature = this.enterpriseNatureList.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'enterpriseNature', enterpriseNature)
          break
        case 'isListedCompany':
          this.isListedCompanyList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          isListedCompany = this.isListedCompanyList.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'listedCompanyFlag', isListedCompany)
          break
        case 'enterpriseType':
          this.enterpriseTypeList.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          enterpriseType = this.enterpriseTypeList.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'enterpriseType', enterpriseType)
          break
        case 'bankCreditLevel':
          this.bankCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          bankCreditLevel = this.bankCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'bankCreditLevel', bankCreditLevel)
          break
        case 'businessCreditLevel':
          this.businessCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          businessCreditLevel = this.businessCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'businessCreditLevel', businessCreditLevel)
          break
        case 'taxCreditLevel':
          this.taxCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          taxCreditLevel = this.taxCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'taxCreditLevel', taxCreditLevel)
          break
        case 'financeCreditLevel':
          this.financeCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          financeCreditLevel = this.financeCreditLevelOptions.find((item) => item.checked)?.value
          this.$set(this.baseInfo, 'financeCreditLevel', financeCreditLevel)
          break
        case 'productQualityCreditLevel':
          this.productQualityCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          productQualityCreditLevel = this.productQualityCreditLevelOptions.find(
            (item) => item.checked
          )?.value
          this.$set(this.baseInfo, 'productQualityCreditLevel', productQualityCreditLevel)
          break
        case 'projectBidCreditLevel':
          this.projectBidCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          projectBidCreditLevel = this.projectBidCreditLevelOptions.find(
            (item) => item.checked
          )?.value
          this.$set(this.baseInfo, 'projectBidCreditLevel', projectBidCreditLevel)
          break
        case 'legalPersonCreditLevel':
          this.legalPersonCreditLevelOptions.forEach((item) => {
            if (item.value === e.value) {
              item.checked = !item.checked
            } else {
              item.disabled = !item.disabled
            }
          })
          legalPersonCreditLevel = this.legalPersonCreditLevelOptions.find(
            (item) => item.checked
          )?.value
          this.$set(this.baseInfo, 'legalPersonCreditLevel', legalPersonCreditLevel)
          break
        default:
          break
      }
    },
    handleClick(key) {
      let title = ''
      let fileList = []
      switch (key) {
        case 'baseInfoFileList':
          title = '营业执照'
          fileList = cloneDeep(this.baseInfo.baseInfoFileList)
          break
        case 'clientFileList':
          title = '业绩表'
          fileList = cloneDeep(this.baseInfo.clientFileList)
          break
        case 'organizationFileList':
          title = '组织结构图'
          fileList = cloneDeep(this.baseInfo.organizationFileList)
          break
        case 'produceDeviceFileList':
          title = '生产设备'
          fileList = cloneDeep(this.baseInfo.produceDeviceFileList)
          break
        case 'testDeviceFileList':
          title = '检验设备'
          fileList = cloneDeep(this.baseInfo.testDeviceFileList)
          break
        case 'certificateFileList':
          title = '证书扫描件'
          fileList = cloneDeep(this.baseInfo.certificateFileList)
          break
        case 'certificateCreditFileList':
          title = '支持材料扫描件、信用中国查询截图'
          fileList = cloneDeep(this.baseInfo.certificateCreditFileList)
          break
        case 'financeFileList':
          title = '近三年资产负债表、利润表、现金流量表'
          fileList = cloneDeep(this.baseInfo.financeFileList)
          break
        default:
          break
      }
      this.$dialog({
        modal: () => import('./components/detailFileMange.vue'),
        data: {
          title,
          fileList,
          type: 'download',
          that: this
        },
        success: (list) => {
          this.$set(this.baseInfo, key, list)
        }
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
.fbox {
  display: flex;
}
.detail {
  background-color: #fff;
  .header {
    text-align: center;
    padding: 20px;
    .title {
      display: inline-block;
      text-align: center;
      font-size: 26px;
      font-weight: 600;
    }
    .btns {
      float: right;
    }
  }
  .content {
    padding: 0 20px;
    .title {
      font-size: 16px;
      padding: 8px 0;
      cursor: pointer;
    }
    .base-info {
      width: 100%;
      .base-info-content {
        padding-left: 0;
        border-left: 1px solid rgba(232, 232, 232, 1);
        border-right: 1px solid rgba(232, 232, 232, 1);
        border-top: 1px solid rgba(232, 232, 232, 1);
        .base-info-line {
          width: 100%;
          min-height: 50px;
          font-size: 14px;
          border-bottom: 1px solid rgba(232, 232, 232, 1);
          .info-title {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 12%;
            background: rgba(99, 134, 193, 0.1);
            padding: 0px 15px;
            font-weight: 500;
            color: rgba(41, 41, 41, 1);
            word-break: break-all;
          }
          .info-content-item {
            display: flex;
            align-items: center;
            line-height: normal;
            width: 13%;
            color: rgba(41, 41, 41, 1);
            padding: 5px 10px;
            word-break: break-all;
          }
        }
      }
    }
  }
}
.checkbox-wrap {
  height: 30px;
  line-height: 30px;
  border-radius: 2px;
  display: flex;
  align-items: center;
}
::v-deep .table-tool-bar {
  height: 0 !important;
}
</style>
