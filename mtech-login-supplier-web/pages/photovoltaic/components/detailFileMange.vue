<!-- 预审调查表-附件管理 -->
<template>
  <mt-dialog ref="fileDialog" height="900" :header="header" @close="close">
    <div class="dialog-content">
      <sc-table
        ref="fileTable"
        :columns="columns"
        :table-data="tableData"
        :loading="loading"
        :is-show-right-btn="false"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <template #fileNameDefault="{ row }">
          <span style="color: #2783fe; cursor: pointer" @click="handlePreview(row)">
            {{ row.fileName }}
          </span>
        </template>
        <template #operateDefault="{ row }">
          <span style="color: #2783fe; cursor: pointer" @click="handleDownload(row)"> 下载 </span>
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/utils.ts'
export default {
  components: { ScTable },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      toolbar: [],
      tableData: [],
      loading: false
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    type() {
      return this.modalData.type
    },
    columns() {
      return [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: '序号',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'fileName',
          title: '文件名称'
          // slots: {
          //   default: 'fileNameDefault'
          // }
        },
        {
          field: 'operate',
          title: '操作',
          slots: {
            default: 'operateDefault'
          }
        }
      ]
    }
  },
  mounted() {
    if (this.type === 'download') {
      this.toolbar = []
      this.tableData = this.modalData?.fileList || []
    } else if (this.type === 'upload') {
      this.toolbar = [
        { code: 'upload', name: '附件上传', status: 'info', loading: false },
        { code: 'delete', name: '删除', status: 'info', loading: false }
      ]
      this.tableData = this.modalData?.fileList || []
    }
    this.$refs['fileDialog'].ejsRef.show()
  },
  methods: {
    handleClickToolBar(e) {
      const selectedRecords = this.$refs['fileTable'].$refs.xGrid.getCheckboxRecords()
      if (['delete'].includes(e.code)) {
        if (!selectedRecords.length) {
          this.$toast({ content: this.$t('请至少选择一条数据！'), type: 'warning' })
          return
        }
      }
      switch (e.code) {
        case 'delete':
          this.$refs['fileTable'].$refs.xGrid.removeCheckboxRow()
          break
        default:
          break
      }
    },
    handleUpload(file) {
      file.id = null
      this.tableData.push(file)
    },
    handlePreview(row) {
      let params = {
        id: row?.fileId || row.id,
        useType: 2
      }
      let _this = this.modalData?.that
      _this.$api.files.filePreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleDownload(row) {
      let _this = this.modalData?.that
      _this.$api.files.downloadPrivateFile({ id: row?.fileId || row.id }).then((res) => {
        download({ fileName: row.fileName, blob: res })
      })
    },
    close() {
      let fileList = this.$refs['fileTable'].$refs.xGrid.getTableData().visibleData.map((item) => {
        return {
          ...item,
          id: null
        }
      })
      this.$emit('confirm-function', fileList)
    }
  }
}
</script>
<style lang="less" scoped>
.dialog-content {
  margin: 16px 0;
  ::v-deep .toolbar-container {
    padding: 0 !important;
  }
  .common-uploader {
    display: none;
  }
}
</style>
