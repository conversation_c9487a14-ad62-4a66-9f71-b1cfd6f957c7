<template>
  <div class="register-wrapper">
    <div class="step-nav">
      <div :class="{ 'step-item': true, active: currentStep >= 0 }">
        <span>{{ $t('注册须知') }}</span>
      </div>
      <span></span>
      <div :class="{ 'step-item': true, active: currentStep >= 1 }">
        <span>{{ $t('填写基本信息') }}</span>
      </div>
      <span></span>
      <div :class="{ 'step-item': true, active: currentStep >= 2 }">
        <span>{{ $t('完成') }}</span>
      </div>
    </div>
    <Step1 v-show="currentStep === 0" @next="currentStep++" />
    <Step2
      v-show="currentStep === 1"
      :currentStep="currentStep"
      @next="currentStep++"
      @prev="currentStep--"
    />
    <Step3 v-show="currentStep === 2" />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Step1 from './steps/step1.vue'
import Step2 from './steps/step2.vue'
import Step3 from './steps/step3.vue'
@Component({
  components: {
    Step1,
    Step2,
    Step3
  }
})
export default class Register extends Vue {
  currentStep: number = 0
}
</script>

<style lang="less" scoped>
.register-wrapper {
  padding: 80px 70px;
  width: 800px;
  margin: 60px auto 160px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 16px 0 rgba(74, 85, 107, 0.06);
  .step-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 48px;
    .step-item {
      display: flex;
      align-items: center;
      font-size: 20px;
      line-height: 40px;
      color: #999;
      span {
        flex: 1;
      }
      &::before {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #ddd;
        color: #fff;
        text-align: center;
        margin-right: 16px;
      }
      &:nth-of-type(1) {
        &::before {
          content: '1';
        }
      }
      &:nth-of-type(2) {
        &::before {
          content: '2';
        }
      }
      &:nth-of-type(3) {
        &::before {
          content: '3';
        }
      }
      &.active {
        color: #333;
        &::before {
          background-color: #2783fe;
        }
      }
    }
    > span {
      margin: 0 16px;
      flex: 1;
      height: 1px;
      background-color: #979797;
    }
  }
  .operation-wrapper {
    display: flex;
    justify-content: space-around;
    margin-top: 40px;
    /deep/.operation-btn {
      width: 240px;
      height: 48px;
      border-radius: 8px;
      border: 1px solid #4e5a70;
      color: #4e5a70;
      font-size: 20px;
      &.e-primary {
        background-color: #4e5a70;
        color: #fff;
      }
    }
  }
}
</style>
