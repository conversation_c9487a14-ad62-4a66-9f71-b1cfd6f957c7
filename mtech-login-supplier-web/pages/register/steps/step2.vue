<template>
  <div id="stepWrapper" class="step-wrapper">
    <mt-form
      :key="$store.state.locale"
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      class="mt-form--wrap"
    >
      <!-- <mt-form-item prop="sourceType" class="form-item" :label="$t('货源类型')">
        <mt-select
          cssClass="e-outline"
          v-model="ruleForm.sourceType"
          :dataSource="sourceList"
          :placeholder="$t('请输入货源类型')"
        ></mt-select>
      </mt-form-item> -->
      <div class="form-item--wrap form-wrapper">
        <mt-form-item
          prop="registerAddressCountryCode"
          class="form-item form-item-prev"
          :label="$t('注册国家/地区')"
        >
          <mt-select
            cssClass="e-outline"
            @change="onCountryChange"
            v-model="ruleForm.registerAddressCountryCode"
            :dataSource="countryList"
            :placeholder="$t('请选择注册国家/地区')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="identityCode"
          class="form-item form-item-next"
          :label="
            $t(ruleForm.registerAddressCountryCode === 'CN' ? '统一社会信用代码' : '相关经营证书')
          "
        >
          <mt-input
            v-model.trim="ruleForm.identityCode"
            type="text"
            cssClass="e-outline"
            :placeholder="
              $t(
                ruleForm.registerAddressCountryCode === 'CN'
                  ? '请输入统一社会信用代码'
                  : '请输入相关经营证书'
              )
            "
            @change="identityCodeChange"
          ></mt-input>
        </mt-form-item>
      </div>
      <!-- <div class="form-item--wrap form-wrapper">
        <mt-form-item
          prop="supplierTypeCode"
          class="form-item form-item-prev"
          :label="$t('供应商类型')"
        >
          <mt-select
            cssClass="e-outline"
            v-model="ruleForm.supplierTypeCode"
            :data-source="supplierTypeList"
            :placeholder="$t('请选择供应商类型')"
            :fields="{ text: 'name', value: 'itemCode' }"
            @change="supplierTypeChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="businessTypeCode" class="form-item form-item-next" :label="$t('业务类型')">
          <mt-select
            cssClass="e-outline"
            v-model="ruleForm.businessTypeCode"
            :data-source="bussinessTypeList"
            :placeholder="$t('请选择业务类型')"
            :fields="{ text: 'name', value: 'itemCode' }"
            @change="businessTypeChange"
          ></mt-select>
        </mt-form-item>
      </div> -->
      <div class="form-item--wrap">
        <mt-form-item prop="enterpriseName" class="form-item" :label="$t('企业名称')">
          <mt-input
            v-model.trim="ruleForm.enterpriseName"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入企业名称')"
            :disabled="firstRegister"
            @change="changeEnterpriseName"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="form-item--wrap">
        <mt-form-item prop="enterpriseEnglishName" class="form-item" :label="$t('企业英文名称')">
          <mt-input
            v-model.trim="ruleForm.enterpriseEnglishName"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入企业英文名称')"
            @change="changeEnterpriseName"
          ></mt-input>
        </mt-form-item>
      </div>
      <div class="form-item--wrap form-wrapper">
        <mt-form-item prop="email" class="form-item form-item-prev" :label="$t('邮箱')">
          <mt-input
            v-model="ruleForm.email"
            cssClass="e-outline"
            type="text"
            :placeholder="$t('请输入邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="mailCode" class="form-item form-item-next" :label="$t('邮箱认证')">
          <mt-input
            v-model="ruleForm.mailCode"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入注册码进行认证')"
          ></mt-input>
          <span class="form-button">
            <mt-button
              cssClass="verifica--button"
              :disabled="mailCodeCountDown >= 0"
              @click="getMailCode"
            >
              {{ mailCodeCountDown >= 0 ? `${mailCodeCountDown}s` : $t('获取验证码') }}
            </mt-button>
          </span>
        </mt-form-item>
      </div>
      <input type="password" hidden autocomplete="new-password" />
      <div class="form-item--wrap form-wrapper">
        <mt-form-item prop="phoneNumber" class="form-item form-item-prev" :label="$t('手机号')">
          <mt-input
            v-model="ruleForm.phoneNumber"
            :disabled="false"
            :showClearButton="true"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入手机号')"
          ></mt-input>
          <p class="mobile--error" v-if="hasMobileError">
            {{ $t('您的手机号已注册，请') }}<a @click="login">{{ $t('直接登录') }}</a>
          </p>
        </mt-form-item>
        <mt-form-item prop="telephone" class="form-item form-item-next" :label="$t('固定电话')">
          <mt-input
            v-model="ruleForm.telephone"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入固定电话')"
          ></mt-input>
        </mt-form-item>
        <!-- <mt-form-item
          prop="smsCode"
          class="form-item form-item-next"
          v-if="ruleForm.registerAddressCountryCode === 'CN'"
          :label="$t('手机验证码')"
        >
          <mt-input
            v-model="ruleForm.smsCode"
            type="text"
            cssClass="e-outline"
            :placeholder="$t('请输入手机验证码')"
          ></mt-input>
          <span class="form-button">
            <mt-button
              cssClass="verifica--button"
              :disabled="smsCodeCountDown >= 0"
              @click="getSmsCode"
            >
              {{
                smsCodeCountDown >= 0
                  ? `${smsCodeCountDown}s`
                  : $t('获取验证码')
              }}
            </mt-button>
          </span>
        </mt-form-item> -->
      </div>
      <div class="form-item--wrap form-wrapper">
        <mt-form-item prop="password" class="form-item form-item-prev" :label="$t('密码')">
          <div class="password-wrapper">
            <mt-input
              v-model="ruleForm.password"
              :type="passwordInputType"
              :placeholder="$t('请输入密码')"
              cssClass="e-outline"
            ></mt-input>
            <svg-icon
              class="icon-style"
              @click="switchPasswordDisplay('passwordInputType')"
              icon-class="eye"
            />
          </div>
        </mt-form-item>
        <mt-form-item
          prop="confirmPassword"
          class="form-item form-item-next"
          :label="$t('确认密码')"
        >
          <div class="password-wrapper">
            <mt-input
              v-model="ruleForm.confirmPassword"
              :type="passwordConfirmInputType"
              :placeholder="$t('请输入密码')"
              cssClass="e-outline"
            ></mt-input>
            <svg-icon
              class="icon-style"
              @click="switchPasswordDisplay('passwordConfirmInputType')"
              icon-class="eye"
            />
          </div>
        </mt-form-item>
      </div>
      <div class="form-item--wrap form-wrapper">
        <mt-form-item prop="lastName" class="form-item form-item-prev" :label="$t('姓')">
          <mt-input
            v-model="ruleForm.lastName"
            type="text"
            :placeholder="$t('请输入姓氏')"
            cssClass="e-outline"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="firstName" class="form-item form-item-next" :label="$t('名')">
          <mt-input
            v-model="ruleForm.firstName"
            type="text"
            :placeholder="$t('请输入名称')"
            cssClass="e-outline"
          ></mt-input>
        </mt-form-item>
      </div>
    </mt-form>
    <div class="operation-wrapper">
      <mt-button cssClass="prev-step operation-btn" @click="handlePrev">{{
        $t('上一步')
      }}</mt-button>
      <mt-button
        cssClass="next-step operation-btn"
        :is-primary="true"
        :disabled="loading || nextStepDisable"
        @click="handleNext"
        >{{ $t('下一步') }}</mt-button
      >
    </div>
    <!-- 提示弹框 -->
    <div class="dialog" v-if="ejsRef">
      <div class="dialogMain">
        <div class="main-hander">{{ $t('信息提示') }}</div>
        <div class="main-text">
          {{
            !nextStepDisable
              ? $t(`您所填写的企业已有供应商账号，注册邮箱为${ruleForm.email},请确认是否重复注册`)
              : $t('请检查社会信用代码是否填写正确')
          }}
        </div>
        <div class="main-button">
          <button @click="cancel">{{ $t('取消') }}</button>
          <button @click="confirm">{{ $t('确认') }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { phoneRegex, pswRegex, emailRegex } from '@/utils/my.validators'
@Component({
  components: {
    tipsDialog: require('../components/tipsDialog.vue').default
  },
  middleware: ['config']
})
export default class Step2 extends Vue {
  @Prop({ type: Number, required: true }) currentStep!: number
  @Watch('currentStep', { immediate: true })
  onWatchCurrentStep() {
    this.ruleForm = {
      registerAddressCountryCode: 'CN'
    }
  }
  enterpriseId!: string
  ruleForm: any = {
    registerAddressCountryCode: 'CN',
    identityCode: null
  }
  rules = {
    // sourceType: [
    //   {
    //     required: true,
    //     message: '请选择货源类型',
    //     trigger: 'blur',
    //   },
    // ],
    phoneNumber: [
      { required: true, message: this.$t('请输入手机号'), trigger: 'blur' },
      { required: true, validator: this.validateMobile, trigger: 'blur' }
    ],
    telephone: [{ required: false, message: this.$t('请输入固定电话'), trigger: 'blur' }],
    // smsCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
    // firstName: [{ required: true, message: '请输入姓氏', trigger: 'blur' }],
    // lastName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    identityCode: [
      {
        required: true,
        message: this.$t('请输入统一社会信用代码'),
        trigger: 'blur'
      },
      { required: true, validator: this.validateIdentityCode, trigger: 'blur' }
    ],
    registerAddressCountryCode: [
      {
        required: true,
        message: this.$t('请选择注册国家/地区'),
        trigger: 'blur'
      }
    ],
    supplierTypeCode: [
      {
        required: true,
        message: this.$t('请选择供应商类型'),
        trigger: 'blur'
      }
    ],
    businessTypeCode: [
      {
        required: true,
        message: this.$t('请选择业务类型'),
        trigger: 'blur'
      }
    ],
    email: [{ required: true, validator: this.validateMail, trigger: 'blur' }],
    enterpriseName: [{ required: true, message: this.$t('请输入企业名称'), trigger: 'blur' }],
    enterpriseEnglishName: [
      { required: true, message: this.$t('请输入企业英文名称'), trigger: 'blur' },
      // {
      //   max: 70,
      //   message: this.$t(`企业英文名称长度不能超过70个字符`),
      //   trigger: 'blur'
      // },
      { required: true, validator: this.validateEnglishName, trigger: 'blur' }
    ],
    mailCode: [{ required: true, message: this.$t('请输入注册码进行认证'), trigger: 'blur' }],
    password: [{ required: true, validator: this.validatePwd, trigger: 'blur' }],
    confirmPassword: [{ required: true, validator: this.validateConfirmPwd, trigger: 'blur' }],
    firstName: [{ required: true, message: this.$t('请输入名称'), trigger: 'blur' }],
    lastName: [{ required: true, message: this.$t('请输入姓氏'), trigger: 'blur' }]
  }
  ejsRef = false
  registerEmil = null
  className = 'modalOpen'
  smsCodeCountDown = -1
  mailCodeCountDown = -1
  smsCodeTimer!: NodeJS.Timeout
  mailCodeTimer!: NodeJS.Timeout
  passwordInputType: string = 'password'
  passwordConfirmInputType: string = 'password'
  supplierTypeList: object[] = []
  bussinessTypeList: object[] = []
  bussinessTypeData: object[] = []
  // sourceList: object[] = [
  //   {
  //     value: 'GENERAL PROCUREMENT',
  //     text: '通采',
  //   },
  //   {
  //     value: 'NON-PRODUCTIVE PROCUREMENT',
  //     text: '非采',
  //   },
  // ]
  countryList: object[] = [
    {
      value: 'CN',
      text: this.$t('中国大陆')
    },
    {
      value: 'US',
      text: '中国大陆外（含港澳台）'
    }
  ]
  hasMobileError = false // 是否显示手机号已注册的错误
  loading: Boolean = false
  nextStepDisable: Boolean = false
  firstRegister: Boolean = true
  validForm() {
    return new Promise((resolve, reject) => {
      ;(this.$refs.ruleForm as any).validate((valid: any) => {
        if (valid) {
          this.loading = true
          this.checkIdentityCode()
            .then(() => {
              this.handleRegister()
                .then((response) => {
                  resolve(response)
                })
                .catch((err) => reject(err))
                .finally(() => {
                  this.loading = false
                })
            })
            .catch(() => {
              this.loading = false
            })
        } else {
          reject(this.ruleForm)
        }
      })
    })
  }

  validateIdentityCode(rule: any, value: any, callback: any) {
    if (this.ruleForm.registerAddressCountryCode === 'CN') {
      let codeRegex = /^[0-9A-Z]{18}$/
      if (!value) {
        callback(new Error(this.$t('请输入统一社会信用代码')))
      } else if (!codeRegex.test(value)) {
        callback(new Error(this.$t('统一社会信用代码格式不正确')))
      } else {
        callback()
      }
    } else {
      callback()
    }
  }

  validateMail(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('请输入邮箱')))
    } else if (!emailRegex.test(value)) {
      callback(new Error(this.$t('邮箱地址格式不正确')))
    } else {
      callback()
    }
  }

  validateMobile(rule: any, value: any, callback: any) {
    this.hasMobileError = false
    if (this.ruleForm.registerAddressCountryCode === 'CN') {
      if (!value) {
        callback(new Error(this.$t('请输入手机号')))
      } else if (!phoneRegex.test(value)) {
        callback(new Error(this.$t('手机号格式不正确')))
      } else {
        callback()
      }
    } else {
      callback()
    }
  }

  validateEnglishName(rule: any, value: any, callback: any) {
    // const EnglishNameRegex = /^[a-zA-Z,.\s]+$/
    const EnglishNameRegex = /^[a-zA-Z0-9,.\s\-_@!#$%^&*()+={}\[\]|\\:;"'<>.?/`~]+$/ // eslint-disable-line
    if (!value) {
      callback(new Error(this.$t('请输入企业英文名称')))
    } else if (!EnglishNameRegex.test(value)) {
      callback(new Error(this.$t('企业英文名称格式不正确')))
    } else {
      callback()
    }
  }

  validatePwd(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('请输入密码')))
    } else if (!pswRegex.test(value)) {
      callback(new Error(this.$t('需包含字母、数字、特殊符号且长度不少于6位')))
    } else {
      callback()
    }
  }

  validateConfirmPwd(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error(this.$t('请再次输入密码')))
    } else if (value !== this.ruleForm.password) {
      callback(new Error(this.$t('两次密码输入内容不一致')))
    } else {
      callback()
    }
  }

  // 检查社会统一信用码和业务类型是否重复，仅中国供应商
  checkIdentityCodeAndBusinessTypeCode() {
    if (this.ruleForm.registerAddressCountryCode !== 'CN') return
    let params = {
      identityCode: this.ruleForm.identityCode.replace(/\s+/g, ''),
      businessTypeCode: this.ruleForm.businessTypeCode
    }
    if (!params.identityCode || !params.businessTypeCode) return
    this.$api.register.checkIdentityCodeAndBusinessTypeCodeApi(params).then((res: any) => {
      if (res.code == 200 && res.data) {
        this.fixed()
      }
    })
  }

  supplierTypeChange(e: any) {
    if (e) {
      this.ruleForm.supplierTypeCode = e?.itemData.itemCode
      this.ruleForm.supplierTypeName = e?.itemData.itemName
      let selectItem: any = this.bussinessTypeData.find(
        (v: any) => v.itemCode === e?.itemData.itemCode
      )
      this.bussinessTypeList = selectItem?.children
    }
  }
  businessTypeChange(e: any) {
    if (e) {
      this.ruleForm.businessTypeCode = e?.itemData.itemCode
      this.ruleForm.businessTypeName = e?.itemData.itemName
      this.checkIdentityCodeAndBusinessTypeCode()
    }
  }

  getSupplierTypeList() {
    this.$api.register
      .queryDict({
        dictCode: 'supplierType'
      })
      .then((res: any) => {
        this.supplierTypeList = res.data
      })
  }
  getBussinessTypeList() {
    this.$api.register
      .queryDict({
        dictCode: 'supplierBussinessRelation'
      })
      .then((res: any) => {
        this.bussinessTypeData = res.data
      })
  }

  // 注册国家change
  onCountryChange(arg: any) {
    this.$set(this.ruleForm, 'enterpriseName', null)
    this.$set(this.ruleForm, 'enterpriseEnglishName', null)
    this.$set(this.ruleForm, 'email', null)
    this.$set(this.rules.identityCode[0], 'required', arg.value === 'CN')
    this.$set(this.rules.identityCode[1], 'required', arg.value === 'CN')
    this.$set(this.rules.phoneNumber[0], 'required', arg.value === 'CN')
    this.$set(this.rules.phoneNumber[1], 'required', arg.value === 'CN')
    this.$set(this.rules.telephone[0], 'required', arg.value !== 'CN')
    if (arg.value == 'CN') {
      this.firstRegister = true
      if (this.ruleForm.identityCode) {
        this.getEnterpriseNameByIdentityCode()
      }
    }
    if (arg.value == 'US') {
      this.firstRegister = false
      this.nextStepDisable = false
      if (this.ruleForm.enterpriseName) {
        this.$api.register.checkNameRepeat(this.ruleForm.enterpriseName).then((res: any) => {
          if (res.code == 200 && res.data) {
            this.ruleForm.email = res.data
            this.fixed()
          }
        })
      }
    }
  }

  async getSmsCode() {
    const phoneNumber = this.ruleForm.phoneNumber
    if (!phoneNumber || !phoneRegex.test(phoneNumber) || this.smsCodeCountDown >= 0) return

    this.hasMobileError = false
    this.$api.register
      .getVerifyCode({
        mobile: phoneNumber
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.smsCodeCountDown = 60
          this.smsCodeTimer = setInterval(() => {
            if (this.smsCodeCountDown > 0) {
              this.smsCodeCountDown--
            } else {
              this.smsCodeCountDown = -1
              clearInterval(this.smsCodeTimer)
            }
          }, 1000)
        } else if (res.code === 602) {
          this.hasMobileError = true
          // (this.$refs.ruleForm as any).addErrorLabels([
          //   {label: "phoneNumber",message: "手机号不能为空"}
          // ])
        }
      })
  }

  switchPasswordDisplay(field: string) {
    this.$set(this, field, this[field] === 'password' ? 'text' : 'password')
  }

  async getMailCode(emailParams: any = { randstr: '', ticket: '' }) {
    const email = this.ruleForm.email
    if (!email || !emailRegex.test(email) || this.mailCodeCountDown >= 0) return

    this.$api.register
      .getMailSmsCode({
        mail: email,
        rand: emailParams.randstr,
        ticket: emailParams.ticket
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.mailCodeCountDown = 60
          this.mailCodeTimer = setInterval(() => {
            if (this.mailCodeCountDown > 0) {
              this.mailCodeCountDown--
            } else {
              this.mailCodeCountDown = -1
              clearInterval(this.mailCodeTimer)
            }
          }, 1000)
        } else if (res.code === 601) {
          this.$toast({
            content: this.$t('网络异常，请稍后重试！'),
            type: 'warning'
          })
        }
      })
  }

  // 上一步
  handlePrev() {
    this.$emit('prev')
  }

  // 下一步
  handleNext() {
    this.validForm().then(() => {
      this.$emit('next')
    })
  }
  identityCodeChange(e: any) {
    if (!e) return
    if (this.ruleForm.registerAddressCountryCode !== 'CN') return
    this.ruleForm.identityCode = e?.replace(/\s+/g, '')
    this.getEnterpriseNameByIdentityCode()
  }
  getEnterpriseNameByIdentityCode() {
    let params = {
      identityCode: this.ruleForm.identityCode
    }
    let codeRegex = /^[0-9A-Z]{18}$/
    if (!codeRegex.test(params.identityCode)) {
      return
    }
    this.$api.register.getEnterpriseNameByIdentityCodeApi(params).then((res: any) => {
      if (res.code == 200) {
        if (res.data) {
          this.nextStepDisable = false
          this.$set(this.ruleForm, 'enterpriseName', res.data.enterpriseName)
          this.$set(this.ruleForm, 'enterpriseEnglishName', res.data.enterpriseEnglishName)
          this.checkIdentityCodeRepeat()
        } else {
          this.nextStepDisable = true
          this.firstRegister = true
          this.$set(this.ruleForm, 'enterpriseName', null)
          this.$set(this.ruleForm, 'enterpriseEnglishName', null)
          this.$set(this.ruleForm, 'email', null)
          this.fixed()
        }
      }
    })
  }
  //查社会统一信用码
  checkIdentityCodeRepeat() {
    if (this.ruleForm.registerAddressCountryCode !== 'CN') return
    this.$api.register.checkIdentityCodeRepeat(this.ruleForm.identityCode).then((res: any) => {
      if (res.code == 200 && res.data) {
        this.ruleForm.email = res.data
        this.firstRegister = false
        this.fixed()
      }
    })
  }
  //查企业名称
  changeEnterpriseName(e: any) {
    if (!e) return
    if (this.ruleForm.registerAddressCountryCode !== 'US') return
    this.$api.register.checkNameRepeat(this.ruleForm.enterpriseName).then((res: any) => {
      if (res.code == 200 && res.data) {
        this.ruleForm.email = res.data
        this.fixed()
      }
    })
  }
  // 查询社会统一信用代码注册次数
  checkIdentityCode() {
    return new Promise((resolve, reject) => {
      this.$api.register.checkIdentityCode(this.ruleForm.identityCode).then((res: any) => {
        if (res.code === 200) {
          resolve(res)
        } else {
          if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
            ;(this.$refs.ruleForm as any).addErrorLabels(res.data.errorLabels)
          }
          reject(res)
        }
      })
    })
  }
  //弹窗 --取消
  cancel() {
    this.fixed_cancel()
    this.ruleForm.email = null
    if (this.ruleForm.registerAddressCountryCode == 'US') {
      this.ruleForm.enterpriseName = null
    }
    if (this.ruleForm.registerAddressCountryCode == 'CN') {
      this.ruleForm.identityCode = null
    }
  }
  //弹窗 --确认
  confirm() {
    this.fixed_cancel()
  }
  //禁止body滚动
  fixed() {
    this.ejsRef = true
    document.body.classList.add(this.className)
    document.body.style.height = '100%'
    document.body.style.overflow = 'hidden'
  }
  //同意body滚动
  fixed_cancel() {
    this.ejsRef = false
    document.body.classList.remove(this.className)
    document.body.removeAttribute('style')
  }
  // 注册提交
  handleRegister() {
    return new Promise((resolve, reject) => {
      const params = {
        status: 0,
        ...this.ruleForm
      }
      if (this.enterpriseId) {
        params.enterpriseId = this.enterpriseId
      }
      if (this.$route.query?.registerInvalidKey) {
        params.registerInvalidKey = this.$route.query.registerInvalidKey
      }
      this.$api.register
        .register(params)
        .then((res: any) => {
          if (res.code === 200) {
            resolve({
              ...this.ruleForm,
              userId: res.data?.userId || ''
            })
          } else {
            if (res.data && res.data.errorLabels && res.data.errorLabels.length) {
              ;(this.$refs.ruleForm as any).addErrorLabels(res.data.errorLabels)
            }
            reject(res)
          }
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  }

  mounted() {
    // this.getSupplierTypeList()
    // this.getBussinessTypeList()
  }
}
</script>

<style lang="less" scoped>
.mt-form--wrap {
  /deep/ .label {
    line-height: 30px;
    font-weight: normal;
    color: #666;
  }
  .form-button {
    position: absolute;
    right: 16px;
    top: 38px;
    /deep/ .mt-button {
      &.is-disabled {
        .verifica--button {
          color: #ccc;
        }
      }
      .verifica--button {
        box-shadow: none;
        padding: 0;
        font-size: 16px;
        color: #2783fe;
        background-color: transparent;
        &:hover {
          background-color: transparent;
        }
      }
    }
  }
  .password-wrapper {
    .icon-style {
      position: absolute;
      right: 20px;
      top: 45px;
      cursor: pointer;
    }
  }
}
.form-wrapper {
  display: flex;
  .form-item-prev {
    flex: 1;
  }
  .form-item-next {
    flex: 1;
    margin-left: 20px;
  }
}
.operation-wrapper {
  display: flex;
  justify-content: space-around;
  margin-top: 40px;
  /deep/.mt-button {
    &.is-disabled {
      .operation-btn {
        border: 1px solid #ccc;
        &.e-primary {
          background-color: #ccc;
        }
      }
    }
    .operation-btn {
      width: 240px;
      height: 48px;
      border-radius: 8px;
      border: 1px solid #4e5a70;
      color: #4e5a70;
      font-size: 20px;
      &.e-primary {
        background-color: #4e5a70;
        color: #fff;
      }
    }
  }
}
.dialog {
  z-index: 1001;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  .dialogMain {
    width: 500px;
    height: 300px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main-hander {
      width: 100%;
      height: 70px;
      padding: 20px;
      background-color: #31374e;
      text-align: center;
      color: #fff;
    }
    .main-text {
      width: 100%;
      padding: 20px;
      flex: 1;
    }
    .main-button {
      width: 100%;
      height: 44px;
      padding: 8px;
      display: flex;
      justify-content: right;
      button {
        border: none;
        outline: none;
        background-color: #fff;
        margin: 0 5px;
        padding: 0 5px;
      }
      button:hover {
        background-color: #f6f6f6;
        border-radius: 2px;
      }
    }
  }
}
.modalOpen {
  height: 100% !important;
  overflow: hidden !important;
}
</style>
