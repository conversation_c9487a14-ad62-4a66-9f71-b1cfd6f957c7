<template>
  <div class="step-wrapper">
    <p class="tips">
      {{ $t('请在注册前认真阅读协议，廉洁是与TCL合作的第一原则，让我们共同维护阳光的合作环境！') }}
    </p>
    <div class="guide-file">
      <span>{{ $t('供应商注册指引附件') }}：</span>
      <!-- <a :href="baseUrl + '/docs/Attachment1.doc'" target="_blank"
          >{{ $t('附件') }}1：{{ $t('供应商注册指引附件') }}</a
        > -->
      <a :href="https + ' 6+1 I-TSRM项目-供应商操作手册.pptx'" target="_blank'"
        >{{ $t('附件') }}1：{{ $t('供应商注册指引附件') }}</a
      >
    </div>
    <dl>
      <dt>
        <p>{{ $t('《TCL实业供应商协同平台服务条款及声明》') }}</p>
        <span>{{ $t('附件') }}</span>
      </dt>
      <dd>
        <p>{{ $t('中文版') }}</p>
        <div class="download-wrapper">
          <a :href="baseUrl + '/docs/Chinese.pdf'" target="_blank">{{ $t('下载附件') }}</a>
        </div>
      </dd>
      <dd>
        <p>{{ $t('英文版') }}</p>
        <div class="download-wrapper">
          <a :href="baseUrl + '/docs/English.pdf'" target="_blank">{{ $t('下载附件') }}</a>
        </div>
      </dd>
      <dd>
        <p>{{ $t('越南语版') }}</p>
        <div class="download-wrapper">
          <a :href="baseUrl + '/docs/Vietnamese.pdf'" target="_blank">{{ $t('下载附件') }}</a>
        </div>
      </dd>
      <dd>
        <p>{{ $t('西班牙语版') }}</p>
        <div class="download-wrapper">
          <a :href="baseUrl + '/docs/Spanish.pdf'" target="_blank">{{ $t('下载附件') }}</a>
        </div>
      </dd>
      <dd>
        <p>{{ $t('葡萄牙语版') }}</p>
        <div class="download-wrapper">
          <a :href="baseUrl + '/docs/Portuguese.pdf'" target="_blank">{{ $t('下载附件') }}</a>
        </div>
      </dd>
    </dl>
    <div class="agreement">
      <!-- <div class="guide-file">
        <span>{{ $t('供应商注册指引附件') }}：</span>
        <a :href="baseUrl + '/docs/Attachment1.doc'" target="_blank"
          >{{ $t('附件') }}1：{{ $t('供应商注册指引附件') }}</a
        >
      </div> -->
      <div class="thirdPartyLink">
        <span>{{ $t('请选择语言') }}：</span>
        <mt-select
          :width="100"
          :popupWidth="100"
          cssClass="lang-select e-outline"
          v-model="lang"
          :dataSource="languages"
          @change="languagesSelect"
        ></mt-select>
        <a @click="isViewed = true" :href="jumpUrl" target="_blank">{{ language }}</a>
      </div>
      <div class="clause">
        <mt-checkbox
          :key="checkboxKey"
          :checked="isChecked"
          ref="checkbox"
          @change="onChange"
        ></mt-checkbox>
        <span>{{ $t('我已阅读并同意《TCL实业供应商协同平台服务条款及声明》') }}</span>
      </div>
    </div>
    <div class="operation-wrapper">
      <mt-button cssClass="go-back-btn operation-btn" @click="onGoBack">{{ $t('返回') }}</mt-button>
      <mt-button cssClass="next-step operation-btn" :is-primary="true" @click="handleNext">{{
        $t('下一步')
      }}</mt-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({
  layout: 'blank'
})
export default class Step1 extends Vue {
  https: string = 'https://cloudoss1.tcl.com/PAASPRD:a4987661-srm-public/manual/SDBG'
  language: string = this.$t('我们将根据《隐私声明》来处理您的信息')
  isChecked: boolean = false
  isViewed: boolean = false
  lang: string = 'zh'
  languages: object = [
    { text: '中文', value: 'zh' },
    { text: 'English', value: 'en' },
    { text: 'VietNam', value: 'vi' },
    { text: 'Español', value: 'es' },
    { text: 'Português', value: 'pt' }
  ]
  //   中文/English/VietNam/Español/Português；根据选择的语言打开对应的申明链接
  // 中文：https://www.tcl.com/cn/zh/legal/privacy-policy
  // 英文（English）：https://www.tcl.com/global/en/privacy-notice.html
  // 越南（VietNam）：https://www.tcl.com/vn/vi/legal/privacy-notice
  // 西班牙（Español）：https://www.tcl.com/mx/es/legal/privacy-notice
  // 葡萄牙（Português）：https://www.tcl.com/br/pt/legal/aviso-de-privacidade-global-da-tcl
  checkboxKey: string = '1'
  baseUrl: string = '/login'

  get jumpUrl() {
    let url: string = ''
    switch (this.lang) {
      case 'zh':
        url = 'https://www.tcl.com/cn/zh/legal/privacy-policy'
        break
      case 'en':
        url = 'https://www.tcl.com/global/en/privacy-notice.html'
        break
      case 'vi':
        url = 'https://www.tcl.com/vn/vi/legal/privacy-notice'
        break
      case 'es':
        url = 'https://www.tcl.com/mx/es/legal/privacy-notice'
        break
      case 'pt':
        url = 'https://www.tcl.com/br/pt/legal/aviso-de-privacidade-global-da-tcl'
        break
      default:
        url = 'https://www.tcl.com/cn/zh/legal/privacy-policy'
    }
    return url
  }

  onChange(e: any) {
    if (this.isViewed) {
      this.isChecked = e.checked
    } else {
      this.checkboxKey = -this.checkboxKey + 1 + ''
      this.$tips.warning({
        title: this.$t('提示'),
        content: this.$t('请先点击上方链接阅读TCL隐私声明')
      })
    }
  }

  onGoBack() {
    this.$router.back()
  }

  handleNext() {
    if (!this.isChecked) {
      return this.$tips.warning({
        title: this.$t('提示'),
        content: this.$t('请同意《TCL实业供应商协同平台服务条款及声明》')
      })
    }
    if (!this.isViewed) {
      return this.$tips.warning({
        title: this.$t('提示'),
        content: this.$t('请先点击上方链接阅读TCL隐私声明')
      })
    }
    this.$emit('next')
  }
  languagesSelect(e: any) {
    console.log(e.value)

    switch (e.value) {
      case 'zh':
        this.language = '我们将根据《隐私声明》来处理您的信息'
        break
      case 'en':
        this.language = 'We will process your information in accordance with our Privacy Notice'
        break
      case 'vi':
        this.language = 'We will process your information in accordance with our Privacy Notice'
        break
      case 'es':
        this.language = 'We will process your information in accordance with our Privacy Notice'
        break
      case 'pt':
        this.language = 'We will process your information in accordance with our Privacy Notice'
        break
      default:
        this.language = 'We will process your information in accordance with our Privacy Notice'
        break
    }
  }
}
</script>

<style lang="less" scoped>
.step-wrapper {
  .guide-file {
    line-height: 52px;
    color: #333;
    font-size: 14px;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
}
a {
  color: #3979f9;
  cursor: pointer;
  user-select: none;
}
.tips {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
}
dl {
  dt {
    background-color: #f5f6f7;
    display: flex;
    padding-left: 20px;
    color: #333;
    line-height: 36px;
    p {
      flex: 1;
    }
    span {
      width: 145px;
    }
  }
  dd {
    padding-left: 20px;
    display: flex;
    line-height: 52px;
    font-size: 14px;
    &:nth-child(odd) {
      background-color: #f5f6f7;
    }
    p {
      flex: 1;
    }
    .download-wrapper {
      width: 145px;
      word-break: break-all;
    }
    &:last-child {
      border-bottom: 1px solid #eff0f2;
    }
  }
}
.agreement {
  text-align: center;
  font-size: 14px;
  // .guide-file {
  //   line-height: 52px;
  //   color: #333;
  //   background-color: red;

  // }
  .clause {
    text-align: center;
    color: #666;
  }
}
.thirdPartyLink {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  /deep/ .lang-select {
    color: #999;
    font-size: 16px;
    background-color: #fff;
    height: 32px;
    font-size: 14px;
    line-height: 32px;
    margin: 0;
    input {
      min-height: 32px !important;
    }
    .e-float-line {
      display: none;
    }
    .e-input-group-icon {
      font-size: 12px;
      color: #999;
      margin-top: 3px;
      margin-bottom: 3px;
    }
  }
  a {
    margin-left: 20px;
  }
}
.operation-wrapper {
  display: flex;
  justify-content: space-around;
  margin-top: 40px;
  /deep/.operation-btn {
    width: 240px;
    height: 48px;
    border-radius: 8px;
    border: 1px solid #4e5a70;
    color: #4e5a70;
    font-size: 20px;
    &.e-primary {
      background-color: #4e5a70;
      color: #fff;
    }
  }
}
</style>
