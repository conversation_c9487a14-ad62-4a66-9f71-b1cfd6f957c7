<template>
  <div class="step-wrapper">
    <div class="success-icon"></div>
    <div class="tips">{{ $t('恭喜你完成 TCL-SRM 注册流程') }}</div>
    <div class="tips">{{ $t('审批完成会以邮件方式通知') }}</div>
    <div class="operation">
      <mt-button :is-primary="true" @click="goHomePage">{{ $t('返回首页') }}</mt-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
@Component({
  layout: 'blank'
})
export default class Step3 extends Vue {
  goHomePage() {
    this.$router.push('/')
  }
}
</script>

<style lang="less" scoped>
.success-icon {
  width: 80px;
  height: 80px;
  background-color: #3ebf17;
  border-radius: 50%;
  position: relative;
  margin: 0 auto;
  margin-top: 90px;
  margin-bottom: 50px;
  &::before {
    content: '';
    width: 40px;
    height: 20px;
    border-left: 5px solid #fff;
    border-bottom: 5px solid #fff;
    position: absolute;
    left: 21px;
    top: 27px;
    margin: auto;
    transform: rotate(-45deg);
  }
}
.tips {
  color: #333;
  font-size: 30px;
  line-height: 42px;
  margin-bottom: 16px;
  text-align: center;
}
.step-wrapper {
  /deep/ .e-primary {
    background-color: #4e5a70;
    line-height: 48px;
    width: 240px;
    border-radius: 8px;
    display: block;
    color: #fff;
    font-size: 20px;
  }
}
.operation {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}
</style>
