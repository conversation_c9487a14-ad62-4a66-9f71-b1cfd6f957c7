<template>
  <div class="detail">
    <div class="tabs tabs--height">
      <div class="tab-wrap">
        <ul class="tab-container">
          <li
            v-for="(item, key) in tabList"
            :key="key"
            class="tab-item tab-item--line"
            :class="{ active: key === tabIndex }"
            @click="handleSelectTab(key)"
          >
            {{ item.title }}
          </li>
        </ul>
      </div>
    </div>
    <sc-table
      ref="sctableRef"
      :loading="loading"
      :columns="columns"
      :table-data="currentDetailTableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      align="center"
      keep-source
      :sortable="false"
      :is-show-right-btn="false"
      :is-show-refresh-bth="false"
      @edit-closed="editComplete"
    >
    </sc-table>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  props: {
    detailData: {
      type: Array,
      default: () => {
        return []
      }
    },
    canEdit: {
      type: Boolean,
      default: false
    }
  },
  components: { ScTable },
  data() {
    return {
      tabList: [],
      tabIndex: 0,
      tabName: null,

      loading: false,
      editRules: {
        approvalOperate: [{ required: true, message: this.$t('必填') }]
      },

      currentDetailTableData: [], // 当前显示明细
      detailTableData: {}, // 所有模块明细

      operateOptions: [
        { label: this.$t('通过加分'), value: '1' },
        { label: this.$t('通过不加分'), value: '2' },
        { label: this.$t('驳回'), value: '3' }
      ]
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    columns() {
      let columns = [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          type: 'seq',
          title: this.$t('序号'),
          width: 50,
          fixed: 'left',
          align: 'center'
        },
        {
          field: 'examineType',
          title: this.$t('考核类型')
        },
        {
          field: 'scoreContent',
          title: this.$t('评分内容')
        },
        {
          field: 'problemLevel',
          title: this.$t('问题等级')
        },
        {
          field: 'standardScore',
          title: this.$t('标分')
        },
        {
          field: 'score',
          title: this.$t('得分')
        },
        {
          field: 'explain',
          title: this.$t('稽查发现/得分说明')
        },
        {
          field: 'improveIdea',
          title: this.$t('改善对策')
        },
        {
          field: 'responsiblePerson',
          title: this.$t('责任人')
        },
        {
          field: 'fileId',
          title: this.$t('改善佐证')
        },
        {
          field: 'finishTime',
          title: this.$t('预计完成时间'),
          formatter: ({ cellValue }) => {
            let text = ''
            if (cellValue && cellValue !== '0') {
              // text = dayjs(Number(cellValue)).format('YYYY-MM-DD')
            }
            return text
          }
        },
        {
          field: 'approvalOperate',
          title: this.$t('操作'),
          editRender: {},
          slots: {
            edit: ({ row }) => {
              return [
                <vxe-select
                  v-model={row.approvalOperate}
                  placeholder={this.$t('请选择')}
                  options={this.operateOptions}
                  transfer
                  clearable
                />
              ]
            }
          },
          formatter: ({ cellValue }) => {
            let item = this.operateOptions.find((item) => item.value === cellValue)
            return item ? item.label : ''
          }
        }
      ]
      if (!this.canEdit) {
        columns.pop()
      }
      return columns
    },
    editConfig() {
      return {
        enabled: true,
        trigger: 'click',
        mode: 'row',
        showStatus: true
      }
    }
  },
  watch: {
    detailData: {
      handler(value) {
        this.tabList = value.map((item) => {
          return {
            title: item.module
          }
        })
        this.tabList.forEach((item) => {
          this.detailTableData[item.title] = value.find((v) => v.module === item.title)?.detailList
        })
        this.tabName = this.tabList[this.tabIndex]?.title
        let currentDetailTableData = this.detailTableData[this.tabName] || []
        this.currentDetailTableData = currentDetailTableData
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSelectTab(e) {
      this.tabIndex = e
      this.tabName = this.tabList[e]?.title
      let currentDetailTableData = this.detailTableData[this.tabName] || []
      this.currentDetailTableData = currentDetailTableData
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        // 远程数据才有$event属性
        //1、 校验必填
        this.tableRef.validate([row]).then((valid) => {
          if (valid) {
            this.$toast({ content: this.$t('请完成必填项后进行提交操作'), type: 'warning' })
            return
          }

          let currentDetailTableData = this.tableRef.getTableData().visibleData
          let tabName = this.tabList[this.tabIndex].title
          this.detailTableData[tabName] = currentDetailTableData

          this.$emit('save', this.detailTableData)
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.detail {
  .tabs {
    box-sizing: border-box;
    border: none;
    border-radius: 4px 4px 0 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    .tabs--height {
      height: 30px;
    }
    .tab-wrap {
      overflow: hidden;
      display: flex;
      ul.tab-container {
        display: flex;
        align-items: center;
        list-style: none;
        margin: 0;
        padding: 0;
        border: 0;
        li.tab-item {
          display: flex;
          align-items: center;
          white-space: nowrap;
          position: relative;
          font-size: 14px;
          border: none;
          margin: 0 10px;
          padding: 8px 25px;
          height: 28px;
          color: #9e9e9e;
          cursor: pointer;
          box-sizing: border-box;
          border-bottom: 3px solid transparent;
          &:first-child {
            margin-left: 0;
          }
        }
        .tab-item--line {
          &:hover {
            color: #31374e;
          }
        }
        .tab-item--line.active {
          color: #31374e;
          border-bottom: 2px solid #f55448;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
