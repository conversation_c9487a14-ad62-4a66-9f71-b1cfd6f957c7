<template>
  <div>
    <table class="summary-table">
      <thead>
        <tr>
          <th colspan="8" class="title">{{ $t('供方系统年度审厂汇总表') }}</th>
        </tr>
      </thead>
      <thead>
        <tr>
          <th colspan="8" class="subTitle">{{ $t('一、供应商审核基本资料简述') }}</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th colspan="2">{{ $t('供方名称') }}</th>
          <td colspan="2">{{ formData.supplierName }}</td>
          <th colspan="2">{{ $t('供方法人代表') }}</th>
          <td colspan="2">{{ formData.corporationName }}</td>
        </tr>
        <tr>
          <th colspan="2">{{ $t('审核地点') }}</th>
          <td colspan="2">{{ formData.address }}</td>
          <th colspan="2">{{ $t('联系人/邮箱') }}</th>
          <td colspan="2">{{ formData.contactName }}/{{ formData.email }}</td>
        </tr>
        <tr>
          <th colspan="2">{{ $t('审核方式') }}</th>
          <td colspan="2">{{ formData.examineMode }}</td>
          <th colspan="2">{{ $t('物料类别') }}</th>
          <td colspan="2">{{ formData.itemType }}</td>
        </tr>
        <tr>
          <th colspan="2">{{ $t('检查小组成员') }}</th>
          <td colspan="2">{{ formData.examineGroup }}</td>
          <th colspan="2">{{ $t('检查时间') }}</th>
          <td colspan="2">{{ formData.examineTime }}</td>
        </tr>
      </tbody>
      <thead>
        <tr>
          <th colspan="8" class="subTitle">{{ $t('二、评审评分') }}</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>{{ $t('序号') }}</th>
          <th>{{ $t('评价维度') }}</th>
          <th>{{ $t('合规率') }}</th>
          <th>{{ $t('换算分数') }}</th>
          <th colspan="4">{{ $t('合规率条状图') }}</th>
        </tr>
        <tr v-for="(item, key) in formData.examineModuleList" :key="key">
          <td>{{ key + 1 }}</td>
          <td>{{ item.module }}-{{ item.scoreValue }}{{ $t('分') }}</td>
          <td>{{ item.complianceRate }}</td>
          <td>{{ item.convertScore }}</td>
          <td v-if="item === 1" colspan="4" rowspan="4">test</td>
        </tr>
        <tr>
          <th colspan="2">{{ $t('综合评分(总分100分)') }}</th>
          <td>/</td>
          <td>{{ formData.comprehensiveScore }}</td>
          <th>{{ $t('CRI缺陷') }}</th>
          <td>{{ formData.crDefectFlag ? $t('是') : $t('否') }}</td>
          <td colspan="2">{{ $t('注: 如有CRI项,总分打八折') }}</td>
        </tr>
        <tr>
          <th colspan="2">{{ $t('评审结果') }}</th>
          <td colspan="2">{{ formData.examineResult }}</td>
          <th colspan="2">{{ $t('备注') }}</th>
          <td colspan="2">{{ formData.remark }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style lang="less" scoped>
.summary-table {
  width: 100%;
  text-align: center;
  .title {
    font-size: 20px;
    font-weight: 600;
  }
  .subTitle {
    font-size: 16px;
    font-weight: 600;
  }
  th {
    font-weight: 600;
  }
  th,
  td {
    border: 1px solid #e3e3e3;
    height: 40px;
    line-height: 40px;
  }
}
</style>
