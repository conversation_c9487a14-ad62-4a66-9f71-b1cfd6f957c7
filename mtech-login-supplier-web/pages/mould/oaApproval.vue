<template>
  <div class="factory-audit-score">
    <div>
      <div class="tabs tabs--height">
        <div class="tab-wrap">
          <ul class="tab-container">
            <li
              v-for="(item, key) in tabList"
              :key="key"
              class="tab-item tab-item--line"
              :class="{ active: key === tabIndex }"
              @click="handleSelectTab(key)"
            >
              {{ item.title }}
            </li>
          </ul>
        </div>
      </div>
      <div v-if="tabIndex === 0">
        <Summary v-if="summaryFormData" :form-data="summaryFormData" />
      </div>
      <div v-if="tabIndex === 1">
        <ImprovementSummary :summary-data="summaryData" />
      </div>
      <div v-if="tabIndex === 2">
        <ImprovementDetail :detail-data="detailData" />
      </div>
    </div>
  </div>
</template>

<script>
import Summary from './components/Summary.vue'
import ImprovementSummary from './components/ImprovementSummary.vue'
import ImprovementDetail from './components/ImprovementDetail.vue'
export default {
  layout: 'h5',
  components: { Summary, ImprovementSummary, ImprovementDetail },
  data() {
    return {
      tabList: [
        { title: this.$t('汇总') },
        { title: this.$t('待改善要求汇总') },
        { title: this.$t('待改善要求明细') }
      ],
      tabIndex: 0,

      modelForm: {},

      summaryFormData: null,
      summaryData: [],
      detailData: []
    }
  },
  created() {
    if (this.$route.query?.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      let params = {
        id: this.$route.query?.id
      }
      this.$api.mould.queryOaViewApi(params).then((res) => {
        if (res.code === 200) {
          this.modelForm = res.data
          this.summaryFormData = res.data.summary
          this.summaryData = res.data.improveSummary || []
          this.detailData = res.data.improveDetail
        }
      })
    },
    handleSelectTab(e) {
      this.tabIndex = e
    },
    handleCancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
.factory-audit-score {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  .top {
    display: flex;
    justify-content: flex-end;
    position: fixed;
    top: 100px;
    right: 40px;
    z-index: 999;
    .mt-button {
      margin-left: 20px;
    }
  }
  .tabs {
    box-sizing: border-box;
    border: none;
    border-radius: 4px 4px 0 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    .tabs--height {
      height: 30px;
    }
    .tab-wrap {
      overflow: hidden;
      display: flex;
      ul.tab-container {
        display: flex;
        align-items: center;
        list-style: none;
        margin: 0;
        padding: 0;
        border: 0;
        li.tab-item {
          display: flex;
          align-items: center;
          white-space: nowrap;
          position: relative;
          font-size: 14px;
          border: none;
          margin: 0 10px;
          padding: 8px 25px;
          height: 28px;
          color: #9e9e9e;
          cursor: pointer;
          box-sizing: border-box;
          border-bottom: 3px solid transparent;
          &:first-child {
            margin-left: 0;
          }
        }
        .tab-item--line {
          &:hover {
            color: #31374e;
          }
        }
        .tab-item--line.active {
          color: #31374e;
          border-bottom: 2px solid #f55448;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
