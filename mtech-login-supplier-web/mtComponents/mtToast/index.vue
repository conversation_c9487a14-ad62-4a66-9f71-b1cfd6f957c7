<template>
  <div class="mt-toast">
    <mt-toast id="toast_type" ref="toastRef" :position="position" />
  </div>
</template>
<script>
import { Component, Vue } from 'vue-property-decorator'
// import { ToastPlugin } from '@syncfusion/ej2-vue-notifications'
// Vue.use(ToastPlugin)
const typeSetting = {
  info: 'e-toast-info',
  success: 'e-toast-success',
  warning: 'e-toast-warning',
  error: 'e-toast-danger'
}
@Component
export default class MyMtToast extends Vue {
  // data
  position = { X: 'Right' }
  title = ''
  content = ''
  type = 'info'
  duration = 3000

  /** computed start */
  get propsType() {
    let cssType = ''
    if (this.type) {
      cssType = typeSetting[`${this.type}`] || typeSetting.info
    }
    return cssType || typeSetting.info
  }

  get options() {
    let val = {}
    if (this.title) {
      val.title = this.title
    }
    if (this.content) {
      val.content = this.content
    }
    if (this.icon) {
      val.icon = this.icon
    }
    val.cssClass = this.propsType
    val.timeOut = this.duration || 3000
    return val
  }
  /** computed end */

  mounted() {
    setTimeout(() => {
      this.$refs.toastRef.show({ ...this.options })
    }, 200)
    let timmer = this.options.timeOut + 2000
    setTimeout(() => {
      // 通过父级移除子元素的方式来移除该组件实例和 DOM 节点
      this.$destroy(true)
      this.$el.parentNode.removeChild(this.$el)
    }, timmer)
  }
}
</script>
