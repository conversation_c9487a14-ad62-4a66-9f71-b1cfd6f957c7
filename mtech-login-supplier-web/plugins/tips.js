import Vue from 'vue'
// 导入组件
import Toast from '@/mtComponents/mtToast'

// 创建构造函数
const ToastConstructor = Vue.extend(Toast)
function tips(options) {
  // 处理options
  if (typeof options === 'string') {
    options = { content: options }
  }
  // 生成实例
  const instance = new ToastConstructor({ data: options })

  try {
    document.body.appendChild(instance.$mount().$el)
  } catch (error) {
    console.log('哪里报错了？', error)
  }
}
;['success', 'error', 'warning'].forEach((item) => {
  tips[item] = (options) => {
    if (typeof options === 'string') {
      options = { content: options }
    }
    options.type = item
    return tips(options)
  }
})

export default (context, inject) => {
  context
  inject('tips', tips)
}
