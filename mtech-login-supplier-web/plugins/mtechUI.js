/*
 * @Author: your name
 * @Date: 2021-07-26 11:37:04
 * @LastEditTime: 2021-08-03 17:30:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-login-supplier-web\plugins\mtechUI.js
 */
import Vue from 'vue'
import MtButton from '@mtech-ui/button'
import MtInput from '@mtech-ui/input'
import MtForm from '@mtech-ui/form'
import MtFormItem from '@mtech-ui/form-item'
import MtIcon from '@mtech-ui/icon'
import MtToast from '@mtech-ui/toast'
import MtSelect from '@mtech-ui/select'
import MtCheckbox from '@mtech-ui/checkbox'
import MtRow from '@mtech-ui/row'
import MtCol from '@mtech-ui/col'
import MtProgress from '@mtech-ui/progress'
import MtDialog from '@mtech-ui/dialog'
import { NAME, COMPONENT } from '@/components/Dialog/index.js'
// //  引入样式
import '@mtech-ui/base/build/esm/bundle.css' // 引入基础样式
Vue.use(MtButton)
  .use(MtInput)
  .use(MtCheckbox)
  .use(MtForm)
  .use(MtFormItem)
  .use(MtSelect)
  .use(MtIcon)
  .use(MtRow)
  .use(MtCol)
  .use(MtProgress)
  .use(MtToast)
  .use(MtDialog)

Vue.prototype[NAME] = COMPONENT
