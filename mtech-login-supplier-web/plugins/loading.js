import Vue from 'vue'
import Loading from '@/mtComponents/mtLoading'

const LoadingConstructor = Vue.extend(Loading)
function load(options) {
  // 生成实例
  const instance = new LoadingConstructor({ data: options })
  try {
    document.body.appendChild(instance.$mount().$el)
  } catch (error) {}
}

;['show', 'hide'].forEach((item) => {
  load[item] = (options) => {
    if (item === 'show') {
      options = {
        isShow: true
      }
    }
    if (item === 'hide') {
      options = {
        isShow: false
      }
    }
    return load(options)
  }
})

export default (context, inject) => {
  context
  inject('load', load)
}
