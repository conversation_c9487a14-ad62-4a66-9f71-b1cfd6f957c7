export default async function ({ app, store, error }: any) {
  if (store.state && store.state.countryList && store.state.countryList.length <= 1) {
    try {
      // const res = await app.$api.common.getParamList({})
      // if (res.code == 200 && res.data && res.data.length) {
      //   let list: object[] = []
      //   res.data.forEach((item: any) => {
      //     list.push({
      //       text: item.shortName,
      //       value: item.numericCode,
      //       code: item.numericCode,
      //       id: item.id,
      //       // text: item.value,
      //       // value: item.code,
      //       // code: item.code,
      //       // id: item.id,
      //       // length: item.length,
      //       // maxLength: item.maxLength,
      //       // minLength: item.minLength
      //     })
      //   })
      //   store.dispatch('setCountryList', list)
      // }
    } catch (err) {
      console.log('countyr.ts中，进入了500')
      error({ statusCode: 500 })
    }
  }
}
