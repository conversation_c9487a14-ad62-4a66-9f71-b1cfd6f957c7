<template>
  <div class="err-container">
    <!-- <div class="err-header">企汇购 {{ error.statusCode }}</div> -->
    <div class="err-content">
      <div class="error-box">
        <img class="img-404" src="../assets/images/404.png" />
        <span class="status-code">{{ error.statusCode }}</span>
      </div>
      <p class="err-tip">{{ errMsg[`${error.statusCode}`] }}</p>
      <button class="err-btn-back" @click="$router.go(-1)">返回上一页</button>
    </div>
    <div class="err-footer">
      <p>@Copyright 企汇购信息技术（苏州）有限公司</p>
      <p class="footer-beian" @click="openBeian">备案号：沪ICP备 14039535号-6</p>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
const ERR_MSG = {
  '404': '哎呀，页面走失了！',
  '500': '应用发生错误异常'
}
@Component({
  layout: 'blank'
})
export default class Error extends Vue {
  errMsg: object = ERR_MSG
  @Prop({
    type: Object,
    required: true,
    default: () => {
      return {}
    }
  })
  readonly error!: object

  openBeian() {
    window.open('https://beian.miit.gov.cn/', '_blank')
  }
}
</script>
<style lang="less" scoped>
.err-container {
  min-height: 100vh;
  background: #f0f0f1;
  font-family: PingFangSC-Medium;
  display: flex;
  flex-direction: column;
  .err-header {
    width: 100%;
    line-height: 90px;
    background-color: #fff;
    font-size: 32px;
    color: #444344;
    padding-left: 40px;
  }
  .err-content {
    flex: 1;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .error-box {
      width: 434px;
      height: 400px;
      position: relative;
      .img-404 {
        width: 100%;
        height: 100%;
      }
      .status-code {
        font-size: 120px;
        color: #005da9;
        position: absolute;
        top: 50px;
        left: 102px;
      }
    }
    .err-tip {
      font-size: 24px;
      color: #005da9;
      margin-top: 60px;
    }
    .err-btn-back {
      border: 2px solid #005da9;
      border-radius: 5px;
      font-size: 28px;
      color: #005da9;
      text-align: center;
      padding: 10px 180px;
      margin-top: 40px;
      background: #f0f0f1;
      cursor: pointer;
    }
  }
  .err-footer {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #444344;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 20px;
    .footer-beian {
      cursor: pointer;
    }
  }
}
</style>
