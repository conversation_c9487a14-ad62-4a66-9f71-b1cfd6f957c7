import qs from 'qs'
export default ($axios: any) => ({
  /** 获取手机号登录验证码 */
  loginVerifyCode: (params: Object) => {
    return $axios.$post('/iam/common/login/sms/code', qs.stringify(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' }
    })
  },
  /** 验证码登录 */
  loginVerify: (params: Object) => {
    return $axios.$post('/iam/common/login/sms', qs.stringify(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' }
    })
  },
  validLoginVerifyCode: () => {
    return $axios.$get('/user/common/code/getVerificationCode-valid')
  },

  /** 用户登录前判断是否需要开启防水墙 */
  ifOpenCaptcha: (params: Object) => {
    return $axios.$post('/user/common/login/checkRequireVerification', params)
  },

  /** 登录接口 */
  login: (params: Object) => {
    return $axios.$post('/iam/common/login', qs.stringify(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' }
    })
  },
  validLogin: () => {
    return $axios.$get('/user/common/login/unifyLogin-valid')
  },

  /** 退出登录 */
  logout: () => {
    return $axios.$get('/user/user/logout')
  },

  /** 国际化语言 */
  getLanguage: () => {
    return $axios.$get('/i18n/common/lang/list')
  },

  // 获取校验规则：手机号登录
  unifyLoginByUserName: () => $axios.$get('/user/common/login/unifyLoginByUserName-valid'),

  // 获取校验规则：用户名登录
  unifyLoginByPhone: () => $axios.$get('/user/common/login/unifyLoginByPhone-valid'),
  isClearToken: () => $axios.$get('/iam/common/login/token-verify')
})
