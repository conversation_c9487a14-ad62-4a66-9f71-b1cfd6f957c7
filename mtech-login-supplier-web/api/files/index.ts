export default ($axios: any) => ({
  // 文件服务提供的预览地址
  imageUrl: (params: any) => {
    return $axios.$get('/file/user/mtPreview', params)
  },
  // 通过文件ID获取文件
  downloadPublicFile: (params: any) => {
    return $axios.$get('/file/user/file/downloadPublicFile?id=' + params)
  },
  // Header: api-token
  uploadLogo(params: any) {
    return $axios.$post('/file/user/file/uploadLogo', params)
  },

  // 上传公共文件
  uploadPublicFile(params: any) {
    return $axios.$post('/file/user/file/uploadPublic', params)
  },

  // 删除公共文件
  deletePublicFile(params: any) {
    return $axios.$get('/file/user/file/deletePublicFile', {
      params
    })
  },

  // 附件预览
  filePreview(params: any) {
    return $axios.$get(`/file/user/file/mtPreview`, params)
  },

  downloadPrivateFile(params: any) {
    return $axios.$get(`/supplier/public-api/supplier/preReviewSurvey/download?id=${params.id}`, {
      responseType: 'blob'
    })
  }
  // downloadPrivateFile(params: any) {
  //   $axios.$get(`/file/user/file/downloadPrivateFile?useType=2`, params, {
  //     responseType: 'blob'
  //   })
  // }

  // /api/file/user/file/uploadLogo
})

/**
 *
上传地址POST：
http://file.dev.qeweb.com/api/file/user/file/uploadPublic?useType=1
Header:
api-token

下载地址GET
http://file.dev.qeweb.com/api/file/user/file/downloadPublicFile?id=1416349392800137217
Header:
api-token

文件服务提供的预览地址：
http://file.dev.qeweb.com/api/file/user/mtPreview?id=1416349392800137217

文件地址：
http://minio.dev.qeweb.com/staticfile/image/admin/1/f62f2311-3cde-4938-87b6-dcd353f5872f.jpg

真正的预览地址(kkview，重定向至这里)：
http://filepreview.dev.qeweb.com/preview/onlinePreview?url=http%3A%2F%2Fminio.dev.qeweb.com%2Fstaticfile%2Fimage%2Fadmin%2F1%2Ff62f2311-3cde-4938-87b6-dcd353f5872f.jpg

 */
