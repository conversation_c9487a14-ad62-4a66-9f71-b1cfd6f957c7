import qs from 'qs'
export default ($axios: any) => ({
  // 平台账号注册
  register: (params: Object) => {
    return $axios.$post('/platform/tcl/supplier/registered', params)
  },
  // 查询社会统一信用代码注册次数
  checkIdentityCode(code: string) {
    return $axios.$get(`/platform/tcl/supplier/checkIdentityCode/${code}`)
  },
  // 检查社会统一信用代码
  checkIdentityCodeRepeat(code: string) {
    return $axios.$get(`/platform/tcl/supplier/checkIdentityCodeRepeat?identityCode=${code}`)
  },
  // 检查社会统一信用码和业务类型是否重复，仅中国供应商
  checkIdentityCodeAndBusinessTypeCodeApi(data: Object) {
    return $axios.$post(`/platform/tcl/supplier/checkIdentityCodeAndBusinessTypeCode`, data)
  },
  // 根据统一社会信用代码查询企业名称
  getEnterpriseNameByIdentityCodeApi: (params: Object) => {
    return $axios.$get('/platform/tcl/supplier/getEnterpriseNameByIdentityCode', {
      params
    })
  },
  // 检查企业名称
  checkNameRepeat(code: string) {
    return $axios.$get(`/platform/tcl/supplier/checkNameRepeat?enterpriseName=${code}`)
  },
  // 获取注册时的手机验证码
  getVerifyCode: (params: Object) => {
    return $axios.$post('/iam/common/account/sms_register_code', params)
  },
  // // 获取短信验证码-检查手机号有效性
  // mobileCheckCode(params: any) {
  //   return $axios.$post('/iam/common/account/sms_mobile_check_code', params)
  // },
  // 获取注册时的邮箱验证码
  getMailSmsCode: (params: Object) => {
    return $axios.$post('/iam/common/login/mail_register_code', qs.stringify(params), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8' }
    })
  },
  // 验证用户名是否存在
  checkAccountName: (params: Object) => {
    return $axios.$post('/user/common/register/checkLoginName', params)
  },
  // 注册表单校验规则
  registerValidate: () => {
    return $axios.$get('/user/common/register/register-valid')
  },
  // 保存企业头部信息
  enterpriseSaveHeader: (params: any) => {
    return $axios.$post('/platform/common/enterprise/saveHeader', params)
  },
  // 获取企业头部信息的校验规则
  enterpriseSaveHeaderValid() {
    return $axios.$get('/platform/common/enterprise/saveHeader-valid')
  },

  // 保存企业详情信息
  enterpriseSaveMain: (params: any) => {
    return $axios.$post('/platform/common/enterprise/saveMain', params)
  },
  // 获取企业详情信息的校验规则
  enterpriseSaveMainValid: (params: any) => {
    return $axios.$get('/platform/common/enterprise/saveMain-valid', params)
  },
  // 验证企业名称是否已经注册
  validateCompanyName: (params: any) => {
    return $axios.$get('/platform/common/enterprise/validateCompanyName', {
      params
    })
  },
  // 绑定管理员账号并提交审核
  bindAdminId: (params: any) => {
    return $axios.$get('/platform/admin/enterprise/bindAdminId', {
      params
    })
  },
  // 获取产品服务地址 platform-eshop-tree
  getEshopTree(params: any = {}) {
    return $axios.$post('/masterDataManagement/common/category/platform-product-tree', params)
  },
  //  根据父级获取产品服务
  findPlatformProduct(params: any = {}) {
    return $axios.$post(
      '/masterDataManagement/common/category/find-platform-product-child-categorys',
      params
    )
  },
  // 查询征信企业基本信息接口
  queryEnterpriseInfoByName(params: any = {}) {
    return $axios.$get('/platform/common/enterprise/queryEnterpriseInfoByName', {
      params
    })
  },
  // 字典查询
  queryDict(params: any = {}) {
    return $axios.$post('/masterDataManagement/common/dict-item/item-tree', params)
  },
  // 查询寻源基本信息
  querySourcingInfo(params: any = {}) {
    return $axios.$get('/sourcing/public-api/publicSourcingHeader/queryDetails', {
      params
    })
  },
  // 获取分享图片
  getSharePicture(params: any = {}) {
    return $axios.$get('/sourcing/public-api/publicSourcingHeader/queryPosterFileUrl', {
      params
    })
  }
})
