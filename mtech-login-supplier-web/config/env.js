// module.exports = function () {
//   return {
//     // dev: process.env.NODE_ENV !== 'production' || process.env.NODE_ENV !== 'test',
//     env: {
//       baseUrl:
//         process.env.NODE_ENV === 'production' ? 'https://pro.nuxtjs.org' : process.env.NODE_ENV === 'test' ? 'https://test.nuxtjs.org' : 'http://localhost:3000'
//     }
//     // publicRuntimeConfig: {
//     //   baseURL: process.env.BASE_URL || 'https://nuxtjs.org'
//     // }
//   }
// }
module.exports = {
  baseUrl:
    process.env.NODE_ENV === 'production'
      ? 'https://pro.nuxtjs.org'
      : process.env.NODE_ENV === 'test'
      ? 'https://test.nuxtjs.org'
      : 'http://localhost:3000'
}
