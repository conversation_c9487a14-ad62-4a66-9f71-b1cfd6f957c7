// module.exports = function (config) {
//   config.resolve.alias['@syncfusion/ej2-base'] = '@/node_modules/@syncfusion/ej2-base/dist/ej2-base.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-buttons'] = '@/node_modules/@syncfusion/ej2-buttons/dist/ej2-buttons.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-calendars'] = '@/node_modules/@syncfusion/ej2-calendars/dist/ej2-calendars.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-charts'] = '@/node_modules/@syncfusion/ej2-charts/dist/ej2-charts.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-compression'] = '@/node_modules/@syncfusion/ej2-compression/dist/ej2-compression.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-data'] = '@/node_modules/@syncfusion/ej2-data/dist/ej2-data.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-dropdowns'] = '@/node_modules/@syncfusion/ej2-dropdowns/dist/ej2-dropdowns.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-excel-export'] = '@/node_modules/@syncfusion/ej2-excel-export/dist/ej2-excel-export.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-file-utils'] = '@/node_modules/@syncfusion/ej2-file-utils/dist/ej2-file-utils.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-filemanager'] = '@/node_modules/@syncfusion/ej2-filemanager/dist/ej2-filemanager.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-gantt'] = '@/node_modules/@syncfusion/ej2-gantt/dist/ej2-gantt.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-grids'] = '@/node_modules/@syncfusion/ej2-grids/dist/ej2-grids.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-icons'] = '@/node_modules/@syncfusion/ej2-icons/dist/ej2-icons.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-inputs'] = '@/node_modules/@syncfusion/ej2-inputs/dist/ej2-inputs.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-kanban'] = '@/node_modules/@syncfusion/ej2-kanban/dist/ej2-kanban.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-layouts'] = '@/node_modules/@syncfusion/ej2-layouts/dist/ej2-layouts.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-lists'] = '@/node_modules/@syncfusion/ej2-lists/dist/ej2-lists.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-navigations'] = '@/node_modules/@syncfusion/ej2-navigations/dist/ej2-navigations.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-notifications'] = '@/node_modules/@syncfusion/ej2-notifications/dist/ej2-notifications.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-pdf-export'] = '@/node_modules/@syncfusion/ej2-pdf-export/dist/ej2-pdf-export.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-pivotview'] = '@/node_modules/@syncfusion/ej2-pivotview/dist/ej2-pivotview.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-popups'] = '@/node_modules/@syncfusion/ej2-popups/dist/ej2-popups.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-querybuilder'] = '@/node_modules/@syncfusion/ej2-querybuilder/dist/ej2-querybuilder.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-richtexteditor'] = '@/node_modules/@syncfusion/ej2-richtexteditor/dist/ej2-richtexteditor.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-schedule'] = '@/node_modules/@syncfusion/ej2-schedule/dist/ej2-schedule.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-splitbuttons'] = '@/node_modules/@syncfusion/ej2-splitbuttons/dist/ej2-splitbuttons.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-spreadsheet'] = '@/node_modules/@syncfusion/ej2-spreadsheet/dist/ej2-spreadsheet.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-svg-base'] = '@/node_modules/@syncfusion/ej2-svg-base/dist/ej2-svg-base.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-treegrid'] = '@/node_modules/@syncfusion/ej2-treegrid/dist/ej2-treegrid.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-base'] = '@/node_modules/@syncfusion/ej2-vue-base/dist/ej2-vue-base.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-buttons'] = '@/node_modules/@syncfusion/ej2-vue-buttons/dist/ej2-vue-buttons.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-calendars'] = '@/node_modules/@syncfusion/ej2-vue-calendars/dist/ej2-vue-calendars.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-charts'] = '@/node_modules/@syncfusion/ej2-vue-charts/dist/ej2-vue-charts.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-dropdowns'] = '@/node_modules/@syncfusion/ej2-vue-dropdowns/dist/ej2-vue-dropdowns.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-filemanager'] = '@/node_modules/@syncfusion/ej2-vue-filemanager/dist/ej2-vue-filemanager.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-gantt'] = '@/node_modules/@syncfusion/ej2-vue-gantt/dist/ej2-vue-gantt.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-grids'] = '@/node_modules/@syncfusion/ej2-vue-grids/dist/ej2-vue-grids.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-inputs'] = '@/node_modules/@syncfusion/ej2-vue-inputs/dist/ej2-vue-inputs.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-kanban'] = '@/node_modules/@syncfusion/ej2-vue-kanban/dist/ej2-vue-kanban.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-layouts'] = '@/node_modules/@syncfusion/ej2-vue-layouts/dist/ej2-vue-layouts.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-lists'] = '@/node_modules/@syncfusion/ej2-vue-lists/dist/ej2-vue-lists.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-navigations'] = '@/node_modules/@syncfusion/ej2-vue-navigations/dist/ej2-vue-navigations.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-notifications'] = '@/node_modules/@syncfusion/ej2-vue-notifications/dist/ej2-vue-notifications.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-pivotview'] = '@/node_modules/@syncfusion/ej2-vue-pivotview/dist/ej2-vue-pivotview.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-popups'] = '@/node_modules/@syncfusion/ej2-vue-popups/dist/ej2-vue-popups.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-querybuilder'] = '@/node_modules/@syncfusion/ej2-vue-querybuilder/dist/ej2-vue-querybuilder.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-richtexteditor'] = '@/node_modules/@syncfusion/ej2-vue-richtexteditor/dist/ej2-vue-richtexteditor.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-schedule'] = '@/node_modules/@syncfusion/ej2-vue-schedule/dist/ej2-vue-schedule.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-splitbuttons'] = '@/node_modules/@syncfusion/ej2-vue-splitbuttons/dist/ej2-vue-splitbuttons.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-spreadsheet'] = '@/node_modules/@syncfusion/ej2-vue-spreadsheet/dist/ej2-vue-spreadsheet.umd.min.js'
//   config.resolve.alias['@syncfusion/ej2-vue-treegrid'] = '@/node_modules/@syncfusion/ej2-vue-treegrid/dist/ej2-vue-treegrid.umd.min.js'
// }
