import router from './modules/router'

export const state = () => ({
  application: {},
  countryList: [
    {
      id: 1,
      code: '0086',
      text: '中国 0086',
      value: 1,
      length: 11,
      maxLength: 11,
      minLength: 11,
    },
  ],
  locale: 'zh-CN',
})
export const getters = {
  getApplication: (state: any) => state.application,
  getCountryList: (state: any) => state.countryList,
}

export const mutations = {
  setApplication(state: any, playload: Object) {
    state.application = playload
  },
  setCountryList(state: any, playload: Object[]) {
    state.countryList = playload
  },
  setLocale(state: any, payload: string) {
    state.locale = payload
  },
}

export const actions = {
  // nuxtServerInit({ commit }: any, { app }: any) {
  //   const token: string = app.$cookies.get('token')
  //   if (token) {
  //     commit('user/SET_TOKEN', token)
  //   }
  // },
  setApplication: ({ commit }: any, playload: Object) => {
    commit('setApplication', playload)
  },
  setCountryList: ({ commit }: any, playload: Object[]) => {
    commit('setCountryList', playload)
  },
}

export const modules = {
  router,
}
