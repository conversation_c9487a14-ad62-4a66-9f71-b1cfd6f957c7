const state = () => ({
  asyncStatus: false
})

const getters = {
  asyncStatus: (state: any) => state.asyncStatus
}

const mutations = {
  SET_STATUS(state: any, payload: boolean) {
    state.asyncStatus = payload
  }
}

const actions = {
  initAsync: ({ commit }: any) => {
    commit('SET_STATUS', true)
  },

  clearAsync: ({ commit }: any) => {
    commit('SET_STATUS', false)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
