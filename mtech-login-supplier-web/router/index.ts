const staticRoutes = [
  {
    path: '/',
    redirect: { name: 'login' }
  },
  {
    path: '/login',
    name: 'login',
    component: 'pages/login/index.vue'
  },
  {
    path: '/register',
    name: 'Register',
    component: 'pages/register/index.vue'
  },
  // 重置密码
  {
    path: '/resetPassword',
    name: 'ResetPassword',
    component: 'pages/reset/password/reset/index.vue'
  },
  // 分享页面
  {
    path: '/m/share',
    name: 'share',
    component: 'pages/m/share.vue'
  },
  // 供应商透明寻源单据详情页
  {
    path: '/m/sourcingDetail',
    name: 'sourcingDetail',
    component: 'pages/m/sourcingDetail.vue'
  },
  // step1 - 注册信息填写
  {
    path: '/m/register',
    name: 'mRegister',
    component: 'pages/m/stepRegister.vue'
  },
  // step2 - 基础信息填写
  {
    path: '/m/baseInfo',
    name: 'baseInfo',
    component: 'pages/m/stepBaseInfo.vue'
  },
  // step3 - 成功页面
  {
    path: '/m/success',
    name: 'success',
    component: 'pages/m/stepSuccess.vue'
  },
  {
    path: '/m/appDownload',
    name: 'appDownload',
    component: 'pages/m/appDownload.vue'
  },
  // 模具OA审批页面
  {
    path: '/mould/oaApproval',
    name: 'oaApproval',
    component: 'pages/mould/oaApproval.vue'
  },
  // 光伏OA审批页面
  {
    path: '/photovoltaic/oaApproval',
    name: 'oaApproval',
    component: 'pages/photovoltaic/oaApproval.vue'
  }
]

module.exports = [...staticRoutes]
