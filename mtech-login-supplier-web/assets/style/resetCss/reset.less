button:focus,
button:active:focus,
button.active:focus,
button.focus,
button:active.focus,
button.active.focus {
  outline: none;
  // border-color: transparent;
  box-shadow: none;
}

input[type='number'] {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  -moz-appearance: textfield;
}
.e-error {
  color: #f44336;
  font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system', 'BlinkMacSystemFont';
  font-size: 12px;
  font-weight: normal;
}
// 输入框错误样式重置
.input__wrap /deep/ label.e-error {
  width: fit-content;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


// 国际电话
/**
  * 下拉 start
*/
.mt-input-country-phone-dropdown /deep/ .e-control-wrapper.e-input-group {
  margin-bottom: 0;
}
.mt-input-country-phone-dropdown /deep/ .e-outline.e-control-wrapper.e-input-group {
  border-radius: 4px 0 0 4px;
  .e-input-group-icon {
    font-size: 12px;
  }
}
.mt-input-country-phone-dropdown /deep/ .e-outline.e-control-wrapper.e-input-group.e-error {
  border-radius: 4px 0 0 4px;
}
/** 下拉 end */
/**
  * 输入框 start
*/
.mt-input-country-phone-textbox /deep/ .e-control-wrapper.e-input-group.e-error {
  label {
    left: -135px;
  }
}
.mt-input-country-phone-textbox /deep/ .e-outline.e-control-wrapper.e-input-group {
  border-radius: 0 4px 4px 0;
  position: relative;
  left: -1px;
}
.mt-input-country-phone-textbox /deep/ .e-outline.e-control-wrapper.e-input-group.e-error {
  border-radius: 0 4px 4px 0;
  position: relative;
  left: -1px;
}

/** 输入框 end */


// pager
.e-control.e-pager{
  margin-top: -1px;
}

// dialog
.e-dialog .e-dlg-header-content {
  background-color: #005ca9;
}
.e-dlg-header, .e-dlg-header * {
  color: #fff;
}
.e-dlg-header-content .e-icon-dlg-close {
  color: #fff;
}
.e-dialog .e-btn.e-dlg-closeicon-btn:active span, 
.e-dialog .e-btn.e-dlg-closeicon-btn:focus span,
.e-btn.e-flat, 
.e-css.e-btn.e-flat,
.e-btn.e-flat:hover, 
.e-css.e-btn.e-flat:hover,
.e-dialog .e-btn.e-dlg-closeicon-btn:hover span {
  color: #fff;
}
.e-dialog .e-btn.e-dlg-closeicon-btn:hover, .e-dialog .e-btn.e-dlg-closeicon-btn:focus, .e-dialog .e-btn.e-dlg-closeicon-btn:active{
  background-color: rgba(32, 132, 222, 0.6);
}
.e-dialog .e-dlg-header-content {
  padding: 6px 20px;
}
.e-dialog .e-dlg-content {
  padding: 0;
}
// .e-dialog .e-dlg-header-content + .e-dlg-content {
//   padding-top: 10px;
// }
.mt-form-item__content {
  width: 100%;
}