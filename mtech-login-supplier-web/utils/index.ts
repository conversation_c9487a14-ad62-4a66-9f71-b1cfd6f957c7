// 防抖

// 节流

// 日期格式化

// 倒计时

// storage 存取
export * from './storage'
// ...
export const env = process.env.NODE_ENV

// 调用腾讯防水墙
export const useTCaptcha = (appId = '2015497501 ') => {
  return new Promise((resolve, reject) => {
    if (window && window.TencentCaptcha) {
      window.pfCaptcha = new window.TencentCaptcha(
        appId,
        (res: any) => {
          if (res.ret === 0) {
            resolve(res)
          } else {
            reject(res)
            window.pfCaptcha && window.pfCaptcha.destroy()
          }
        },
        null
      )
      window.pfCaptcha.show()
    } else reject(new Error('未找到TCaptcha.js脚本'))
  })
}

// 整理后台返回的校验规则
// 原始数据：
// {
//   {password: {required: [true, '密码不能为空']}, pattern: [pswRegex, '密码必须为6-20位数字/字母/特殊字符']},
//   {username: {required: [true, '密码不能为空']}, pattern: [pswRegex, '密码必须为6-20位数字/字母/特殊字符']}
// }
// 处理成：
// {
//    {password: [
//      { required: true, message: '密码不能为空', trigger: 'blur' },
//      { pattern: pswRegex, message: '密码必须为6-20位数字/字母/特殊字符', trigger: 'blur' }
//    ]},
//    {username: [
//      { required: true, message: '密码不能为空', trigger: 'blur' },
//      { pattern: pswRegex, message: '密码必须为6-20位数字/字母/特殊字符', trigger: 'blur' }
//    ]}
//  }
export const formatRules = (originRule:object, isBlur: boolean = true) => {
  if(originRule == {}){
    return {}
  }
  var resRules = {}
  for(let i in originRule) {
    resRules[i] = []
    for(let j in originRule[i]) {
      if ( j === 'required' ) {
        resRules[i].push({[j]: originRule[i][j][0], message: originRule[i][j][1], trigger: isBlur ? 'blur' : 'submit'})
      } else {
        resRules[i].push({
          type: j + '' ,
          message: originRule[i][j][1], 
          trigger: isBlur ? 'blur' : 'submit'
        })
      }
      
    }
  }
  return resRules;

  
}
