// @ts-nocheck

import { Component, Mixins } from 'vue-property-decorator'

function broadcast(componentName: string, eventName: string, params?: any) {
  this.$children.forEach((child: any) => {
    let name = child.$options.componentName
    if (name === componentName) {
      child.$emit.apply(child, [eventName].concat(params))
    } else {
      broadcast.apply(child, [componentName, eventName].concat([params]))
    }
  })
}

@Component
class Emitter extends Mixins() {
  dispatch(componentName: string, eventName: string, params?: any) {
    let parent = this.$parent || this.$root
    let name = (parent.$options as any).componentName
    while (parent && (!name || name !== componentName)) {
      parent = parent.$parent
      if (parent) {
        name = (parent.$options as any).componentName
      }
    }
    if (parent) {
      parent.$emit.apply(parent, [eventName].concat(params)) // apply this转成parent，emit('eventName', params)
    }
  }

  broadcast(componentName: string, eventName: string, params: any) {
    broadcast.call(this, componentName, eventName, params)
  }
}

export default Emitter
