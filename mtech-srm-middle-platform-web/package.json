{"name": "mtech-srm-middle-platform-web", "version": "0.0.1", "private": true, "scripts": {"build": "vue-cli-service build  ./src/main.js", "build:report": "vue-cli-service build  ./src/main.js --report", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve ./src/main.js", "buildAll": "npm-run-all theme build --continue-on-error", "serveAll": "npm-run-all --parallel \"theme -- --watch\" serve --continue-on-error", "theme": "node build/theme.js", "dictionary": "node build/dict.js", "upgrade:mtech-ui": "npx update-by-scope -t latest @mtech npm install", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@digis/component-props-state": "^1.2.6", "@digis/dictionary-plugin": "^1.1.10", "@digis/digis-bpmn-editor": "^0.2.4", "@digis/internationalization": "^1.1.17", "@mtech-common/utils": "^1.0.0", "@mtech-form-design/deploy": "^1.5.1", "@mtech-sso/single-sign-on": "^1.3.1", "@mtech-ui/base": "^1.10.10", "@mtech-ui/select": "^1.10.1", "@mtech/common-loading": "^1.0.0", "@mtech/common-permission": "^1.2.4", "@mtech/common-tree-view": "^0.2.3", "@mtech/mtech-common-uploader": "^1.8.4", "@tinymce/tinymce-vue": "^3.2.8", "core-js": "^3.18.3", "css-vars-ponyfill": "^2.4.7", "currency.js": "^2.0.4", "echarts": "^5.5.1", "lodash": "^4.17.21", "moment": "^2.29.1", "qiankun": "^2.6.3", "tinymce": "^5.10.6", "vue": "^2.6.11", "vue-router": "^3.5.1", "vue-typescript": "^0.7.0", "vuex": "^3.6.2", "vxe-table": "^3.6.13", "xe-utils": "^3.5.11"}, "devDependencies": {"@commitlint/cli": "^17.0.1", "@commitlint/config-conventional": "^17.0.0", "@digis-vue-cli-preset/vue-cli-plugin-performance": "^1.1.5", "@mtech-common/http": "^1.0.5", "@mtech-micro-frontend/vue-cli-plugin-micro": "^1.0.0", "@mtech/eslint-config-vue": "0.0.4", "@mtech/vue-property-decorator": "^9.1.2", "@types/js-cookie": "^2.2.7", "@types/webpack-env": "^1.16.3", "@typescript-eslint/eslint-plugin": "^4.27.0", "@typescript-eslint/parser": "^4.27.0", "@vue/cli-plugin-babel": "^4.5.14", "@vue/cli-plugin-eslint": "^4.5.14", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "^4.5.14", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^7.0.0", "axios": "^0.21.1", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.5", "chalk": "^2.4.2", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "husky": "^8.0.3", "js-cookie": "^2.2.1", "less-loader": "^10.0.1", "lint-staged": "^13.1.4", "node-sass": "^4.14.1", "npm-run-all": "^4.1.5", "pdfobject": "^2.2.7", "raw-loader": "^4.0.2", "sass-loader": "^8.0.0", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.5", "ts-loader": "^9.2.3", "ts-transformer-keys": "^0.4.3", "typescript": "^4.3.2", "vue-class-component": "^8.0.0-rc.1", "vue-cli-plugin-style-resources-loader": "~0.1.5", "vue-template-compiler": "^2.6.11", "vuex-module-decorators": "^1.0.1", "webpack-fix-style-only-entries": "^0.6.1", "webpack-glob-entry": "^2.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mtMicro": {"type": "slave"}}