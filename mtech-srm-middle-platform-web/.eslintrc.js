module.exports = {
  root: true,
  env: {
    node: true
  },
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2017,
    sourceType: 'module'
  },
  extends: ['@mtech/eslint-config-vue'],
  // 'vue-typescript',
  ecmaFeatures: {
    legacyDecorators: true
  },
  // plugins: ['prettier'],
  // add your custom rules here
  rules: {
    'no-useless-escape': 0, //关闭 禁止不必要的转义字符
    'vue/no-template-shadow': 'off',
    '@typescript-eslint/no-inferrable-types': 'off',
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-this-alias': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'no-redeclare': 'off',
    'vue/attribute-hyphenation': 'off',
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/no-extra-semi': 0,
    'prettier/prettier': [
      'error',
      {
        semi: false,
        endOfLine: 'auto'
      }
    ]
  }
}
