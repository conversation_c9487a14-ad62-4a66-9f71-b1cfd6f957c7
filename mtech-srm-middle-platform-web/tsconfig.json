{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "resolveJsonModule": true, "baseUrl": ".", "types": ["webpack-env"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "src/views/workflow/controller/PaletteProvider.js", "src/components/Dialog/index.js", "src/utils/codeEditor.js"], "exclude": ["node_modules"]}