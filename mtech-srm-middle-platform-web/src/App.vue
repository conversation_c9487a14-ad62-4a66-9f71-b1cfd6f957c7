<template>
  <div id="middlePlatform">
    <mt-loading v-show="loading"></mt-loading>
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'App',
  data() {
    return {
      active: 0
    }
  },
  computed: {
    ...mapState(['loading'])
  },

  mounted() {
    console.log('mapState', mapState)
  },

  methods: {
    next() {
      if (this.active++ > 2) this.active = 0
    }
  }
}
</script>

<style>
#middlePlatform {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
