<!-- 语言管理 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="code" :label="$t('语言编码')">
          <mt-input v-model="searchFormModel.code" :placeholder="$t('请输入')" show-clear-button />
        </mt-form-item>
        <mt-form-item prop="name" :label="$t('语言名称')">
          <mt-input v-model="searchFormModel.name" :placeholder="$t('请输入')" show-clear-button />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        :row-config="{ height: 36 }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        style="padding-top: unset"
        @edit-closed="editComplete"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, ToolBar } from './config'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    return {
      searchFormModel: {},
      columns: columnData,
      tableData: [],
      toolbar: ToolBar,
      pageSettings: {
        currentPage: 1,
        pageSize: 50, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      apiWaitingQuantity: 0
    }
  },
  created() {
    this.handleSearch()
  },
  methods: {
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      let params = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$api.i18n
        .pageLanguageApi(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.tableData = res?.data?.records || []
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    editComplete(args) {
      console.log('editComplete', args)
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 1、 校验必填
        if (!this.isValidData(row)) {
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        // 2、 调保存接口
        this.handleSave(row)
      }
    },
    isValidData(data) {
      const { code, name } = data
      let valid = false
      if (!code) {
        this.$toast({ content: this.$t('请输入语言编码'), type: 'warning' })
      } else if (!name) {
        this.$toast({ content: this.$t('请输入语言名称'), type: 'warning' })
      } else {
        valid = true
      }
      return valid
    },
    handleSave(row) {
      let params = { ...row }
      if (row.id.includes('row_')) {
        params.id = null
      }
      this.apiStartLoading()
      this.$api.i18n
        .saveLanguageApi(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
        })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      if (code === 'Add') {
        const item = {
          id: null,
          code: null,
          name: null
        }
        $grid.insert([item])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
        return
      }
      if (code === 'CloseEdit') {
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (code === 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleDelete(ids)
          }
        })
      }
    },
    handleDelete(ids) {
      this.apiStartLoading()
      this.$api.i18n
        .deleteLanguageApi({ ids })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  .vxe-default-select {
    background: #fff;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>
