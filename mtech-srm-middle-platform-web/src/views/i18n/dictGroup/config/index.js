import { i18n } from '@/main.js'

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'groupCode',
    title: i18n.t('词组编码'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  },
  {
    field: 'groupName',
    title: i18n.t('词组名称'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { placeholder: '请输入' } }
  }
]

export const ToolBar = [
  {
    code: 'Add',
    name: i18n.t('新增'),
    status: 'info'
  },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    status: 'info',
    transfer: true
  },
  {
    code: 'Delete',
    name: i18n.t('删除'),
    status: 'info'
  }
]
