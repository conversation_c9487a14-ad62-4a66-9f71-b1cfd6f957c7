<!-- 词条管理 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="code" :label="$t('编码')">
          <mt-input v-model="searchFormModel.code" :placeholder="$t('请输入')" show-clear-button />
        </mt-form-item>
        <mt-form-item prop="groupCode" :label="$t('词组编码')">
          <mt-select
            v-model="searchFormModel.groupCode"
            :data-source="groupCodeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="dictType" :label="$t('所属类别')">
          <mt-select
            v-model="searchFormModel.dictType"
            :data-source="dictTypeOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
        <mt-form-item prop="displayText" :label="$t('多语言内容')">
          <mt-input
            v-model="searchFormModel.displayText"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="itemCode" :label="$t('条目编码')">
          <mt-input
            v-model="searchFormModel.itemCode"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="createUserName" :label="$t('创建人')">
          <mt-input
            v-model="searchFormModel.createUserName"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="createTime" :label="$t('创建时间')">
          <mt-date-range-picker
            v-model="searchFormModel.createTime"
            :placeholder="$t('请选择')"
            @change="(e) => createTimeChange(e)"
          />
        </mt-form-item>
        <mt-form-item prop="status" :label="$t('状态')">
          <mt-select
            v-model="searchFormModel.status"
            :data-source="statusOptions"
            :fields="{ text: 'label', value: 'value' }"
            :placeholder="$t('请选择')"
            :show-clear-button="true"
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        :row-config="{ height: 36 }"
        :column-config="{ resizable: true }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 20, oSize: 10 }"
        style="padding-top: unset"
        @edit-closed="editComplete"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #groupCodeEdit="{ row }">
          <vxe-select
            v-model="row.groupCode"
            :options="groupCodeOptions"
            transfer
            filterable
            :placeholder="$t('请选择')"
            @change="groupCodeChange(row)"
          />
        </template>
        <template #dictTypeDefault="{ row }">
          {{ row.dictType.value + `-` + row.dictType.label }}
        </template>
        <template #dictTypeEdit="{ row }">
          <vxe-select
            v-model="row.dictType.value"
            :options="dictTypeOptions"
            transfer
            filterable
            :placeholder="$t('请选择')"
            @change="dictTypeChange(row)"
          />
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import { columnData, ToolBar, statusOptions } from './config'
import { getHeadersFileName, download } from '@/utils/utils'
export default {
  components: {
    CollapseSearch,
    ScTable
  },
  data() {
    return {
      searchFormModel: {},
      columns: columnData,
      tableData: [],
      toolbar: ToolBar,
      pageSettings: {
        currentPage: 1,
        pageSize: 50, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      apiWaitingQuantity: 0,

      langOptions: [],
      groupCodeOptions: [],
      dictTypeOptions: [],
      statusOptions
    }
  },
  created() {
    this.getLangList()
    this.getDictGroupList()
    this.getDictTypeList()
    this.handleSearch()
  },
  methods: {
    getDictTypeList() {
      this.$api.i18n.getDictTypeListEntryApi().then((res) => {
        if (res.code === 200) {
          this.dictTypeOptions = res.data.map((item) => {
            return {
              label: item.value + '-' + item.label,
              value: item.value,
              text: item.label
            }
          })
        }
      })
    },
    dictTypeChange(row) {
      const { dictType } = row
      this.dictTypeOptions.forEach((item) => {
        if (item.value === dictType.value) {
          dictType.label = item.text
        }
      })
    },
    getDictGroupList() {
      let params = {
        page: {
          pages: 1,
          size: 100
        }
      }
      this.$api.i18n.pageDictGroupApi(params).then((res) => {
        if (res.code === 200) {
          this.groupCodeOptions = res.data.records.map((item) => {
            return {
              label: item.groupCode + '-' + item.groupName,
              value: item.groupCode,
              groupId: item.id
            }
          })
        }
      })
    },
    groupCodeChange(row) {
      const { groupCode } = row
      this.groupCodeOptions.forEach((item) => {
        if (item.value === groupCode) {
          row.groupId = item.groupId
        }
      })
    },
    getLangList() {
      this.$api.i18n.getLanguageApi().then((res) => {
        if (res.code === 200) {
          this.langOptions = res.data.map((item) => {
            return {
              langCode: item.code,
              displayText: null
            }
          })
          this.handleColumn(res.data)
        }
      })
    },
    handleColumn(arr) {
      const langColumnData = []
      const firstColumn = columnData.slice(0, 4)
      const lastColumn = columnData.slice(4)
      arr.forEach((item, index) => {
        langColumnData.push({
          field: `displayText_${item.code}`,
          title: item.name,
          minWidth: 200,
          showOverflow: true,
          editRender: {},
          slots: {
            // 使用 JSX 渲染
            default: ({ row }) => {
              return row['dataList'][index].displayText
            },
            edit: ({ row }) => {
              return [
                <div>
                  <vxe-input
                    v-model={row['dataList'][index].displayText}
                    clearable
                    placeholder={this.$t('请输入')}
                  />
                </div>
              ]
            }
          }
        })
      })
      this.columns = [].concat(firstColumn).concat(langColumnData).concat(lastColumn)
    },
    createTimeChange(e) {
      if (e.startDate) {
        this.searchFormModel['createStartTime'] = dayjs(
          dayjs(e.startDate).format('YYYY-MM-DD 00:00:00')
        ).valueOf()
        this.searchFormModel['createEndTime'] = dayjs(
          dayjs(e.endDate).format('YYYY-MM-DD 23:59:59')
        ).valueOf()
      } else {
        this.searchFormModel['createStartTime'] = null
        this.searchFormModel['createEndTime'] = null
      }
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      let params = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$api.i18n.pageEntryApi(params).then((res) => {
        if (res.code === 200) {
          const total = res?.data?.total || 0
          this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
          this.pageSettings.totalRecordsCount = Number(total)
          const records = res?.data?.records || []
          this.tableData = records
        }
      })
    },
    editComplete(args) {
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === this.$t('取消编辑')) {
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 1、 校验必填
        if (!this.isValidData(row)) {
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
          return
        }
        // 2、 调保存接口
        this.handleSave(row)
      }
    },
    isValidData(data) {
      const { groupCode, dictType, dataList } = data
      let valid = false
      if (
        dataList?.some(
          (item) => item.langCode === 'zh' && (item.displayText === null || item.displayText === '')
        )
      ) {
        this.$toast({ content: this.$t('请输入默认语言'), type: 'warning' })
      } else if (!groupCode) {
        this.$toast({ content: this.$t('请选择词组编码'), type: 'warning' })
      } else if (!dictType.value) {
        this.$toast({ content: this.$t('请选择所属类别'), type: 'warning' })
      } else {
        valid = true
      }
      return valid
    },
    handleSave(row) {
      let params = { ...row }
      if (params.id.includes('row_')) {
        params.id = null
      }
      params.dictType = params.dictType.value
      this.$api.i18n
        .saveEntryApi(params)
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
        })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const codes = []
      selectedRecords.forEach((item) => {
        codes.push(item.code)
      })
      const commonToolbar = ['Delete', 'Enable', 'Disable', 'Refresh']
      if (code === 'Add') {
        const item = {
          id: null,
          dictType: {
            label: null,
            value: this.searchFormModel.dictType
          },
          dataList: this.langOptions,
          groupCode: this.searchFormModel.groupCode
        }
        $grid.insert([item])
        this.$nextTick(() => {
          // 获取最新的表格视图数据
          const currentViewRecords = $grid.getTableData().visibleData
          // 将新增的那一条设置为编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
        })
        return
      }
      if (code === 'CloseEdit') {
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (selectedRecords.length === 0 && commonToolbar.includes(code)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      if (code === 'Delete') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleDelete(codes)
          }
        })
      }
      if (code === 'Enable') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认启用选中的数据？')
          },
          success: () => {
            this.handleEnable(codes)
          }
        })
      }
      if (code === 'Disable') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认禁用选中的数据？')
          },
          success: () => {
            this.handleDisable(codes)
          }
        })
      }
      if (code === 'Translate') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认翻译选中的数据？')
          },
          success: () => {
            this.handleTranslate(codes)
          }
        })
      }
      if (code === 'Refresh') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认刷新选中的数据？')
          },
          success: () => {
            this.handleRefresh(codes)
          }
        })
      }
      if (code === 'Import') {
        this.handleImport()
      }
      if (code === 'Export') {
        this.handleExport(codes)
      }
    },
    handleDelete(codes) {
      this.$api.i18n.deleteEntryApi({ codes }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleCustomSearch()
        }
      })
    },
    handleEnable(codes) {
      this.$api.i18n.enableEntryApi({ codes }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleCustomSearch()
        }
      })
    },
    handleDisable(codes) {
      this.$api.i18n.disableEntryApi({ codes }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleCustomSearch()
        }
      })
    },
    handleTranslate(codes) {
      this.$api.i18n.translateEntryApi({ codes }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleCustomSearch()
        }
      })
    },
    handleRefresh(codes) {
      this.$api.i18n.refreshEntryApi({ codes }).then((res) => {
        if (res.code === 200) {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.handleCustomSearch()
        }
      })
    },
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('导入'),
          importApi: this.$api.i18n.importEntryApi,
          downloadTemplateApi: this.$api.i18n.tempDownloadEntryApi,
          paramsKey: 'excel'
        },
        success: () => {
          this.handleSearch()
        }
      })
    },
    handleExport(codeList) {
      let params = {
        codeList,
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.$api.i18n.exportEntryApi(params).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
.lang-item {
  line-height: 30px;
  height: 30px;
  border-bottom: 1px solid #e8eaec;
  &:last-child {
    border-bottom: none;
  }
}
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  .vxe-default-select {
    background: #fff;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
</style>
