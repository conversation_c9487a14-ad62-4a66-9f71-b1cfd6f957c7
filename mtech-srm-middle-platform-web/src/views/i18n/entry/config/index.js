import { i18n } from '@/main.js'

export const statusOptions = [
  { label: i18n.t('草稿'), value: 0 },
  { label: i18n.t('启用'), value: 1 },
  { label: i18n.t('禁用'), value: 2 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true,
    fixed: 'left'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    showOverflow: true,
    editRender: { name: 'input', attrs: { disabled: true } }
  },
  {
    field: 'status',
    title: i18n.t('状态'),
    width: 80,
    showOverflow: true,
    formatter: ({ cellValue }) => {
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'code',
    title: i18n.t('编码'),
    width: 130,
    showOverflow: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'itemCode',
    title: i18n.t('条目编码'),
    width: 130,
    showOverflow: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
  },
  {
    field: 'groupCode',
    title: i18n.t('词组编码'),
    width: 150,
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'groupCodeEdit'
    }
  },
  {
    field: 'dictType',
    title: i18n.t('所属类别'),
    width: 130,
    showOverflow: true,
    editRender: {},
    slots: {
      default: 'dictTypeDefault',
      edit: 'dictTypeEdit'
    }
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    width: 80,
    showOverflow: true
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    width: 130,
    showOverflow: true
  }
]

export const ToolBar = [
  {
    code: 'Add',
    name: i18n.t('新增'),
    status: 'info'
  },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    status: 'info',
    transfer: true
  },
  {
    code: 'Delete',
    name: i18n.t('删除'),
    status: 'info'
  },
  {
    code: 'Enable',
    name: i18n.t('启用'),
    status: 'info'
  },
  {
    code: 'Disable',
    name: i18n.t('禁用'),
    status: 'info'
  },
  {
    code: 'Translate',
    name: i18n.t('翻译'),
    status: 'info'
  },
  {
    code: 'Refresh',
    name: i18n.t('重新加载'),
    status: 'info'
  },
  {
    code: 'Import',
    name: i18n.t('导入'),
    status: 'info'
  },
  {
    code: 'Export',
    name: i18n.t('导出'),
    status: 'info'
  }
]
