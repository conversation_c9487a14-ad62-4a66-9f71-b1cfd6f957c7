<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <!-- 绩效模板 -->
    <mt-form v-if="isPerformanceTask" ref="perfRuleForm" :model="perfFormModel" :rules="perfRules">
      <mt-form-item prop="taskTypeId" :label="$t('任务名称')">
        <mt-select
          v-model="perfFormModel.taskTypeId"
          :data-source="taskTypeData"
          :fields="{ text: 'itemName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择任务名称')"
          @change="taskTypeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="orgIdList" :label="$t('组织')">
        <mt-DropDownTree
          :popup-height="500"
          :fields="orgFields"
          v-model="perfFormModel.orgIdList"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择组织')"
          :filter-bar-placeholder="$t('请输入关键字')"
          :show-check-box="true"
          id="orgTreeSelectID"
        ></mt-DropDownTree>
      </mt-form-item>
      <mt-form-item v-if="isShowSupplier" prop="supplierCodeList" :label="$t('供应商')">
        <mt-multi-select
          v-model="perfFormModel.supplierCodeList"
          :data-source="supplierData"
          :show-clear-button="true"
          :allow-filtering="true"
          :filtering="remoteSupplierList"
          :fields="{ text: 'supplierName', value: 'supplierCode' }"
          :enable-group-check-box="true"
          mode="CheckBox"
          :placeholder="$t('请选择供应商')"
        ></mt-multi-select>
      </mt-form-item>
      <mt-form-item prop="templateType" :label="$t('模板类型')" v-if="isShowTemplate">
        <mt-select
          v-model="perfFormModel.templateType"
          :data-source="templateTypeData"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :placeholder="$t('请选择模板类型')"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="evaluationCycle" :label="$t('评价周期')">
        <mt-select
          v-model="perfFormModel.evaluationCycle"
          :data-source="isShowLayeredAndGraded ? detailEvaluationCycleData : evaluationCycleData"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :show-clear-button="true"
          :placeholder="$t('请选择评价周期')"
          @change="detailsToBeScoredModelChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item v-if="isShowLayeredAndGraded" prop="evaluateYear" :label="$t('评价年份')">
        <mt-select
          v-model="perfFormModel.evaluateYear"
          :data-source="detailsEvaluateYearData"
          :show-clear-button="true"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t('请选择评价周期')"
        ></mt-select>
      </mt-form-item>
      <!-- <mt-form-item v-if="isShowLayeredAndGraded" :label="$t('品类')">
        <mt-multi-select
          v-model="perfFormModel.categoryCode"
          :data-source="categoryData"
          :show-clear-button="true"
          :allow-filtering="true"
          :filtering="remoteCategoryList"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :enable-group-check-box="true"
          mode="CheckBox"
          :placeholder="$t('请选择品类')"
          @change="changeCategory"
        ></mt-multi-select>
      </mt-form-item> -->
      <mt-form-item v-if="isShowLayeredAndGraded || taskTypeCode === 'DDFMX'" :label="$t('品类')">
        <mt-multi-select
          v-model="perfFormModel.categoryList"
          :allow-filtering="true"
          float-label-type="Never"
          filter-type="Contains"
          :data-source="categoryListArrList"
          :filter-bar-placeholder="$t('请输入品类名称或编码进行搜索')"
          :fields="{ text: 'textAndValue', value: 'id' }"
          @change="selectCategoryas"
          :placeholder="$t('请选择品类名称:')"
        ></mt-multi-select>
      </mt-form-item>
      <mt-form-item
        v-if="!isShowLayeredAndGraded"
        prop="achievementsStartMonth"
        :label="$t('绩效月份起')"
      >
        <mt-date-picker
          v-model="perfFormModel.achievementsStartMonth"
          format="yyyy-MM"
          start="Year"
          depth="Year"
          :disabled="isEdit"
          :show-clear-button="true"
          :allow-edit="false"
          @change="dateStartChange"
          :placeholder="$t('选择绩效月份起')"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item
        v-if="!isShowLayeredAndGraded"
        prop="achievementsEndMonth"
        :label="$t('绩效月份止')"
      >
        <mt-date-picker
          v-model="perfFormModel.achievementsEndMonth"
          format="yyyy-MM"
          start="Year"
          depth="Year"
          :disabled="true"
          :show-clear-button="true"
          :allow-edit="false"
          @change="dateEndChange"
          :placeholder="$t('选择绩效月份止')"
        ></mt-date-picker>
      </mt-form-item>
    </mt-form>
    <!-- 配额模板 -->
    <mt-form v-else ref="quotaRuleForm" :model="quotaFormModel" :rules="quotaRules">
      <mt-form-item prop="taskTypeId" :label="$t('任务名称')">
        <mt-select
          v-model="quotaFormModel.taskTypeId"
          :data-source="taskTypeData"
          :fields="{ text: 'itemName', value: 'id' }"
          :show-clear-button="true"
          :placeholder="$t('请选择任务名称')"
          @change="taskTypeChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item prop="orgIdList" :label="$t('组织')">
        <mt-DropDownTree
          :popup-height="500"
          :fields="orgFields"
          v-model="quotaFormModel.orgIdList"
          :allow-filtering="true"
          filter-type="Contains"
          :placeholder="$t('请选择组织')"
          :filter-bar-placeholder="$t('请输入关键字')"
          :allow-multi-selection="false"
          id="orgTreeSelectID"
        ></mt-DropDownTree>
      </mt-form-item>
      <mt-form-item prop="evaluateYear" :label="$t('评价年份')">
        <mt-select
          v-model="quotaFormModel.evaluateYear"
          :data-source="performanceEvaluateEnum"
          :show-clear-button="true"
          :fields="{ text: 'text', value: 'value' }"
          :placeholder="$t('请选择评价周期')"
        ></mt-select>
      </mt-form-item>
      <!-- <mt-form-item prop="remark" :label="$t('报错提示')">
        <mt-input
          v-model="quotaFormModel.remark"
          :show-clear-button="true"
          :disabled="true"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="createUserName" :label="$t('提交人')">
        <mt-input
          v-model="quotaFormModel.createUserName"
          :show-clear-button="true"
          :disabled="true"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="createTime" :label="$t('提交时间')">
        <mt-input
          v-model="quotaFormModel.createTime"
          :show-clear-button="true"
          :disabled="true"
        ></mt-input>
      </mt-form-item>
      <mt-form-item prop="q_status" :label="$t('状态')">
        <mt-select
          v-model="quotaFormModel.q_status"
          :data-source="statusList"
          :show-clear-button="true"
          :disabled="true"
        ></mt-select>
      </mt-form-item> -->
      <mt-form-item v-if="!isPerformanceResult" prop="month" :label="$t('月份')">
        <mt-date-picker
          v-model="quotaFormModel.month"
          format="yyyy-MM"
          start="Year"
          depth="Year"
          :show-clear-button="true"
          :placeholder="$t('选择筛选月份')"
        ></mt-date-picker>
      </mt-form-item>
      <mt-form-item v-if="!isPerformanceResult" prop="siteCode" :label="$t('工厂')">
        <mt-multi-select
          v-model="quotaFormModel.siteCode"
          :data-source="factoryData"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :fields="{ text: 'dimensionNameValue', value: 'dimensionCodeValue' }"
          :enable-group-check-box="true"
          mode="CheckBox"
          :placeholder="$t('请选择工厂')"
          @change="changeFactory"
        ></mt-multi-select>
      </mt-form-item>
      <mt-form-item v-if="!isPerformanceResult" prop="categoryCode" :label="$t('品类')">
        <mt-multi-select
          v-model="quotaFormModel.categoryCode"
          :data-source="categoryData"
          :show-clear-button="true"
          :allow-filtering="true"
          :filtering="remoteCategoryList"
          :fields="{ text: 'categoryName', value: 'categoryCode' }"
          :enable-group-check-box="true"
          :disabled="isDiabledCategory"
          mode="CheckBox"
          :placeholder="$t('请选择品类')"
          @change="changeCategory"
        ></mt-multi-select>
      </mt-form-item>
      <mt-form-item v-if="!isPerformanceResult" prop="itemCode" :label="$t('物料')">
        <mt-multi-select
          v-model="quotaFormModel.itemCode"
          :data-source="itemData"
          :show-clear-button="true"
          :allow-filtering="true"
          :filtering="remoteItemList"
          :fields="{ text: 'itemName', value: 'itemCode' }"
          :enable-group-check-box="true"
          :disabled="isDiabledCategory"
          mode="CheckBox"
          :placeholder="$t('请选择物料')"
        ></mt-multi-select>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { debounce, formateTime, isEmpty } from '@/utils/utils'
import { statusList } from '../config'

const getNextMonth = () => {
  const nowdate = new Date()
  nowdate.setMonth(nowdate.getMonth() + 1, 1)
  const y = nowdate.getFullYear()
  const m = nowdate.getMonth() + 1
  return y + '-' + m
}

export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: this.$t('任务新增'),
      orgFields: null,
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      taskTypeCode: '',
      perfFormModel: {
        taskTypeId: '',
        orgIdList: [],
        supplierCodeList: [],
        templateType: '',
        evaluationCycle: '',
        achievementsStartMonth: '',
        achievementsEndMonth: '',
        evaluateYear: '',
        categoryId: '',
        categoryName: '',
        categoryCode: ''
      },
      quotaFormModel: {
        orgIdList: [],
        taskTypeId: '',
        remark: '',
        createUserName: '',
        createTime: '',
        month: getNextMonth(),
        siteCode: '',
        categoryCode: null,
        itemCode: null
      },
      perfRules: {
        taskTypeId: [
          {
            required: true,
            message: this.$t('任务名称不能为空'),
            trigger: 'blur'
          }
        ],
        orgIdList: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        templateType: [
          {
            required: true,
            message: this.$t('请选择模板类型'),
            trigger: 'blur'
          }
        ],
        evaluationCycle: [
          {
            required: true,
            message: this.$t('请选择评价周期'),
            trigger: 'blur'
          }
        ],
        evaluateYear: [
          {
            required: true,
            message: this.$t('请选择评价年份'),
            trigger: 'blur'
          }
        ],
        achievementsStartMonth: [
          {
            required: true,
            message: this.$t('请选择绩效月份起'),
            trigger: 'blur'
          }
        ],
        achievementsEndMonth: [
          {
            required: true,
            message: this.$t(''),
            trigger: 'blur'
          }
        ]
      },
      quotaRules: {
        taskTypeId: [
          {
            required: true,
            message: this.$t('任务名称不能为空'),
            trigger: 'blur'
          }
        ],
        orgIdList: [
          {
            required: true,
            message: this.$t('请选择组织'),
            trigger: 'blur'
          }
        ],
        evaluateYear: [
          {
            required: true,
            message: this.$t('请选择评价年份'),
            trigger: 'blur'
          }
        ],
        month: [
          {
            required: true,
            message: this.$t('筛选月份不能为空'),
            trigger: 'blur'
          }
        ],
        siteCode: [
          {
            required: true,
            message: this.$t('工厂不能为空'),
            trigger: 'blur'
          }
        ]
      },
      statusList,
      taskTypeData: [],
      orgData: [],
      supplierData: [],
      templateTypeData: [],
      evaluationCycleData: [],
      detailEvaluationCycleData: [], // 分层分级，评价周期
      detailsEvaluateYearData: [], // 分层分级，评价年份
      categoryListArrList: [], // 分层分级 品类
      factoryData: [], // 工厂数据
      categoryData: [], // 品类数据
      itemData: [], // 物料数据
      isEdit: false,
      isDiabledCategory: false // 根据登录用户角色权限判断是否要disabled品类和物料选择
    }
  },
  computed: {
    isShowTemplate() {
      // return ['JSDF', 'DDFMX', 'ZHJXJS', 'JSFW'].includes(this.taskTypeCode)
      return ['JSDF', 'DDFMX', 'ZHJXJS'].includes(this.taskTypeCode)
    },
    isShowSupplier() {
      // return this.taskTypeCode !== 'JSFW'
      return !['JSDF', 'PLFCFJ'].includes(this.taskTypeCode)
    },
    isShowLayeredAndGraded() {
      return this.taskTypeCode === 'PLFCFJ'
    },
    isPerformanceResult() {
      return this.taskTypeCode === 'JSJGHZ'
    },
    performanceEvaluateEnum() {
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      return [
        { text: `${currentYear}H1`, value: `${currentYear}H1` },
        { text: `${currentYear}H2`, value: `${currentYear}H2` },
        { text: `${currentYear - 1}H1`, value: `${currentYear - 1}H1` },
        { text: `${currentYear - 1}H2`, value: `${currentYear - 1}H2` }
      ]
    },
    taskList() {
      const { id, itemCode, itemName } = this.taskTypeData?.find(
        (item) => item.id === this.taskTypeId
      )

      return {
        taskTypeCode: itemCode,
        taskTypeId: id,
        taskTypeName: itemName
      }
    },
    orgList() {
      // 处理提交的orgList数据
      const _map = []
      const _orgData = this.flatTreeData(this.orgData)
      _orgData?.map((item) => {
        if (
          this.perfFormModel.orgIdList?.includes(item.id) ||
          this.quotaFormModel.orgIdList?.includes(item.id)
        ) {
          const { orgCode, id, orgLevel, orgName, orgType } = item
          _map.push({
            orgCode: orgCode,
            orgId: id,
            orgLevel: orgLevel,
            orgName: orgName,
            orgType: orgType
          })
        }
      })
      return _map
    },
    supplierList() {
      // 处理提交的orgList数据
      const _map = []
      this.supplierData?.map((item) => {
        if (this.perfFormModel.supplierCodeList?.includes(item.supplierCode)) {
          const { supplierCode, id, supplierName } = item
          _map.push({
            supplierCode: supplierCode || '',
            supplierId: id || '',
            supplierName: supplierName || ''
          })
        }
      })
      return _map
    },
    // 判断是绩效任务还是配额任务
    isPerformanceTask() {
      return (
        ['JSDF', 'DDFMX', 'ZHJXJS', 'JSFW', 'PLFCFJ'].includes(this.taskTypeCode) ||
        !this.taskTypeCode
      )
    },
    taskTypeId() {
      return this.quotaFormModel.taskTypeId || this.perfFormModel.taskTypeId
    }
  },
  created() {
    this.getLogerRole()
    this.getSelectDictList()
    this.getOrgList()
    this.getFactoryData()
    this.remoteSupplierList = debounce(this.getSupplierList, 300)
    this.remoteCategoryList = debounce(this.getCategoryData, 300)
    this.remoteItemList = debounce(this.getItemData, 300)
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.perfRuleForm.resetFields()

    // this.filteringList({ text: this.perfFormModel.categoryCode })
    this.getCategorys('')
  },
  watch: {
    'perfFormModel.orgIdList': {
      handler() {
        this.$nextTick(() => {
          this.$refs.perfRuleForm?.validateField('orgIdList')
        })
      }
    },
    'quotaFormModel.orgIdList': {
      handler() {
        this.$nextTick(() => {
          this.$refs.quotaRuleForm?.validateField('orgIdList')
        })
      }
    },
    'perfFormModel.evaluationCycle': {
      handler() {
        if (!this.perfFormModel.achievementsStartMonth) return
        this.perfFormModel.achievementsEndMonth = this.ctrlDateTime(
          this.perfFormModel.achievementsStartMonth,
          'dateStart'
        )
      }
    },
    'perfFormModel.orgIdArr': {
      handler() {
        this.$nextTick(() => {
          this.$refs.dialogRef.validateField('orgIdArr')
        })
      }
    },
    isPerformanceTask() {
      this.$refs.perfRuleForm?.resetFields()
      this.$refs.quotaRuleForm?.resetFields()
    }
  },
  methods: {
    // 选择品类
    selectCategoryas(e) {
      this.formObject.categoryCode = e.itemData.categoryCode
      this.formObject.categoryId = e.itemData.id
      this.formObject.categoryName = e.itemData.categoryName
    },
    // // 分层分级 品类
    // filteringList: throttle(function (e) {
    //   let { text } = e
    //   if (text) {
    //     let params = {
    //       page: {
    //         current: 1,
    //         size: 9999
    //       }
    //     }
    //     const rules = []
    //     const columnData = ['categoryCode', 'categoryName']
    //     for (let i = 0; i < columnData.length; i++) {
    //       const field = columnData[i]
    //       let obj = {
    //         field,
    //         label: '',
    //         operator: field.includes('Code') ? 'equal' : 'contains',
    //         type: 'string',
    //         value: text
    //       }
    //       rules.push(obj)
    //     }
    //     if (rules.length) {
    //       params.condition = 'or'
    //       params.rules = rules
    //     }
    //     this.$api.pqTasckCenter.queryPermissionCategories(params).then((res) => {
    //       const { code, data } = res
    //       if (code === 200) {
    //         this.categoryListArrList = data.records.map((i) => {
    //           return {
    //             ...i,
    //             textAndValue: `${i.categoryCode}-${i.categoryName}`
    //           }
    //         })
    //         this.noRecordsTemplate = '没有找到记录'
    //       }
    //     })
    //   }
    // }, 300),
    // 品类下拉框事件
    getCategorys(val) {
      this.$api.pqTasckCenter
        .getCategoryListAll({
          condition: 'and',
          page: {
            current: 1,
            size: 10000
          },
          pageFlag: false,
          rules: [
            {
              field: 'tree_level',
              operator: 'equal',
              type: 'int',
              value: 1
            },
            {
              condition: 'and',
              rules: [
                {
                  field: 'categoryCode',
                  type: 'string',
                  operator: 'contains',
                  value: val
                },
                {
                  condition: 'or',
                  field: 'categoryName',
                  type: 'string',
                  operator: 'contains',
                  value: val
                }
              ]
            }
          ]
        })
        .then((res) => {
          // this.categoryList = res.data.records
          this.categoryListArrList = res.data.records.map((i) => {
            return { ...i, textAndValue: `${i.categoryCode} - ${i.categoryName}` }
          })
        })
    },
    // 评价年份动态变化
    detailsToBeScoredModelChange(e) {
      const { itemData } = e
      const date = new Date()
      const year = date.getFullYear()
      if (itemData.itemCode === 'YEAR') {
        this.detailsEvaluateYearData = [
          { text: `${year - 1}`, value: `${year - 1}` },
          { text: `${year}`, value: `${year}` },
          { text: `${year + 1}`, value: `${year + 1}` }
        ]
      } else if (itemData.itemCode === 'HALF-YEAR') {
        this.detailsEvaluateYearData = [
          { text: `${year - 1}` + 'H1', value: `${year - 1}` + 'H1' },
          { text: `${year}` + 'H1', value: `${year}` + 'H1' },
          { text: `${year + 1}` + 'H1', value: `${year + 1}` + 'H1' }
        ]
      }
    },
    mapCategory(list = []) {
      const res = []
      list.forEach((item) => {
        const exitItem = this.categoryListArrList.find((el) => el.id === item)
        if (exitItem) {
          res.push({
            categId: exitItem.id,
            categCode: exitItem.categoryCode,
            categName: exitItem.categoryName
          })
        }
      })
      return res
    },
    getLogerRole() {
      this.$api.pqTasckCenter.queryLogerRole().then((res) => {
        if (res.code == 200) {
          this.isDiabledCategory = res.data === 'yes'
        }
      })
    },
    // 扁平化树形结构数据
    flatTreeData(tree, children_key = 'childrenList') {
      if (!tree || Object.prototype.toString.call(tree) !== '[object Array]' || tree.length <= 0)
        return []
      return tree.reduce(
        (pre, cur) => pre.concat(cur, this.flatTreeData(cur[children_key], children_key)),
        []
      )
    },
    // 弹框 - 开启
    onOpen(args) {
      args.preventFocus = true
      this.$refs.perfRuleForm.clearValidate()
    },
    // 弹框 - 关闭
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    handleConfirm() {
      this.isPerformanceTask ? this.handlePerfConfirm() : this.handleQuotaConfirm()
    },
    // 弹框 - 确认
    handlePerfConfirm() {
      this.$refs.perfRuleForm.validate((valid) => {
        if (!valid) return
        let month = new Date(this.perfFormModel.achievementsStartMonth).getMonth() + 1
        let evaluationCycle = this.perfFormModel.evaluationCycle
        if (!this.isShowLayeredAndGraded) {
          if (evaluationCycle === 'YEAR' && month != 1) {
            this.$toast({
              content: this.$t('评价周期为年度时，绩效起始月份只可选择1月'),
              type: 'warning'
            })
            return
          } else if (evaluationCycle === 'HALF-YEAR' && month != 1 && month != 7) {
            this.$toast({
              content: this.$t('评价周期为半年度时，绩效起始月份只可选择1月或7月'),
              type: 'warning'
            })
            return
          } else if (
            evaluationCycle === 'SEASON' &&
            month != 1 &&
            month != 4 &&
            month != 7 &&
            month != 10
          ) {
            this.$toast({
              content: this.$t('评价周期为季度时，绩效起始月份只可选择1月、4月、7月或10月'),
              type: 'warning'
            })
            return
          }
        }

        const _addForm = {}
        for (var key in this.perfFormModel) {
          if (Object.hasOwnProperty.call(this.perfFormModel, key)) {
            if (key !== 'taskTypeId') {
              if (key === 'orgIdList') {
                _addForm['orgList'] = this.orgList
              } else if (key === 'supplierCodeList') {
                _addForm['supplierList'] = this.supplierList
              } else if (key === 'categoryList') {
                const tempList = this.perfFormModel[key] || []
                _addForm[key] = this.mapCategory(tempList)
              } else {
                _addForm[key] = this.perfFormModel[key]
              }
            }
          }
        }
        const postData = {
          ...this.taskList,
          evaluateYear: this.taskList.evaluateYear,
          evaluationCycle: this.taskList.evaluationCycle,
          parameter: {
            ..._addForm,
            achievementsEndMonth: this.perfFormModel.achievementsEndMonth
              ? formateTime(new Date(this.perfFormModel.achievementsEndMonth), 'yyyy-MM') + '-01'
              : '',
            achievementsStartMonth: this.perfFormModel.achievementsStartMonth
              ? formateTime(new Date(this.perfFormModel.achievementsStartMonth), 'yyyy-MM') + '-01'
              : ''
          }
        }
        this.$api.pqTasckCenter
          .saveTask(postData)
          .then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
          .catch(() => {
            this.$emit('handleAddDialogShow', false)
          })
      })
    },
    handleQuotaConfirm() {
      console.log('handleAddDialogShow', this.quotaFormModel)
      this.$refs.quotaRuleForm.validate((valid) => {
        if (!valid) return
        const siteList = this.filterFactoryName(this.quotaFormModel.siteCode)
        const categoryList = this.filterCategoryName(this.quotaFormModel.categoryCode)
        const itemList = this.filterItemName(this.quotaFormModel.itemCode)
        const month = formateTime(this.quotaFormModel.month, 'yyyy-MM')

        const postData = {
          ...this.taskList,
          parameter: {
            yearMonth: month,
            site: {
              selectAll: !siteList?.length,
              dataList: siteList
            },
            category: {
              selectAll: this.isDiabledCategory || !categoryList?.length,
              dataList: categoryList
            },
            item: {
              selectAll: this.isDiabledCategory || !itemList?.length,
              dataList: itemList
            }
          }
        }
        const params = {
          parameter: {
            ...this.quotaFormModel,
            orgList: this.orgList,
            achievementsEndMonth: this.perfFormModel.achievementsEndMonth
              ? formateTime(new Date(this.perfFormModel.achievementsEndMonth), 'yyyy-MM') + '-01'
              : '',
            achievementsStartMonth: this.perfFormModel.achievementsStartMonth
              ? formateTime(new Date(this.perfFormModel.achievementsStartMonth), 'yyyy-MM') + '-01'
              : ''
          },
          taskTypeId: this.quotaFormModel.taskTypeId,
          taskTypeCode: this.taskTypeCode,
          taskTypeName: this.taskList.taskTypeName
        }
        delete params.parameter.month
        this.$api.pqTasckCenter[this.taskTypeCode === 'JSJGHZ' ? 'saveTask' : 'saveQuotaTask'](
          this.taskTypeCode === 'JSJGHZ' ? params : postData
        )
          .then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess')
            }
          })
          .catch(() => {
            this.$emit('handleAddDialogShow', false)
          })
      })
    },
    // 任务名称切换
    taskTypeChange(v) {
      if (v.e) {
        this.taskTypeCode = v.itemData?.itemCode
        this.perfFormModel.taskTypeId = v.itemData?.id
        this.quotaFormModel.taskTypeId = v.itemData?.id
      }
    },
    changeFactory(list) {
      console.log('factory-list: ', list)
      this.quotaFormModel.categoryCode = null
      this.quotaFormModel.itemCode = null
      this.getCategoryAllData()
    },
    changeCategory() {
      this.quotaFormModel.itemCode = null
    },
    filterFactoryName(list) {
      const selectedList = this.factoryData.filter((item) => list.includes(item.dimensionCodeValue))
      return selectedList.map((item) => {
        return {
          id: item.dimensionIdValue,
          name: item.dimensionNameValue,
          code: item.dimensionCodeValue
        }
      })
    },
    filterCategoryName(list) {
      const selectedList = this.categoryData.filter((item) => list.includes(item.categoryCode))
      return selectedList.map((item) => {
        return {
          id: item.categoryId,
          name: item.categoryName,
          code: item.categoryCode
        }
      })
    },
    filterItemName(list) {
      const selectedList = this.itemData.filter((item) => list.includes(item.itemCode))
      return selectedList.map((item) => {
        return {
          id: item.itemId,
          name: item.itemName,
          code: item.itemCode
        }
      })
    },
    getOrgList() {
      this.$api.pqTasckCenter.getStructureListOrgForSpecial().then((res) => {
        if (res.code == 200) {
          this.orgData = res.data
          this.orgFields = {
            dataSource: res.data,
            value: 'id',
            text: 'orgName',
            child: 'childrenList'
          }
        }
      })
    },

    // 获取任务名称
    async getSelectDictList() {
      this.taskTypeData = await this.getDictItems('TASK')
      this.templateTypeData = await this.getDictItems('MB-TYPE')
      this.evaluationCycleData = await this.getDictItems('EVALUATION-PERIOD')
      this.detailEvaluationCycleData = await this.getDictItems('PER_EVALUATION')
      // this.perfFormModel.taskTypeId = this.taskTypeData[0]?.id
    },
    dateStartChange(date) {
      this.perfFormModel.achievementsEndMonth = this.ctrlDateTime(date, 'dateStart')
    },
    dateEndChange() {
      // this.perfFormModel.startDate = this.ctrlDateTime(date, 'dateEnd')
    },
    ctrlDateTime(date, type) {
      const _period = this.perfFormModel.evaluationCycle
      const _date = formateTime(date, 'yyyy-MM')
      let _time = ''

      switch (_period) {
        case 'MONTH':
          _time = this.calculateDate(_date, 0, type)
          break
        case 'YEAR':
          _time = this.calculateDate(_date, 11, type)
          break
        case 'HALF-YEAR':
          _time = this.calculateDate(_date, 5, type)
          break
        case 'SEASON':
          _time = this.calculateDate(_date, 2, type)
          break
        default:
          _time = this.calculateDate(_date, 0, type)
      }
      return _time
    },
    calculateDate(date, num, type) {
      let arr = date.split('-')
      let year = parseInt(arr[0])
      let month = parseInt(arr[1])
      month = type === 'dateStart' ? month + num : month - num
      if (month > 12) {
        let yearNum = parseInt((month - 1) / 12)
        month = month % 12 == 0 ? 12 : month % 12
        year += yearNum
      } else if (month <= 0) {
        month = Math.abs(month)
        let yearNum = parseInt((month + 12) / 12)
        let n = month % 12
        if (n == 0) {
          year -= yearNum
          month = 12
        } else {
          year -= yearNum
          month = Math.abs(12 - n)
        }
      }
      month = month < 10 ? '0' + month : month
      return year + '-' + month
    },

    // 根据名称获取字典数据
    getDictItems(key) {
      return this.$api.pqTasckCenter
        .getDictCode({
          dictCode: key,
          nameLike: ''
        })
        .then((res) => {
          if (res.code === 200 && !isEmpty(res.data)) {
            return res.data
          }
        })
    },
    // 获取供应商列表数据
    getSupplierList(e) {
      this.$api.pqTasckCenter.getSupplierList({ fuzzyNameOrCode: e.text }).then((res) => {
        if (res.code === 200) {
          this.supplierData = res.data
        }
      })
    },
    // 获取工厂列表数据
    getFactoryData() {
      // const params = {
      //   dimensionCode: 'SITE',
      //   subjectId: 0,
      //   subjectType: 0
      // }
      // this.$api.pqTasckCenter.queryFactory(params).then((res) => {
      //   if (res.code === 200) {
      //     this.factoryData = res.data
      //   }
      // })
      const params = {
        orgCode: 'SITE'
      }
      this.$api.pqTasckCenter.getOrgListByCode(params).then((res) => {
        if (res.code === 200) {
          this.factoryData = res.data
        }
      })
    },
    getCategoryAllData() {
      if (!this.quotaFormModel.siteCode?.length) return
      const params = {
        codeList: this.quotaFormModel.siteCode,
        field: 'code'
      }
      this.$api.pqTasckCenter.getCategoryData(params).then((res) => {
        if (res.code === 200) {
          this.categoryAllData = res.data || []
        }
      })
    },
    // 获取品类列表数据
    getCategoryData(e) {
      if (!this.quotaFormModel.siteCode?.length) return
      const params = {
        codeList: this.quotaFormModel.siteCode,
        fuzzyName: e.text
      }
      this.$api.pqTasckCenter.getCategoryData(params).then((res) => {
        if (res.code === 200) {
          this.categoryData = res.data || []
        }
      })
    },
    // 获取物料列表数据
    getItemData(e) {
      if (!this.quotaFormModel.siteCode?.length) return
      const selectedCategroy = this.quotaFormModel.categoryCode
      const codeList = selectedCategroy?.length
        ? selectedCategroy
        : this.categoryAllData.map((item) => item.categoryCode)
      const params = {
        codeList,
        fuzzyName: e.text,
        factoryList: this.quotaFormModel.siteCode
      }
      this.$api.pqTasckCenter.getItemData(params).then((res) => {
        if (res.code === 200) {
          this.itemData = res.data
        }
      })
    }
  }
}
</script>

<style>
.create-proj-dialog .e-dlg-content {
  padding-top: 20px !important;
}
</style>
