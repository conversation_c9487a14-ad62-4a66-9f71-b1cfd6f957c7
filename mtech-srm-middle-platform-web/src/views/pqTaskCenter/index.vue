<template>
  <div class="task-center">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-template-page>

    <add-dialog
      class="task-dialog"
      v-if="addDialogShow"
      :dialog-data="dialogData"
      :type="this.type"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
    <!-- 文件弹窗 -->
    <uploader-dialog ref="uploaderDialog"></uploader-dialog>
  </div>
</template>

<script>
import { pageConfig } from './config'
import addDialog from './components/addDialog.vue'
import UploaderDialog from '@/components/Upload/uploaderDialog'
import { download } from '@/utils/utils'
export default {
  components: {
    addDialog: addDialog,
    UploaderDialog
  },
  props: {
    role: {
      type: String,
      default: 'normal'
    }
  },
  data() {
    return {
      pageConfig: pageConfig(this.role),
      type: '0',
      addDialogShow: false,
      dialogData: null
    }
  },
  created() {
    if (this.$route.query && this.$route.query.taskCode) {
      this.pageConfig[0].grid.asyncConfig.rules = [
        {
          field: 'taskCode',
          operator: 'equal',
          value: this.$route.query.taskCode
        }
      ]
    }
  },
  mounted() {
    if (window.taskCenterInterval) {
      window.clearInterval(window.taskCenterInterval)
    }
    window.taskCenterInterval = setInterval(() => {
      this.refreshTableData()
    }, 10000)
  },
  beforeDestroy() {
    window.clearInterval(window.taskCenterInterval)
  },
  deactivated() {
    window.clearInterval(window.taskCenterInterval)
  },
  methods: {
    handleClickToolBar(e) {
      if (e.gridRef.getMtechGridRecords().length <= 0 && !(e.toolbar.id == 'Add')) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      let _id = [],
        _status = []
      e.gridRef.getMtechGridRecords().map((item) => {
        _id.push(item.id)
        _status.push(item.status)
      })
      if (e.toolbar.id == 'Add') {
        this.handleAdd()
      }
    },
    handleClickCellTitle(args) {
      const { field, data } = args
      if (field === 'operatorView') {
        // 文件弹框
        this.handleFileDialog(data.fileList)
      }
      if (field === 'operatorDownload') {
        // 全部下载
        // this.handleDownload(data.fileList)
        data.fileList.forEach((itemFiles) => {
          if (itemFiles.progress === undefined) {
            this.handlerDownloadPrivateFile(itemFiles)
          }
        })
      }
    },
    handlerDownloadPrivateFile(data) {
      this.$loading()
      this.$api.fileService
        .downloadPrivateFile({
          id: data.sysFileId || data.id
        })
        .then((res) => {
          download({ fileName: data.fileName, blob: res.data })
          if (res.data.size) {
            this.$hloading()
          }
        })
        .finally(() => {
          this.$hloading()
        })
    },
    // 点击文件数量
    handleFileDialog(data) {
      if (!data || data.length <= 0) return
      const fileData = data.map((item) => {
        return {
          id: item.id,
          fileName: item.fileName,
          url: item.url
        }
      })

      const dialogParams = {
        fileData,
        isView: true,
        title: this.$t('文件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },
    handleAdd() {
      this.addDialogShow = true
      this.dialogData = {
        dialogType: 'add'
      }
    },

    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    },
    refreshTableData() {
      this.$refs.templateRef && this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>
<style lang="scss">
.task-center {
  // height: 100%;
  .mt-select-index {
    float: left;
  }
}
#middlePlatform > .mt-loading {
  position: fixed;
}
</style>
