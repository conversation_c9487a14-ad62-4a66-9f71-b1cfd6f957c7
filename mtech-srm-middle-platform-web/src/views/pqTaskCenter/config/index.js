import { i18n } from '@/main.js'
import Vue from 'vue'

export const statusList = [
  { value: 0, text: i18n.t('进行中'), cssClass: '' },
  { value: 1, text: i18n.t('已完成'), cssClass: '' },
  { value: 2, text: i18n.t('失败'), cssClass: '' }
]
// list toolbar
export const toolbar = [{ id: 'Add', icon: 'icon_solid_Createorder', title: '新增' }]
// list column
const columnData = [
  {
    field: 'taskCode',
    headerText: i18n.t('任务ID')
  },
  {
    field: 'taskName',
    headerText: i18n.t('任务名称')
  },
  {
    field: 'progressPercent',
    headerText: i18n.t('进度比例'),
    template: () => {
      return {
        template: Vue.component('headers', {
          template: `
            <Progress v-if="data.progressPercent" :percent="percent" />
          `,
          data() {
            return {
              data: {},
              percent: 0
            }
          },
          mounted() {
            if (this.data.progressPercent) {
              let arr = this.data.progressPercent.split('/')
              if (arr[0] !== 'null') {
                this.percent = (arr[0] / arr[1]) * 100
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'displayParameter',
    headerText: i18n.t('参数')
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },
  {
    field: 'fileList',
    headerText: i18n.t('文件数'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        return data?.length ? `${data.length}` : `0`
      }
    },
    ignore: true
  },
  {
    field: 'operator',
    headerText: i18n.t('操作'),
    // cellTools: [],
    template: () => {
      return {
        template: Vue.component('operator', {
          template: `
        <div>
            <mt-button :disabled="!data.fileList || !data.fileList.length" type='text' style="margin-right: 5px" :plain="true" @click='handleView'>{{ i18n.t("查看") }}</mt-button>
            <mt-button :disabled="!data.fileList || !data.fileList.length" type='text' :plain="true" @click='handleDownload'>{{ i18n.t("下载") }}</mt-button>
        </div>
                   `,
          data: function () {
            return {
              data: {},
              i18n
            }
          },
          methods: {
            handleView() {
              this.$parent.$emit('handleClickCellTitle', {
                field: 'operatorView',
                data: { ...this.data }
              })
            },
            handleDownload() {
              this.$parent.$emit('handleClickCellTitle', {
                field: 'operatorDownload',
                data: { ...this.data }
              })
            }
          }
        })
      }
    },
    ignore: true
  },
  {
    field: 'responseContent',
    headerText: i18n.t('返回消息')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间')
  }
]
// list pageConfig
export const pageConfig = (role) => [
  {
    toolbar: toolbar,
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    gridId: 'd3419706-4272-44c0-903e-279e67b355fc',
    grid: {
      columnData,
      asyncConfig: {
        url:
          role === 'super'
            ? 'platform/tenant/task/administratorsQueryBuilder'
            : 'platform/tenant/task/queryBuilder',
        params: {}
      }
    }
  }
]
