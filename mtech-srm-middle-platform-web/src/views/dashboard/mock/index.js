export const taskList = [
  {
    icon: 'mt-icon-icon_Require',
    title: '需求',
    list: [
      {
        content: '处理中需求',
        number: '51'
      },
      {
        content: '被驳回需求',
        number: '2'
      },
      {
        content: '已下单需求',
        number: '51'
      },
      {
        content: '待收货需求',
        number: '51'
      },
      {
        content: '已完成需求',
        number: '10'
      }
    ]
  },
  {
    icon: 'mt-icon-caigou-02',
    title: '采购单',
    list: [
      {
        content: '处理中采购单',
        number: '51'
      },
      {
        content: '被驳回采购单',
        number: '2'
      },
      {
        content: '已下单采购单',
        number: '51'
      },
      {
        content: '已完成订单',
        number: '51'
      }
    ]
  },
  {
    icon: 'mt-icon-shouhou-02',
    title: '售后',
    list: [
      {
        content: '处理中售后单',
        number: '51'
      },
      {
        content: '被驳回售后单',
        number: '2'
      },
      {
        content: '带邮寄售后单',
        number: '51'
      },
      {
        content: '待确认售后单',
        number: '51'
      },
      {
        content: '已完成售后单',
        number: '10'
      }
    ]
  },
  {
    icon: 'mt-icon-caiwu-02',
    title: '财务',
    list: [
      {
        content: '可开票订单',
        number: '￥850032.5'
      },
      {
        content: '将要逾期订单',
        number: '￥10260'
      },
      {
        content: '已逾期订单',
        number: '￥10105'
      }
    ]
  }
]

export const todoList = [
  {
    title: '待审批',
    number: '5',
    list: [
      {
        type: '采购申请',
        title: '张张提交了立项审批',
        time: '2021-08-03 12:22'
      },
      {
        type: '评标管理',
        title: '周周新增了评标管理',
        time: '2021-08-02 15:35'
      },
      {
        type: '采购申请',
        title: '胡胡提交了立项审批',
        time: '2021-08-01 17:58'
      }
    ]
  },

  {
    title: '待办任务',
    number: '2',
    list: [
      {
        type: '采购执行',
        title: '王王提交了采购申请',
        time: '2021-08-03 11:55'
      },
      {
        type: '评标管理',
        title: '张张提交了立项审批',
        time: '2021-08-02 08:36'
      },
      {
        type: '采购申请',
        title: '周周提交了采购申请',
        time: '2021-08-02 07:54'
      }
    ]
  }
]

export const fileData = [
  {
    id: getRandom(),
    belongTo: '供应商',
    fileType: 'PPT',
    fileName: '演示.ppt',
    version: '0.1.2'
  },
  {
    id: getRandom(),
    belongTo: '报价',
    fileType: 'WORD',
    fileName: '报价整理.doc',
    version: '0.1.3'
  },
  {
    id: getRandom(),
    belongTo: '供应商',
    fileType: 'PDF',
    fileName: '供应商信息.pdf',
    version: '2.0.0'
  },
  {
    id: getRandom(),
    belongTo: '报价',
    fileType: 'EXCEL',
    fileName: '价格汇总.xlsx',
    version: '0.0.1'
  }
]

export const calendarList = [
  {
    id: getRandom(),
    date: '2021-08-03',
    type: 0,
    content: '寻源需求提交上级管理并审批寻源需求提交上级管理并审批'
  },
  {
    id: getRandom(),
    date: '2021-08-03',
    type: 1,
    content: '配置阶段，允许从以下阶段进入：阶段一,阶段三....'
  },
  {
    id: getRandom(),
    date: '2021-08-02',
    type: 0,
    content: '提交邀请，邀请的供应商名称为‘仓南兴东印务有限公司’'
  },
  {
    id: getRandom(),
    date: '2021-08-02',
    type: 1,
    content: '上海钢联电子商务股份有限公司'
  }
]

export const noticeList = [
  {
    id: getRandom(),
    date: '2021-08-03',
    type: 0,
    content: '配置阶段，允许从以下阶段进入：阶段一,阶段三....'
  },
  {
    id: getRandom(),
    date: '2021-08-03',
    type: 1,
    content: '寻源需求提交上级管理并审批寻源需求提交上级管理并审批'
  },
  {
    id: getRandom(),
    date: '2021-08-02',
    type: 0,
    content: '物料组织关系配置和物料主数据信息汇总'
  },
  {
    id: getRandom(),
    date: '2021-08-02',
    type: 1,
    content: '组织管理的分类和配置规则'
  },
  {
    id: getRandom(),
    date: '2021-08-02',
    type: 1,
    content: '待审批的任务需在当月完成'
  }
]

export const newTodoList = [
  {
    title: '待处理列表',
    number: '5',
    list: [
      {
        type: '0',
        title: '张张提交了立项审批张张提交了立项审批张张提交了立项审批张张提交了立项审批',
        time: '2021-08-03 12:22'
      },
      {
        type: '1',
        title: '周周新增了评标管理张张提交了立项审批张张提交了立项审批张张提交了立项审批',
        time: '2021-08-02 15:35'
      },
      {
        type: '2',
        title: '胡胡提交了立项审批张张提交了立项审批张张提交了立项审批张张提交了立项审批',
        time: '2021-08-01 17:58'
      }
    ]
  },

  {
    title: '已处理列表',
    number: '2',
    list: [
      {
        type: '1',
        title: '王王提交了采购申请',
        time: '2021-08-03 11:55'
      },
      {
        type: '2',
        title: '张张提交了立项审批',
        time: '2021-08-02 08:36'
      },
      {
        type: '0',
        title: '周周提交了采购申请',
        time: '2021-08-02 07:54'
      }
    ]
  }
]

export const announcementList = [
  {
    type: '0',
    title: '这是一个信息消息信息文案的最长演，这是一个信息消息信息文案的最长演',
    time: '2021-08-03 12:22'
  },
  {
    type: '1',
    title: '这是一个信息消息信息文案的最长演，这是一个信息消息信息文案的最长演',
    time: '2021-08-02 15:35'
  },
  {
    type: '2',
    title: '这是一个信息消息信息文案的最长演，这是一个信息消息信息文案的最长演',
    time: '2021-08-01 17:58'
  }
]

function getRandom() {
  return Math.floor(Math.random() * 1000000)
}
