<template>
  <div class="task-item">
    <div class="task-header">
      <i v-if="item.icon" :class="['mt-icons', item.icon]"></i>
      <span>{{ item.title }}</span>
    </div>
    <ul v-if="item.list" class="task-ul">
      <li v-for="(liItem, index) in item.list" :key="index" class="task-li">
        <span>{{ liItem.content }}</span>
        <span class="task-number">{{ liItem.number }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.task-item {
  flex: 1;
  width: 316px;
  height: 310px;
  background: #fff;
  margin: 0 8px 8px 0;
  &:last-child {
    margin-right: 12px;
  }
  // 4个的list
  .task-header {
    padding: 0 20px;
    font-size: 18px;
    color: #292929;
    font-weight: 600;
    line-height: 60px;
    background: rgba(99, 134, 193, 0.06);
    .mt-icons {
      color: #00469c;
      margin-right: 20px;
    }
  }
  .task-ul {
    padding: 0 20px;
    .task-li {
      font-size: 14px;
      color: #232b39;
      display: flex;
      justify-content: space-between;
      line-height: 45px;
      border-bottom: 1px solid rgba(99, 134, 193, 0.06);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .task-number {
        color: #00469c;
        font-weight: 600;
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
