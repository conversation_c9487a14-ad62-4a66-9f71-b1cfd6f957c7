<template>
  <div class="work-list-item todo-list-item">
    <div class="todo-header">
      <div>
        <span class="todo-header-title">{{ item.title }}</span>
        <span class="todo-header-number">{{ item.number }}</span>
      </div>
      <i class="mt-icons mt-icon-icon_More icon-more"></i>
    </div>
    <ul v-if="item.list" class="todo-ul">
      <li v-for="(liItem, index) in item.list" :key="index" class="todo-li">
        <div class="todo-li-cont">
          <div class="todo-li-type">{{ liItem.type }}</div>
          <span class="todo-li-title">{{ liItem.title }}</span>
        </div>
        <span class="todo-li-time">{{ liItem.time }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.work-list-item {
  flex: 1;
  width: 316px;
  height: 310px;
  background: #fff;
  margin: 0 8px 8px 0;
  &:last-child {
    margin-right: 12px;
  }

  // 下面2个的list
  .todo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    padding: 0 20px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
    .todo-header-title {
      font-size: 18px;
      color: #292929;
      font-weight: 600;
    }
    .todo-header-number {
      display: inline-block;
      padding: 1px 6px;
      color: #fff;
      background: #eda133;
      border-radius: 8px;
      margin-left: 10px;
    }
  }
  .todo-ul {
    padding: 0 20px;
    .todo-li {
      padding: 10px 0;
      display: flex;
      align-items: center;
      .todo-li-cont {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .todo-li-type {
          font-size: 14px;
          display: inline-block;
          padding: 8px 15px;
          color: #00469c;
          font-weight: 600;
          background: rgba(99, 134, 193, 0.1);
          border: 1px solid rgba(99, 134, 193, 0.3);
          border-radius: 4px;
          margin-right: 10px;
        }
        .todo-li-title {
          font-size: 16px;
          color: #232b39;
          // font-weight: 600;
        }
      }
      .todo-li-time {
        font-size: 14px;
        color: #6386c1;
      }
    }
  }
}

.icon-more {
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -2px;
  display: inline-block;
  font-size: 12px;
  transform: scale(0.3);
}
</style>
