import Vue from 'vue'
const iconSetting = {
  PPT: 'mt-icon-icon_ppt',
  WORD: 'mt-icon-icon_word',
  PDF: 'mt-icon-icon_pdf',
  EXCEL: 'mt-icon-icon_excel'
}

export const downloadColumns = [
  {
    field: 'belongTo',
    headerText: '文件归属',
    width: '150'
  },
  {
    field: 'fileType',
    width: '120',
    headerText: '文件类型',
    template: function () {
      return {
        template: Vue.component('fileTypeOption', {
          template: `<div><i :class="['mt-icons', icon]"></i><span style="margin-left: 5px">{{data.fileType}}</span></div>`,
          data() {
            return { data: { data: {} } }
          },
          computed: {
            icon() {
              const { fileType } = this.data
              return fileType ? iconSetting[fileType] : ''
            }
          }
        })
      }
    }
  },
  {
    field: 'fileName',
    width: '150',
    headerText: '文件名',
    template: function () {
      return {
        template: Vue.component('fileNameOption', {
          template: `<span style="color: #6386C1; cursor: pointer;">{{data.fileName}}</span>`,
          data() {
            return { data: { data: {} } }
          }
        })
      }
    }
  },
  {
    field: 'version',
    width: '100',
    headerText: '版本'
  },
  {
    field: 'operator',
    width: '100',
    headerText: '操作',
    template: function () {
      return {
        template: Vue.component('operatorOption', {
          template: `<span style="color: #6386C1; cursor: pointer;" @click="downloadFile(data)">下载</span>`,
          data() {
            return { data: { data: {} } }
          },
          methods: {
            downloadFile(e) {
              console.log('下载', e)
            }
          }
        })
      }
    }
  }
]
