import { i18n } from '@/main.js'
export const defaultToDoData = [
  {
    taskGroup: 'DELIVERY',
    taskGroupName: '送货协同',
    record: [
      {
        taskGroup: 'DELIVERY',
        taskGroupName: '送货协同',
        taskBusinessName: i18n.t('加载中。。。'),
        taskBusinessType: 'init',
        totalCount: '.'
      }
    ]
  },
  {
    taskGroup: 'OUTSOURCE',
    taskGroupName: '委外',
    record: [
      {
        taskGroup: 'OUTSOURCE',
        taskGroupName: '委外',
        taskBusinessName: i18n.t('加载中。。。'),
        taskBusinessType: 'init',
        totalCount: '.'
      }
    ]
  },
  {
    taskGroup: 'ORDERDELIVERY',
    taskGroupName: '订单交货',
    record: [
      {
        taskGroup: 'ORDERDELIVERY',
        taskGroupName: '订单交货',
        taskBusinessName: i18n.t('加载中。。。'),
        taskBusinessType: 'init',
        totalCount: '.'
      }
    ]
  }
]
// 送货协同 接口请求顺序
export const deliveryRequestSort = ['getDeliveryItem', 'getDeliverySynFail', 'getDeliveryShipping']
// 委外 请求的item入参顺序
export const outSourceRequestSort = [
  'getKtPickUp',
  'getKtOutCancelDirect',
  'getKtOutCancel',
  'getKtExchange',
  'getKtPickUpFail',
  'getKtPickUpComfirmed',
  'getKtOutCancelComfirmed',
  'getKtExchangeComfirmed'
]
// 订单交货 接口请求顺序
export const orderDeliveryRequestSort = [
  'getOrderComfirmed',
  'getDemandPlanReleased',
  'getDemandPlanFeedback',
  'getDemandPlanNoPrint'
]
