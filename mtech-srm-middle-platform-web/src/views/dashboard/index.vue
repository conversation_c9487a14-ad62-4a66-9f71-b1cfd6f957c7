<template>
  <div class="dashboard-page">
    <div class="dashboard-body">
      <!-- 上半部分 -->
      <div class="work-list-body">
        <div class="work-list-left">
          <div class="work-list">
            <task-item
              v-for="(task, taskIndex) in taskList"
              :key="taskIndex"
              :item="task"
            ></task-item>
          </div>
          <div class="work-list todo-list-box">
            <todo-item
              v-for="(todo, todoIndex) in todoList"
              :key="todoIndex"
              :item="todo"
            ></todo-item>
          </div>
        </div>
        <div class="work-date-box">
          <!-- <mt-select v-model="role" :data-source="roleList"></mt-select> -->
          <div class="calendar">
            <!-- <mt-calendar :show-today-button="false"></mt-calendar> -->
          </div>
          <ul class="calendar-ul">
            <li v-for="(calendar, index) in calendarList" :key="index" class="calendar-li">
              <i class="mt-icons mt-icon-icon_outline_Timeselection icon-clock"></i>
              <span class="calendar-date">{{ calendar.date }}</span>
              <span class="calendar-title">
                {{ calendar.type ? `[${$t('目标')}]` : `[${$t('内容')}]` }}
                {{ calendar.content }}</span
              >
            </li>
          </ul>
          <div class="add-calendar-btn">
            <i class="mt-icons mt-icon-icon_solid_add icon-add"></i>
            <span>{{ $t('添加新日程') }}</span>
          </div>
        </div>
      </div>
      <!-- 下半部分 -->
      <div class="data-notice-box">
        <div class="data-download-left">
          <div class="data-download-header">{{ $t('资料下载') }}</div>
          <mt-data-grid :column-data="columnData" :data-source="fileData"></mt-data-grid>
        </div>
        <div class="notice-right">
          <div class="notice-title">
            <span>{{ $t('公告') }}</span>
            <i class="mt-icons mt-icon-icon_More icon-more"></i>
          </div>
          <ul class="notice-ul">
            <li v-for="notice in noticeList" :key="notice.id" class="notice-li">
              <span class="notice-li-type">
                {{ notice.type ? `[${$t('目标')}]` : `[${$t('内容')}]` }}
              </span>
              <span class="notice-li-title">{{ notice.content }}</span>
              <span class="notice-li-date">{{ notice.date }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TaskItem from './component/task-item.vue'
import TodoItem from './component/todo-item.vue'
import { downloadColumns } from './config/columns'
import { taskList, todoList, fileData, calendarList, noticeList } from './mock/index'
export default {
  components: { TaskItem, TodoItem },
  data() {
    return {
      columnData: downloadColumns,
      fileData: fileData,
      role: 0,
      roleList: [
        {
          text: this.$t('身份：采购员'),
          value: 0
        },
        {
          text: this.$t('身份：销售员'),
          value: 1
        }
      ],
      taskList: taskList,
      todoList: todoList,
      calendarList: calendarList,
      noticeList: noticeList
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style>
.mt-icon-icon_ppt {
  color: rgb(237, 86, 51);
}
.mt-icon-icon_word {
  color: rgb(99, 134, 193);
}
.mt-icon-icon_pdf {
  color: rgb(218, 62, 26);
}
.mt-icon-icon_excel {
  color: rgb(138, 204, 64);
}
</style>

<style lang="scss" scoped>
.dashboard-page {
  padding-top: 20px;
  background: #fafafa;
  min-width: 1100px;
  overflow-x: scroll;

  /deep/.e-calendar {
    max-width: initial;
  }
  .dashboard-body {
    background: rgba(137, 120, 120, 0.06);
  }

  .icon-more {
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -2px;
    display: inline-block;
    font-size: 12px;
    transform: scale(0.3);
  }
  // 样式覆盖
  .work-date-box /deep/.e-dropdownlist.e-lib.e-input {
    font-size: 16px;
    font-weight: 600;
    text-align: center;
  }
  .work-date-box /deep/.e-input-group.e-control-wrapper.e-ddl.e-lib.e-keyboard.e-valid-input {
    border-color: #e8e8e8;
  }

  .work-list-body {
    display: flex;
    .work-list-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      .todo-list-box {
        flex: 1;
        .todo-list-item {
          height: 100%;
        }
      }
    }
    .work-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .work-date-box {
      flex-shrink: 0;
      width: 332px;
      height: 660px;
      background: #fff;
      position: relative;
      padding-top: 10px;
      .calendar {
        width: 100%;
        height: 270px;
        margin-bottom: 40px;
      }
      .calendar-ul {
        margin-bottom: 75px;
        border-top: 1px solid #e8e8e8;
        .calendar-li {
          font-size: 14px;
          color: #232b39;
          padding: 0 10px;
          line-height: 44px;
          border-bottom: 1px solid #e8e8e8;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .icon-clock {
            color: #6386c1;
            margin-right: 6px;
            font-size: 20px;
            position: relative;
            top: 2px;
          }
          .calendar-date {
            margin-right: 15px;
          }
        }
      }
      .add-calendar-btn {
        display: inline-block;
        padding: 8px 15px;
        border: 1px solid #00469c;
        border-radius: 4px;
        text-align: center;
        font-size: 16px;
        color: #292929;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20px;
        .icon-add {
          color: #6386c1;
          margin-right: 8px;
          font-weight: 600;
        }
      }
    }
  }

  .data-notice-box {
    height: 330px;
    margin-top: 8px;
    display: flex;
    .data-download-left {
      flex: 1;
      height: 100%;
      background: #fff;
      margin-right: 12px;
      overflow-x: scroll;
      .data-download-header {
        padding: 0 20px;
        font-size: 18px;
        color: #292929;
        font-weight: 600;
        line-height: 60px;
        border-bottom: 1px solid #e8e8e8;
      }
    }
    .notice-right {
      flex-shrink: 0;
      width: 332px;
      height: 100%;
      background: #fff;
      .notice-title {
        text-align: center;
        font-size: 18px;
        color: #292929;
        font-weight: 600;
        line-height: 60px;
        border-bottom: 1px solid #e8e8e8;
        position: relative;
      }
    }
    .notice-ul {
      .notice-li {
        padding: 0 10px;
        line-height: 40px;
        display: flex;
        font-size: 14px;
        &:hover {
          background: #f5f6f9;
        }
        > span {
          display: inline-block;
        }
        .notice-li-type {
          color: #00469c;
          font-weight: 600;
        }
        .notice-li-title {
          flex: 1;
          color: #232b39;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .notice-li-date {
          color: #6386c1;
        }
      }
    }
  }
}
</style>
