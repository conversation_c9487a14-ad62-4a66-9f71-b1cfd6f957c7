<!--消息模板管理-->
<template>
  <div class="tabs-overall">
    <!-- tabs切换 -->
    <mt-tabs
      tab-id="message-template"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 平台管理 -->
    <platform-message
      v-if="singleTabs.code == 'platform'"
      :single-tabs="singleTabs"
    ></platform-message>
    <!-- 寻源 -->
    <find-source v-if="singleTabs.code == 'sourcing'" :single-tabs="singleTabs"></find-source>
    <!-- 采购执行 -->
    <procurement-execution
      v-if="singleTabs.code == 'purchase_execute'"
      :single-tabs="singleTabs"
    ></procurement-execution>
  </div>
</template>
<script>
// 默认看板小，中间内容大
export default {
  components: {
    // 平台管理
    platformMessage: () =>
      import(
        /* webpackChunkName: "@/views/messageCenter/templateManagement/index.vue" */ './tabs/platformMessage/index.vue'
      ),
    // 寻源
    findSource: () =>
      import(
        /* webpackChunkName: "@/views/messageCenter/templateManagement/index.vue" */ './tabs/findSource/index.vue'
      ),
    // 采购执行
    procurementExecution: () =>
      import(
        /* webpackChunkName: "@/views/messageCenter/templateManagement/index.vue" */ './tabs/procurementExecution/index.vue'
      )
  },
  data() {
    return {
      dataSource: [], // tabs数据
      tabsIndex: 0, // tabs显示
      // code: 'platform', // 显示
      singleTabs: {} // 单个tabs数据
    }
  },
  mounted() {
    this.initialCallInterface() // 初始调用接口
  },
  methods: {
    initialCallInterface() {
      // tabs顶部信息
      this.$api.messageCenter.templateGroupList().then((res) => {
        res.data.map((item) => {
          this.dataSource.push({ title: item.name, code: item.code, id: item.id })
        })
        this.singleTabs = res.data[0]
      })
    },
    // 切换tabs
    handleSelectTab(e, item) {
      this.singleTabs = item
      console.log(item.code)
      this.tabsIndex = e
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs-overall {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
