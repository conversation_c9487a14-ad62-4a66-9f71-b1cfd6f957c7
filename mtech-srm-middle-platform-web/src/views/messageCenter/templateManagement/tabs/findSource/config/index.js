import { i18n } from '@/main.js'
import Vue from 'vue'
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Enable', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'Disable', icon: 'icon_table_disable', title: i18n.t('停用') }
  // { id: 'Submit', icon: 'icon_solid_Submit', title: '提交' }
]
export const columnData = () => [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'code',
    headerText: i18n.t('模板编码')
  },
  {
    field: 'title',
    headerText: i18n.t('模板名称')
  },
  {
    field: 'desc',
    headerText: i18n.t('模板描述')
  },
  {
    field: 'channel',
    headerText: i18n.t('发送渠道'),
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template:
            '<div v-if="send" class="sendDiv"> <div v-for="(item,index) in send" @click="channelButtonClick(item)" :key="index"><span :class="[item.state ? \'sendChannel\' : \'unselectedChannel\']">{{item.name}}</span></div></div>',
          data() {
            return {
              data: {},
              send: [
                { name: '站内信', code: 'IN_MAIL', state: false },
                { name: '短信', code: 'SMS', state: false },
                { name: '邮件', code: 'EMAIL', state: false },
                { name: '国际短信', code: 'SMS_INTERNATIONAL', state: false },
                { name: '待办消息', code: 'TO_DO_MSG', state: false }
              ],
              defaultOn: {
                IN_MAIL: '站内信', // 站内信
                SMS: '短信', // 短信
                EMAIL: '邮件', // 邮件
                SMS_INTERNATIONAL: '国际短信', // 国际短信
                TO_DO_MSG: '待办消息' // 待办消息
              }
            }
          },
          mounted() {
            this.processtheData()
          },
          methods: {
            processtheData() {
              // console.log(this.data.channel, '渠道')
              if (this.data.channel && this.data.channel.length > 0) {
                // console.log(this.data.channel, this.send)
                this.data.channel.map((item) => {
                  // console.log(item)
                  this.send.map((itemTow) => {
                    // console.log(itemTow.code)
                    if (item === itemTow.code) {
                      itemTow.state = true
                    }
                  })
                })
              }
            },
            channelButtonClick(item) {
              const id = this.data.id
              const parameter = {
                channelType: item.code,
                templateId: id
              }
              if (!item.state) {
                this.$api.messageCenter.templateOpenChannel(parameter).then((res) => {
                  console.log(res, '启用')
                  this.$parent.$emit('refreshList')
                })
              } else {
                this.$api.messageCenter.templateCloseChannel(parameter).then((res) => {
                  console.log(res, '关闭')
                  this.$parent.$emit('refreshList')
                })
              }
            }
          }
        })
      }
    }
  }
]
// export const pageConfig = [
//   {
//     toolbar,
//     grid: {
//       height: 'auto',
//       columnData,
//       dataSource: []
//     }
//   }
// ]
