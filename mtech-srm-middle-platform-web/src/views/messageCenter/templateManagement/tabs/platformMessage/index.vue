<template>
  <div class="hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      class="template-height"
      @refreshList="refreshList"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>
<script>
import { toolbar, columnData } from './config'
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      i18n,
      pageConfig: [
        {
          toolbar,
          grid: {
            columnData: [],
            dataSource: [],
            asyncConfig: {}
          }
        }
      ],
      queryBuilder: [] // 列表数据
    }
  },
  props: {
    singleTabs: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {},
  mounted() {
    const userInfo = sessionStorage.userInfo ? JSON.parse(sessionStorage.userInfo) : {}
    if (userInfo.tenantId && userInfo.tenantId !== '-99') {
      this.pageConfig[0].toolbar = []
    }
    this.initialCallInterface() // 初始调用接口
  },
  methods: {
    // 列表接口调用
    initialCallInterface() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: 'message/tenant/template/queryBuilder',
        params: {
          defaultRules: [{ field: 'groupId', operator: 'equal', value: this.singleTabs.id }],
          page: {
            current: 0,
            size: 100
          }
        },
        serializeList: (list) => {
          this.queryBuilder = list
          return list
        }
      })
      this.$set(this.pageConfig[0].grid, 'columnData', columnData(this.queryBuilder))
      this.$set(this.pageConfig[0].grid, 'dataSource', this.queryBuilder)
    },
    // 点击头部
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && (e.toolbar.id === 'Delete' || e.toolbar.id === 'Edit')) {
        this.$toast({ content: i18n.t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickDelete(_selectRows)
      } else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      }
      // else if (e.toolbar.id === 'refreshDataByLocal') {
      //   // 刷新
      //   this.initialCallInterface()
      // }
    },
    // 新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: ".components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: i18n.t('新增消息模板'),
          groupId: this.singleTabs.id
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 删除
    handleClickDelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      this.$api.messageCenter.templatedel(_selectIds).then(() => {
        this.$refs.templateRef.refreshCurrentGridData()

        this.$toast({
          content: i18n.t('操作成功'),
          type: 'success'
        })
      })
    },
    // 编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: i18n.t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: ".components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: i18n.t('编辑消息模板'),
          groupId: this.singleTabs.id,
          id: _selectRows[0].id
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },

    // js文件刷新列表
    refreshList() {
      this.$refs.templateRef.refreshCurrentGridData()
    }
  }
}
</script>

<style lang="scss">
.hander {
  flex: 1;
  .sendDiv {
    display: flex;
    justify-content: space-between;
    align-items: center;
    div {
      height: 20px;
      margin: 0 10px;
      .sendChannel {
        width: 100%;
        height: 20px;
        background: #eff2f8;
        padding: 0 5px;
        border-radius: 2px;
        text-align: center;
        line-height: 20px;
        vertical-align: middle;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(99, 134, 193, 1);
      }
      .unselectedChannel {
        width: 100%;
        height: 20px;
        background: #f4f4f4;
        padding: 0 5px;
        border-radius: 2px;
        text-align: center;
        line-height: 20px;
        vertical-align: middle;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        color: #9a9a9a;
      }
      span:hover {
        cursor: pointer;
      }
    }
  }
}
</style>
