<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="rolling">
        <div class="slider-content">
          <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
            <div class="mtFormItem">
              <mt-form-item prop="name" :label="i18n.t('模板名称')">
                <mt-input
                  v-model="formObject.name"
                  float-label-type="Never"
                  :placeholder="i18n.t('请输入模板名称')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="code" :label="i18n.t('模板编码')">
                <mt-input
                  v-model="formObject.code"
                  float-label-type="Never"
                  :disabled="disabled"
                  :placeholder="i18n.t('请输入模板编码')"
                ></mt-input>
              </mt-form-item>
              <mt-form-item prop="title" :label="i18n.t('消息标题')">
                <mt-input
                  v-model="formObject.title"
                  float-label-type="Never"
                  :placeholder="i18n.t('请输入消息标题')"
                ></mt-input>
              </mt-form-item>
              <!-- 第一层显示 -->
              <mt-form-item
                v-for="(item, index) in dynamicInput"
                :key="index"
                prop="channelCode"
                :label="item.title"
              >
                <!--   :disabled="item.keys === 'ext.sms_code' && disabled ? true:false " -->
                <mt-select
                  v-if="item.type == 'select'"
                  v-model="formObject[item.keys]"
                  float-label-type="Never"
                  :data-source="item.valueList"
                  :fields="{ text: 'text', value: 'value' }"
                  @change="accounmaintTypeSelect($event, item)"
                  :placeholder="i18n.t('请选择') + item.title"
                ></mt-select>
                <mt-input
                  v-if="item.type == 'text'"
                  v-model="formObject[item.keys]"
                  float-label-type="Never"
                  :placeholder="i18n.t('请输入') + item.title"
                ></mt-input>
              </mt-form-item>
            </div>
            <div class="remark">
              <span>{{ i18n.t('备注') }}:</span>
              <mt-input :placeholder="i18n.t('请输入备注')" v-model="formObject.desc"></mt-input>
              <i class="mt-icons mt-icon-icon_list_edit"></i>
            </div>
            <div class="shortText">
              <mt-form-item prop="shortText" :label="i18n.t('短文本消息内容配置')">
                <mt-input
                  v-model="formObject.content"
                  float-label-type="Never"
                  :multiline="true"
                  :rows="3"
                  placeholder=""
                ></mt-input>
              </mt-form-item>
            </div>
            <div class="richText">
              <p>{{ i18n.t('富文本消息内容配置') }}</p>
              <rich-text-editor
                ref="MtRichTextEditor"
                :toolbar-settings="toolbarSettings"
                :background-color="backgroundColor"
                v-model="formObject.richContent"
              >
              </rich-text-editor>
            </div>
          </mt-form>
        </div>
      </div>

      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ i18n.t('保存') }}</span>
        <span @click="cancel">{{ i18n.t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { i18n } from '@/main.js'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor'
export default {
  components: {
    RichTextEditor
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      i18n,
      // 确认 取消按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: i18n.t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: i18n.t('保存') }
        }
      ],
      // 初始值为空
      formObject: {
        name: '', // 模板名称
        code: '', // 模板编码
        desc: '', // 备注
        richContent: '', // 富文本
        content: '', // 短文本
        title: '', // 消息标题
        id: '', // 编辑用到的单行id
        ext: {} // 动态值
      },
      // 必填
      formRules: {
        name: [
          {
            required: true,
            message: i18n.t('请输入模板名称'),
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: i18n.t('请输入模板编码'),
            trigger: 'blur'
          }
        ],
        title: [
          {
            required: true,
            message: i18n.t('请输入消息标题'),
            trigger: 'blur'
          }
        ]
      },
      // 工具栏设置
      toolbarSettings: {
        enable: true,
        enableFloating: true,
        type: 'Expand',
        items: [
          'Bold',
          'Italic',
          'Underline',
          '|',
          'Formats',
          'Alignments',
          'OrderedList',
          'UnorderedList',
          '|',
          'CreateLink',
          'Image',
          'backgroundColor',
          '|',
          'SourceCode',
          'Undo',
          'Redo'
        ],
        itemConfigs: {}
      },
      // 配置选中元素可用的背景色调
      backgroundColor: {
        columns: 5,
        modeSwitcher: true,
        colorCode: {
          Custom: [
            '#ffff00',
            '#00ff00',
            '#00ffff',
            '#ff00ff',
            '#0000ff',
            '#ff0000',
            '#000080',
            '#008080',
            '#008000',
            '#800080',
            '#800000',
            '#808000',
            '#c0c0c0',
            '#000000',
            '#000000'
          ]
        }
      },
      disabled: false, // 模板编码
      dynamicInput: [] // 动态表单数据
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    groupId() {
      return this.modalData.groupId
    },
    id() {
      return this.modalData.id
    }
  },
  mounted() {
    this.initialCallInterface() // 初始调用接口
  },
  methods: {
    // 新增时调用获取配置信息
    initialCallInterface() {
      this.$api.messageCenter.templateConfig().then((res) => {
        for (const key in res.data) {
          const title = key.split('.')[1]
          if (key.split('.')[0] === 'ext') {
            console.log(key, '带ext')
            this.formObject.ext[title] = res.data[key].defaultValue
            this.formObject[title] = res.data[key].defaultValue
            res.data[key].keys = title
          } else {
            console.log(key, '不带ext')
            this.formObject[key] = ''
            res.data[key].keys = key
          }
          this.dynamicInput.push(res.data[key])
          // 必填
          if (res.data[key].type === 'select') {
            this.formRules[key] = [
              {
                required: true,
                message: this.$t('请选择') + res.data[key].title,
                trigger: 'blur'
              }
            ]
          }
          if (res.data[key].type === 'text') {
            this.formRules[key] = [
              {
                required: true,
                message: this.$t('请输入') + res.data[key].title,
                trigger: 'blur'
              }
            ]
          }
        }
        console.log(this.formObject, this.dynamicInput, '维护formObject,和动态表单数据')
        if (this.id) {
          this.editOperation()
        }
      })
    },
    // 编辑操作
    editOperation() {
      this.disabled = true
      this.$api.messageCenter.templateInfo(this.id).then((res) => {
        // 赋值操作--mt-input
        this.formObject = res.data
        for (const key in this.formObject.ext) {
          this.formObject[key] = this.formObject.ext[key]
        }
        console.log(this.formObject, '编辑数据')
      })
    },
    // 动态下拉框change事件
    accounmaintTypeSelect(e, item) {
      console.log(e, item)
    },
    // 点击取消
    cancel() {
      this.$emit('cancel-function')
    },
    // 点击确认
    confirm() {
      if (!this.formObject.name) {
        this.$toast({ content: i18n.t('模板名称不能为空'), type: 'warning' })
        return
      }
      if (!this.formObject.code) {
        this.$toast({ content: i18n.t('模板编码不能为空'), type: 'warning' })
        return
      }
      this.formObject.groupId = this.groupId // id
      this.formObject.channelTypeList = [] // 通道
      for (const key in this.formObject.ext) {
        this.formObject.ext[key] = this.formObject[key]
      }
      // '新增'
      if (!this.id) {
        this.$api.messageCenter.templateAdd(this.formObject).then(() => {
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function')
        })
      }
      // '编辑'
      if (this.id) {
        // parameter.id = this.formObject.id // id
        this.$api.messageCenter.templateUpdate(this.formObject).then(() => {
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
          this.$emit('confirm-function')
        })
      }

      // this.$refs.ruleForm.validate((valid) => {})
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.searchInput {
  /deep/ .e-input-group {
    border: none;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 920px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }
    .rolling {
      width: 100%;
      height: 100%;
      max-height: 670px;
      overflow-y: scroll;
      .slider-content {
        .mtFormItem {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          .mt-form-item {
            width: 324px;
            height: 40px;
          }
        }
        .remark {
          width: 200px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          span {
            display: block;
            width: 42px;
            height: 30px;
            line-height: 28px;
            font-size: 14px;
            font-family: PingFangSC;
            font-weight: normal;
            color: rgba(41, 41, 41, 1);
          }
          .mt-input {
            width: 140px;
            /deep/.e-input-group {
              border: none;
            }
          }
        }
        .whetherEnable {
          margin-top: 10px;
          width: 672px;
          height: 100%;
          list-style: none;
          li {
            width: 100%;
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(232, 232, 232, 1);
            span {
              display: block;
              width: 50%;
              box-sizing: border-box;
              padding-left: 32px;
            }
          }
          .bdbottom {
            border-bottom: none !important;
          }
        }
        .shortText {
          width: 100%;
          height: 100%;
          margin-top: 20px;
        }
        .richText {
          margin-top: 20px;
          p {
            font-weight: 600;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 20px;
          }
        }
      }
    }

    // .add-rule-group {
    //   margin-top: 30px;
    //   height: 14px;
    //   font-size: 14px;
    //   font-weight: 600;
    //   color: rgba(0, 70, 156, 1);
    //   width: 100%;
    //   text-align: center;
    //   cursor: pointer;
    // }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
</style>
