<!-- 消息接收配置 -->
<template>
  <div class="notice-hander hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>
<script>
import { pageConfig, toolbar, columnDataView, columnData, columnListData } from './config'
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig,
      url: ''
    }
  },
  computed: {
    routeInfo() {
      // 1:管理员列表 2：采方列表  3：供方列表
      const path = this.$route?.path
      let type = 2
      let url = ''
      let detailUrl = ''
      switch (path) {
        case '/middlePlatform/system-notice-management':
          type = 1
          url = '/message/admin/notice/pageQuery'
          detailUrl = '/middlePlatform/system-notice-management/detail'
          break
        case '/middlePlatform/pur/system-notice':
          type = 2
          url = '/message/tenant/notice/for/queryPur'
          detailUrl = '/middlePlatform/pur/system-notice/detail'
          break
        case '/middlePlatform/sup/system-notice':
          type = 3
          url = '/message//tenant/notice/supplier/querySup'
          detailUrl = '/middlePlatform/sup/system-notice/detail'
          break
      }
      return {
        type,
        url,
        detailUrl
      }
    }
  },
  watch: {
    $route: {
      handler() {
        // 管理员有权限操作
        const _toolbar = this.routeInfo.type === 1 ? toolbar : []
        this.$set(this.pageConfig[0], 'toolbar', _toolbar)
        this.$set(this.pageConfig[0].grid, 'asyncConfig', {
          url: this.routeInfo.url,
          params: {}
        })
        if (this.routeInfo.type === 1) {
          this.$set(this.pageConfig[0].grid, 'columnData', columnDataView)
        } else if (this.routeInfo.type === 2) {
          this.$set(this.pageConfig[0].grid, 'columnData', columnListData)
        } else {
          this.$set(this.pageConfig[0].grid, 'columnData', columnData)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 刷新页面
    initialCallInterface() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: this.routeInfo?.url,
        params: {}
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 表头操作
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id === 'Delete' || e.toolbar.id === 'Edit' || e.toolbar.id === 'Withdraw')
      ) {
        this.$toast({ content: i18n.t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickDelete(_selectRows)
      } else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      } else if (e.toolbar.id === 'Withdraw') {
        this.handleClickWidthdraw(_selectRows)
      }
    },
    // 新增
    handleClickAdd() {
      this.$router.push({
        path: 'system-notice-management/add'
      })
    },
    // 编辑 (已保存的公告 -> status 1)
    handleClickEdit(_selectRows) {
      if (_selectRows.length !== 1) {
        this.$toast({ content: i18n.t('只能选择一行'), type: 'warning' })
        return
      }
      if (_selectRows[0].status !== 1 && _selectRows[0].status !== 3) {
        this.$toast({ content: i18n.t('公告发布后不可修改'), type: 'warning' })
        return
      }
      this.$router.push({
        path: 'system-notice-management/edit',
        query: { id: _selectRows[0].id }
      })
    },
    // 删除 （已保存的公告 -> status 1）
    handleClickDelete(rows) {
      let _flag = true
      const _selectIds = rows.map((item) => {
        if (item.status !== 1) _flag = false
        return item.id
      })
      if (!_flag) {
        this.$toast({
          content: i18n.t('只能删除已保存的数据'),
          type: 'warning'
        })
        return
      }
      this.$api.messageCenter.noticeDel(_selectIds).then((res) => {
        if (res.code === 200) {
          this.initialCallInterface()
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    // 撤回 （已发布的公告 -> status 2）
    handleClickWidthdraw(_selectRows) {
      if (_selectRows.length !== 1) {
        this.$toast({ content: i18n.t('只能选择一行'), type: 'warning' })
        return
      }

      if (_selectRows[0].status !== 2) {
        this.$toast({ content: i18n.t('只能撤回已发布的数据'), type: 'warning' })
        return
      }
      this.$api.messageCenter.noticeWithdraw({ id: _selectRows[0].id }).then((res) => {
        if (res.code === 200) {
          this.initialCallInterface()
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
        }
      })
    },
    // 跳转详情页
    handleClickCellTitle(e) {
      const { data } = e
      // 自动触发公告直接跳转对应连接
      if (data.detailLink) {
        window.open(data.detailLink, '_blank')
        return
      }
      this.$router.push({
        path: this.routeInfo?.detailUrl,
        query: {
          id: data.id
        }
      })
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      if (tool.id === 'View') {
        this.$dialog({
          modal: () => import('./components/browseRecordsDialog.vue'),
          data: {
            title: this.$t('浏览记录'),
            data
          },
          success: () => {
            // this.$refs.templateRef.refreshCurrentGridData();
          }
        })
      }
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .template-height {
    flex: 1;
    .sendChannel {
      display: inline-block;
      padding: 0 5px;
      height: 20px;
      background: #eff2f8;
      border-radius: 2px;
      text-align: center;
      line-height: 20px;
      vertical-align: middle;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      margin: 0 10px;
    }
  }
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
