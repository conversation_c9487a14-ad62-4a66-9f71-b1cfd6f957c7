<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="top">
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <div>
        <span v-if="noticeInfo.startTime && noticeInfo.endTime">
          {{ $t('有效期：') }}
          {{
            `${formateTime(new Date(noticeInfo.startTime), 'yyyy-MM-dd')} ~ ${formateTime(
              new Date(noticeInfo.endTime),
              'yyyy-MM-dd'
            )}`
          }}
        </span>
        <span v-if="!isSup && noticeInfo.noticeType.length" style="margin-left: 20px"
          >{{ $t('公告类型') }}：{{ getNoticeTypeText() }}</span
        >
        <span v-if="!isSup && noticeInfo.targetSystem.length" style="margin-left: 20px"
          >{{ $t('发布范围') }}：{{ getTargetText() }}</span
        >
      </div>
    </div>
    <div v-if="noticeInfo.noticeExtList.length" style="padding-bottom: 16px">
      {{ $t('公司') }}：{{ getCompanyList() }}
    </div>

    <div class="notice-detail">
      <p class="title">{{ noticeInfo.title }}</p>
      <div class="content" v-html="noticeInfo.body"></div>
      <div class="bottom">
        <p class="creator">{{ noticeInfo.releaseUserName || noticeInfo.createUserName }}</p>
      </div>
      <div class="bottom">
        <p class="time">
          {{ $moment(noticeInfo.releaseTime || noticeInfo.createTime).format('YYYY-MM-DD') }}
        </p>
      </div>
    </div>

    <div v-if="!isSup" class="supplier-table">
      <div class="supplier-item" v-if="radioType === '0'">
        <div class="supplier-list">
          <div class="additon-title">{{ $t('供应商状态') }}：</div>
          <mt-multi-select
            disabled
            ref="supplierStatusRef"
            v-model="noticeInfo.supplierStatus"
            :data-source="supplierStatusData"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </div>
      </div>
      <div class="supplier-item" v-if="radioType === '1'">
        <div class="supplier-list">
          <div class="additon-title">{{ $t('供应商列表') }}：</div>
          <SupplierList :supplier-data="supplierData" disabled />
        </div>
      </div>
    </div>

    <!-- 相关附件 -->
    <mt-template-page
      :height="10"
      ref="additionTemplate"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      class="ml20"
    >
      <div slot="slot-filter" class="second-top-filter">
        <div class="second-left-status">
          <span class="additon-title">{{ $t('相关附件') }}：</span>
        </div>
      </div>
    </mt-template-page>
  </div>
</template>

<script>
import { i18n } from '@/main.js'
import SupplierList from './components/supplierList'
import { additionPageConfig } from './config'
import { download, formateTime } from '@/utils/utils'
export default {
  components: {
    SupplierList
  },
  data() {
    return {
      formateTime,
      noticeInfo: {
        title: '',
        body: '',
        createUserName: '',
        createTime: '',
        releaseUserName: '',
        releaseTime: '',
        startTime: '',
        endTime: '',
        noticeType: '',
        targetSystem: []
      },
      pageConfig: additionPageConfig,
      supplierStatusData: [
        {
          text: this.$t('注册'),
          value: 1
        },
        {
          text: this.$t('潜在'),
          value: 2
        },
        {
          text: this.$t('合格'),
          value: 10
        }
      ],
      radioType: '',
      supplierData: []
    }
  },
  computed: {
    isSup() {
      // 是否是供应商路由
      return this.$route?.path === '/middlePlatform/sup/system-notice/detail' ? true : false
    }
  },
  watch: {},
  mounted() {
    this.getNoticeInfo()
  },
  methods: {
    getCompanyList() {
      let text = ''
      this.noticeInfo.noticeExtList.forEach((item, index) => {
        if (index > 0) {
          text += `，${item.companyName}`
        } else {
          text += item.companyName
        }
      })
      return text
    },
    getNoticeTypeText() {
      let text = ''
      let arr = [
        { value: 'SYS_NOTICE', text: i18n.t('系统通知'), cssClass: '' },
        { value: 'ITEM_NOTICE', text: i18n.t('事项公告'), cssClass: '' },
        { value: 'PURCHASE_NOTICE', text: i18n.t('采购公告'), cssClass: '' },
        { value: 'SOURCING_NOTICE', text: i18n.t('寻源公告'), cssClass: '' },
        { value: 'SUCCESSFUL_NOTICE', text: i18n.t('中标公告'), cssClass: '' }
      ]
      arr.forEach((i) => {
        if (i.value === this.noticeInfo.noticeType) {
          text = i.text
        }
      })
      return text
    },
    getTargetText() {
      let text = []
      if (this.noticeInfo.targetSystem && this.noticeInfo.targetSystem.length) {
        for (let i = 0; i < this.noticeInfo.targetSystem.length; i++) {
          const ele = this.noticeInfo.targetSystem[i]
          if (ele === 'PUR') {
            text.push('SRM采方平台')
          }
          if (ele === 'SUP') {
            text.push('SRM供方平台')
          }
        }
      }
      return text.join('，')
    },
    goBack() {
      this.$router.go(-1)
    },
    getNoticeInfo() {
      const noticeMethod = this.isSup ? 'getSupNotice' : 'getPurNotice'
      this.$api.messageCenter[noticeMethod](this.$route.query?.id).then((res) => {
        if (res.code === 200) {
          const {
            title,
            body,
            createUserName,
            createTime,
            releaseUserName,
            releaseTime,
            noticeFileList,
            supplierList,
            supplierStatus,
            startTime,
            endTime,
            targetSystem,
            noticeType,
            noticeExtList
          } = res.data
          this.noticeInfo = Object.assign({}, this.noticeInfo, {
            title,
            body,
            createUserName,
            createTime,
            releaseUserName,
            releaseTime,
            startTime: startTime || '',
            endTime: endTime || '',
            targetSystem: targetSystem || [],
            noticeType: noticeType || '',
            noticeExtList: noticeExtList || []
          })
          if (supplierStatus && supplierStatus.length) {
            this.noticeInfo.supplierStatus = supplierStatus
            this.radioType = '0'
          }
          if (supplierList && supplierList.length) {
            this.supplierData = supplierList
            this.radioType = '1'
          }
          // 列表页
          this.$set(this.pageConfig[0].grid, 'dataSource', noticeFileList)
          if (this.$route.query && this.$route.query.id) {
            // 添加浏览记录
            this.$api.messageCenter.browseRecordsAdd({ noticeId: this.$route.query.id })
          }
        }
      })
    },
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Download') {
        let _selectRows = e.grid.getSelectedRecords()
        if (_selectRows.length <= 0) {
          this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
          return
        }

        _selectRows.forEach((item) => {
          this.handlerDownloadFile(item)
        })
      }
    },
    // 下载文件
    handlerDownloadFile(data) {
      this.$api.messageCenter.downloadFile({ id: data.fileId }).then((res) => {
        download({ fileName: data.fileName, blob: res.data })
      })
    },
    handleClickCellTitle(e) {
      if (e.field == 'fileName') {
        let params = { id: e.data.fileId }
        this.$api.messageCenter.getFilePreview(params).then((res) => {
          window.open(`${res.data}`)
        })
      }
    }
  }
}
</script>
<style lang="scss">
.notice-detail {
  strong {
    font-weight: bolder;
  }
  em {
    font-style: italic;
  }
  p {
    margin-bottom: 4px;
  }
}
</style>
<style lang="scss" scoped>
.top {
  width: 100%;
  text-align: right;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
  align-items: center;
}
.notice-detail {
  background: #ffffff;
  padding: 24px;
  .title {
    font-weight: bold;
    font-size: 18px;
    text-align: center;
    margin-bottom: 24px;
  }
  .content {
    margin-bottom: 24px;
  }
  .bottom {
    display: block;
    height: 30px;
    .creator,
    .time {
      width: 150px;
      float: right;
      text-align: center;
    }
  }
}
.additon-title {
  display: inline-block;
  font-weight: bold;
  padding: 16px 0;
}
.supplier-table {
}
</style>
