<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-form-flex"
    header="$t('浏览记录')"
    @beforeClose="cancel"
  >
    <div class="grid-wrap">
      <div style="display: flex; font-size: 16px; font-weight: bold">
        <div style="padding-right: 40px">
          <span style="margin-right: 20px">{{ $t('标题') }}</span>
          <span>{{ title }}</span>
        </div>
        <div>
          <span style="margin-right: 20px">{{ $t('发布时间') }}</span>
          <span>{{ releaseTime }}</span>
        </div>
      </div>
      <mt-template-page
        :padding-top="false"
        ref="templatePage"
        :hidden-tabs="true"
        :use-tool-template="false"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
      >
      </mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { browseColumnData } from '../config'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [],
      buttons: [
        // {
        //   click: this.cancel,
        //   buttonModel: { content: '取消' }
        // },
        // {
        //   click: this.confirm,
        //   buttonModel: { isPrimary: 'true', content: '确定' }
        // }
      ]
    }
  },
  computed: {
    title() {
      return this.modalData.data.title
    },
    releaseTime() {
      return this.modalData.data.releaseTime
    }
  },
  mounted() {
    this.show()
  },
  methods: {
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Download') {
        let rule = this.$refs.templatePage.getCurrentUsefulRef().pluginRef.queryBuilderRules || {}
        let params = {
          condition: rule.condition || '',
          page: { current: 1, size: 9999 },
          rules: rule.rules || [],
          defaultRules: [
            {
              label: this.$t('公告id'),
              field: 'noticeId',
              type: 'string',
              operator: 'equal',
              value: this.modalData.data.id
            }
          ]
        }
        this.$api.messageCenter.browseRecordsExport(params).then((res) => {
          if (res) {
            const fileName = getHeadersFileName(res)
            download({ fileName, blob: res.data })
          }
        })
      }
    },
    show() {
      this.$refs.dialog.ejsRef.show()
      this.pageConfig = [
        {
          toolbar: {
            tools: [
              [{ id: 'Download', icon: 'icon_solid_Download', title: this.$t('导出') }],
              ['Filter', 'Refresh', 'Setting']
            ]
          },
          gridId: '103177fa-011e-42cd-85ea-98876409e02d',
          grid: {
            columnData: browseColumnData,
            asyncConfig: {
              url: 'message/tenant/noticeViewLog/pageQuery',
              params: {
                defaultRules: [
                  {
                    label: this.$t('公告id'),
                    field: 'noticeId',
                    type: 'string',
                    operator: 'equal',
                    value: this.modalData.data.id
                  }
                ]
              }
            }
          }
        }
      ]
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      // let selectRows =
      //   !!this.$refs.templatePage.getCurrentUsefulRef() &&
      //   !!this.$refs.templatePage.getCurrentUsefulRef().gridRef &&
      //   !!this.$refs.templatePage.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      //     ? this.$refs.templatePage.getCurrentUsefulRef().gridRef.getMtechGridRecords()
      //     : []
      // this.$emit('confirm-function', {
      //   data: selectRows
      // })
      console.log('点击', this.modalData)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  padding: 20px 0;
}
.grid-wrap {
  padding-top: 20px;
  height: 480px;
}
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
/deep/ .mt-select-index {
  display: inline-block;
}
</style>
