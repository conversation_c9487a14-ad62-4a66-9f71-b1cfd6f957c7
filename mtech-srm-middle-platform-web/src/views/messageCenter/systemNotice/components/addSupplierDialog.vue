<template>
  <mt-dialog
    ref="dialog"
    css-class="dialog-form-flex"
    :header="$t('选择供应商')"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div class="grid-wrap">
      <mt-template-page
        :padding-top="false"
        ref="templatePage"
        :hidden-tabs="true"
        :use-tool-template="false"
        :template-config="pageConfig"
      >
      </mt-template-page>
    </div>
  </mt-dialog>
</template>

<script>
import { addSupplierPageConfig } from '../config'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageConfig: [],
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.show()
    this.pageConfig = addSupplierPageConfig(this.modalData.data)
  },
  methods: {
    show() {
      this.$refs.dialog.ejsRef.show()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    confirm() {
      let selectRows =
        !!this.$refs.templatePage.getCurrentUsefulRef() &&
        !!this.$refs.templatePage.getCurrentUsefulRef().gridRef &&
        !!this.$refs.templatePage.getCurrentUsefulRef().gridRef.getMtechGridRecords()
          ? this.$refs.templatePage.getCurrentUsefulRef().gridRef.getMtechGridRecords()
          : []
      this.$emit('confirm-function', {
        data: selectRows
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  padding: 20px 0;
}
.grid-wrap {
  padding-top: 20px;
  height: 480px;
}
.dialog-form-flex .e-dlg-content {
  padding: 40px !important;
  .mt-form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .mt-form-item {
      width: 390px;
      .mt-form-item__content {
        width: 100%;
      }
    }
  }
}
/deep/ .mt-select-index {
  display: inline-block;
}
</style>
