<template>
  <div class="full-height">
    <mt-template-page
      ref="supplierTemplateRef"
      :template-config="supplierPageConfig"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>
<script>
import { supplierPageConfig } from '../config'
export default {
  props: {
    supplierData: {
      // 初始化供应商数据
      type: Array,
      default() {
        return []
      }
    },
    noticeExtList: {
      // 已选的公司数据
      type: Array,
      default() {
        return []
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      supplierPageConfig: supplierPageConfig(),
      supplierList: [] // 选中供应商列表
    }
  },
  computed: {
    allSupplierData() {
      return this.supplierData && this.supplierData.length
        ? JSON.parse(JSON.stringify(this.supplierData))
        : []
    }
  },
  watch: {
    supplierList(v) {
      this.$emit('change', v)
    }
  },
  mounted() {
    // 编辑页初始化渲染
    if (this.disabled) {
      // this.$set(this.supplierPageConfig[0], 'toolbar', [])
      this.supplierPageConfig = supplierPageConfig(this.disabled)
    }
    this.$set(this.supplierPageConfig[0].grid, 'dataSource', this.allSupplierData)
  },
  methods: {
    handleClickToolBar(e) {
      let _selectGridRecords = e.gridRef.getMtechGridRecords()
      let idList = []
      _selectGridRecords.map((item) => {
        idList.push(item.id)
      })
      if (e.toolbar.id === 'Delete') {
        this.handleDelFn(_selectGridRecords, idList) //删除
      } else if (e.toolbar.id === 'Add') {
        this.handleAddFn(e) //新增
      } else if (e.toolbar.id === 'Import') {
        this.handleImport() //新增
      }
    },
    //导入
    handleImport() {
      this.$dialog({
        modal: () => import('@/components/uploadDialog'),
        data: {
          title: this.$t('上传/导入'),
          paramsKey: 'importFile',
          importApi: this.$api.messageCenter.supplierImport,
          downloadTemplateApi: this.$api.messageCenter.supplierTempDownload
        },
        success: (res) => {
          // 列表数据拼接，公司列表数据回显
          const { data } = res
          const obj = {}
          this.supplierList.push(...data.supplerRefInfo)
          this.supplierList = this.supplierList.reduce((prev, cur) => {
            obj[cur.id] ? '' : (obj[cur.id] = true && prev.push(cur))
            return prev
          }, [])
          console.log(this.supplierList)
          this.$set(this.supplierPageConfig[0].grid, 'dataSource', this.supplierList)
          this.$emit('change', this.supplierList, data.companyCodeSet)
        }
      })
    },
    //新增
    handleAddFn() {
      if (!this.noticeExtList.length) {
        this.$toast({ content: this.$t('请先选择公司'), type: 'warning' })
        return false
      }
      this.$dialog({
        modal: () => import('./addSupplierDialog.vue'),
        data: {
          title: this.$t('选择供应商'),
          data: {
            noticeExtList: this.noticeExtList
          }
        },
        success: (rows) => {
          // 添加数据到供应商列表
          const obj = {}
          this.supplierList.push(...rows.data)
          this.supplierList = this.supplierList.reduce((prev, cur) => {
            obj[cur.id] ? '' : (obj[cur.id] = true && prev.push(cur))
            return prev
          }, [])
          console.log(this.supplierList)
          this.$set(this.supplierPageConfig[0].grid, 'dataSource', this.supplierList)
        }
      })
    },
    //删除
    handleDelFn(_selectGridRecords, idList) {
      if (_selectGridRecords.length <= 0) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      this.supplierList = this.supplierList.filter((item) => !idList.includes(item.id))
      this.$set(this.supplierPageConfig[0].grid, 'dataSource', this.supplierList)
    }
  }
}
</script>
<style lang="scss" scoped>
.full-height {
  flex: 1;
}
.content-arrow {
  &--btn {
    height: 30px;
    width: 30px;
    border: 2px solid #98aac3;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    color: #98aac3;
    margin: 10px;
    cursor: pointer;
  }
}
.transfer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.transfer > .list {
  width: 48%;
  height: 390px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
  .mt-data-grid {
    margin: 0 auto;
  }
}
.range-type {
  margin: 24px 0 16px 30px;
}
.accordion-title {
  float: left;
  margin-bottom: 16px;
  padding: 18px 0 18px 0;
  width: 100%;
  border-bottom: 1px solid rgba(232, 232, 232, 1);
  span {
    display: inline-block;
    border-left: 2px solid #00469c;
    text-indent: 10px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(41, 41, 41, 1);
    margin-left: 10px;
  }
}
/deep/ .mt-select-index {
  display: inline-block;
}
</style>
