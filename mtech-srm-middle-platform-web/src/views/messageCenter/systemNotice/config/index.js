import { i18n } from '@/main.js'
import Vue from 'vue'
// list toolbar
export const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
  { id: 'Edit', icon: 'icon_solid_edit', title: '编辑' },
  { id: 'Withdraw', icon: 'MT_Undo', title: '撤回' },
  { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
]

// supplier toolbar
const supplierToolbar = {
  tools: [
    [
      { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
      { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' },
      { id: 'Import', icon: 'icon_solid_Import', title: '导入' }
    ],
    []
  ]
}
// addition toolbar
const additionToolbar = {
  tools: [[{ id: 'Download', icon: 'icon_solid_Download', title: '下载' }], []]
}

const addSupplierToolbar = {
  tools: [[], ['Filter', 'Refresh', 'Setting']]
}

// list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'title',
    headerText: '标题',
    cellTools: []
  },
  {
    field: 'noticeType',
    headerText: '类型',
    valueConverter: {
      type: 'map',
      map: [
        { value: 'SYS_NOTICE', text: i18n.t('系统通知'), cssClass: '' },
        { value: 'ITEM_NOTICE', text: i18n.t('事项公告'), cssClass: '' },
        { value: 'PURCHASE_NOTICE', text: i18n.t('采购公告'), cssClass: '' },
        { value: 'SOURCING_NOTICE', text: i18n.t('寻源公告'), cssClass: '' },
        { value: 'SUCCESSFUL_NOTICE', text: i18n.t('中标公告'), cssClass: '' }
      ]
    }
  },
  {
    field: 'status',
    headerText: '状态',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('已保存'), cssClass: '' },
        { value: 2, text: i18n.t('已发布'), cssClass: '' },
        { value: 3, text: i18n.t('已撤销'), cssClass: '' },
        { value: 4, text: i18n.t('已删除'), cssClass: '' }
      ]
    }
  },
  {
    field: 'createUserName',
    headerText: '创建人'
  },
  {
    field: 'createTime',
    headerText: '创建时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  },
  {
    field: 'releaseUserName',
    headerText: '发布人'
  },
  {
    field: 'releaseTime',
    headerText: '发布时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  }
]

// list column
export const columnListData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'title',
    headerText: '标题',
    cellTools: []
  },
  {
    field: 'noticeType',
    headerText: '类型',
    valueConverter: {
      type: 'map',
      map: [
        { value: 'SYS_NOTICE', text: i18n.t('系统通知'), cssClass: '' },
        { value: 'ITEM_NOTICE', text: i18n.t('事项公告'), cssClass: '' },
        { value: 'PURCHASE_NOTICE', text: i18n.t('采购公告'), cssClass: '' },
        { value: 'SOURCING_NOTICE', text: i18n.t('寻源公告'), cssClass: '' },
        { value: 'SUCCESSFUL_NOTICE', text: i18n.t('中标公告'), cssClass: '' }
      ]
    }
  },
  {
    field: 'targetSystem',
    headerText: '发布范围',
    ignore: true,
    template: () => {
      return {
        template: Vue.component('targetSystem', {
          template: `
        <div>
            {{ getTargetText() }}
        </div>
                   `,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            getTargetText() {
              let text = []
              if (this.data.targetSystem && this.data.targetSystem.length) {
                for (let i = 0; i < this.data.targetSystem.length; i++) {
                  const ele = this.data.targetSystem[i]
                  if (ele === 'PUR') {
                    text.push('SRM采方平台')
                  }
                  if (ele === 'SUP') {
                    text.push('SRM供方平台')
                  }
                }
              }
              return text.join('，')
            }
          }
        })
      }
    }
  },
  {
    field: 'status',
    headerText: '状态',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('已保存'), cssClass: '' },
        { value: 2, text: i18n.t('已发布'), cssClass: '' },
        { value: 3, text: i18n.t('已撤销'), cssClass: '' },
        { value: 4, text: i18n.t('已删除'), cssClass: '' }
      ]
    }
  },
  {
    field: 'createUserName',
    headerText: '创建人'
  },
  {
    field: 'createTime',
    headerText: '创建时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  },
  {
    field: 'releaseUserName',
    headerText: '发布人'
  },
  {
    field: 'releaseTime',
    headerText: '发布时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  }
]

// list column
export const columnDataView = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'title',
    headerText: '标题',
    cellTools: []
  },
  {
    field: 'noticeType',
    headerText: '类型',
    valueConverter: {
      type: 'map',
      map: [
        { value: 'SYS_NOTICE', text: i18n.t('系统通知'), cssClass: '' },
        { value: 'ITEM_NOTICE', text: i18n.t('事项公告'), cssClass: '' },
        { value: 'PURCHASE_NOTICE', text: i18n.t('采购公告'), cssClass: '' },
        { value: 'SOURCING_NOTICE', text: i18n.t('寻源公告'), cssClass: '' },
        { value: 'SUCCESSFUL_NOTICE', text: i18n.t('中标公告'), cssClass: '' }
      ]
    }
  },
  {
    field: 'targetSystem',
    headerText: '发布范围',
    searchOptions: {
      elementType: 'multi-select',
      operator: 'in',
      multiple: true,
      dataSource: [
        {
          text: 'SRM采方平台',
          value: 'PUR'
        },
        {
          text: 'SRM供方平台',
          value: 'SUP'
        }
      ]
    },
    template: () => {
      return {
        template: Vue.component('targetSystem', {
          template: `
        <div>
            {{ getTargetText() }}
        </div>
                   `,
          data: function () {
            return {
              data: {}
            }
          },
          methods: {
            getTargetText() {
              let text = []
              if (this.data.targetSystem && this.data.targetSystem.length) {
                for (let i = 0; i < this.data.targetSystem.length; i++) {
                  const ele = this.data.targetSystem[i]
                  if (ele === 'PUR') {
                    text.push('SRM采方平台')
                  }
                  if (ele === 'SUP') {
                    text.push('SRM供方平台')
                  }
                }
              }
              return text.join('，')
            }
          }
        })
      }
    }
  },
  {
    field: 'status',
    headerText: '状态',
    valueConverter: {
      type: 'map',
      map: [
        { value: 1, text: i18n.t('已保存'), cssClass: '' },
        { value: 2, text: i18n.t('已发布'), cssClass: '' },
        { value: 3, text: i18n.t('已撤销'), cssClass: '' },
        { value: 4, text: i18n.t('已删除'), cssClass: '' }
      ]
    }
  },
  {
    field: 'createUserName',
    headerText: '创建人'
  },
  {
    field: 'createTime',
    headerText: '创建时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  },
  {
    field: 'releaseUserName',
    headerText: '发布人'
  },
  {
    field: 'releaseTime',
    headerText: '发布时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  },
  {
    field: 'browseRecords',
    headerText: '浏览记录',
    ignore: true,
    cellTools: [{ id: 'View', icon: 'icon_solid_Submit', title: i18n.t('查看') }]
  }
]

// supplier column
export const supplierColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'companyCode',
    headerText: '公司代码'
  },
  {
    field: 'companyName',
    headerText: '公司名称'
  },
  {
    field: 'supplierCode',
    headerText: '供应商编码'
  },
  {
    field: 'supplierName',
    headerText: '供应商名称'
  },
  {
    field: 'categoryName',
    headerText: '外部物料组'
  }
]

// addition file column
export const additionColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'fileName',
    headerText: '文件名称',
    cssClass: 'field-content',
    cellTools: []
  },
  {
    field: 'fileSize',
    headerText: '文件大小'
  },
  {
    field: 'fileType',
    headerText: '文件类型'
  }
]

// list pageConfig
export const pageConfig = [
  {
    toolbar: [],
    grid: {
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '',
        params: {}
      }
    }
  }
]

// release page supplier pageConfig
export const supplierPageConfig = (disabled) => [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    toolbar: !disabled ? supplierToolbar : [],
    grid: {
      height: 'auto',
      columnData: supplierColumnData,
      dataSource: []
    }
  }
]

// release dialog add supplier pageConfig
export const addSupplierPageConfig = (modalData) => {
  const noticeExtList = modalData.noticeExtList
  let defaultRules = []
  if (noticeExtList.length) {
    defaultRules = [
      {
        field: 'companyCode',
        label: '公司编码',
        operator: 'in',
        type: 'string',
        value: noticeExtList.map((i) => i.companyCode)
      }
    ]
  }
  return [
    {
      toolbar: addSupplierToolbar,
      gridId: '36bcb9ae-e813-4620-ab73-c863fca4cac4',
      grid: {
        columnData: supplierColumnData,
        asyncConfig: {
          url: 'supplier/tenant/buyer/process/perspective/queryCategoryName',
          // url: 'supplier/tenant/buyer/process/perspective/query',
          params: {},
          defaultRules
        }
      }
    }
  ]
}

// detail addition file pageConfig
export const additionPageConfig = [
  {
    useToolTemplate: false,
    useBaseConfig: false,
    toolbar: additionToolbar,
    grid: {
      allowPaging: false,
      columnData: additionColumnData,
      dataSource: []
    }
  }
]

export const browseColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'viewUserName',
    headerText: '用户名'
  },
  {
    field: 'viewUserDesc',
    headerText: '用户描述'
  },
  {
    field: 'createDate',
    headerText: '浏览时间',
    searchOptions: {
      elementType: 'date-range',
      operator: 'between',
      serializeValue: (e) => {
        let obj = e.map((x) => Number(new Date(x.toString())))
        obj[1] = obj[1] + Number(86400000)

        //自定义搜索值，规则
        return obj
      }
    }
  },
  {
    field: 'ipAddress',
    headerText: 'IP地址'
  }
]
