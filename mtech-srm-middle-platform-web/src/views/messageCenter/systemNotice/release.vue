<template>
  <div class="full-height pt20 vertical-flex-box">
    <div class="top">
      <!-- 右侧各种操作按钮 -->
      <mt-button css-class="e-flat invite-btn" :is-primary="true" @click="goBack">{{
        $t('返回')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="handleSave">{{
        $t('保存')
      }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="handleRelease">{{
        $t('发布')
      }}</mt-button>
    </div>

    <div class="notice-add-form">
      <mt-form ref="ruleForm" :model="noticeForm" :rules="rules" :validate-on-rule-change="false">
        <mt-form-item prop="title" :label="$t('公告标题')">
          <mt-input v-model="noticeForm.title"></mt-input>
        </mt-form-item>
        <mt-form-item prop="noticeType" :label="$t('公告类型')">
          <mt-select
            ref="businessRef"
            v-model="noticeForm.noticeType"
            :data-source="noticeTypeData"
            :show-clear-button="true"
            :placeholder="$t('请选择')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item prop="targetSystem" :label="$t('发布范围')">
          <mt-multi-select
            ref="targetSystemRef"
            v-model="noticeForm.targetSystem"
            :data-source="targetSystemData"
            :placeholder="$t('请选择')"
          ></mt-multi-select>
        </mt-form-item>

        <mt-form-item :label="$t('有效期从')" prop="startTime">
          <mt-date-picker v-model="noticeForm.startTime" :show-clear-button="true"></mt-date-picker>
        </mt-form-item>

        <mt-form-item :label="$t('有效期至')" prop="endTime">
          <mt-date-picker v-model="noticeForm.endTime" :show-clear-button="true"></mt-date-picker>
        </mt-form-item>

        <mt-form-item
          v-if="noticeForm.noticeType === 'ITEM_NOTICE' && hasSupplier"
          prop="noticeExtList"
          :label="$t('公司')"
        >
          <mt-multi-select
            ref="supplierStatusRef"
            v-model="noticeForm.noticeExtList"
            :data-source="companyListData"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :fields="{ text: 'orgName', value: 'orgCode' }"
            :placeholder="$t('请选择')"
            @change="companyChange"
          ></mt-multi-select>
        </mt-form-item>

        <mt-form-item
          prop="radioType"
          :label="$t('选择供应商')"
          v-if="noticeForm.noticeType === 'ITEM_NOTICE' && hasSupplier"
        >
          <mt-radio v-model="radioType" :data-source="radioData"></mt-radio>
        </mt-form-item>

        <div class="supplier-table" v-if="noticeForm.noticeType === 'ITEM_NOTICE' && hasSupplier">
          <div class="supplier-item" v-if="radioType === '0'">
            <mt-form-item prop="supplierStatus" :label="$t('供应商状态')">
              <mt-multi-select
                ref="supplierStatusRef"
                v-model="noticeForm.supplierStatus"
                :data-source="supplierStatusData"
                :placeholder="$t('请选择')"
              ></mt-multi-select>
            </mt-form-item>
          </div>
          <div class="supplier-item" v-if="radioType === '1'">
            <div class="supplier-list">
              <div class="label">{{ $t('供应商列表') }}</div>
              <SupplierList
                :supplier-data="supplierData"
                @change="getSelectedData"
                :notice-ext-list="noticeExtList"
              />
            </div>
          </div>
        </div>
        <mt-form-item
          prop="body"
          class="full-width notice-content"
          height="500px"
          :label="$t('公告内容')"
        >
          <rich-text-editor ref="MtRichTextEditor" v-model="noticeForm.body" :height="300" />
        </mt-form-item>
      </mt-form>
      <div>
        <p
          style="cursor: pointer; color: rgba(0, 0, 0, 0.87); font-size: 14px; font-weight: 600"
          @click="handleUpload()"
        >
          {{
            noticeForm.noticeFileList.length
              ? `${$t('上传附件' + ', ' + $t('文件数'))}: ${noticeForm.noticeFileList.length},${$t(
                  '点此操操作'
                )}`
              : `${$t('上传附件')}, ${$t('点此操操作')}`
          }}
        </p>
        <mt-common-uploader
          ref="uploader"
          class="common-uploader"
          :save-url="saveUrl"
          :download-url="downloadUrl"
          :is-single-file="false"
          type="line"
          v-model="noticeForm.noticeFileList"
        ></mt-common-uploader>
      </div>
    </div>
  </div>
</template>

<script>
import { formateTime } from '@/utils/utils'
import SupplierList from './components/supplierList'
import RichTextEditor from '@/components/RichTextEditor/RichTextEditor'
import { cloneDeep } from 'lodash'
import MtCommonUploader from '@/components/mtech-common-uploader'

export default {
  components: {
    SupplierList,
    RichTextEditor,
    MtCommonUploader
  },
  data() {
    return {
      noticeForm: {
        title: '',
        noticeType: '',
        targetSystem: [],
        startTime: new Date(),
        endTime: this.getNextYearToday(),
        supplierStatus: null,
        supplierList: null,
        body: '',
        noticeFileList: [],
        noticeExtList: []
      },
      noticeExtList: [],
      otherNoticeForm: {
        id: '',
        noticeCode: ''
      },
      copyNoticeForm: null,
      supplierData: null,
      categoryCodeData: null,
      rules: {
        title: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        noticeType: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        targetSystem: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        startTime: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        endTime: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        body: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }],
        noticeExtList: [{ required: true, message: this.$t('该项必填'), trigger: 'blur' }]
      },
      companyListData: [],
      targetSystemData: [
        {
          text: this.$t('SRM采方平台'),
          value: 'PUR'
        },
        {
          text: this.$t('SRM供方平台'),
          value: 'SUP'
        }
      ],
      supplierStatusData: [
        {
          text: this.$t('注册'),
          value: 1
        },
        {
          text: this.$t('潜在'),
          value: 2
        },
        {
          text: this.$t('合格'),
          value: 10
        }
      ],
      noticeTypeData: [
        {
          text: this.$t('系统通知'),
          value: 'SYS_NOTICE'
        },
        {
          text: this.$t('事项公告'),
          value: 'ITEM_NOTICE'
        }
      ],
      radioType: '0',
      radioData: [
        {
          label: this.$t('按供应商状态'),
          value: '0'
        },
        {
          label: this.$t('按供应商列表'),
          value: '1'
        }
      ],
      isSaving: false,
      saveUrl: '/api/file/user/file/uploadPublic?useType=1', // 文件上传路径待
      downloadUrl: '/api/file/user/file/downloadPublicFile' //文件下载
    }
  },
  computed: {
    tenantId() {
      return this.$store.state.user.tenantId
    },
    isEdit() {
      //是否编辑页
      return this.$route?.path === '/middlePlatform/system-notice-management/edit' ? true : false
    },
    hasSupplier() {
      return (
        this.noticeForm.targetSystem &&
        this.noticeForm.targetSystem.length > 0 &&
        !this.noticeForm.targetSystem.includes('PUR')
      ) // 发布范围不存在采方时显示
    }
  },
  watch: {
    radioType(v) {
      if (v === '0') {
        this.supplierList = null
      } else {
        this.supplierStatus = null
      }
    }
  },
  mounted() {
    this.getPermissionCompany()
    this.copyNoticeForm = cloneDeep(this.noticeForm)
    if (this.isEdit) this.renderEditData()
  },
  methods: {
    handleUpload() {
      this.$refs.uploader.$children[1].showFileBaseInfo()
    },
    getNextYearToday() {
      let time = new Date()
      let y = time.getFullYear()
      let m = time.getMonth() + 1
      let d = time.getDate()
      return new Date(`${y + 1}-${m}-${d}`)
    },
    getPermissionCompany() {
      this.$api.messageCenter.getPermissionCompany().then((res) => {
        this.companyListData = res.data || []
      })
    },
    companyChange(arg) {
      const { value } = arg
      const noticeExtList = []
      this.companyListData.forEach((i) => {
        value.forEach((j) => {
          if (j === i.orgCode) {
            noticeExtList.push({
              companyId: i.id,
              companyName: i.orgName,
              companyCode: i.orgCode
            })
          }
        })
      })
      this.noticeExtList = noticeExtList
    },
    goBack() {
      // this.$router.go(-1)
      this.$router.push({
        path: '/middlePlatform/system-notice-management'
      })
      this.noticeForm = Object.assign({}, this.noticeForm, this.copyNoticeForm)
    },
    handlePostData(type) {
      const postData = this.getNoticeData()
      const validInfo = this.validNoticeData(postData)
      if (validInfo.length > 0) {
        let _tips = ''
        validInfo.map((item) => {
          _tips = `${item}<br/>`
        })
        this.$toast({ content: _tips, type: 'warning' })
        return
      }
      postData.type = type
      this.isSaving = !this.isSaving
      this.$api.messageCenter
        .noticeSave(postData)
        .then((res) => {
          if (res.code === 200) {
            if (type === 'save') {
              if (res.data) {
                this.$toast({ content: this.$t('保存成功'), type: 'success' })
                this.renderEditData(res.data)
                this.$router.push({
                  path: '/middlePlatform/system-notice-management/edit',
                  query: { id: res.data, timeStamp: new Date().getTime() }
                })
                return
              }
              this.$toast({ content: this.$t('保存失败'), type: 'warning' })
              this.isSaving = !this.isSaving
            } else {
              this.goBack()
              this.isSaving = !this.isSaving
            }
          } else {
            this.noticeForm = Object.assign({}, this.noticeForm, this.copyNoticeForm)
          }
        })
        .catch(() => {
          this.isSaving = !this.isSaving
        })
    },
    handleSave() {
      if (!this.isSaving) {
        this.handlePostData('save')
      }
    },
    handleRelease() {
      if (!this.isSaving) {
        this.handlePostData('release')
      }
    },
    getSelectedData(supplierList, companyCodeList) {
      if (companyCodeList && companyCodeList.length) {
        const noticeExtList = [...this.noticeForm.noticeExtList, ...companyCodeList]
        this.noticeForm.noticeExtList = Array.from(new Set(noticeExtList))
      }
      this.$set(this.noticeForm, 'supplierList', [...supplierList])
    },
    // 数据校验
    validNoticeData(data) {
      let _msg = []
      // 基础数据校验 标题、公告类型、发布范围、公告内容
      if (!data.title || data.title.length > 256) {
        _msg.push(this.$t('标题为空或过长'))
      }
      if (!data.noticeType) {
        _msg.push(this.$t('公告类型不能为空'))
      }
      // 如果是事项公告 & SRM 供方平台
      if (
        data.noticeType === 'ITEM_NOTICE' &&
        this.hasSupplier &&
        this.radioType === '0' &&
        (!data.supplierStatus || data.supplierStatus.length <= 0)
      ) {
        _msg.push(this.$t('供应商状态不能为空'))
      }
      if (
        data.noticeType === 'ITEM_NOTICE' &&
        this.hasSupplier &&
        this.radioType === '1' &&
        (!data.supplierList || data.supplierList.length <= 0)
      ) {
        _msg.push(this.$t('供应商数据不能为空'))
      }
      if (!data.targetSystem) {
        _msg.push(this.$t('发布范围不能为空'))
      }
      if (!data.body) {
        _msg.push(this.$t('公告内容不能为空'))
      }
      if (!data.startTime || !data.endTime) {
        _msg.push(this.$t('有效期不能为空'))
      }
      if (data.startTime > data.endTime) {
        _msg.push(this.$t('有效期开始时间不能晚于结束时间'))
      }
      if (
        data.noticeType === 'ITEM_NOTICE' &&
        this.hasSupplier &&
        (!data.noticeExtList || !data.noticeExtList.length)
      ) {
        _msg.push(this.$t('公司数据不能为空'))
      }
      return _msg
    },
    // 获取页面公告信息
    getNoticeData() {
      const {
        title,
        noticeType,
        targetSystem,
        supplierStatus,
        body,
        startTime,
        endTime,
        noticeExtList
      } = this.noticeForm
      const noticeFileList = this.transerDataId(cloneDeep(this.noticeForm.noticeFileList), 'file')
      const supplierList = this.transerDataId(cloneDeep(this.noticeForm.supplierList), 'supplier')
      const baseInfo = {
        title,
        noticeType,
        targetSystem,
        body,
        noticeFileList,
        startTime: `${formateTime(new Date(startTime), 'yyyy-MM-dd')} 00:00:00`,
        endTime: `${formateTime(new Date(endTime), 'yyyy-MM-dd')} 23:59:59`,
        noticeExtList:
          noticeType === 'ITEM_NOTICE' && this.hasSupplier && noticeExtList.length
            ? this.noticeExtList
            : []
      }
      if (this.isEdit) {
        Object.assign(baseInfo, {
          id: this.otherNoticeForm.id,
          noticeCode: this.otherNoticeForm.noticeCode
        })
      }

      if (noticeType === 'ITEM_NOTICE' && this.hasSupplier && this.radioType === '0') {
        return {
          ...baseInfo,
          supplierStatus,
          supplierList: null
        }
      }
      if (noticeType === 'ITEM_NOTICE' && this.hasSupplier && this.radioType === '1') {
        return {
          ...baseInfo,
          supplierList: supplierList,
          supplierStatus: null
        }
      }
      return baseInfo
    },
    // 初始化渲染编辑页面数据
    renderEditData(id) {
      this.$api.messageCenter
        .getEditNotice(id ? id : this.$route.query?.id)
        .then((res) => {
          if (res.code === 200) {
            const {
              title,
              noticeType,
              targetSystem,
              supplierStatus,
              supplierList,
              body,
              noticeFileList,
              id,
              noticeCode,
              startTime,
              endTime,
              noticeExtList
            } = res.data

            this.noticeForm = Object.assign({}, this.noticeForm, {
              title,
              noticeType,
              targetSystem,
              supplierStatus,
              supplierList,
              body,
              noticeFileList: noticeFileList || [],
              startTime: new Date(startTime) || null,
              endTime: new Date(endTime) || null,
              noticeExtList: noticeExtList?.map((i) => i.companyCode) || []
            })
            this.noticeExtList = noticeExtList
            this.otherNoticeForm.id = id
            this.otherNoticeForm.noticeCode = noticeCode

            this.supplierData = supplierList
            if (supplierList?.length > 0) {
              this.radioType = '1'
            }
            this.isSaving = false
          }
        })
        .catch(() => {
          this.isSaving = false
        })
    },
    // 处理noticeFileList数据 上传附件的id数据 type : supplier | file
    transerDataId(data, type) {
      const _field = type === 'supplier' ? 'supplierId' : 'fileId'
      if (!data || !Array.isArray(data)) return
      return data.map((item) => {
        const _id = item.id
        delete item.id
        return {
          [_field]: _id,
          ...item
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// @import '../../../../../node_modules/@mtech/mtech-common-uploader/build/esm/bundle.css';
.top {
  width: 100%;
  text-align: right;
  margin-bottom: 16px;
}
.bottom-tables {
  height: 100%;
}
.notice-add-form {
  /deep/ .mt-form-item {
    width: calc(33% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;

    .full-width {
      width: calc(100% - 20px) !important;
    }
    .mt-form-item-topLabel .label {
      margin-bottom: 4px;
      margin-top: 4px;
    }
    &.notice-content .mt-form-item-topLabel .label {
      margin-bottom: 8px;
    }
  }
}
.common-uploader {
  /deep/ .cell-operable-title {
    display: none;
  }
}
.supplier-list > div {
  margin-right: 10px;
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
}
.supplier-item {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  padding: 30px;
  margin-bottom: 16px;
  /deep/ .mt-form-item {
    width: calc(28% - 20px);
    min-width: 200px;
    display: inline-flex;
    margin-right: 20px;
  }
}

/deep/ .e-rte-content {
  height: 300px !important;
}
.search-btn {
  display: inline-block;
  vertical-align: bottom;
  margin-bottom: 20px;
  margin-left: 50px;
}
</style>
