import { i18n } from '@/main.js'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
  { id: 'Edit', icon: 'icon_solid_edit', title: i18n.t('编辑') },
  { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') },
  { id: 'Enable', icon: 'icon_table_enable', title: i18n.t('启用') },
  { id: 'Disable', icon: 'icon_table_disable', title: i18n.t('停用') },
  { id: 'upload', icon: 'icon_solid_upload', title: i18n.t('上传') },
  { id: 'Download', icon: 'icon_solid_Download', title: i18n.t('下载') }
  // { id: 'Submit', icon: 'icon_solid_Submit', title: '提交' }
]
const map = [
  {
    status: true,
    label: i18n.t('启用'),
    cssClass: ['OfferBid-status0']
  },
  {
    status: false,
    label: i18n.t('停用'),
    cssClass: ['OfferBid-status1']
  }
]
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'channelCode',
    headerText: i18n.t('渠道类型')
  },
  {
    field: 'name',
    headerText: i18n.t('账户名称')
  },
  {
    field: 'username',
    headerText: i18n.t('用户名')
  },
  {
    field: 'enable',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      map: map,
      fields: { text: 'label', value: 'status' }
    },
    cellTools: [
      {
        id: 'none',
        // icon: 'icon_solid_add',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data.enable === true
        }
      }
    ]
  }
]
export const pageConfig = [
  {
    toolbar,
    grid: {
      height: 'auto',
      columnData,
      // dataSource: []
      asyncConfig: {
        url: 'message/tenant/account/queryBuilder',
        params: {}
        // serializeList: (list) => {
        //   console.log(list, "遍历的数组");
        //   return list;
        // },
      }
    }
  }
]
