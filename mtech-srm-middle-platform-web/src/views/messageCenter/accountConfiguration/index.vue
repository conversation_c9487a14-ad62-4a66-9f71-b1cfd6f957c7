<!-- 消息账户配置 -->
<template>
  <div class="hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
import { i18n } from '@/main.js'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig
    }
  },
  props: {},
  computed: {},
  mounted() {},
  methods: {
    // 刷新页面
    initialCallInterface() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: 'message/tenant/account/queryBuilder',
        params: {}
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 点击行内
    handleClickCellTool(e) {
      if (e.tool.id === 'none') this.discontinuedOperations(e.data)
    },
    // 点击表头
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (
        _selectRows.length <= 0 &&
        (e.toolbar.id === 'Delete' || e.toolbar.id === 'Edit' || e.toolbar.id === 'Download')
      ) {
        this.$toast({ content: i18n.t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickDelete(_selectRows)
      } else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      } else if (e.toolbar.id === 'upload') {
        this.handleClickUpload()
      } else if (e.toolbar.id === 'Download') {
        this.handleClickDownload(_selectRows)
      }
    },
    // 新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: ".components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: i18n.t('新增账户')
        },
        success: () => {
          this.initialCallInterface()
        }
      })
    },
    // 删除
    handleClickDelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      console.log(_selectIds)
      this.$api.messageAccount.accountDel(_selectIds).then(() => {
        this.initialCallInterface()
        this.$toast({
          content: i18n.t('操作成功'),
          type: 'success'
        })
      })
    },
    // 编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: i18n.t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: ".components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: i18n.t('编辑账户'),
          id: _selectRows[0].id
        },
        success: () => {
          this.initialCallInterface()
        }
      })
    },
    // 停用
    discontinuedOperations(data) {
      console.log(data, '停用')
    },
    // 上传
    handleClickUpload() {
      this.$dialog({
        modal: () => import(/* webpackChunkName: "components/upload" */ './components/upload.vue'),
        data: {
          title: this.$t('上传')
        },
        success: () => {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 下载
    handleClickDownload(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: i18n.t('只能编辑一行'), type: 'warning' })
        return
      }
      const id = _selectRows[0].id
      const pramas = {
        accountId: id
      }
      this.$api.messageAccount.smsTemplateRefDownload(pramas).then((res) => {
        // console.log('=========', res)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hander {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .template-height {
    flex: 1;
  }
}
</style>
