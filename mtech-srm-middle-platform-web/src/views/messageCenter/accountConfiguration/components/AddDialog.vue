<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="rolling">
        <div class="slider-content">
          <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
            <!-- form-input -->
            <div class="formInput">
              <!-- 初始显示 -->
              <mt-form-item prop="channelCode" :label="$t('账户类型')">
                <mt-select
                  v-model="formObject.channelCode"
                  float-label-type="Never"
                  :data-source="channelCode"
                  :disabled="disabled ? true : false"
                  @change="channelCodeSelect"
                  :placeholder="$t('请选择账户类型')"
                ></mt-select>
                <!--  :fields="{ text: 'scoreDetailName', value: 'scoreDetailName' }" -->
              </mt-form-item>
              <!-- 第一层显示 -->
              <mt-form-item
                v-for="(item, index) in dynamicInput"
                :key="index"
                prop="channelCode"
                :label="item.title"
              >
                <mt-select
                  v-if="item.type == 'select'"
                  v-model="formObject[item.keys]"
                  float-label-type="Never"
                  :disabled="item.keys === 'sms_code' && disabled ? true : false"
                  :data-source="item.valueList"
                  :fields="{ text: 'text', value: 'value' }"
                  @change="accounmaintTypeSelect($event, item)"
                  :placeholder="$t('请选择') + item.title"
                ></mt-select>
                <mt-input
                  v-if="item.type == 'text'"
                  v-model="formObject[item.keys]"
                  float-label-type="Never"
                  :placeholder="$t('请输入') + item.title"
                ></mt-input>
              </mt-form-item>
            </div>
          </mt-form>
        </div>
      </div>
      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ $t('保存') }}</span>
        <span @click="cancel">{{ $t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { i18n } from '@/main.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      i18n,
      // 确认 取消按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('保存') }
        }
      ],
      // 初始值为空
      formObject: {
        channelCode: '', // 账户类型
        ext: {} // 动态值
      },
      // 必填
      formRules: {
        channelCode: [
          {
            required: true,
            message: i18n.t('请输入账户类型'),
            trigger: 'blur'
          }
        ]
      },
      // 账户类型
      channelCode: [
        { text: i18n.t('短信'), value: 'SMS' },
        { text: i18n.t('邮箱'), value: 'EMAIL' },
        { text: i18n.t('国际短信'), value: 'SMS_INTERNATIONAL' }
      ],
      uploadInfo: {}, // 上传后信息
      disabled: false,
      dynamicInput: [], // 所有数据
      dynamicInputTwo: []
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    id() {
      return this.modalData.id
    }
  },
  mounted() {
    if (this.id) {
      this.disabled = true
      this.editCallInterface()
    }
  },
  methods: {
    // 编辑操作
    editCallInterface() {
      this.$api.messageAccount.accountInfo(this.id).then((res) => {
        console.log(res.data, '编辑数据')
        // 第一步 (回选账户类型)
        this.formObject = res.data
        for (const key in this.formObject.ext) {
          this.formObject[key] = this.formObject.ext[key]
        }
        // 第二部 (创建所选账户类型的表单)
        const smsServiceProvider = []
        for (const key in res.data.formInfo) {
          const title = key.split('.')[1]
          if (key.split('.')[0] === 'ext') {
            // console.log(key, '带ext')
            this.formObject.ext[title] = ''
            res.data.formInfo[key].keys = title
          } else {
            // console.log(key, '不带ext')
            res.data.formInfo[key].keys = key
          }
          smsServiceProvider.push(res.data.formInfo[key])
        }
        this.dynamicInput = smsServiceProvider
        console.log(this.formObject, this.dynamicInput, '编辑info数据')
      })
    },
    // ----监听 第一层 账户类型
    channelCodeSelect(e) {
      if (!this.id) {
        const channelType = {
          channelType: e.itemData.value
        }
        this.$api.messageAccount.accountConfig(channelType).then((res) => {
          this.dynamicInput = [] // 第一层数组
          this.dynamicInputTwo = [] // 第一层数组
          // console.log(res.data, '第二层数据')
          for (const key in res.data) {
            const title = key.split('.')[1]
            if (key.split('.')[0] === 'ext') {
              console.log(key, '带ext')
              this.formObject.ext[title] = res.data[key].defaultValue
              this.formObject[title] = res.data[key].defaultValue
              res.data[key].keys = title
            } else {
              console.log(key, '不带ext')
              this.formObject[key] = ''
              res.data[key].keys = key
            }

            this.dynamicInput.push(res.data[key])
            this.dynamicInputTwo.push(res.data[key])
            // 必填
            if (res.data[key].type === 'select') {
              this.formRules[key] = [
                {
                  required: true,
                  message: this.$t('请选择') + res.data[key].title,
                  trigger: 'blur'
                }
              ]
            }
            if (res.data[key].type === 'text') {
              this.formRules[key] = [
                {
                  required: true,
                  message: this.$t('请输入') + res.data[key].title,
                  trigger: 'blur'
                }
              ]
            }
          }
          console.log(this.formObject, this.dynamicInput, '维护formObject和数组')
        })
      }
    },
    // ----监听 第二层 (选择短信/国际短信)
    accounmaintTypeSelect(e, item) {
      let smsServiceProvider = [] // 短信下维护的数组
      smsServiceProvider = [...this.dynamicInputTwo]
      this.dynamicInput = []
      if (item.keys === 'sms_code') {
        const parameter = {
          smsType: e.itemData.value
        }
        this.$api.messageAccount.accountSmsConfig(parameter).then((res) => {
          // console.log(res)
          for (const key in res.data) {
            const title = key.split('.')[1]
            if (key.split('.')[0] === 'ext') {
              console.log(key, '带ext')
              this.formObject.ext[title] = res.data[key].defaultValue
              this.formObject[title] = res.data[key].defaultValue
              res.data[key].keys = title
            } else {
              console.log(key, '不带ext')
              this.formObject[key] = ''
              res.data[key].keys = key
            }

            smsServiceProvider.push(res.data[key])

            // 必填
            if (res.data[key].type === 'select') {
              this.formRules[key] = [
                {
                  required: true,
                  message: this.$t('请选择') + res.data[key].title,
                  trigger: 'blur'
                }
              ]
            }
            if (res.data[key].type === 'text') {
              this.formRules[key] = [
                {
                  required: true,
                  message: this.$t('请输入') + res.data[key].title,
                  trigger: 'blur'
                }
              ]
            }
          }
          this.dynamicInput = this.dynamicInput.concat(smsServiceProvider) // 合并数组

          // console.log(this.dynamicInput)
        })
      }
    },
    // ----监听 第三层 (选择短信/国际短信)
    ChangeSmsServiceProvider(e, item) {
      console.log(e, item, '第二层')
    },
    // 上传
    chooseFiles() {
      // console.log(this.$t("点击上传"), data);
      // this.$loading()
      // let {files} = data.target
      // let params = {
      //   type: "array",
      //   limit: 50 * 1024,
      //   msg: "单个文件，限制50M",
      // };
      // if (files.length < 1) {
      //   this.$hloading()
      //   // 您未选择需要上传的文件
      //   return;
      // }
      // let _data = new FormData(),
      //   isOutOfRange = false;
      // for (let i = 0; i < files.length; i++) {
      //   _data.append("UploadFiles", files[i]);
      //   if (files[i].size > params.limit * 1024) {
      //     isOutOfRange = true;
      //     break;
      //   }
      // }
      // if (isOutOfRange) {
      //   this.$hloading()
      //   this.$toast({
      //     content: params.msg,
      //   });
      //   return;
      // }
      // _data.append("useType", 1); // (1 admin 平台管理 2 tenant 租户id 3 user 用户id)
      // fileData = _data
      // this.uploadFile()
    },
    // 关闭
    cancel() {
      this.$emit('cancel-function')
    },
    // 保存
    confirm() {
      for (const key in this.formObject.ext) {
        this.formObject.ext[key] = this.formObject[key]
      }
      const parameter = this.formObject
      console.log(parameter, '调用保存上传的数据')
      // 判断新增
      if (!this.id) {
        this.$api.messageAccount.accountAdd(parameter).then(() => {
          this.$emit('confirm-function')
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
        })
      } else {
        console.log(parameter)
        parameter.id = this.id
        this.$api.messageAccount.accountUpdate(parameter).then(() => {
          this.$emit('confirm-function')
          this.$toast({
            content: i18n.t('操作成功'),
            type: 'success'
          })
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.searchInput {
  /deep/ .e-input-group {
    border: none;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 720px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }
    .rolling {
      width: 100%;
      height: 100%;
      max-height: 670px;
      overflow-y: scroll;
      .slider-content {
        width: 100%;
        .mt-form {
          width: 100%;
          .formInput {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            .mt-form-item {
              width: 324px;
              height: 40px;
            }
          }
        }
      }
    }

    // .add-rule-group {
    //   margin-top: 30px;
    //   height: 14px;
    //   font-size: 14px;
    //   font-weight: 600;
    //   color: rgba(0, 70, 156, 1);
    //   width: 100%;
    //   text-align: center;
    //   cursor: pointer;
    // }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
</style>
