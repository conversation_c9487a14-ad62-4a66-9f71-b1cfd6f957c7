import Vue from 'vue'
const toolbar = [
  { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
  { id: 'Edit', icon: 'icon_solid_edit', title: '编辑' },
  { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' },
  { id: 'Enable', icon: 'icon_table_enable', title: '启用' },
  { id: 'Disable', icon: 'icon_table_disable', title: '停用' }
  // { id: 'Submit', icon: 'icon_solid_Submit', title: '提交' }
]
const columnData = [
  {
    type: 'checkbox',
    width: '60'
  },
  {
    field: 'templateText',
    headerText: '模板信息'
  },
  {
    field: 'stationTextList',
    headerText: '接收人信息',
    template: function () {
      return {
        template: Vue.component('actionOption', {
          template:
            '<div class="sendDiv"><span class="sendChannel" v-for="(item,index) in send" :key="index">{{item}}</span></div>',
          data() {
            return {
              data: {},
              send: []
            }
          },
          mounted() {
            this.processtheData()
          },
          methods: {
            processtheData() {
              this.send = this.data.stationTextList
            },
            handleSelect(e) {
              this.$bus.$emit('handleChangeExpertName', {
                index: this.data.index,
                value: e.value
              })
            }
          }
        })
      }
    }
  }
]
export const pageConfig = [
  {
    toolbar,
    grid: {
      height: 'auto',
      columnData,
      // dataSource: []
      asyncConfig: {
        url: 'message/tenant/tenantReceiver/queryBuilder',
        params: {}
        // serializeList: (list) => {
        //   console.log(list, "遍历的数组");
        //   return list;
        // },
      }
    }
  }
]
