<template>
  <div class="slider-panel-container">
    <div class="slider-modal mt-flex-direction-column">
      <div class="slider-header mt-flex">
        <div class="slider-title">{{ header }}</div>
        <div class="slider-close" @click="cancel">+</div>
      </div>
      <div class="rolling">
        <div class="slider-content">
          <mt-form ref="dialogRef" :model="formObject" :rules="formRules">
            <div class="formInput">
              <mt-form-item prop="receivType" :label="i18n.t('接收类型')">
                <mt-select
                  v-model="formObject.receivType"
                  float-label-type="Never"
                  :data-source="receivType"
                  @change="changeReceivType"
                  :placeholder="i18n.t('请选择接收类型')"
                ></mt-select>
              </mt-form-item>
              <mt-form-item
                v-if="formObject.receivType == 'employees'"
                prop="personnel"
                :label="i18n.t('人员')"
              >
                <mt-multi-select
                  v-model="formObject.personnel"
                  float-label-type="Never"
                  :data-source="personnel"
                  :fields="{ text: 'employeeName', value: 'id' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  :filtering="inputPersonnel"
                  @change="changePersonnel"
                  :placeholder="i18n.t('请选择人员')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item
                v-if="formObject.receivType == 'jobs'"
                prop="jobsName"
                :label="i18n.t('岗位名称')"
              >
                <mt-multi-select
                  v-model="formObject.jobsName"
                  float-label-type="Never"
                  :data-source="personnel"
                  :fields="{ text: 'standardStationName', value: 'id' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  :filtering="inputPersonnel"
                  @change="changePersonnel"
                  :placeholder="i18n.t('请选择岗位名称')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item
                v-if="formObject.receivType == 'user'"
                prop="userName"
                :label="i18n.t('用户名称')"
              >
                <mt-multi-select
                  v-model="formObject.userName"
                  float-label-type="Never"
                  :data-source="personnel"
                  :fields="{ text: 'userName', value: 'id' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  :filtering="inputPersonnel"
                  @change="changePersonnel"
                  :placeholder="i18n.t('请选择用户名称')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item
                v-if="formObject.receivType == 'role'"
                prop="roleName"
                :label="i18n.t('角色名称')"
              >
                <mt-multi-select
                  v-model="formObject.roleName"
                  float-label-type="Never"
                  :data-source="personnel"
                  :fields="{ text: 'roleName', value: 'id' }"
                  :show-clear-button="true"
                  :allow-filtering="true"
                  :filtering="inputPersonnel"
                  @change="changePersonnel"
                  :placeholder="i18n.t('请选择角色名称')"
                ></mt-multi-select>
              </mt-form-item>
              <mt-form-item prop="receiveRange" :label="i18n.t('接收范围')">
                <mt-select
                  v-model="formObject.receiveRange"
                  float-label-type="Never"
                  :data-source="receiveRange"
                  :fields="{ text: 'name', value: 'id' }"
                  @change="changeReceiveRange"
                  :placeholder="i18n.t('请选择接收范围')"
                ></mt-select>
              </mt-form-item>
            </div>
          </mt-form>
        </div>
      </div>

      <div class="slider-footer mt-flex">
        <span @click="confirm">{{ i18n.t('保存') }}</span>
        <span @click="cancel">{{ i18n.t('取消') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { utils } from '@mtech-common/utils'
import { i18n } from '@/main.js'
export default {
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      i18n,
      // 确认 取消按钮
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: i18n.t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: i18n.t('保存') }
        }
      ],
      // 初始值为空
      formObject: {
        receivType: '', // 接收类型
        receiveRange: '', // 接收范围/id
        personnel: [], // 人员
        personnelArr: [], // 人员数组
        jobsName: [], // 岗位
        roleName: [], // 角色
        userName: [], // 用户
        editId: '' // 编辑时单条数据的id
      },
      // 必填
      formRules: {
        receivType: [
          {
            required: true,
            message: i18n.t('接收类型'),
            trigger: 'blur'
          }
        ],
        receiveRange: [
          {
            required: true,
            message: i18n.t('接收人'),
            trigger: 'blur'
          }
        ]
      },
      // 接收类型
      receivType: [
        { text: i18n.t('岗位'), value: 'jobs' },
        { text: i18n.t('员工'), value: 'employees' },
        { text: i18n.t('用户'), value: 'user' },
        { text: i18n.t('角色'), value: 'role' }
      ],
      personnel: [], // 人员
      receiveRange: [] // 接收范围
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    id() {
      return this.modalData.id
    }
  },
  mounted() {
    this.initialCallInterface()
    this.inputPersonnel = utils.debounce(this.inputPersonnel, 1000)
    if (this.id) {
      // this.disabled = true
      this.editCallInterface()
    }
  },
  methods: {
    // 编辑数据查询
    editCallInterface() {
      this.$api.messageReceiving.tenantReceiverInfo(this.id).then((res) => {
        console.log(res, '编辑数据查询')
        this.formObject.editId = res.data.id
        // 如果是员工接口查询出来的
        if (res.data.receiverJson[0].type === 'EMPLOYEE') {
          this.formObject.receivType = 'employees'
          res.data.receiverJson.map((item) => {
            this.formObject.personnel.push(item.id)
          })
        }
        // 如果是岗位接口查询出来的
        if (res.data.receiverJson[0].type === 'STATION') {
          this.formObject.receivType = 'jobs'
          res.data.receiverJson.map((item) => {
            this.formObject.jobsName.push(item.id)
          })
        }
        // 如果是用户接口查询出来的
        if (res.data.receiverJson[0].type === 'UID') {
          this.formObject.receivType = 'user'
          res.data.receiverJson.map((item) => {
            this.formObject.userName.push(item.id)
          })
        }
        // 如果是角色接口查询出来的
        if (res.data.receiverJson[0].type === 'ROLE') {
          this.formObject.receivType = 'role'
          res.data.receiverJson.map((item) => {
            this.formObject.roleName.push(item.id)
          })
        }
        this.formObject.receiveRange = res.data.templateRefId
      })
    },
    // 接口
    initialCallInterface() {
      this.$api.messageCenter.templateGroupList().then((res) => {
        this.receiveRange = res.data
        console.log(this.receiveRange)
      })
    },
    // input事件
    inputPersonnel(e) {
      // inputPersonnel(e = { text: '' }) {
      console.log(e)
      this.$api.messageReceiving.employeefuzzyQuery({ employeeName: e.text }).then((res) => {
        const personnel = [...res.data, ...this.personnel]
        // 数组去重
        for (var i = 0; i < personnel.length; i++) {
          // 首次遍历数组
          for (var j = i + 1; j < personnel.length; j++) {
            // 再次遍历数组
            if (personnel[i].id === personnel[j].id) {
              // 判断连个值是否相等
              personnel.splice(j, 1) // 相等删除后者
              j--
            }
          }
        }
        this.personnel = personnel
        console.log(this.personnel, '数组所有查询出来的人员')
      })
    },
    // 人员change事件
    changePersonnel(e) {
      this.formObject.personnelArr = []
      e.value.map((item) => {
        this.personnel.map((items) => {
          if (item === items.id) {
            this.formObject.personnelArr.push(items)
          }
        })
      })
    },
    // 接收类型选择员工
    changeReceivType(e) {
      console.log(this.formObject)
      if (e.itemData.text === i18n.t('员工')) {
        const params = {
          employeeName: '',
          tenantld: ''
        }
        this.$api.messageReceiving.employeefuzzyQuery(params).then((res) => {
          this.personnel = res.data
          console.log(this.personnel, '员工数据')
        })
      }
      if (e.itemData.text === i18n.t('岗位')) {
        const params = {
          rules: [{ field: 'standardStationName', operator: 'contains', value: i18n.t('架构师') }],
          page: {
            current: 0
          }
        }
        this.$api.messageReceiving.stationPagedQuery(params).then((res) => {
          this.personnel = res.data.records
          console.log(this.personnel, '岗位数据')
        })
      }
      if (e.itemData.text === i18n.t('用户')) {
        const params = {
          condition: '',
          defaultRules: [],
          page: {
            current: 1,
            size: 100
          },
          pageFlag: false,
          rules: [],
          tenantId: 0
        }
        this.$api.messageReceiving.userPagedQuery(params).then((res) => {
          this.personnel = res.data.records
          console.log(this.personnel, '用户数据')
        })
      }
      if (e.itemData.text === i18n.t('角色')) {
        const params = {
          condition: '',
          defaultRules: [],
          page: {
            current: 1,
            size: 100
          },
          pageFlag: false,
          rules: []
        }
        this.$api.messageReceiving.rolePagedQuery(params).then((res) => {
          this.personnel = res.data.records
          console.log(this.personnel, '角色数据')
        })
      }
    },
    // 监听接收范围
    changeReceiveRange(e) {
      this.receiveRange.map((item) => {
        if (item.name === e.itemData.name) this.formObject.receiveRange = item.id
      })
      console.log(this.formObject)
    },
    // 取消
    cancel() {
      this.$emit('cancel-function')
    },
    // 保存
    confirm() {
      // 选择员工 EMPLOYEE
      // 角色     ROLE
      // 岗位       STATION
      // 用户    UID
      const receiverList = []
      console.log(this.formObject)
      // 岗位
      if (this.formObject.receivType === 'jobs') {
        this.formObject.personnelArr.map((item) => {
          receiverList.push({ id: item.id, name: item.standardStationName, type: 'STATION' })
        })
      }
      // 员工
      if (this.formObject.receivType === 'employees') {
        this.formObject.personnelArr.map((item) => {
          receiverList.push({ id: item.id, name: item.employeeName, type: 'EMPLOYEE' })
        })
      }
      // 用户
      if (this.formObject.receivType === 'user') {
        this.formObject.personnelArr.map((item) => {
          receiverList.push({ id: item.id, name: item.userName, type: 'UID' })
        })
      }
      // 角色
      if (this.formObject.receivType === 'role') {
        this.formObject.personnelArr.map((item) => {
          receiverList.push({ id: item.id, name: item.roleName, type: 'ROLE' })
        })
      }
      console.log(receiverList, '所有选中的员工信息')
      const params = {
        receiverList: receiverList,
        refType: 'TEMPLATE_GROUP',
        templateRefId: this.formObject.receiveRange
      }
      // 判断新增
      if (!this.id) {
        this.$api.messageReceiving.tenantReceiverAdd(params).then((res) => {
          console.log(res)
          this.$emit('confirm-function')
        })
      }
      // 编辑
      if (this.id) {
        params.id = this.formObject.editId
        this.$api.messageReceiving.tenantReceiverUpdate(params).then((res) => {
          console.log(res)
          this.$emit('confirm-function')
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .mt-col {
  padding: 0 10px 10px 10px;
}
/deep/ .select-container {
  height: 46px;
  padding: 5px;
}
/deep/ .e-input-group {
  padding-left: 5px;
}
.searchInput {
  /deep/ .e-input-group {
    border: none;
  }
}
.slider-panel-container {
  .slider-modal {
    width: 720px;
    border: none;
    .slider-header {
      height: 58px;
      background: #00469c;
      color: #fff;
      font-size: 18px;

      .slider-title {
        color: #fff;
        font-size: 18px;
      }

      .slider-close {
        color: #fff;
      }
    }
    .rolling {
      width: 100%;
      height: 100%;
      max-height: 670px;
      overflow-y: scroll;
      .slider-content {
        .formInput {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          .mt-form-item {
            width: 324px;
            height: 40px;
          }
        }
      }
    }

    // .add-rule-group {
    //   margin-top: 30px;
    //   height: 14px;
    //   font-size: 14px;
    //   font-weight: 600;
    //   color: rgba(0, 70, 156, 1);
    //   width: 100%;
    //   text-align: center;
    //   cursor: pointer;
    // }

    .slider-footer {
      height: 56px;
      background: rgba(245, 245, 245, 1);
      color: #00469c;
      font-size: 14px;
      flex-direction: row;
      box-shadow: none;
    }
  }
}
</style>
