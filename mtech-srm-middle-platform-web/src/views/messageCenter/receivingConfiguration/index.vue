<!-- 消息接收配置 -->
<template>
  <div class="hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      class="template-height"
      @handleClickToolBar="handleClickToolBar"
    />
  </div>
</template>
<script>
import { pageConfig } from './config'
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig
    }
  },
  methods: {
    // 刷新页面
    initialCallInterface() {
      this.$set(this.pageConfig[0].grid, 'asyncConfig', {
        url: 'message/tenant/tenantReceiver/queryBuilder',
        params: {}
      })
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 表头操作 tenantReceiverDel
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (_selectRows.length <= 0 && (e.toolbar.id === 'Delete' || e.toolbar.id === 'Edit')) {
        this.$toast({ content: i18n.t('请先选择一行'), type: 'warning' })
        return
      }
      if (e.toolbar.id === 'Add') {
        this.handleClickAdd()
      } else if (e.toolbar.id === 'Delete') {
        this.handleClickDelete(_selectRows)
      } else if (e.toolbar.id === 'Edit') {
        this.handleClickEdit(_selectRows)
      }
      // const _selectRows = e.grid.getSelectedRecords()
    },
    // 新增
    handleClickAdd() {
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: ".components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: i18n.t('新增/编辑邮件账户')
        },
        success: () => {
          this.initialCallInterface()
        }
      })
    },
    // 删除
    handleClickDelete(_selectRows) {
      const _selectIds = []
      _selectRows.map((item) => {
        _selectIds.push(item.id)
      })
      console.log(_selectIds)
      this.$api.messageReceiving.tenantReceiverDel(_selectIds).then(() => {
        this.initialCallInterface()
        this.$toast({
          content: i18n.t('操作成功'),
          type: 'success'
        })
      })
    },
    // 编辑
    handleClickEdit(_selectRows) {
      if (_selectRows.length > 1) {
        this.$toast({ content: i18n.t('只能编辑一行'), type: 'warning' })
        return
      }
      this.$dialog({
        modal: () =>
          import(/* webpackChunkName: ".components/AddDialog.vue" */ './components/AddDialog.vue'),
        data: {
          title: i18n.t('编辑账户'),
          id: _selectRows[0].id
        },
        success: () => {
          this.initialCallInterface()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.hander {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .template-height {
    flex: 1;
    .sendChannel {
      display: inline-block;
      padding: 0 5px;
      height: 20px;
      background: #eff2f8;
      border-radius: 2px;
      text-align: center;
      line-height: 20px;
      vertical-align: middle;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      margin: 0 10px;
    }
    // /deep/.e-gridcontent{
    //   height: 100%;
    // }
  }
}
</style>
