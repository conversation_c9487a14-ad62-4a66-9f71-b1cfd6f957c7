<template>
  <mt-dialog ref="dialog" :header="$t('新增地址')" :buttons="buttons" @close="hide">
    <mt-form ref="form" class="form--wrap" :model="formData" :rules="formRules">
      <mt-form-item :label="$t('地址用途:')" prop="addressPurpose">
        <mt-checkbox-group v-model="formData.addressPurpose">
          <mt-checkbox
            :content="{ label: $t('收货地址'), id: 'useForDeliveryDialog' }"
          ></mt-checkbox>
          <mt-checkbox :content="{ label: $t('收票地址'), id: 'useForTicketDialog' }"></mt-checkbox>
        </mt-checkbox-group>
      </mt-form-item>

      <mt-form-item :label="$t('收货人：')" prop="name">
        <mt-input
          v-model.trim="formData.name"
          maxlength="30"
          :placeholder="$t('请输入收货人名称')"
        />
      </mt-form-item>
      <mt-form-item :label="$t('联系方式：')" prop="phone">
        <mt-input
          v-model.trim="formData.phone"
          maxlength="15"
          :placeholder="$t('请输入收货联系人电话')"
        />
      </mt-form-item>
      <mt-form-item :label="$t('所在地区：')" prop="addressArea">
        <address-select
          :address="addressData"
          :areaLevel="['province', 'city', 'town']"
          @change="changeAddress"
        />
      </mt-form-item>
      <mt-form-item :label="$t('详细地址：')" prop="detailAddress">
        <mt-input
          v-model.trim="formData.detailAddress"
          maxlength="40"
          :placeholder="$t('请输入收货详细地址')"
        />
      </mt-form-item>
      <mt-form-item :label="$t('固定电话：')" prop="staticPhone">
        <mt-input
          v-model.trim="formData.staticPhone"
          maxlength="20"
          :placeholder="$t('请输联系的固定电话')"
        />
      </mt-form-item>
      <mt-form-item :label="$t('邮编：')" prop="postCode">
        <mt-input
          v-model.trim="formData.postCode"
          maxlength="6"
          :placeholder="$t('请输入邮编信息')"
        />
      </mt-form-item>
      <mt-form-item :label="$t('紧急联系人：')" prop="emergencyPerson">
        <mt-input
          v-model.trim="formData.emergencyPerson"
          maxlength="30"
          :placeholder="$t('请输入紧急联系人')"
        />
      </mt-form-item>
      <mt-form-item :label="$t('紧急联系电话：')" prop="emergencyPhone">
        <mt-input
          v-model.trim="formData.emergencyPhone"
          maxlength="15"
          :placeholder="$t('请输入紧急联系人电话')"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import AddressSelect from '@/components/AddressSelect.vue'
import { phoneRegex } from '@/utils/validator'

@Component({
  components: {
    AddressSelect
  }
})
export default class AddAddressDialog extends Vue {
  @Prop()
  value!: boolean

  @Prop({
    default: ''
  })
  id!: string

  formData: any = {
    addressArea: ''
  }

  formRules = {
    addressPurpose: [
      { required: true, message: (this as any).$t('请选择地址用途'), trigger: 'blur' }
    ],
    name: [{ required: true, message: (this as any).$t('请输入收货人名称'), trigger: 'blur' }],
    phone: [
      { required: true, message: (this as any).$t('请输入电话号码'), trigger: 'blur' },
      { validator: this.validatePhoneNo, trigger: 'blur' }
    ],
    addressArea: [{ required: true, message: (this as any).$t('请选在所在地区'), trigger: 'blur' }],
    detailAddress: [
      { required: true, message: (this as any).$t('请输入详细地址'), trigger: 'blur' }
    ],
    staticPhone: [{ validator: this.validateStaticPhone, trigger: 'blur' }],
    postCode: [{ validator: this.validatePostcode, trigger: 'blur' }],
    emergencyPhone: [{ validator: this.validatePhoneNo, trigger: 'blur' }]
  }

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: (this as any).$t('取消') }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: (this as any).$t('确定') }
    }
  ]

  get addressData() {
    return {
      provinceCode: this.formData.provinceCode,
      cityCode: this.formData.cityCode,
      townCode: this.formData.countyCode,
      provinceName: this.formData.provinceName,
      cityName: this.formData.cityName,
      townName: this.formData.countyName
    }
  }

  get isEdit() {
    return !!this.id
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
    this.resetAddressData()
  }

  show() {
    if (this.id) {
      this.getAddressDetail()
    } else {
      this.resetAddressData()
    }
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
  }

  handleConfirm() {
    ;(this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        this.saveAddress()
      }
    })
  }

  changeAddress(formData: any) {
    const { provinceCode, provinceName, cityCode, cityName, townCode, townName } = formData

    if (provinceCode && cityCode && townCode) {
      const addressArea = provinceName + cityName + townName
      this.$set(this.formData, 'addressArea', addressArea)
    } else {
      this.$set(this.formData, 'addressArea', '')
    }

    this.formData.countyCode = townCode
    this.formData.countyName = townName
    this.formData.provinceCode = provinceCode
    this.formData.provinceName = provinceName
    this.formData.cityCode = cityCode
    this.formData.cityName = cityName
  }

  validatePhoneNo(rule: any, value: any, callback: any) {
    if (value && !phoneRegex.test(value)) {
      callback(new Error((this as any).$t('电话号码格式不正确')))
    } else {
      callback()
    }
  }

  validateStaticPhone(rule: any, value: any, callback: any) {
    const regex = /\d{3}-\d{8}|\d{4}-\d{7}/
    if (value && !regex.test(value)) {
      callback(new Error((this as any).$t('固定电话格式不正确')))
    } else {
      callback()
    }
  }

  validatePostcode(rule: any, value: any, callback: any) {
    const regex = /\d{6}/
    if (value && !regex.test(value)) {
      callback(new Error((this as any).$t('邮编格式不正确')))
    } else {
      callback()
    }
  }

  private resetAddressData() {
    this.formData = {
      addressArea: '',
      addressPurpose: [],
      name: '', // 收货人
      phone: '', // 联系方式
      countyCode: '',
      countyName: '',
      cityCode: '',
      cityName: '',
      townCode: '',
      townName: '',
      detailAddress: '', // 详细地址
      staticPhone: '', // 固定电话
      postCode: '', // 邮编
      emergencyPerson: '', // 紧急联系人
      emergencyPhone: '' // 紧急联系人电话
    }
  }

  private mergeAddressData(data: any = {}) {
    this.resetAddressData()
    Object.assign(this.formData, data)

    if (data.useForDelivery) {
      this.formData.addressPurpose.push('useForDeliveryDialog')
    }
    if (data.useForTicket) {
      this.formData.addressPurpose.push('useForTicketDialog')
    }
  }

  private getAddressDetail() {
    this.$api.address
      .addressDetail({
        id: this.id
      })
      .then((res: any) => {
        this.mergeAddressData(res.data || {})
      })
      .catch((err: any) => {
        this.$toast({
          type: 'error',
          content: err.msg || (this as any).$t('详情获取失败')
        })
      })
  }

  private saveAddress() {
    const param = this.formatParam()

    const api = this.isEdit ? this.$api.address.addressUpdate : this.$api.address.addressAdd

    api(param)
      .then((res: any) => {
        this.$toast({
          type: 'success',
          content:
            res.msg || (this.isEdit ? (this as any).$t('编辑成功') : (this as any).$t('新增成功'))
        })
        this.$emit('save', param)
        this.hide()
      })
      .catch((err: any) => {
        this.$toast({
          type: 'error',
          content:
            err.msg || (this.isEdit ? (this as any).$t('编辑失败') : (this as any).$t('新增失败'))
        })
      })
  }

  private formatParam() {
    const param = Object.assign({}, this.formData, {
      useForDelivery: +this.formData.addressPurpose.includes('useForDeliveryDialog'),
      useForTicket: +this.formData.addressPurpose.includes('useForTicketDialog')
    })

    delete param.addressPurpose

    return param
  }
}
</script>

<style lang="scss" scoped>
.form--wrap {
  margin-top: 40px;
}
/deep/ .mt-form-item .label {
  white-space: normal;
  word-break: break-all;
}
</style>
