<template>
  <div class="address-item">
    <mt-form>
      <mt-form-item :label="$t('别名')" labelWidth="100px" labelStyle="left">
        <div v-show="isShowEditBtn">
          <mt-icon
            class="address-item--icon icon-color"
            name="icon_solid_editsvg"
            @click.native="editAlias"
          />
          <span>{{ address.alias }}</span>
          <span class="address-item--defaultname">{{ defaultAddressName }}</span>
        </div>

        <div v-show="!isShowEditBtn" class="address-alias--wrap">
          <mt-input
            v-model="newAlias"
            class="address-alias--input"
            maxlength="30"
            :placeholder="$t('请输入地址名称，建议名称：公司，仓库等')"
          ></mt-input>
          <mt-button type="primary" @click="saveAlias">{{ $t('保存') }}</mt-button>
        </div>
      </mt-form-item>

      <mt-form-item :label="$t('地址用途')" labelWidth="100px" labelStyle="left">
        <span style="margin-right: 12px">
          <mt-icon
            v-if="address.useForDelivery"
            name="a-icon_MultipleChoice_on"
            class="icon-color"
          ></mt-icon>
          <mt-icon v-else name="a-icon_MultipleChoice_off" class="icon-color"></mt-icon>
          <span style="margin-left: 12px">{{ $t('收货地址') }}</span>
        </span>

        <span>
          <mt-icon
            v-if="address.useForTicket"
            name="a-icon_MultipleChoice_on"
            class="icon-color"
          ></mt-icon>
          <mt-icon v-else name="a-icon_MultipleChoice_off" class="icon-color"></mt-icon>
          <span style="margin-left: 12px">{{ $t('收票地址') }}</span>
        </span>

        <!-- <mt-checkbox-group  >
          <mt-checkbox :checked="address.useForDelivery" :label="$t('收货地址')" value="useForDelivery" :readonly="true"></mt-checkbox>
          <mt-checkbox :checked="address.useForTicket" :label="$t('收票地址')" value="useForTicket" :readonly="true"></mt-checkbox>
        </mt-checkbox-group> -->
      </mt-form-item>

      <mt-row>
        <mt-col :span="12">
          <mt-form-item :label="$t('收货人')" labelWidth="100px" labelStyle="left">{{
            address.name
          }}</mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('联系方式')" labelWidth="100px" labelStyle="left">{{
            address.phone
          }}</mt-form-item>
        </mt-col>
      </mt-row>

      <mt-form-item :label="$t('所在地区')" labelWidth="100px" labelStyle="left">
        {{ address.location }}
      </mt-form-item>
      <mt-form-item :label="$t('详细地址')" labelWidth="100px" labelStyle="left">
        {{ address.fullAddress }}
      </mt-form-item>
      <mt-row>
        <mt-col :span="12">
          <mt-form-item :label="$t('固定电话')" labelWidth="100px" labelStyle="left">
            {{ address.staticPhone || $t('暂无') }}
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('邮编')" labelWidth="100px" labelStyle="left">
            {{ address.postCode || $t('暂无') }}
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row>
        <mt-col :span="12">
          <mt-form-item :label="$t('紧急联系人')" labelWidth="100px" labelStyle="left">
            {{ address.emergencyPerson || $t('暂无') }}
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('紧急联系电话')" labelWidth="100px" labelStyle="left">
            {{ address.emergencyPhone || $t('暂无') }}
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>

    <div class="button-wrap">
      <mt-tool-tip
        v-if="isShowDefaultAddressBtn"
        :target="`#${addressId}`"
        ref="tooltipTitle"
        :content="tooltipContent"
        :beforeOpen="beforeOpen"
        position="BottomCenter"
        tipPointerPosition="Middle"
        opensOn="Click"
        class="address-item--btn"
        style="width: inherit"
        @close="closeTooltip"
      >
        <mt-button :id="addressId" type="primary" @click="handleDefaultAddress">{{
          $t('设为默认地址')
        }}</mt-button>
      </mt-tool-tip>
      <mt-button
        v-if="isShowDeleteBtn"
        class="address-item--btn"
        type="primary"
        @click="handleDelete"
        >{{ $t('删除') }}</mt-button
      >
      <mt-button type="primary" @click="handleEdit">{{ $t('编辑') }}</mt-button>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from '@mtech/vue-property-decorator'
import SetDefaultAddress from './SetDefaultAddress.vue'
const Bus: any = require('@/utils/bus.js').default

@Component({
  components: {
    SetDefaultAddress
  }
})
export default class AddressItem extends Vue {
  @Prop({
    default: {
      id: '',
      alias: '',
      useForDelivery: 0,
      useForTicket: 0,
      name: '', // 收货人
      phone: '', // 联系方式
      fullAddress: '', // 详细地址
      staticPhone: '', // 固定电话
      postCode: '', // 邮编
      emergencyPerson: '', // 紧急联系人
      emergencyPhone: '' // 紧急联系人电话
    }
  })
  address!: any

  newAlias = ''
  isShowEditBtn = true

  tooltipContent = function () {
    return {
      template: SetDefaultAddress
    }
  }

  get addressId() {
    return `default-address-${this.address.id}`
  }

  get isShowDefaultAddressBtn() {
    const { defaultDeliveryFlag, defaultTicketFlag, useForDelivery, useForTicket } = this.address
    if (defaultDeliveryFlag && defaultTicketFlag && useForDelivery && useForTicket) {
      return false
    } else if (defaultDeliveryFlag && useForDelivery) {
      return false
    } else if (defaultTicketFlag && useForTicket) {
      return false
    } else {
      return true
    }
  }

  get isShowDeleteBtn() {
    const { defaultDeliveryFlag, defaultTicketFlag } = this.address
    return !(defaultDeliveryFlag || defaultTicketFlag)
  }

  get defaultAddressName() {
    const { defaultDeliveryFlag, defaultTicketFlag } = this.address

    if (defaultDeliveryFlag && defaultTicketFlag) {
      return (this as any).$t('默认地址（收货、收票）')
    } else if (defaultDeliveryFlag) {
      return (this as any).$t('默认地址（收货）')
    } else if (defaultTicketFlag) {
      return (this as any).$t('默认地址（收票）')
    } else {
      return ''
    }
  }

  created() {
    Bus.$on('close', this.closeTooltip)
    Bus.$on('confirm', this.confirmTooltip)
  }

  beforeDestroy() {
    Bus.$off('close', this.closeTooltip)
    Bus.$off('confirm', this.confirmTooltip)
  }

  handleDelete() {
    // if (this.isDefaultAddress) {
    //   this.$toast({
    //     type: 'warning',
    //     content: '默认地址不可删除'
    //   })
    //   return
    // }
    this.$dialog({
      data: {
        title: (this as any).$t('删除'),
        message: (this as any).$t('删除后将不可恢复，您是否确认删除该地址？')
      },
      success: () => {
        this.deleteAddress()
      }
    })
  }

  handleEdit() {
    const param = Object.assign({}, this.address)
    this.$emit('edit', param)
  }

  handleDefaultAddress() {}

  beforeOpen(event: any) {
    const { element } = event
    element.style.backgroundColor = '#fff'
    Bus.$emit('dataBind', this.address)
  }

  closeTooltip() {
    const ref = this.$refs.tooltipTitle as any
    ref && ref.ejsRef.close()
  }

  confirmTooltip(data: any) {
    const { defaultDeliveryFlag, defaultTicketFlag, id } = data
    if (id === this.address.id) {
      this.address.defaultDeliveryFlag = defaultDeliveryFlag
      this.address.defaultTicketFlag = defaultTicketFlag
      this.$emit('refresh')
      // this.closeTooltip(data.id)
      this.closeTooltip()
    }
  }

  editAlias() {
    this.newAlias = this.address.alias
    this.isShowEditBtn = false
  }

  saveAlias() {
    this.$api.address
      .addressAlias({
        id: this.address.id,
        alias: this.newAlias
      })
      .then(() => {
        this.address.alias = this.newAlias
        this.isShowEditBtn = true
      })
      .catch((err: any) => {
        this.$toast({
          type: 'error',
          content: err.msg || (this as any).$t('更新失败')
        })
      })
  }

  private deleteAddress() {
    this.$api.address
      .addressDelete({
        id: this.address.id
      })
      .then((res: any) => {
        this.$toast({
          type: 'success',
          content: res.msg || (this as any).$t('删除成功')
        })
        this.$emit('delete', this.address.id)
      })
      .catch((err: any) => {
        this.$toast({
          type: 'error',
          content: err.msg || (this as any).$t('删除失败')
        })
      })
  }
}
</script>
<style lang="scss" scoped>
.address-item {
  border: 1px solid #e8e8e8;
  position: relative;
  padding: 24px 24px 24px 0;
  /deep/ .mt-form-item {
    height: 35px;
  }
  .button-wrap {
    position: absolute;
    bottom: 36px;
    right: 24px;
  }
  .address-item--btn {
    margin-right: 12px;
  }
  .address-item--icon {
    cursor: pointer;
    margin-right: 12px;
  }
  .address-item--defaultname {
    color: #00469c;
    margin-left: 20px;
  }
  .address-alias--wrap {
    width: 100%;
    .address-alias--input {
      width: 300px;
      margin-right: 24px;
    }
  }
  .icon-color {
    color: #00469c;
  }
}
/deep/ .mt-form-item .label {
  white-space: normal;
  word-break: break-all;
}
</style>
