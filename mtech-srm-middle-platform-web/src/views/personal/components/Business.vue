<template>
  <div class="business">
    <div class="title">
      {{ $t('企业管理') }}
    </div>
    <div class="content" v-show="!isShowCreateEnterprise">
      <BusinessAdd />
      <BusinessCreate :refresh="refreshEnterprise" @create="createEnterprise" />
    </div>

    <CreateNewEnterprise
      class="content"
      v-if="isShowCreateEnterprise"
      @close="closeCreateEnterprise"
    />
  </div>
</template>

<script>
import BusinessAdd from './BusinessAdd.vue'
import BusinessCreate from './BusinessCreate.vue'
import CreateNewEnterprise from './CreateNewEnterprise.vue'

export default {
  data() {
    return {
      isShowCreateEnterprise: false,
      refreshEnterprise: false
    }
  },
  components: {
    BusinessAdd,
    BusinessCreate,
    CreateNewEnterprise
  },
  methods: {
    createEnterprise() {
      this.isShowCreateEnterprise = true
    },
    closeCreateEnterprise() {
      this.isShowCreateEnterprise = false
      this.refreshEnterprise = true
      setTimeout(() => {
        this.refreshEnterprise = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.business {
  .title {
    font-size: 20px;
    font-weight: bold;
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    border-bottom: 2px solid #e8e8e8;
  }
  .content {
    padding: 10px 20px;
  }
}
</style>
