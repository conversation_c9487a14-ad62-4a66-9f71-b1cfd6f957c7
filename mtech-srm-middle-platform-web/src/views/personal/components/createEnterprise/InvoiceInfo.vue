<template>
  <div class="enterprise-info--wrap">
    <div class="enterprise-btn--wrap">
      <span class="service-btn" @click="showInfoData">
        <svg-icon-platform icon-class="icon_login_choice"></svg-icon-platform>
        {{ $t('新增') }}
      </span>
    </div>

    <mt-data-grid
      class="data-grid--content"
      :dataSource="dataSource"
      :columnData="columnData"
      :rowTemplate="rowTemplate"
      :allowPaging="false"
      :editSettings="editSettings"
      ref="eventDataGrid"
    ></mt-data-grid>

    <InvoiceInfoDialog v-model="isShowInfoData" :data="currRowData" @save="saveInvoiceInfo" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import { invoiceInfoColumnData } from './config/index'
import InvoiceInfoDialog from './InvoiceInfoDialog.vue'
import InvocieInfoRow from './InvocieInfoRow.vue'
import bus from './config/eventBus'

@Component({
  components: {
    InvoiceInfoDialog
  }
})
export default class InvoiceInfo extends Vue {
  @Prop()
  enterpriseId!: string

  @Prop()
  enterpriseData!: any

  isShowInfoData = false
  dataSource: any[] = []

  columnData = invoiceInfoColumnData
  rowTemplate = function () {
    return { template: InvocieInfoRow }
  }

  editSettings = {
    allowEditing: true,
    allowAdding: true,
    allowDeleting: true
  }

  currRowData = {}

  @Watch('enterpriseData.enterpriseInvoiceDTOList')
  onWatchInvoiceList(val: any[]) {
    if (val && val.length) {
      const list = val.map((v) => {
        v.registerAddress = `${v.registerAddressCountryName || ''}${
          v.registerAddressProvinceName || ''
        }${v.registerAddressCityName || ''}${v.registerAddressDetail || ''}`
        return v
      })
      this.dataSource.unshift(...list)
    }
  }

  mounted() {
    bus.$on('InvoiceInfoRow-Edit', this.editRowData)
    bus.$on('InvoiceInfoRow-Delete', this.deleteRowData)
  }

  showInfoData() {
    this.currRowData = {}
    this.isShowInfoData = true
  }

  editRowData(data: any) {
    this.currRowData = data
    this.isShowInfoData = true
  }

  deleteRowData(data: any) {
    const ref: any = this.$refs.eventDataGrid

    const dataList: any[] = ref.ejsInstances.getCurrentViewRecords()

    this.dataSource = dataList.filter((v) => v.primaryId !== data.primaryId)
  }

  saveInvoiceInfo(data: any) {
    const ref: any = this.$refs.eventDataGrid
    const dataList: any[] = ref.ejsInstances.getCurrentViewRecords()

    const existRowIndex = dataList.findIndex((v) => v.primaryId === data.primaryId)

    if (existRowIndex > -1) {
      // ref.ejsInstances.updateRow(existRowIndex, data)
      dataList.splice(existRowIndex, 1, data)
      this.dataSource = dataList
    } else {
      ref.ejsInstances.addRecord(data)
    }
  }

  getFormData() {
    const ref: any = this.$refs.eventDataGrid
    const list: any[] = ref.ejsInstances.getCurrentViewRecords()
    return new Promise((resolve, reject) => {
      try {
        const params = list.map((data) => {
          const temp = { ...data }
          temp.enterpriseId = this.enterpriseId
          delete temp.registerAddress
          delete temp.primaryId
          return temp
        })
        resolve({
          enterpriseInvoiceDTOList: params
        })
      } catch (error) {
        reject(error)
      }
    })
  }
}
</script>

<style scoped lang="scss">
.data-grid--content {
  /deep/ .e-headertext {
    color: #292929;
    font-weight: 500;
    font-size: 14px;
  }
}
.enterprise-btn--wrap {
  text-align: right;
  margin-bottom: 15px;
  .service-btn {
    cursor: pointer;
  }
}
</style>
