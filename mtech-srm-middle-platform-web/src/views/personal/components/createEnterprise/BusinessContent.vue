<template>
  <mt-form class="business-content" ref="form" :model="formData" :rules="formRules">
    <mt-row :gutter="20">
      <mt-col :span="6">
        <imgUploader
          style="height: 200px"
          :exts="['.jpg', '.bmp', '.jpeg', '.png', '.webp']"
          :preview="true"
          size="middle"
          :isLogo="true"
          :logoUrl="formData.enterpriseLogoPath"
          @success="saveEnterpriseLogo"
        >
          <div class="upload-tip--text">
            <p>{{ $t('点击上传企业logo') }}</p>
            <p class="warn">{{ $t('图片大小不超过5M') }}</p>
          </div>
        </imgUploader>
        <!-- <mt-uploader :asyncSettings="asyncSettings" :hasFileLists="false" ></mt-uploader> -->
      </mt-col>

      <mt-col :span="18">
        <mt-row :gutter="68">
          <mt-col :span="12">
            <mt-form-item :label="$t('企业名称')" prop="enterpriseName">
              <mt-input
                type="text"
                :readonly="isReadonly"
                :showClearButton="false"
                v-model="formData.enterpriseName"
              ></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item :label="$t('企业中文简称')" prop="enterpriseShortName">
              <mt-input type="text" v-model="formData.enterpriseShortName"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="68">
          <mt-col :span="12">
            <mt-form-item :label="$t('企业英文全称')" prop="enterpriseFullEnglishName">
              <mt-input type="text" v-model="formData.enterpriseFullEnglishName"></mt-input>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item :label="$t('曾用名')" prop="enterpriseFormerName">
              <mt-input type="text" v-model="formData.enterpriseFormerName"></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="68">
          <mt-col :span="12">
            <mt-form-item :label="$t('企业身份代码类型')" prop="identityTypeCode">
              <mt-select
                :fields="dictSelectField"
                :dataSource="enterpriseIdentityTypeList"
                :readonly="isReadonly"
                :showClearButton="false"
                v-model="formData.identityTypeCode"
                :placeholder="$t('请选择企业身份代码类型')"
                @change="changeEnterpriseIdentityCode"
              >
              </mt-select>
            </mt-form-item>
          </mt-col>
          <mt-col :span="12">
            <mt-form-item :label="$t('编码')" prop="identityCode">
              <mt-input
                type="text"
                :readonly="isReadonly"
                :showClearButton="false"
                v-model="formData.identityCode"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-col>
    </mt-row>

    <mt-row :gutter="68">
      <mt-col :span="12">
        <mt-form-item :label="$t('工商类型')" prop="enterpriseRegisterTypeCode">
          <mt-select
            :fields="dictSelectField"
            :dataSource="enterpriseTypeList"
            :readonly="isReadonly"
            :showClearButton="false"
            v-model="formData.enterpriseRegisterTypeCode"
            :placeholder="$t('请选择工商类型')"
            @change="changeEnterpriseType"
          >
          </mt-select>
        </mt-form-item>
      </mt-col>
      <mt-col :span="12">
        <mt-form-item :label="$t('法人代表')" prop="corporation">
          <mt-input
            type="text"
            :readonly="isReadonly"
            :showClearButton="false"
            v-model="formData.corporation"
          ></mt-input>
        </mt-form-item>
      </mt-col>
    </mt-row>

    <mt-row :gutter="68">
      <mt-col :span="12">
        <mt-form-item :label="$t('注册资金')" prop="registerCapital">
          <div class="business-div--capital formitem-row--wrap">
            <mt-input
              class="business-input--capital"
              type="text"
              :showClearButton="false"
              v-model="formData.registerCapital"
              @change="formatCurrency"
            ></mt-input>

            <mt-select
              class="business-select--capital"
              :fields="currencyField"
              :dataSource="capitalCurrencyList"
              :readonly="isReadonly"
              :showClearButton="false"
              :allowFiltering="true"
              v-model="formData.capitalCurrency"
              :placeholder="$t('请选择币种')"
            >
            </mt-select>
          </div>
        </mt-form-item>
      </mt-col>
      <mt-col :span="12">
        <mt-form-item :label="$t('成立时间')" prop="enterpriseRegisteredDate">
          <mt-date-picker
            v-model="formData.enterpriseRegisteredDate"
            :readonly="isReadonly"
            :showClearButton="false"
          ></mt-date-picker>
        </mt-form-item>
      </mt-col>
    </mt-row>

    <mt-row :gutter="68">
      <mt-col :span="12">
        <mt-form-item :label="$t('核准时间')" prop="issuranceDate">
          <mt-date-picker
            v-model="formData.issuranceDate"
            :readonly="isReadonly"
            :showClearButton="false"
          ></mt-date-picker>
        </mt-form-item>
      </mt-col>
      <mt-col :span="12">
        <mt-form-item :label="$t('营业期限')" prop="businessDateRange">
          <div class="formitem-row--wrap business-div--date">
            <mt-date-range-picker
              class="business-date--range"
              v-model="formData.businessDateRange"
              :separator="$t('至')"
              :placeholder="$t('选择开通时间')"
              :showClearButton="false"
              :readonly="isReadonly || formData.longTermFlag"
            ></mt-date-range-picker>
            <mt-checkbox
              :checked="formData.longTermFlag"
              @change="changeLongTermFlag"
              :label="$t('长期有效')"
              :disabled="isReadonly"
            />
          </div>
        </mt-form-item>
      </mt-col>
    </mt-row>

    <mt-row>
      <mt-col>
        <mt-form-item :label="$t('注册地址')" prop="registerAddressCountryCode">
          <address-select
            :address="registerAddressData"
            :areaLevel="['country', 'province', 'city', 'detail']"
            @change="changeRegisterAddress"
          />
        </mt-form-item>
      </mt-col>
    </mt-row>

    <mt-row :gutter="20">
      <mt-col>
        <mt-form-item :label="$t('经营地址')" prop="operationAddressCountryCode">
          <address-select
            :address="operationAddressData"
            :areaLevel="['country', 'province', 'city', 'detail']"
            @change="changeOperationAddress"
          />
        </mt-form-item>
      </mt-col>
    </mt-row>

    <mt-row :gutter="20">
      <mt-col>
        <mt-form-item :label="$t('经营范围')" prop="businessScope">
          <mt-input
            v-model="formData.businessScope"
            :maxlength="1000"
            :multiline="true"
            :rows="5"
            :readonly="isReadonly"
            :showClearButton="false"
            type="text"
            :placeholder="$t('请输入经营范围')"
          />
        </mt-form-item>
      </mt-col>
    </mt-row>
  </mt-form>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from '@mtech/vue-property-decorator'
import imgUploader from './uploader.vue'
import AddressSelect from '../../../../components/AddressSelect.vue'
import currency from 'currency.js'

@Component({
  components: {
    imgUploader,
    AddressSelect
  }
})
export default class BusinessContent extends Vue {
  @Prop()
  firstData!: any

  @Prop()
  enterpriseData!: any

  isReadonly = false
  enterpriseTypeList: any[] = []
  enterpriseIdentityTypeList: any[] = []
  capitalCurrencyList: any[] = []

  dictSelectField: any = {
    value: 'itemCode',
    text: 'name'
  }

  currencyField = {
    value: 'currencyCode',
    text: 'currencyName'
  }

  formData: any = {}

  formRules: any = {
    enterpriseName: [
      { required: true, message: (this as any).$t('请输入企业名称'), trigger: 'blur' }
    ],
    identityTypeCode: [
      { required: true, message: (this as any).$t('请选择企业身份代码类型'), trigger: 'blur' }
    ],
    identityCode: [{ required: true, message: (this as any).$t('请输入编码'), trigger: 'blur' }],
    enterpriseRegisterTypeCode: [
      { required: true, message: (this as any).$t('请选择工商类型'), trigger: 'blur' }
    ],
    corporation: [{ required: true, message: (this as any).$t('请选择法人代表'), trigger: 'blur' }],
    registerCapital: [{ required: true, validator: this.validateRegisterCapital, trigger: 'blur' }],
    enterpriseRegisteredDate: [
      { required: true, message: (this as any).$t('请选择成立时间'), trigger: 'blur' }
    ],
    issuranceDate: [
      { required: true, message: (this as any).$t('请选择核准时间'), trigger: 'blur' }
    ],
    businessDateRange: [
      { required: true, message: (this as any).$t('请选择营业期限'), trigger: 'blur' }
    ],
    registerAddressCountryCode: [
      { required: true, validator: this.validateAddress, trigger: 'blur' }
    ],
    businessScope: [
      { required: true, message: (this as any).$t('请输入经营范围'), trigger: 'blur' }
    ]
  }

  get enterpriseId() {
    return this.firstData?.enterpriseId || ''
  }

  get registerAddressData() {
    return {
      countryCode: this.enterpriseData.registerAddressCountryCode,
      provinceCode: this.enterpriseData.registerAddressProvinceCode,
      cityCode: this.enterpriseData.registerAddressCityCode,
      countryName: this.enterpriseData.registerAddressCountryName,
      provinceName: this.enterpriseData.registerAddressProvinceName,
      cityName: this.enterpriseData.registerAddressCityName,
      addressDetail: this.enterpriseData.registerAddressDetail
    }
  }

  get operationAddressData() {
    return {
      countryCode: this.enterpriseData.operationAddressCountryCode,
      provinceCode: this.enterpriseData.operationAddressProvinceCode,
      cityCode: this.enterpriseData.operationAddressCityCode,
      countryName: this.enterpriseData.operationAddressCountryName,
      provinceName: this.enterpriseData.operationAddressProvinceName,
      cityName: this.enterpriseData.operationAddressCityName,
      addressDetail: this.enterpriseData.operationAddressDetail
    }
  }

  @Watch('enterpriseData')
  onWatchEnterpriseData(val: any) {
    if (val && Object.keys(val).length) {
      Object.keys(val).forEach((key) => {
        this.$set(this.formData, key, val[key])
      })

      if (val.businessStartDate && val.businessEndDate) {
        this.$set(this.formData, 'businessDateRange', [val.businessStartDate, val.businessEndDate])
      }
      if (!val.enterpriseName && this.firstData.enterpriseName) {
        this.$set(this.formData, 'enterpriseName', this.firstData.enterpriseName)
      }
      this.$set(this.formData, 'longTermFlag', !!val.longTermFlag)

      // this.$set(this.formData, 'enterpriseName', val)
      // Object.assign(this.formData, val)
    }
  }

  mounted() {
    this.getEnterpriseTypeList()
    this.getEnterpriseIdentityTypeList()
    this.getCapitalCurrency()
    // this.businessDataSource[0].content = this.$refs.business
  }

  saveEnterpriseLogo(id: string) {
    this.formData.enterpriseLogoFileId = id
  }

  formatCurrency(value: string) {
    const reg = /^[\d|,]*\.?\d{0,2}$/g

    if (reg.test(value)) {
      value = currency(value, { separator: ',', symbol: '' }).format()
      this.$set(this.formData, 'registerCapital', value)
      this.$set(this.formData, 'registerCapitalBak', value)
    } else {
      this.$set(this.formData, 'registerCapital', this.formData.registerCapitalBak)
    }
  }

  changeEnterpriseType(event: any) {
    const { itemData } = event
    this.formData.enterpriseRegisterTypeName = itemData?.name
  }

  changeEnterpriseIdentityCode(event: any) {
    const { itemData } = event
    this.formData.identityTypeName = itemData?.name
  }

  changeLongTermFlag(event: any) {
    // this.formData.longTermFlag = event.checked
    this.$set(this.formData, 'longTermFlag', event.checked)
  }

  changeRegisterAddress(formData: any) {
    this.formData.registerAddressCountryCode = formData.countryCode
    this.formData.registerAddressCountryName = formData.countryName
    this.formData.registerAddressProvinceCode = formData.provinceCode
    this.formData.registerAddressProvinceName = formData.provinceName
    this.formData.registerAddressCityCode = formData.cityCode
    this.formData.registerAddressCityName = formData.cityName
    this.formData.registerAddressDetail = formData.addressDetail
  }

  changeOperationAddress(formData: any) {
    this.formData.operationAddressCountryCode = formData.countryCode
    this.formData.operationAddressCountryName = formData.countryName
    this.formData.operationAddressProvinceCode = formData.provinceCode
    this.formData.operationAddressProvinceName = formData.provinceName
    this.formData.operationAddressCityCode = formData.cityCode
    this.formData.operationAddressCityName = formData.cityName
    this.formData.operationAddressDetail = formData.addressDetail
  }

  validateAddress(rule: any, value: any, callback: any) {
    const {
      registerAddressCountryCode,
      registerAddressProvinceCode,
      registerAddressCityCode,
      registerAddressDetail
    } = this.formData
    if (
      registerAddressCountryCode &&
      registerAddressProvinceCode &&
      registerAddressCityCode &&
      registerAddressDetail
    ) {
      callback()
    } else {
      callback(new Error((this as any).$t('请输入完整的注册地址')))
    }
  }

  validateRegisterCapital(rule: any, value: any, callback: any) {
    const reg = /^[\d|,]*\.?\d{0,2}$/g
    if (reg.test(value)) {
      callback()
    } else {
      callback(new Error((this as any).$t('请输入正确的数字格式')))
    }
  }

  // 获取表单元素
  getFormData() {
    return new Promise((resolve, reject) => {
      try {
        ;(this.$refs.form as any).validate((valid: boolean) => {
          if (valid) {
            const keyList = [
              'enterpriseLogoFileId',
              'enterpriseName',
              'enterpriseShortName',
              'enterpriseFullEnglishName',
              'enterpriseFormerName',
              'enterpriseRegisterTypeCode',
              'enterpriseRegisterTypeName',
              'corporation',
              'capitalCurrency',
              'enterpriseRegisteredDate',
              'issuranceDate',
              'businessScope',
              'registerAddressCountryCode',
              'registerAddressCountryName',
              'registerAddressProvinceCode',
              'registerAddressProvinceName',
              'registerAddressCityCode',
              'registerAddressCityName',
              'registerAddressDetail',
              'operationAddressCountryCode',
              'operationAddressCountryName',
              'operationAddressProvinceCode',
              'operationAddressProvinceName',
              'operationAddressCityCode',
              'operationAddressCityName',
              'operationAddressDetail',
              'identityTypeCode',
              'identityTypeName',
              'identityCode'
            ]
            const data: any = {
              enterpriseId: this.enterpriseId,
              businessStartDate: this.formData.businessDateRange[0],
              businessEndDate: this.formData.businessDateRange[1],
              longTermFlag: this.formData.longTermFlag ? 1 : 0,
              registerCapital: this.formData.registerCapital
                ? this.formData.registerCapital.split(',').join('')
                : undefined
            }
            keyList.forEach((key) => {
              data[key] = this.formData[key]
            })
            resolve(data)
          } else {
            reject(valid)
          }
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  addErrorLabels(errorLabels: any[]) {
    ;(this.$refs.form as any).addErrorLabels(errorLabels)
  }

  // 获取企业类型
  private async getEnterpriseTypeList() {
    const res = await this.$api.common.getDictItemTree({
      dictCode: 'EnterpriseType'
    })
    this.enterpriseTypeList = res?.data || []
  }

  // 获取企业身份代码类型
  private async getEnterpriseIdentityTypeList() {
    const res = await this.$api.common.getDictItemTree({
      dictCode: 'EnterpriseIdentityType'
    })
    this.enterpriseIdentityTypeList = res?.data || []
  }

  // 获取币种列表
  private async getCapitalCurrency() {
    const res = await this.$api.common.getCapitalCurrency()
    this.capitalCurrencyList = res?.data || []
  }
}
</script>

<style lang="scss" scoped>
.formitem-row--wrap {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.business-div--capital {
  .business-input--capital {
    width: 100%;
    margin-right: 20px;
  }

  .business-select--capital {
    width: 30%;
    max-width: 140px;
  }
}
.business-div--date {
  .business-date--range {
    margin-right: 20px;
  }
}
.upload-tip--text {
  color: #98aac3;
  margin-bottom: 20px 0;
  line-height: 28px;
  text-align: center;
  .warn {
    color: #f13e3e;
  }
}
</style>
