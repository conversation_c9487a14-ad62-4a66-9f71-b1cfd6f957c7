<template>
  <mt-dialog
    ref="dialog"
    :header="$t('选择主要产品/服务')"
    class="select-industry--dialog"
    :buttons="buttons"
    @close="hide"
  >
    <div class="search--wrap">
      <label class="">{{ $t('选择主要产品/服务') }}：</label>
      <mt-input
        ref="input"
        class="search-input"
        cssClass="e-outline"
        v-model="searchText"
        :placeholder="$t('请输入关键字进行过滤')"
        @keyup.enter.native="searchTextByEnter"
        @change="searchTextFromTree"
      />
    </div>

    <mt-treeView
      v-show="!isShowSearchTree"
      class="tree-view--template"
      ref="treeView"
      :fields="sourceTreeData"
      :auto-check="true"
      :show-check-box="true"
      @nodeSelected="nodeSelected"
    ></mt-treeView>

    <div>
      <mt-treeView
        v-if="isShowSearchTree"
        class="tree-view--template"
        ref="searchTreeView"
        :fields="searchTreeData"
        :auto-check="true"
        :show-check-box="true"
      ></mt-treeView>
    </div>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class SelectProductDialog extends Vue {
  @Prop()
  value!: boolean

  MAX_COUNT = 20

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: (this as any).$t('取消') }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: (this as any).$t('确定') }
    }
  ]

  searchIndustryList: any[] = [] // 查询出来的产品服务
  allIndustryData: any[] = [] // 完整的产品服务
  isShowSearchTree = false
  searchText = ''

  sourceTreeData = {
    dataSource: [],
    id: 'id',
    text: 'categoryName',
    child: 'children',
    parentID: 'parentId'
  }

  searchTreeData = {
    dataSource: [],
    id: 'id',
    text: 'categoryName',
    child: 'children'
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  mounted() {
    this.getIndustryData(0)
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
  }

  searchTextByEnter() {
    ;(this.$refs.input as any).ejsInstances.focusOut()
  }

  searchTextFromTree() {
    if (this.searchText) {
      this.$api.enterprise
        .getEshopTree({
          keyword: this.searchText
        })
        .then((res: any) => {
          if (res.code === 200 && res.data) {
            this.searchTreeData.dataSource = res.data?.treeList || []
            this.isShowSearchTree = true
          }
        })
    } else {
      this.isShowSearchTree = false
    }
  }

  nodeSelected(event: any) {
    const { nodeData } = event
    this.getIndustryData(nodeData.id)
  }

  private handleConfirm() {
    const ref = this.$refs.treeView as any
    const allCheckedNodes = ref.ejsInstances.getAllCheckedNodes()

    if (allCheckedNodes.length >= this.MAX_COUNT) {
      this.$toast({
        content: `最多添加${this.MAX_COUNT}个产品/服务`,
        type: 'warning'
      })
      return
    }

    const treeDataList = allCheckedNodes.map((v: string) => {
      return ref.ejsInstances.getNode(v)
    })
    treeDataList.forEach((v: any) => {
      v.categoryCode = this.insertCateCode(v.id)
    })
    this.$emit('save', treeDataList)
    this.hide()
  }

  insertCateCode(id?: string | number) {
    let code = null
    const [result] = this.allIndustryData.filter((i) => i.id == id)
    if (result) {
      code = result.categoryCode
    }
    return code
  }

  private async getIndustryData(id?: string | number) {
    if (this.allIndustryData.some((v) => v.parentId === id)) {
      return
    }

    const res = await this.$api.enterprise.findPlatformProduct({
      id: id
    })

    if (res.code === 200 && res.data) {
      const ref = this.$refs.treeView as any
      ref.ejsInstances.addNodes(res.data || [])
      ref.ejsInstances.expandAll([id])

      this.allIndustryData.push(...(res.data || []))
    }
  }
}
</script>

<style lang="scss" scoped>
.search--wrap {
  margin: 20px;
  display: flex;
  align-items: center;
  label {
    color: #3a3a3a;
  }
  .search-input {
    flex: 1;
  }
}
</style>
