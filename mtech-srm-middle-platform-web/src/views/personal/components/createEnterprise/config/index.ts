import { i18n } from '@/main'
export const enterpriseInfoColumnData = [
  // { width: '60', type: 'checkbox' },
  { field: 'fileType', headerText: i18n.t('文件类型'), width: 150 },
  { field: 'fileId', headerText: i18n.t('附件上传'), width: 500 },
  { field: 'template', headerText: i18n.t('模版'), width: 150 },
  { field: 'fileRule', headerText: i18n.t('上传文件规则') }
  // { field: 'remark', headerText: i18n.t('备注') }
]

export const invoiceInfoColumnData = [
  // { width: '60', type: 'checkbox' },
  {
    field: 'invoiceTitle',
    headerText: i18n.t('企业名称')
  },
  { field: 'taxNo', headerText: i18n.t('税号') },
  { field: 'registerAddress', headerText: i18n.t('注册地址') },
  { field: 'bankName', headerText: i18n.t('开户银行') },
  { field: 'bankAccount', headerText: i18n.t('银行账号') },
  { field: 'phoneNo', headerText: i18n.t('电话号码') }
]

export const invoiceInfoPageConfig = [
  {
    toolbar: ['add'],

    grid: {
      // selectionSettings: { checkboxOnly: true },
      columnData: [
        { width: '60', type: 'checkbox' },
        {
          field: 'invoiceTitle',
          headerText: i18n.t('企业名称')
        },
        { field: 'taxNo', headerText: i18n.t('税号') },
        { field: 'registerAddress', headerText: i18n.t('注册地址') },
        { field: 'bankName', headerText: i18n.t('开户银行') },
        { field: 'bankAccount', headerText: i18n.t('银行账号') },
        { field: 'phoneNo', headerText: i18n.t('电话号码') }
      ],
      dataSource: []
    }
  }
]
