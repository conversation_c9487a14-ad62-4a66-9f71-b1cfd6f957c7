<template>
  <div class="per-warp">
    <mt-tabs
      :eTab="false"
      :dataSource="dataSource"
      class="mt-tabs"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <div v-if="item == 1" class="inner-wrap">
      <featurePermission />
    </div>
    <div v-if="item == 0" class="inner-wrap">
      <dataPermission />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import featurePermission from './featurePermission/index.vue'
import dataPermission from './dataPermission/index.vue'
@Component({
  components: {
    featurePermission,
    dataPermission
  }
})
export default class PerMission extends Vue {
  dataSource: Array<any> = [
    {
      title: (this as any).$t('数据权限'),
      content: '0'
    },
    {
      title: (this as any).$t('功能权限'),
      content: '1'
    }
  ]

  item: any = 0

  handleSelectTab(e) {
    if (this.dataSource.length === 1) return
    this.item = e
  }

  created() {
    const tenantId = this.$store.state.user?.tenantId
    if (tenantId === '-99') {
      this.dataSource = [
        {
          title: (this as any).$t('功能权限'),
          content: '1'
        }
      ]
      this.item = 1
    }
  }
}
</script>

<style lang="scss" scoped>
.per-warp {
  height: 100%;
  .inner-wrap {
    height: calc(100vh - 240px);
    position: relative;
  }
}
</style>
