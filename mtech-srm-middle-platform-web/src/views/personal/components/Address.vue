<template>
  <div class="address--wrap">
    <div class="title">
      <span>{{ $t('我的地址') }}</span>

      <mt-button class="address-button--add" type="primary" @click="showAddress">{{
        $t('新增地址')
      }}</mt-button>
    </div>
    <div class="body" v-if="addressList.length">
      <AddressItem
        class="address-item--wrap"
        v-for="item in addressList"
        :key="item.id"
        :address="item"
        @edit="showAddress"
        @refresh="getAddressList"
        @delete="getAddressList"
      />

      <mt-page
        :pageSettings="page.pageSettings"
        :totalPages="page.totalPages"
        @currentChange="goToPage"
        @sizeChange="changePageSize"
      ></mt-page>
    </div>

    <div class="address--null" v-else>
      {{ $t('暂无地址数据') }}
    </div>

    <AddAddressDialog v-model="isShowDialog" :id="currEditAddress.id" @save="saveAddress" />
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import AddressItem from './componentsAddress/AddressItem.vue'
import AddAddressDialog from './componentsAddress/AddAddressDialog.vue'

@Component({
  components: {
    AddressItem,
    AddAddressDialog
  }
})
export default class Address extends Vue {
  isShowDialog = false
  currEditAddress = {}
  addressList = []

  page = {
    pageSettings: { pageSize: 5, pageCount: 1, pageSizes: [10, 20, 30, 60] },
    totalPages: 10
  }

  mounted() {
    this.getAddressList()
  }

  goToPage(count: number) {
    this.page.pageSettings.pageCount = count
    this.getAddressList()
  }

  changePageSize(size: number) {
    this.page.pageSettings.pageSize = size
    this.page.pageSettings.pageCount = 1
    this.getAddressList()
  }

  showAddress(data?: any) {
    if (data) {
      this.currEditAddress = data
    } else {
      this.currEditAddress = {}
    }
    this.isShowDialog = true
  }

  saveAddress() {
    this.getAddressList()
  }

  private getAddressList() {
    this.$api.address
      .addressList({
        page: {
          current: this.page.pageSettings.pageCount,
          size: this.page.pageSettings.pageSize
        }
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.addressList = res.data?.records || []

          // this.addressList = (res.data?.records || []).map((item: any) => {
          //   const { defaultDeliveryFlag, defaultTicketFlag, alias } = item

          //   if (alias) {
          //     return item
          //   }
          //   if (defaultDeliveryFlag && defaultTicketFlag) {
          //     item.alias = '默认地址（收货、收票）'
          //   } else if (defaultDeliveryFlag) {
          //     item.alias = '默认地址（收货）'
          //   } else if (defaultTicketFlag) {
          //     item.alias = '默认地址（收票）'
          //   } else {
          //     item.alias = ''
          //   }
          //   return item
          // })
          this.page.totalPages = Number(res.data.pages)
        }
      })
  }
}
</script>
<style lang="scss" scoped>
.address--wrap {
  .title {
    font-size: 20px;
    font-weight: bold;
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    border-bottom: 2px solid #e8e8e8;
  }
  .body {
    margin: 30px 20px;
  }
}
.address-button--add {
  float: right;
}
.address-item--wrap {
  margin-bottom: 12px;
}
.address--null {
  text-align: center;
  margin-top: 48px;
}
</style>
