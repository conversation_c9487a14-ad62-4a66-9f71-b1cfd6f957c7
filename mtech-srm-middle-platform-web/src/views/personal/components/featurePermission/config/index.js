import { i18n } from '@/main.js'

export const PERM_DIALOG_PAGE_PLUGIN = function (dataBound, rowDataBound, actionComplete) {
  return [
    {
      treeGrid: {
        allowPaging: false,
        autoCheckHierarchy: true,
        // selectionSettings: { type: "Multiple" },
        columnData: [
          {
            field: 'name',
            headerText: i18n.t('权限名称'),
            width: '350'
          },
          {
            field: 'permissionTypeName',
            width: '200',
            headerText: i18n.t('权限类型')
          },
          {
            width: '50',
            type: 'checkbox'
          }
        ],
        dataSource: [],
        childMapping: 'children',

        rowDataBound,
        dataBound,
        actionComplete
      }
    }
  ]
}

export const PERM_DIALOG_PAGE_PLUGIN_REVIEW_EMPTY = [
  {
    treeGrid: {
      allowPaging: false,
      autoCheckHierarchy: true,
      columnData: [
        {
          field: 'name',
          headerText: i18n.t('权限名称'),
          width: '350'
        },
        {
          field: 'permissionTypeName',
          width: '200',
          headerText: i18n.t('权限类型')
        },
        {
          width: '50',
          type: 'checkbox'
        }
      ],
      dataSource: [],
      childMapping: 'children'
    }
  }
]
