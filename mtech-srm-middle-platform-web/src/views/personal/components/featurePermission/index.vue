<template>
  <div class="chid-permission">
    <div class="roleTitle">{{ $t('所属角色') }}：</div>
    <div class="feature-wrapper">
      <div class="feature-left">
        <ul class="role-list" v-if="roleInculesList.length">
          <li
            v-for="(item, index) in roleInculesList"
            :class="{ active: index === index_ }"
            :key="index"
            @click="changeIndex(index)"
          >
            {{ item.roleName }}
          </li>
        </ul>
        <div v-else>{{ $t('暂无角色') }}</div>
      </div>
      <div class="feature-right" v-show="roleInculesList.length">
        <permAssignDisabled
          ref="permAssign"
          :role-info="roleInfo"
          :urls="urls"
        ></permAssignDisabled>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import permAssignDisabled from './components/permAssignDisabled.vue'
@Component({
  components: {
    permAssignDisabled
  }
})
export default class featurePermission extends Vue {
  roleInculesList: any = []
  created() {
    this.getRoleListById()
  }

  roleInfo: any = {}
  urls: any = {
    list: this.$api.permission.menuTreeGet,
    selectNodes: this.$api.permission.actionNodeSelectGet
  }

  index_: any = 0
  changeIndex(index) {
    if (index === this.index_) return
    this.index_ = index
    ;(this.$refs.permAssign as any)?.clearDataSource()
    this.roleInfo = this.roleInculesList[this.index_]
    if (this.roleInculesList.length) {
      this.permDialogOpen()
    }
  }

  async getRoleListById() {
    const param = {
      userId:
        this.$store.state.user?.uid ?? this.$store.state.user?.userId ?? '1502170682581024769',
      tenantId: this.$store.state.user?.tenantId ?? '17706479458443265'
    }
    const res = await (this as any).$api.permission.querySelfRoleList(param)
    this.roleInculesList = res.data || []
    this.roleInfo = this.roleInculesList[this.index_]
    this.permDialogOpen()
  }

  async permDialogOpen() {
    if (!this.roleInculesList.length) return
    await (this.$refs.permAssign as any).treeDataGet(this.roleInculesList[this.index_])
  }
}
</script>

<style scoped lang="scss">
.chid-permission {
  padding: 20px;
  height: 100%;
  .roleTitle {
    font-size: 14px;
    padding-left: 10px;
    font-weight: bold;
    margin-bottom: 20px;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      width: 2px;
      height: 14px;
      background: #00469c;
    }
  }
  .feature-wrapper {
    display: flex;
    height: 100%;
    .feature-left {
      width: 220px;
      padding-right: 20px;
      height: 100%;
      overflow-x: auto;
      .role-list {
        li {
          cursor: pointer;
          font-size: 16px;
          line-height: 1.5;
          padding: 10px 0 10px 10px;
        }
        li.active {
          background-color: rgba(0, 70, 156, 0.161);
          color: #00469c;
          position: relative;
          border-radius: 4px;
        }
      }
    }
    .feature-right {
      flex: 1;
      height: 100%;
    }
  }
}
/deep/ .feature-wrapper {
  .e-checkbox-wrapper .e-frame,
  .e-css.e-checkbox-wrapper .e-frame {
    background-color: #f1f1f1;
    border-color: #bdbdbd;
  }
  .e-grid .e-rowcell .e-css.e-checkbox-wrapper {
    pointer-events: none;
    cursor: default;
  }
  .e-checkbox-wrapper .e-frame.e-check,
  .e-css.e-checkbox-wrapper .e-frame.e-check {
    border-color: #bdbdbd;
    color: #00469c;
  }
  .e-checkbox-wrapper.e-checkbox-disabled .e-frame,
  .e-css.e-checkbox-wrapper.e-checkbox-disabled .e-frame {
    background-color: #fff;
    border-color: #bdbdbd;
    color: #bdbdbd;
  }
  .e-columnheader .e-css.e-checkbox-wrapper {
    pointer-events: none;
    cursor: default;
  }
  .e-grid td.e-rowcell .e-check {
    background-color: #bdbdbd !important;
  }
}
</style>
