<template>
  <div class="chid-permission">
    <div class="feature-wrapper">
      <div class="feature-left">
        <ul class="role-list">
          <li
            v-for="(item, index) in enumsList"
            :class="{ active: index == activeIndex }"
            :key="index"
            @click="getSelectedPermission(item, index)"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
      <div class="feature-right">
        <mt-template-page ref="tepPage" :template-config="pageConfig"></mt-template-page>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
@Component({})
export default class dataPermission extends Vue {
  enumsList: Array<any> = []
  activeIndex: any = 0
  currentValue: any = ''
  pageConfig: any = [
    {
      useToolTemplate: false,
      grid: {
        lineIndex: true,
        columnData: [
          {
            field: 'dimensionCodeValue',
            headerText: (this as any).$t('编码')
          },
          {
            field: 'dimensionNameValue',
            headerText: (this as any).$t('名称')
          }
        ],
        dataSource: []
      }
    }
  ]

  getSelectedPermission(data, index) {
    this.activeIndex = index
    this.currentValue = data.value
    this.getValueList()
  }

  created() {
    this.getDimensionEnumList()
  }

  // 获取维度枚举
  async getDimensionEnumList() {
    const res: any = await (this as any).$api.permission.permissionDimensionEnum()
    this.enumsList = res.data
    if (this.enumsList.length) {
      this.getSelectedPermission(this.enumsList[this.activeIndex], this.activeIndex)
    }
  }

  async getValueList() {
    const roots: any = {
      subjectId:
        this.$store.state.user?.uid ?? this.$store.state.user?.userId ?? '1502170682581024769',
      subjectType: 0,
      dimensionCode: this.currentValue
    }
    const res: any = await (this as any).$api.permission.querySelfPermissionList(roots)
    this.pageConfig[0].grid.dataSource = res.data
  }
}
</script>

<style scoped lang="scss">
.chid-permission {
  height: 100%;
  padding: 20px;
  .feature-wrapper {
    display: flex;
    height: 100%;
    .feature-left {
      width: 220px;
      padding-right: 20px;
      height: 100%;
      overflow-x: auto;
      .role-list {
        li {
          cursor: pointer;
          font-size: 16px;
          line-height: 1.5;
          padding: 10px 0 10px 10px;
        }
        li.active {
          background-color: rgba(0, 70, 156, 0.161);
          color: #00469c;
          position: relative;
          border-radius: 4px;
        }
      }
    }
    .feature-right {
      flex: 1;
      height: 100%;
    }
  }
}
</style>
