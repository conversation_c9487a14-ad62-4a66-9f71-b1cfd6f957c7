<!--通知列表-->
<template>
  <div>
    <mt-tabs
      tab-id="notice"
      :e-tab="false"
      :data-source="dataSource"
      @handleSelectTab="handleSelectTab"
    ></mt-tabs>
    <!-- 任务通知 -->
    <taskNotice v-if="tabsIndex == 0"></taskNotice>
    <!-- 消息通知 -->
    <messageNotice
      v-if="tabsIndex == 1"
      @clickMessageRead="clickMessageRead"
      :all-messages="allMessages"
    ></messageNotice>
  </div>
</template>
<script>
import { i18n } from '@/main.js'
// 默认看板小，中间内容大
export default {
  components: {
    // 任务通知
    taskNotice: () =>
      import(
        /* webpackChunkName: "@/views/notice/tabs/taskNotice/index.vue" */ './tabs/taskNotice/index.vue'
      ),
    // 消息通知
    messageNotice: () =>
      import(
        /* webpackChunkName: "@/views/notice/tabs/messageNotice/index.vue" */ './tabs/messageNotice/index.vue'
      )
  },
  data() {
    return {
      i18n,
      dataSource: [
        {
          value: '1501027937004331009',
          title: i18n.t('任务通知'),
          code: 'platform'
        },
        {
          value: '1504401652924895233',
          title: this.$t('消息通知'),
          code: 'sourcing'
        }
      ], // tabs数据

      tabsIndex: 0, // tabs显示
      allMessages: []
    }
  },
  mounted() {
    this.initialCallInterface() // 初始调用接口
  },
  methods: {
    initialCallInterface() {
      //我的全部消息
      const parameter = {
        page: {
          current: 1,
          size: 100
        }
      }
      this.$api.source.inmailMy(parameter).then((res) => {
        this.allMessages = res.data.records
        console.log(res, '我的所有消息')
      })
    },
    // 切换tabs
    handleSelectTab(e) {
      this.tabsIndex = e
    },
    //点击消息以后已读
    clickMessageRead() {
      this.initialCallInterface() // 初始调用接口
    }
  }
}
</script>
<style lang="scss" scoped>
.tabs-overall {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
