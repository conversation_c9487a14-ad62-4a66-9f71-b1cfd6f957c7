/*
 * @Author: your name
 * @Date: 2022-03-31 10:11:50
 * @LastEditTime: 2022-03-31 12:00:48
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \i18n\i18nReplace.js
 */
var fs = require('fs')

const targetUrl = 'index.vue' // 文件路径，替换即可
let text = fs.readFileSync(targetUrl).toString()

text = htmlAttrTextReplace()
text = htmlStrTextReplace()
text = jsTextReplace()

fileRewrite(targetUrl, text)

// 属性文本替换，如：label="xxx"
function htmlAttrTextReplace() {
  return text.replace(/[a-z]+="[\u4E00-\u9FFF]+"/g, function (str) {
    const cn = str.split('"')[1]

    if (str.startsWith('label=')) {
      return `:label="$t('${cn}')"`
    } else if (str.startsWith('placeholder=')) {
      return `:placeholder="$t('${cn}')"`
    } else {
      return str
    }
  })
}

// 模板的字符串替换，如：<div class="uploader-spu--remark">请拖拽图片或点击上传</div>
function htmlStrTextReplace() {
  return text.replace(/>[\u4E00-\u9FFF]+</g, function (str) {
    const cn = str.split('>')[1].split('<')[0]

    return `>{{ $t("${cn}") }}<`
  })
}

// js内文字替换，如：headerText: "公司法人",注意替换规则为  "xxx",
function jsTextReplace() {
  return text.replace(/"[\u4E00-\u9FFF]+",/g, function (str) {
    const cn = str.split('"')[1]

    return `this.$t("${cn}"),`
  })
}

function fileRewrite(targetFile, ctx) {
  fs.writeFile(targetFile, ctx, function (err) {
    if (err) {
      return console.error(err)
    }
    console.log('⚠️     完成了！     ⚠️')
  })
}
