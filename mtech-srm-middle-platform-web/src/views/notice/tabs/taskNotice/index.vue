<!--任务通知-->
<template>
  <div class="main">
    <div class="eachMessage">
      <div class="eachMessageDiv">
        <i :class="[true ? 'newMessageI' : 'readMessageI']"></i>
        <p :class="[true ? 'newMessageP' : 'readMessageP']">
          这是一条新的通知信息，左侧有红点表示该信息未读。当用户第二次打开此下拉，或者当前窗口点击“全部已读”之后变成已读状态。
        </p>
        <div class="dealWithDiv">
          <span class="dealWith">{{ $t('前往处理') }}</span>
          <span :class="[true ? 'newMessageSpan' : 'readMessageSpan']">2022-02-01 14:52:13</span>
        </div>
      </div>
    </div>
    <div class="allRead" @click="allReadClick">{{ $t('全部已读') }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  components: {},
  mounted() {},
  watch: {},
  computed: {},
  methods: {
    allReadClick() {
      console.log('全部已读')
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  margin: 0;
  width: 100%;
  .eachMessage {
    padding: 0 16px;
    box-sizing: border-box;
    position: relative;
    .eachMessageDiv {
      width: 100%;
      padding: 16px 0;
      border-bottom: 1px solid #e8e8e8;

      // 新消息红点
      .newMessageI {
        display: block;
        width: 4px;
        height: 4px;
        background-color: #ed5633;
        position: absolute;
        left: 5px;
        top: 20px;
      }
      //已读消息红点
      .readMessageI {
        display: none;
      }
      //新消息文本
      .newMessageP {
        text-align: left;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(41, 41, 41, 1);
        margin-bottom: 16px;
      }
      //已读消息文本
      .readMessageP {
        text-align: left;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(154, 154, 154, 1);
        margin-bottom: 16px;
      }
      //时间和前往处理
      .dealWithDiv {
        display: flex;
        align-items: center;
        .dealWith {
          height: 12px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(0, 70, 156, 1);
        }
        //新消息时间显示
        .newMessageSpan {
          width: 119px;
          height: 12px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(99, 134, 193, 1);
          margin-left: 16px;
        }
        //已读消息时间显示
        .readMessageSpan {
          width: 119px;
          height: 12px;
          font-size: 12px;
          font-family: PingFangSC;
          font-weight: normal;
          color: rgba(154, 154, 154, 1);
          margin-left: 16px;
        }
      }
    }
  }
  .eachMessage:hover {
    cursor: pointer;
  }
  .allRead {
    width: 76px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    margin-top: 22px;
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: 500;
    color: rgba(0, 70, 156, 1);
  }
  .allRead:hover {
    cursor: pointer;
  }
}
</style>
