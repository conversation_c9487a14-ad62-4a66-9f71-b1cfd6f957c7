<template>
  <div class="workflow-add">
    <WorkflowController @control="handleController" :form-data="formData" />
    <MtechBpmnEditor ref="editor"> </MtechBpmnEditor>
  </div>
</template>

<script>
import MtechBpmnEditor from '@digis/digis-bpmn-editor'
import WorkflowController from './components/WorkflowController.vue'

export default {
  components: {
    MtechBpmnEditor,
    WorkflowController
  },
  data() {
    return {
      formData: {
        id: '',
        workflowCode: '',
        workflowName: '',
        applicationId: '',
        version: 0
      },
      publishData: {
        data: '',
        templateId: '',
        workflowId: '',
        workflowName: ''
      }
    }
  },
  methods: {
    handleController(type, formData) {
      if (type === 'save' && !this.formData.id) {
        this.add(formData)
      } else if (type === 'save' && this.formData.id) {
        this.save(this.formData.id, formData)
      } else if (type === 'publish') {
        this.publish(formData)
      } else if (type === 'close') {
        this.close()
      }
    },
    add(formData) {
      const reg = /^[a-zA-Z]{1,20}$/
      if (!formData.workflowCode) {
        this.$toast({ content: this.$t('请输入模板编码'), type: 'warning' })
        return
      }
      if (!reg.test(formData.workflowCode)) {
        this.$toast({ content: this.$t('模板编码只能包含字母长度20位'), type: 'warning' })
        return
      }
      if (!formData.workflowName) {
        this.$toast({ content: this.$t('请输入模板名称'), type: 'warning' })
        return
      }
      if (!formData.applicationId) {
        this.$toast({ content: this.$t('请选择应用模块'), type: 'warning' })
        return
      }
      const editor = this.$refs.editor
      editor.getXML().then((res) => {
        // const _data = res
        this.$api.approvalTemplate
          .saveTemp({
            category: 0,
            workflowName: formData.workflowName,
            workflowCode: formData.workflowCode,
            applicationId: formData.applicationId,
            type: this.workflowType,
            data: res,
            version: formData.version
          })
          .then((res) => {
            this.publishData.templateId = res.data.templateId
            this.publishData.workflowId = res.data.workflowId
            this.publishData.workflowName = res.data.workflowName
            this.publishData.applicationId = res.data.applicationId
            console.log('保存成功', res)
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          })
          .catch((err) => {
            this.$toast({
              content: err.msg || this.$t('保存失败'),
              type: 'error'
            })
          })
      })
    },
    save(id, formData) {
      const editor = this.$refs.editor
      editor.getXML().then((res) => {
        this.$api.workflow
          .saveFlowTemplate({
            id: id,
            name: formData.name,
            data: res
          })
          .then((res) => {
            this.formData.id = res.data.id
            this.formData.key = res.data.key
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          })
          .catch((err) => {
            this.$toast({
              content: err.msg || this.$t('保存失败'),
              type: 'error'
            })
          })
      })
    },
    publish() {
      if (!this.publishData.templateId) {
        this.$toast({
          content: this.$t('请先保存模板，然后发布'),
          type: 'warning'
        })
        return
      }

      const editor = this.$refs.editor
      editor.getXML().then((res) => {
        this.publishData.data = res
        this.$api.approvalTemplate
          .publishTemp(this.publishData)
          .then((res) => {
            console.log('发布成功', res)
            this.$toast({
              content: this.$t('发布成功'),
              type: 'success'
            })
            setTimeout(() => {
              this.$router.push({
                path: '/middlePlatform/approval-template'
              })
            }, 1500)
          })
          .catch((err) => {
            console.error('保存失败', err)
            this.$toast({
              content: err.msg || this.$t('保存失败'),
              type: 'errror'
            })
          })
      })
    },
    close() {
      this.$router.push({
        path: '/middlePlatform/approval-template'
      })
    }
  },
  computed: {
    categoryId() {
      return 0
    },
    type() {
      return 'instance'
    },
    workflowType() {
      if (this.type === 'template') {
        return 'TemplateModel'
      } else if (this.type === 'instance') {
        return 'ProcessModel'
      } else {
        return ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-add {
  width: 100%;
  height: calc(100% - 86px);
}
</style>
