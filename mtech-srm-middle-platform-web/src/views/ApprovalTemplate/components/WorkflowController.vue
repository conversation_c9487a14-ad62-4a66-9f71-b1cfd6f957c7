<template>
  <div class="workflow-controller">
    <mt-form>
      <mt-row justify="space-between" type="flex" :gutter="20">
        <mt-col :span="8">
          <mt-form-item :label="$t('模板编码')">
            <mt-input
              v-model.trim="controllerData.workflowCode"
              :placeholder="$t('请输入模板编码')"
              :readonly="idDisabledKey"
              :show-clear-button="!idDisabledKey"
              max-length="20"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="8">
          <mt-form-item :label="$t('模板名称')">
            <mt-input
              v-model.trim="controllerData.workflowName"
              :placeholder="$t('请输入模板名称')"
              max-length="25"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="8">
          <mt-form-item :label="$t('应用模块')">
            <mt-select
              v-model="controllerData.applicationId"
              float-label-type="Never"
              :allow-filtering="true"
              :data-source="applicationData"
              :fields="{ text: 'applicationName', value: 'applicationId' }"
              :placeholder="$t('请选择应用模块')"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="8">
          <mt-form-item :label="$t('版本号')">
            <mt-input
              v-model="controllerData.version"
              :placeholder="$t('请输入模板编码')"
              :readonly="true"
              :show-clear-button="false"
            />
          </mt-form-item>
        </mt-col>
        <mt-col style="flex: 0 0 230px">
          <mt-form-item>
            <mt-button
              v-if="hasSaveBtn"
              class="btn--revert"
              icon-css="mt-icons mt-icon-icon_solid_Save1"
              css-class="e-flat"
              icon-position="Right"
              @click.native="handleController('save')"
              >{{ $t('保存') }}</mt-button
            >
            <mt-button
              class="btn--revert"
              icon-css="mt-icons mt-icon-icon_solid_Release"
              css-class="e-flat"
              icon-position="Right"
              @click.native="handleController('publish')"
              >{{ $t('发布') }}</mt-button
            >
            <mt-button
              class="btn--revert"
              icon-css="mt-icons mt-icon-icon_solid_close"
              css-class="e-flat"
              icon-position="Right"
              @click.native="handleController('close')"
              >{{ $t('关闭') }}</mt-button
            >
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </div>
</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      controllerData: {
        workflowCode: '',
        workflowName: '',
        applicationId: '',
        version: ''
      },
      applicationData: []
    }
  },
  methods: {
    handleController(type) {
      this.$emit('control', type, this.controllerData)
    },
    getApplicationList() {
      this.$api.approvalConfig.queryApplicationList({}).then((res) => {
        if (res.code === 200) {
          this.applicationData = res.data
        }
      })
    }
  },
  watch: {
    formData: {
      handler: function () {
        this.controllerData = {
          ...this.formData
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    idDisabledKey() {
      return !!this.formData.workflowCode
    },
    hasSaveBtn() {
      return !(Number(this.formData.version) > 0 && this.isEdit)
    }
  },
  created() {
    this.getApplicationList()
  }
}
</script>

<style lang="scss" scoped>
.workflow-controller {
  background: #f5f5f5;
  padding: 20px 20px 0 20px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
}
/deep/ .e-flat {
  font-size: 12px !important;
}
</style>
