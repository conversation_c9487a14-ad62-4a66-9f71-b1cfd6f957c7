<!--
 * @Author: your name
 * @Date: 2021-10-29 20:13:32
 * @LastEditTime: 2021-10-29 20:20:20
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalProcess\components\tenan.vue
-->
<template>
  <span
    >{{ $t('当前租户') }}：<span class="tenantName">{{ user.tenantName }}</span></span
  >
</template>
<script>
export default {
  name: 'Tenan',
  data() {
    return {
      user: {}
    }
  },
  methods: {
    getUserInfo() {
      this.$api.approvalCenter.getUserInfo({}).then((res) => {
        if (res.data) this.user = res.data
      })
    }
  },
  created() {
    this.getUserInfo()
  }
}
</script>
<style lang="scss" scoped>
.tenantName {
  font-size: 20px;
  font-weight: bold;
}
</style>
