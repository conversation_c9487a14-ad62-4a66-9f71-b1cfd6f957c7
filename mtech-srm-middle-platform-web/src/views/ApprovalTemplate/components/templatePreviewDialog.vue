<!--
 * @Author: your name
 * @Date: 2021-10-14 20:47:57
 * @LastEditTime: 2021-10-28 16:48:29
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalTemplate\components\templatePreviewDialog.vue
-->
<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :close="close"
    :buttons="button"
    :header="header"
  >
    <mt-form>
      <mt-form-item :label="$t('审批流名称')">
        <mt-input v-model="data.workflowName" :show-clear-button="false" :readonly="true" />
      </mt-form-item>

      <mt-form-item :label="$t('模板编码')">
        <mt-input v-model="data.workflowCode" :show-clear-button="false" :readonly="true" />
      </mt-form-item>

      <mt-form-item :label="$t('版本号')">
        <mt-input v-model="data.version" :show-clear-button="false" :readonly="true" />
      </mt-form-item>

      <mt-form-item :label="$t('应用模块')">
        <mt-input v-model="data.applicationName" :readonly="true" :show-clear-button="false" />
      </mt-form-item>

      <mt-form-item :label="$t('更新人')">
        <mt-input v-model="data.updateUserName" :readonly="true" :show-clear-button="false" />
      </mt-form-item>

      <mt-form-item :label="$t('更新时间')">
        <mt-input v-model="data.updateTime" :readonly="true" :show-clear-button="false" />
      </mt-form-item>

      <mt-form-item :label="$t('状态')">
        <mt-input v-model="data.workflowStatusView" :readonly="true" :show-clear-button="false" />
      </mt-form-item>
    </mt-form>

    <div class="template-scroll" v-html="processImage"></div>
  </mt-dialog>
</template>
<script>
// import Bus from '@/utils/bus'
// import moment from 'moment'
// import templateStep from "@/views/ApprovalTemplate/components/templateStep";
export default {
  data() {
    return {
      buttonType: {
        0: [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('返回') }
          },
          {
            click: this.edit,
            buttonModel: { content: this.$t('编辑') }
          }
        ],
        1: [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('返回') }
          },
          {
            click: this.edit,
            buttonModel: { content: this.$t('编辑') }
          },
          {
            click: this.stop,
            buttonModel: { content: this.$t('禁用') }
          }
        ],
        2: [
          {
            click: this.cancel,
            buttonModel: { content: this.$t('返回') }
          },
          {
            click: this.edit,
            buttonModel: { content: this.$t('编辑') }
          }
        ]
      },
      data: {},
      processImage: '',
      isChanged: false
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    templateId() {
      return this.modalData.templateId
    },
    button() {
      return this.buttonType[this.data.workflowStatus]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    console.log(this.$t('版本'), '5555555')
  },
  methods: {
    cancel() {
      this.$emit('confirm-function', this.isChanged)
    },
    close() {
      this.$emit('confirm-function', this.isChanged)
    },
    getTemplateData(templateId) {
      return new Promise((resolve) => {
        this.$api.approvalTemplate
          .getTempDetailById({
            templateId
          })
          .then((res) => {
            this.data = {
              ...res.data
            }
            this.data.updateTime = this.$api.common.Dateformat(this.data.updateTime)
            resolve(res.data.workflowId)
          })
      })
    },
    getTempImg(templateId) {
      this.$api.approvalTemplate.getTempImg({ workflowId: templateId }).then((res) => {
        this.processImage = res.data
      })
    },
    edit() {
      const data = this.data
      this.$emit('confirm-function')
      this.modalData.parentVm.$router.push({
        path: `/middlePlatform/template-edit/${data.workflowId}/${data.applicationId}/${data.templateId}`
      })
    },
    stop() {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否停用该条记录？')
        },
        success: () => {
          this.$api.approvalTemplate
            .changeTempStatus({ templateId: this.templateId, workflowStatus: 0 })
            .then((res) => {
              if (res.code === 200) {
                this.isChanged = true
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.getTemplateData(this.templateId)
              }
            })
        }
      })
    }
  },
  created() {
    this.getTemplateData(this.templateId).then((res) => {
      this.getTempImg(res)
    })
  }
}
</script>
<style lang="scss" scoped>
.template-approval-detail {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  li {
    margin-right: 15px;
    margin-bottom: 20px;
    font-size: 14px;
    min-width: 220px;

    label {
      display: inline-block;
      width: 90px;
      text-align: right;
      font-weight: bold;
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
  }
}
.template-scroll {
  width: 100%;
  overflow: auto;
}
</style>
