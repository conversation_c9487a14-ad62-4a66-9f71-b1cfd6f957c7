import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-14 12:23:18
 * @LastEditTime: 2021-10-25 21:56:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalProcess\config\index.js
 */
import columTime from '../components/columTime.vue'

export const listToolBar = {
  useBaseConfig: false,
  tools: [
    [
      {
        id: 'Add',
        icon: 'icon_solid_Createorder',
        title: i18n.t('新增')
      }
    ],
    ['Refresh']
  ]
}
export const listColumnData = [
  {
    field: 'workflowCode',
    headerText: i18n.t('模板编码'),
    cssClass: '',
    width: 200,
    allowFiltering: false
  },
  {
    field: 'workflowName',
    headerText: i18n.t('模板名称'),
    width: 200
  },
  {
    field: 'applicationName',
    headerText: i18n.t('应用模块')
  },
  {
    field: 'version',
    headerText: i18n.t('版本号')
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    template: function () {
      return {
        template: columTime
      }
    }
  },
  {
    field: 'workflowStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: {
        text: 'label',
        value: 'status'
      },
      map: [
        {
          status: 0,
          label: i18n.t('已禁用'),
          cssClass: ['statusOff']
        },
        {
          status: 1,
          label: i18n.t('已启用'),
          cssClass: ['statusOn']
        },
        {
          status: 2,
          label: i18n.t('草稿'),
          cssClass: ['statusOn cg']
        }
      ]
    },
    cellTools: [
      {
        id: 'preview',
        icon: 'icon_solid_Activateorder',
        title: i18n.t('查看')
      }
      // {
      //     id: "edit",
      //     icon: "icon_solid_edit",
      //     title: i18n.t("编辑"),
      //   },
      //   {
      //     id: "stop",
      //     icon: "icon_solid_edit",
      //     title: i18n.t("停用"),
      //     visibleCondition: (data) => {
      //           //enableStatus	状态 0 草稿 1 启用 2 停用
      //           return data["workflowStatus"] != 0;
      //         },
      //   }
    ]
  }
]
