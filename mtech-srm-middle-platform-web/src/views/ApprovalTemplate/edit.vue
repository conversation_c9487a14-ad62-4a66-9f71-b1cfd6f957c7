<template>
  <div class="workflow-edit">
    <WorkflowController @control="handleController" :form-data="formData" :is-edit="true" />
    <MtechBpmnEditor :id="workflowId" ref="editor"> </MtechBpmnEditor>
  </div>
</template>

<script>
import MtechBpmnEditor from '@digis/digis-bpmn-editor'
import WorkflowController from './components/WorkflowController.vue'

export default {
  components: {
    MtechBpmnEditor,
    WorkflowController
  },
  data() {
    return {
      formData: {
        templateId: '',
        workflowCode: '',
        workflowName: '',
        workflowId: '',
        applicationId: '',
        version: 0
      }
    }
  },
  methods: {
    getTemplateData(workflowId) {
      this.$api.approvalTemplate
        .getTempDetailByWorkId({
          workflowId: workflowId
        })
        .then((res) => {
          const { name, key, version } = res.data
          const e = this.formData
          e.templateId = this.templateId
          e.applicationId = this.applicationId
          e.workflowCode = key
          e.workflowName = name
          e.workflowId = this.workflowId
          e.version = version
          console.log(this.formData)
        })
    },
    handleController(type, formData) {
      if (type === 'save' && this.formData.templateId) {
        this.save(formData)
      }
      if (type === 'publish') {
        this.publish(formData)
      } else if (type === 'close') {
        this.close()
      }
    },
    save(formData) {
      const editor = this.$refs.editor
      editor.getXML().then((res) => {
        this.$api.approvalTemplate
          .updateTemp({
            applicationId: formData.applicationId,
            templateId: this.formData.templateId,
            workflowId: this.formData.workflowId,
            data: res,
            workflowName: formData.workflowName
          })
          .then(() => {
            this.$toast({
              content: this.$t('保存成功'),
              type: 'success'
            })
          })
          .catch((err) => {
            this.$toast({
              content: err.msg || this.$t('保存失败'),
              type: 'error'
            })
          })
      })
    },
    publish(formData) {
      if (!this.formData.templateId) {
        this.$toast({
          content: this.$t('请先保存模板，然后发布'),
          type: 'warning'
        })
        return
      }

      const editor = this.$refs.editor
      editor.getXML().then((res) => {
        this.$api.approvalTemplate
          .publishTemp({
            applicationId: formData.applicationId,
            data: res,
            templateId: this.formData.templateId,
            workflowId: this.formData.workflowId,
            workflowName: formData.workflowName
          })
          .then(() => {
            this.$toast({
              content: this.$t('发布成功'),
              type: 'success'
            })
            setTimeout(() => {
              this.$router.push({
                path: '/middlePlatform/approval-template'
              })
            }, 1500)
          })
          .catch((err) => {
            this.$toast({
              content: err.msg || this.$t('发布失败'),
              type: 'errror'
            })
          })
      })
    },

    close() {
      this.$router.push({
        path: '/middlePlatform/approval-template'
      })
    }
  },
  created() {
    this.getTemplateData(this.workflowId)
  },
  computed: {
    workflowId() {
      return this.$route.params.id || ''
    },
    applicationId() {
      return this.$route.params.aid || ''
    },
    templateId() {
      return this.$route.params.tid || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-edit {
  width: 100%;
  height: calc(100% - 86px);
}
</style>
