<!--
 * @Author: your name
 * @Date: 2021-10-14 12:01:24
 * @LastEditTime: 2021-11-01 15:01:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalProcess\index.vue
-->
<template>
  <div class="approval-template-wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <div slot="slot-filter">
        <div class="solt-wrap">
          <p class="solt-item">
            <tenant />
          </p>
          <p class="solt-item">
            <mt-select
              :width="300"
              :data-source="applicationList"
              :show-clear-button="true"
              :placeholder="$t('请选择应用模块')"
              :fields="{ text: 'applicationName', value: 'applicationId' }"
              @change="applicationIdChange($event)"
            ></mt-select>
          </p>
        </div>
      </div>
    </mt-template-page>
  </div>
</template>
<script>
import { listToolBar, listColumnData } from './config'
import tenant from '@/views/ApprovalTemplate/components/tenant'
// import Bus from "@/utils/bus";
export default {
  components: {
    tenant
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: listToolBar,
          grid: {
            columnData: listColumnData,
            // dataSource:listData,
            asyncConfig: {
              url: this.$api.approvalTemplate.getTempListApi
            }
          }
        }
      ],
      applicationList: []
    }
  },
  mounted() {},
  methods: {
    handleClickToolBar(e) {
      // const { grid, toolbar } = e
      if (e.toolbar.id === 'Add') {
        this.addTemplate()
      }

      // const sections = grid.getSelectedRecords()
      // if (sections.length === 0) {
      // }
      // const idList = sections.map((v) => v.id)

      // if (toolbar.id === 'version') {
      //   // this.viewHistory(idList);
      // } else if (toolbar.id === 'Delete') {
      //   // this.deleteTemplate(idList);
      // }
    },
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.editTemplate(data)
      } else if (tool.id === 'preview') {
        this.previewTemplate(data.templateId)
      } else if (tool.id === 'stop') {
        this.changeStatus(data.templateId)
      }
    },
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      // if (e.field === 'column1') {
      // }
    },
    addTemplate() {
      this.$router.push({
        path: '/middlePlatform/template-add',
        query: {
          type: 'template'
        }
      })
    },
    editTemplate(data) {
      this.$router.push({
        path: `/middlePlatform/template-edit/${data.workflowId}/${data.applicationId}/${data.templateId}`
      })
    },
    previewTemplate(templateId) {
      this.$dialog({
        modal: () => import('./components/templatePreviewDialog.vue'),
        data: {
          title: this.$t('审批流模板详情'),
          templateId: templateId,
          parentVm: this
        },
        success: (data) => {
          console.log(data)
          if (data) this.$refs.tempaltePageRef.refreshCurrentGridData()
        },
        close: (data) => {
          if (data) this.$refs.tempaltePageRef.refreshCurrentGridData()
        }
      })
    },
    changeStatus(templateId) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('是否停用该条记录？')
        },
        success: () => {
          this.$api.approvalTemplate
            .changeTempStatus({ templateId, workflowStatus: 0 })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('操作成功'), type: 'success' })
                this.$refs.tempaltePageRef.refreshCurrentGridData()
              }
            })
        }
      })
    },
    getApplationList() {
      this.$api.approvalConfig.queryApplicationList({}).then((res) => {
        this.applicationList = res.data
      })
    },
    applicationIdChange({ itemData }) {
      this.$nextTick(() => {
        if (itemData) {
          const { applicationId } = itemData
          this.pageConfig[0].grid.asyncConfig.url = `${this.$api.approvalTemplate.getTempListApi}?applicationId=${applicationId}`
        } else {
          this.pageConfig[0].grid.asyncConfig.url = this.$api.approvalTemplate.getTempListApi
        }
      })
    }
  },
  created() {
    this.getApplationList()
  }
}
</script>

<style lang="scss" scoped>
.approval-template-wrap {
  width: 100%;
  height: 100%;
}
/deep/ .statusOff {
  color: #ed5633;
  padding: 2px;
  font-size: 13px;
  background: rgba(237, 86, 51, 0.3);
  position: relative;
  top: -5px;
}
/deep/ .statusOn {
  background: rgba(99, 134, 193, 0.3);
  padding: 2px;
  font-size: 13px;
  color: rgba(99, 134, 193, 1);
  position: relative;
  top: -5px;
}
/deep/ .cg {
  padding: 2px 8.5px;
}
.solt-wrap {
  background: #fff;
  padding: 10px;
  .solt-item {
    padding: 10px 0;
  }
}
</style>
