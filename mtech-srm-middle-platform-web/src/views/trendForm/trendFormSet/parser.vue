<template>
  <div>
    <Parser ref="Parser" :form-conf="formConf" v-if="show"></Parser>

    <button style="margin-right: 20px" @click="getFormData">{{ $t('手动获取') }}</button>
    <button style="margin-right: 20px" @click="setFormData">set{{ $t('数据') }}</button>
    <button style="margin-right: 20px" @click="refsh">{{ $t('刷新') }}</button>
  </div>
</template>

<script>
import Parser from '@mtech-form-design/form-parser'
import { formConf } from './formConf'
export default {
  components: {
    Parser
  },
  props: {
    msg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: true,
      formConf
    }
  },
  created() {
    console.log('formConf123', formConf)
  },
  methods: {
    refsh() {
      this.show = false
      this.$nextTick(() => {
        this.show = true
      })
    },
    getFormData() {
      const data = this.$refs.Parser.getFormData()
      console.log('f2', this.formConf)
      console.log('手动获取提交数据', data)
    },
    setFormData() {
      this.$refs.Parser.setFormData({
        select1: '1',
        select2: '1',
        field1645870225301: [
          {
            OrderID: 4,
            CustomerName: this.$t('测试1'),
            OrderDate: 'Mon Jan 01 2001 00:00:00 GMT+0800',
            Freight: 20,
            ShippedDate: 'Mon Jan 01 2001 00:00:00 GMT+0800',
            ShipCountry: this.$t('苏州')
          }
        ],
        field16458702253055: [
          {
            OrderID: 1,
            CustomerName: this.$t('测试1'),
            OrderDate: 'Mon Jan 01 2001 00:00:00 GMT+0800',
            Freight: 20,
            ShippedDate: 'Mon Jan 01 2001 00:00:00 GMT+0800',
            ShipCountry: this.$t('苏州')
          }
        ],
        field1645873028160: ['选项-3', '选项-4', '选项-5'],
        field1645873202022: [
          {
            name: '111.jpg',
            size: 123,
            type: '.jpg',
            id: 1232132132321,
            url: 'http://www.baidu.com'
          }
        ]
      })
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style>
@import '@mtech-form-design/form-parser/build/esm/bundle.css';
</style>
