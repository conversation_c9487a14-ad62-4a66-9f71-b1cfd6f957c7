<template>
  <div class="platmenu-page--wrap">
    <div class="platmenu--wrap">
      <div class="tree-view--wrap">
        <CategoryTree
          v-if="show"
          class="tree-view--wrap"
          type="processModel"
          @nodeSelected="nodeSelected"
          @refresh="refresh"
        />
      </div>

      <div class="platmenu-content">
        <mt-template-page
          ref="mainTemplatePage"
          :hidden-tabs="true"
          :template-config="componentConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
          @handleClickCellTitle="handleClickCellTitle"
        >
        </mt-template-page>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import CategoryTree from '../components/CategoryTree.vue'
import { treeViewData, componentConfig } from '../config'
const Bus: any = require('@/utils/bus.js').default

@Component({
  components: { CategoryTree }
})
export default class WorkflowList extends Vue {
  componentConfig: any = componentConfig
  show = true
  dataArr = []
  applicationId = '10548041430523932'
  treeViewData = treeViewData
  setObj = { header: '' }
  setData = {}
  parentId = '0'
  treeViewShow = false
  classifyId = ''

  /**
   * 生命周期
   * **/
  async created() {}

  mounted() {
    Bus.$on('handleClickCellToolFlow', this.handleClickCellTool)
    Bus.$on('handleClickCellTitleFlow', this.handleClickCellTitle)
  }

  addMainNode() {
    console.log(this.$refs.AddNodeForm)
    const fuc: any = this.$refs.AddNodeForm
    fuc.parentId = '0'
    fuc.reserForm()
    fuc.show()
  }

  nodeSelected(e: any) {
    console.log(e)
    this.queryCriteria(e.nodeData)
  }

  // 在点击节点或者初始化的时候调用
  queryCriteria(e: any) {
    const a: any = this.componentConfig
    a[0].grid.asyncConfig.defaultRules[0].value = e.id
    this.classifyId = e.id
  }

  refresh() {
    const a: any = this.componentConfig
    this.show = false
    this.$nextTick(() => {
      this.show = true
      a[0].grid.asyncConfig.defaultRules[0].value = ''
    })
  }

  addOptSucces(func: any) {
    // const func:any = this.$refs.treeView
    // 可在次添加默认选中的节点
    const id: any = func.getCommonMethods().getTreeData()[0].id
    this.queryCriteria(func.getCommonMethods().getNode(id))

    func.getCommonMethods().expandAll()
  }

  handleClickToolBar(e: any) {
    console.log('use-handleClickToolBar', e)
    if (e.toolbar.id === 'Add') {
      this.$router.push({
        path: '/middlePlatform/formEdit',
        query: { classifyId: this.classifyId, type: 'add', id: '' }
      })
      // const routeUrl = this.$router.resolve({
      //   path: '/middlePlatform/formEdit',
      //   query: { classifyId: this.classifyId, type: 'add', id: '' }
      // })
      // window.open(routeUrl.href, '_blank')
    } else if (e.toolbar.id === 'Delete') {
      const func: any = this.$refs.mainTemplatePage
      // console.log()
      const arr: any = func.getCurrentTabRef().grid.getSelectedRecords()
      const pushArr: any = []
      if (arr.length > 0) {
        arr.forEach((e: any) => {
          console.log(e)
          pushArr.push(e.id)
        })
      } else {
        this.$toast({
          content: '请至少选择一个！',
          type: 'warning'
        })
        return
      }
      this.delWordG(pushArr)
    }
  }

  // 删除字段权限
  delWordG(pushArr: any) {
    this.$dialog({
      data: {
        title: '删除',
        message: '是否确认删除？'
      },
      success: () => {
        this.$api.form
          .deleteDesign({
            ids: pushArr
          })
          .then((r: any) => {
            console.log(r)
            this.refreshMain()
          })
      }
    })
  }

  // 刷新main列表
  refreshMain() {
    const func: any = this.$refs.mainTemplatePage
    func.refreshCurrentGridData()
  }

  // 行内编辑删除操作
  handleClickCellTool(e: any) {
    console.log('use-handleClickCellTool', e)
    if (e.tool.id === 'delete') {
      this.delWordG([e.data.id])
    } else if (e.tool.id === 'edit') {
      // const func:any = this.$refs.WordGridDialogEdit
      this.classifyId = e.data.classifyId
      this.$router.push({
        path: '/middlePlatform/formEdit',
        query: { id: e.data.id, type: 'edit', classifyId: this.classifyId, version: e.data.version }
      })
    } else if (e.tool.id === 'publish') {
      this.$api.form.updataForm({ id: e.data.id, isActive: true }).then((r: any) => {
        if (r.code === 200) {
          const func: any = this.$refs.mainTemplatePage
          func.refreshCurrentGridData()
        }
      })
    }
  }

  handleClickCellTitle(e: any) {
    console.log('use-handleClickCellTitle', e)
    if (e.field === 'column1') {
      // todo title click
    }
  }

  handleGridCurrentChange(data: any) {
    console.log('use-handleGridCurrentChange', data)
    this.fetchGridData({ currentPage: data.currentPage - 1 })
  }

  handleGridSizeChange(data: any) {
    console.log('use-handleGridSizeChange', data)
    this.fetchGridData({
      count: data.count
    })
  }

  // 查询页面元素树结构
  // async getFind() {
  //   const res = await this.$api.form.getFind()

  //   if (res.code === 200 && res.data) {
  //     this.treeViewData = Object.assign({}, this.treeViewData, {
  //       dataSource: res.data
  //     })
  //     this.treeViewShow = true
  //     this.$nextTick(() => {
  //       const func:any = this.$refs.treeView
  //       this.addOptSucces(func)
  //     })
  //   }
  // }

  async fetchGridData(event: { count?: number; currentPage?: number }) {
    const res = await this.$api.detail.queryApi({
      current: event.currentPage
    })

    if (res.code === 200 && res.data) {
      console.log(res.data)
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .selectClass {
  border: none !important;
}
.platmenu-page--wrap {
  display: flex;
  height: 100%;
  flex-direction: column;
}
.user-list {
  height: 100%;
  width: 100%;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
}
.addClass {
  display: inline-block;
  margin: 10px;
}
.platmenu-page--header {
  padding: 20px;
  background: #fff;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .mr-20 {
    margin-right: 20px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-8 {
    margin-right: 8px;
  }
  .mb-20 {
    margin-bottom: 20px;
  }
  .mb-10 {
    margin-bottom: 10px;
  }

  .header-second--wrap {
    .title {
      font-size: 14px;
      color: rgba(41, 41, 41, 1);
    }
  }

  .header-third--wrap {
    color: #4f5b6d;
    > span {
      cursor: pointer;
    }
  }
}

.platmenu--wrap {
  flex: 1;
  width: 100%;
  display: flex;
  font-size: 14px;
}

.platmenu-content {
  margin: 0 0 0 10px;
  width: 100%;
  border: 1px solid rgba(232, 232, 232, 1);
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
}

.grid-tab--wrap {
  border: 1px solid rgba(232, 232, 232, 1);
  padding: 0 30px;
  white-space: nowrap;

  .grid-tab--item {
    display: inline-block;
    color: #9a9a9a;
    margin: 12px 60px 12px 0;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 6px 10px;

    &.active {
      color: #00469c;
      background: rgba(0, 70, 156, 0.06);
      border-color: rgba(0, 70, 156, 0.1);
    }
  }
}

.tree-view--wrap {
  flex: 0 0 400px;
  background: #fff;
  border-right: 1px solid #eee;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);

  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }

  ::v-deep .e-list-text {
    width: 100% !important;
  }

  /deep/ .e-list-text {
    width: 100% !important;
  }
}

.data-grid--wrap {
  height: calc(100% - 55px);
  overflow: hidden;
}
</style>
