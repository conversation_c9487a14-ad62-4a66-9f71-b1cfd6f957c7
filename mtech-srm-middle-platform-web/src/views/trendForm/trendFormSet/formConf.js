export const formConf = {
  fields: [
    {
      __config__: {
        labelWidth: null,
        showLabel: true,
        offset: 0,
        required: true,
        span: 24,
        document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html',
        regList: [],
        events: [
          {
            activeColShow: true,
            name: 'input',
            code: "console.log(this.formData)\nconsole.log(this)\nthis.selectInput()\nthis.formData.select2 = '2'"
          }
        ],
        label: '下拉选择1',
        tag: 'mt-select',
        tagIcon: 'mt-icon-icon_outline_Dropdownselection',
        layout: 'colFormItem',
        ruleTrigger: 'blur',
        dataConsumer: 'dataSource',
        dataType: 'static',
        bgUrl: 'https://s2-relay.360buyimg.com/relay/cache/cut/2c51a9fc18de1e9fd8826d892a006ae7',
        formId: 1645708263270,
        renderKey: '131994.09999999404',
        defaultValue: '1'
      },
      dataSource: [
        {
          text: '1',
          value: '1'
        },
        {
          text: '2',
          value: '2'
        }
      ],
      placeholder: '请选择下拉选择',
      style: {
        width: '100%'
      },
      showClearButton: true,
      disabled: false,
      fields: {
        text: 'text',
        value: 'value'
      },
      allowFiltering: false,
      __vModel__: 'select1'
    },
    {
      __config__: {
        labelWidth: null,
        showLabel: true,
        offset: 0,
        required: true,
        span: 24,
        document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html',
        regList: [],
        events: [
          {
            activeColShow: false,
            name: 'input',
            code: 'console.log(this.formData)\nconsole.log(this)\nthis.selectInput()'
          }
        ],
        label: '下拉选择2',
        tag: 'mt-select',
        tagIcon: 'mt-icon-icon_outline_Dropdownselection',
        layout: 'colFormItem',
        ruleTrigger: 'blur',
        dataConsumer: 'dataSource',
        dataType: 'static',
        bgUrl: 'https://s2-relay.360buyimg.com/relay/cache/cut/2c51a9fc18de1e9fd8826d892a006ae7',
        formId: 1645865539134,
        renderKey: '471342.5',
        defaultValue: null
      },
      dataSource: [
        {
          text: '1',
          value: '1'
        },
        {
          text: '2',
          value: '2'
        },
        {
          text: '3',
          value: '3'
        }
      ],
      placeholder: '请选择下拉选择',
      style: {
        width: '100%'
      },
      showClearButton: true,
      disabled: false,
      fields: {
        text: 'text',
        value: 'value'
      },
      allowFiltering: false,
      __vModel__: 'select2'
    },
    {
      __config__: {
        labelWidth: null,
        showLabel: true,
        offset: 0,
        required: true,
        span: 24,
        document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html',
        regList: [],
        events: [],
        layout: 'colFormItem',
        tagIcon: 'mt-icon-icon_outline_Form',
        tag: 'mt-data-grid',
        renderKey: '145359',
        bgUrl: 'https://s1-relay.360buyimg.com/relay/cache/cut/43dd07a85b302a2d21aab42046b3b4fb',
        defaultValue: [],
        dataConsumer: 'dataSource',
        dataType: 'dynamic',
        label: '表格',
        formId: 1645902954858
      },
      on: {
        actionComplete: 'actionComplete'
      },
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'OrderID',
          headerText: '订单ID',
          width: '100',
          type: 'number',
          isPrimaryKey: true,
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'CustomerName',
          headerText: '客户名称',
          width: '100',
          type: 'uploader',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'OrderDate',
          headerText: '订单日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'Freight',
          headerText: '运费',
          width: '100',
          format: 'C2',
          type: 'number',
          editType: 'numericedit',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShippedDate',
          headerText: '装运日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          editType: 'datepickeredit',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShipCountry',
          headerText: '装运城市',
          width: '100',
          textAlign: 'Right',
          type: 'string',
          editType: 'dropdownedit',
          edit: {
            params: {}
          },
          activeColShow: false
        }
      ],
      allowSorting: true,
      allowFiltering: false,
      filterSettings: {},
      editSettings: {
        allowDeleting: true,
        allowEditing: true,
        checkboxShow: true,
        allowAdding: true
      },
      toolbar: ['Add', 'Edit', 'Delete', 'Update', 'Cancel', 'Print'],
      __vModel__: 'field1645902954858'
    },
    {
      __config__: {
        labelWidth: null,
        showLabel: true,
        offset: 0,
        required: true,
        span: 24,
        document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html',
        regList: [],
        events: [],
        layout: 'colFormItem',
        tagIcon: 'mt-icon-icon_outline_Form',
        tag: 'mt-data-grid',
        renderKey: '156741.29999998212',
        bgUrl: 'https://s1-relay.360buyimg.com/relay/cache/cut/43dd07a85b302a2d21aab42046b3b4fb',
        defaultValue: [],
        dataConsumer: 'dataSource',
        dataType: 'dynamic',
        label: '表格',
        formId: 1645902966240
      },
      on: {
        actionComplete: 'actionComplete'
      },
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'OrderID',
          headerText: '订单ID',
          width: '100',
          type: 'number',
          isPrimaryKey: true,
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'CustomerName',
          headerText: '客户名称',
          width: '100',
          type: 'uploader',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'OrderDate',
          headerText: '订单日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'Freight',
          headerText: '运费',
          width: '100',
          format: 'C2',
          type: 'number',
          editType: 'numericedit',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShippedDate',
          headerText: '装运日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          editType: 'datepickeredit',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShipCountry',
          headerText: '装运城市',
          width: '100',
          textAlign: 'Right',
          type: 'string',
          editType: 'dropdownedit',
          edit: {
            params: {}
          },
          activeColShow: false
        }
      ],
      allowSorting: true,
      allowFiltering: false,
      filterSettings: {},
      editSettings: {
        allowDeleting: true,
        allowEditing: true,
        checkboxShow: true,
        allowAdding: true
      },
      toolbar: ['Add', 'Edit', 'Delete', 'Update', 'Cancel', 'Print'],
      __vModel__: 'field1645902966240'
    },
    {
      __config__: {
        labelWidth: null,
        showLabel: true,
        offset: 0,
        required: true,
        span: 24,
        document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html',
        regList: [],
        events: [],
        layout: 'colFormItem',
        tagIcon: 'mt-icon-icon_outline_Form',
        tag: 'mt-data-grid',
        renderKey: '156936.79999998212',
        bgUrl: 'https://s1-relay.360buyimg.com/relay/cache/cut/43dd07a85b302a2d21aab42046b3b4fb',
        defaultValue: [],
        dataConsumer: 'dataSource',
        dataType: 'dynamic',
        label: '表格',
        formId: 1645902966436
      },
      on: {
        actionComplete: 'actionComplete'
      },
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'OrderID',
          headerText: '订单ID',
          width: '100',
          type: 'number',
          isPrimaryKey: true,
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'CustomerName',
          headerText: '客户名称',
          width: '100',
          type: 'uploader',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'OrderDate',
          headerText: '订单日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'Freight',
          headerText: '运费',
          width: '100',
          format: 'C2',
          type: 'number',
          editType: 'numericedit',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShippedDate',
          headerText: '装运日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          editType: 'datepickeredit',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShipCountry',
          headerText: '装运城市',
          width: '100',
          textAlign: 'Right',
          type: 'string',
          editType: 'dropdownedit',
          edit: {
            params: {}
          },
          activeColShow: false
        }
      ],
      allowSorting: true,
      allowFiltering: false,
      filterSettings: {},
      editSettings: {
        allowDeleting: true,
        allowEditing: true,
        checkboxShow: true,
        allowAdding: true
      },
      toolbar: ['Add', 'Edit', 'Delete', 'Update', 'Cancel', 'Print'],
      __vModel__: 'field1645902966436'
    },
    {
      __config__: {
        labelWidth: null,
        showLabel: true,
        offset: 0,
        required: true,
        span: 24,
        document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html',
        regList: [],
        events: [],
        layout: 'colFormItem',
        tagIcon: 'mt-icon-icon_outline_Form',
        tag: 'mt-data-grid',
        renderKey: '157111.09999999404',
        bgUrl: 'https://s1-relay.360buyimg.com/relay/cache/cut/43dd07a85b302a2d21aab42046b3b4fb',
        defaultValue: [],
        dataConsumer: 'dataSource',
        dataType: 'dynamic',
        label: '表格',
        formId: 1645902966610
      },
      on: {
        actionComplete: 'actionComplete'
      },
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'OrderID',
          headerText: '订单ID',
          width: '100',
          type: 'number',
          isPrimaryKey: true,
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'CustomerName',
          headerText: '客户名称',
          width: '100',
          type: 'uploader',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'OrderDate',
          headerText: '订单日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'Freight',
          headerText: '运费',
          width: '100',
          format: 'C2',
          type: 'number',
          editType: 'numericedit',
          textAlign: 'Right',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShippedDate',
          headerText: '装运日期',
          width: '100',
          format: 'yMd',
          textAlign: 'Right',
          type: 'date',
          editType: 'datepickeredit',
          edit: {
            params: {}
          },
          activeColShow: false
        },
        {
          field: 'ShipCountry',
          headerText: '装运城市',
          width: '100',
          textAlign: 'Right',
          type: 'string',
          editType: 'dropdownedit',
          edit: {
            params: {}
          },
          activeColShow: false
        }
      ],
      allowSorting: true,
      allowFiltering: false,
      filterSettings: {},
      editSettings: {
        allowDeleting: true,
        allowEditing: true,
        checkboxShow: true,
        allowAdding: true
      },
      toolbar: ['Add', 'Edit', 'Delete', 'Update', 'Cancel', 'Print'],
      __vModel__: 'field1645902966610'
    }
  ],
  customButton: [
    {
      text: '关闭',
      type: 'text',
      id: 'close',
      icon: 'mt-icons mt-icon-icon_solid_close'
    },
    {
      text: '发布',
      type: 'text',
      id: 'publish',
      icon: 'mt-icons mt-icon-icon_solid_pushorder'
    }
  ],
  saveFormItem: true,
  showDemo: true,
  formRef: 'mtForm',
  formModel: 'formData',
  size: 'medium',
  labelPosition: 'right',
  labelWidth: 100,
  formRules: 'rules',
  disabled: false,
  span: 24,
  parserData: [],
  lifecycleEvents: {
    mounted: [''],
    beforeDestroy: []
  },
  methods: [
    {
      name: 'selectInput',
      code: "alert('23')",
      activeColShow: false
    }
  ]
}
