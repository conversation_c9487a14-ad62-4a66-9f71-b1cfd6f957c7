<template>
  <div class="hello">
    <div class="header" v-if="showHeader">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules" class="form-class">
        <mt-form-item prop="code" class="form-item" :label="$t('模板编码')">
          <mt-input
            :disabled="enterType == 'edit'"
            v-model="ruleForm.code"
            :width="300"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入模板编码')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="name" :label="$t('模板名称')">
          <mt-input
            :width="300"
            v-model="ruleForm.name"
            type="text"
            :placeholder="$t('请输入模板名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item :label="$t('关联业务类型')">
          <!-- <mt-input
                :width="300"
                v-model="ruleForm.businessType"
                type="text"
                :placeholder="$t('请输入关联业务类型')"
                ></mt-input> -->
          <mt-DropDownTree
            id="filter"
            :width="300"
            :fields="selectArr"
            v-if="selectArr.dataSource.length > 0"
            v-model="ruleForm.businessType"
            @change="change"
            :placeholder="$t('请选择关联业务类型')"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item prop="version" :label="$t('版本号')">
          <mt-input
            :disabled="true"
            :width="300"
            v-model="ruleForm.version"
            type="text"
            :placeholder="$t('请输入版本号')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
    <Deploy
      ref="Deploy"
      v-if="enterType != 'edit' || showDeploy"
      :template-datasource="templateDatasource"
      @onSave="onSave"
      @onShowDemo="onShowDemo"
      :field-select="fieldSelect"
      :data-source="dataSource"
      @onButton="onButton"
      @onTemplateDatasourceSelect="deylopTreeSelect"
      @onTemplate="onTemplate"
    >
    </Deploy>
  </div>
</template>

<script>
// import Deploy from '@mtech-form-design/deploy'
import { dataSource } from '../config'
export default {
  // components: {
  //   Deploy
  // },
  props: {
    msg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      showDeploy: false,
      templateDatasource: {
        hasTabs: true,
        leftComponents: [
          {
            title: this.$t('基础模板'),
            list: [],
            fileds: {
              dataSource: [
                {
                  id: '01',
                  name: 'Local Disk (C:)',
                  expanded: true, // 展开
                  children: [
                    {
                      id: '01-01',
                      name: 'Program Files',
                      children: [
                        { id: '01-01-01', name: 'Windows NT' },
                        { id: '01-01-02', name: 'Windows Mail' },
                        { id: '01-01-03', name: 'Windows Photo Viewer' }
                      ]
                    },
                    {
                      id: '01-02',
                      name: 'Users',

                      children: [
                        { id: '01-02-01', name: 'Smith' },
                        { id: '01-02-02', name: 'Public' },
                        { id: '01-02-03', name: 'Admin' }
                      ]
                    },
                    {
                      id: '01-03',
                      name: 'Windows',
                      children: [
                        { id: '01-03-01', name: 'Boot' },
                        { id: '01-03-02', name: 'FileManager' },
                        { id: '01-03-03', name: 'System32' }
                      ]
                    }
                  ]
                }
              ],
              id: 'id',
              text: 'name',
              child: 'children'
            }
          },
          {
            title: this.$t('用户不存在'),
            list: [],
            fileds: {
              dataSource: [
                {
                  id: '01',
                  name: 'Local Disk (C:)',
                  expanded: true, // 展开
                  children: [
                    {
                      id: '01-01',
                      name: 'Program Files',
                      children: [
                        { id: '01-01-01', name: 'Windows NT' },
                        { id: '01-01-02', name: 'Windows Mail' },
                        { id: '01-01-03', name: 'Windows Photo Viewer' }
                      ]
                    },
                    {
                      id: '01-02',
                      name: 'Users',

                      children: [
                        { id: '01-02-01', name: 'Smith' },
                        { id: '01-02-02', name: 'Public' },
                        { id: '01-02-03', name: 'Admin' }
                      ]
                    },
                    {
                      id: '01-03',
                      name: 'Windows',
                      children: [
                        { id: '01-03-01', name: 'Boot' },
                        { id: '01-03-02', name: 'FileManager' },
                        { id: '01-03-03', name: 'System32' }
                      ]
                    }
                  ]
                }
              ],
              id: 'id',
              text: 'name',
              child: 'children'
            }
          }
        ]
      },
      isActive: false,
      dataSource,
      template: {},
      showHeader: true,
      ruleForm: {
        code: '',
        name: '',
        businessType: [],
        version: '1'
      },
      rules: {
        code: [
          { required: true, message: this.$t('请输入编码'), trigger: 'blur' },
          {
            min: 4,
            max: 25,
            message: this.$t('长度在 4 到 25 个字符'),
            trigger: 'blur'
          }
        ],
        name: [
          { required: true, message: this.$t('请输入名称'), trigger: 'blur' }
          // {
          //   min: 4,
          //   max: 25,
          //   message: this.$t('长度在 4 到 25 个字符'),
          //   trigger: 'blur'
          // }
        ],
        businessType: [{ required: true, message: this.$t('请选择业务类型'), trigger: 'blur' }],
        version: [{ required: true, message: this.$t('版本号'), trigger: 'blur' }]
      },
      selectArr: {
        dataSource: [],
        value: 'itemCode',
        text: 'name',
        parentID: 'parentId',
        child: 'children'
      },
      fieldSelect: {
        dataSource: [],
        text: 'name',
        id: 'field'
      }
    }
  },
  computed: {
    classifyId() {
      return this.$route.query.classifyId
    },
    enterType() {
      return this.$route.query.type
    },
    id() {
      return this.$route.query.id
    },
    version() {
      return this.$route.query.version || '1'
    }
  },
  mounted() {
    this.dataSource.fields = []
    this.$api.form.getBusinessType().then((r) => {
      // this.$set(this.selectArr, 'dataSource', r.data)
      this.selectArr = Object.assign({}, this.selectArr, {
        dataSource: r.data
      })
    })
    if (this.enterType === 'edit') {
      this.$api.form.getForm({ id: this.id, version: this.version }).then((r) => {
        this.ruleForm = {
          ...r.data
        }
        console.log('this.ruleForm', this.ruleForm)
        if (this.ruleForm.businessType) {
          this.ruleForm.businessType = [this.ruleForm.businessType]
        } else {
          this.ruleForm.businessType = []
        }
        this.dataSource = Object.assign({}, this.dataSource, r.data.template)

        if (!this.dataSource.lifecycleEvents?.canValiteFormData) {
          this.dataSource.lifecycleEvents.canValiteFormData = []
        }

        setTimeout(() => {
          this.showDeploy = true
        }, 10)
      })
    }
    // this.$api.form.getField({ page: { current: 1, size: 100000000 }, current: 1, size: 10000000 }).then(r => {

    this.getSystemTemplate()
  },
  methods: {
    change(e) {
      console.log('e', e)
      this.$api.form.getField({ businessType: e.value[0] }).then((r) => {
        // this.fieldSelect.dataSource = r.data.data
        this.$set(this.fieldSelect, 'dataSource', r.data)
      })
    },
    deylopTreeSelect(e) {
      console.log(e)
      const params = {
        current: 1,
        size: 9999,
        classifyId: e.node.nodeData.id,
        name: '',
        businessType: ''
      }
      if (e.index === 0) {
        this.$api.form.getTemplateList(params).then((r) => {
          this.templateDatasource.leftComponents[e.index].list = r.data.data
        })
      } else {
        this.$api.form.getMyTemplateList(params).then((r) => {
          this.templateDatasource.leftComponents[e.index].list = r.data.data
        })
      }
    },
    deylopMet(r, type) {
      const ele = {
        __config__: {
          layout: 'rowFormItem',
          tagIcon: 'mt-icon-icon_outline_Rowcontainer',
          label: this.$t('行容器'),
          layoutTree: true,
          children: [],
          events: [],
          bgUrl: 'https://s5-relay.360buyimg.com/relay/cache/cut/e8526d763f97bd2c670bcd3944b00dd2',
          document: 'https://h5.m.jd.com/babelDiy/Zeus/2yaikWWrfkZM8ZYC7xaoNWhzkp99/index.html'
        },
        type: 'default',
        justify: 'start',
        gutter: 0,
        align: 'top'
      }
      this.$set(ele.__config__, 'children', r.data.template.fields)
      console.log('ele124', ele, r.data.template.fields, type)
      if (type === 'add') {
        console.log('acc', this.$refs.Deploy)
        this.$refs.Deploy.addComponent(ele)
      } else {
        console.log('abc', this.$refs.Deploy)
        this.$refs.Deploy.draggComponent(ele, 'model')
      }
    },
    onTemplate(e, i, type) {
      if (i === 0) {
        this.$api.form.getSystemTemplateX(e).then((r) => {
          this.deylopMet(r, type)
        })
      } else {
        this.$api.form.getSystemTemplate(e).then((r) => {
          this.deylopMet(r, type)
        })
      }
    },
    getSystemTemplate() {
      this.$api.form.getSystemTemplateFind().then((r) => {
        this.templateDatasource.leftComponents[0].fileds.dataSource = r.data
      })
      this.$api.form.getTemplateFind().then((r) => {
        this.templateDatasource.leftComponents[1].fileds.dataSource = r.data
      })
    },
    onShowDemo(data) {
      this.showHeader = !data
    },
    onSave(data) {
      console.log(data)
      this.template = data
      this.isActive = false
      this.submitForm('ruleForm')
    },
    // 自定义按钮事件
    onButton(data) {
      if (data.id === 'publish') {
        this.template = this.$refs.Deploy.getFormData()
        this.isActive = true
        this.submitForm('ruleForm')
      } else if (data.id === 'close') {
        // this.$refs.Deploy.drawingList.push()
        this.$dialog({
          data: {
            title: this.$t('警告'),
            message: this.$t('是否确定退出？')
          },
          success: () => {
            this.$router.go(-1)
          }
        })
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            businessType: this.ruleForm.businessType[0],
            classifyId: this.classifyId,
            isActive: this.isActive,
            template: this.template
          }
          console.log('isActive', params)
          if (this.enterType === 'edit' || this.ruleForm.id) {
            this.$api.form.updataForm(params).then((r) => {
              if (r.code === 200 && this.isActive) {
                this.$router.go(-1)
              }
            })
          } else {
            this.$api.form.addForm(params).then((r) => {
              this.ruleForm.id = r.data.id
              if (r.code === 200 && this.isActive) {
                this.$router.go(-1)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.header {
  height: 100px;
  background: linear-gradient(rgba(250, 250, 250, 1), rgba(250, 250, 250, 1)),
    linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 1));
  border-bottom: 1px solid rgba(232, 232, 232, 1);
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  display: flex;
  .form-class {
    width: 100%;
    display: flex;
    height: 100%;
    align-items: center;
    align-content: space-between;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: nowrap;
    box-sizing: border-box;
    // padding-top: 11px;
    padding: 11px 63px 0 10px;
    /deep/ .mt-form-item {
      margin: 0 10px;
    }
  }
}
/deep/ .mt-container {
  padding: 0 !important;
}
/deep/ .e-bigger.e-small .e-upload .e-file-select-wrap,
/deep/ .e-upload .e-file-select-wrap {
  padding: 16px 12px 16px 12px;
}
/deep/ .rightScroll {
  height: calc(100vh - 71px);
}
/deep/ .e-input-group-icon.e-ddt-icon {
  background-color: transparent !important;
}
/deep/ .e-ddt.e-input-group.e-control-wrapper .e-clear-icon {
  background-color: transparent !important;
}
</style>
