<template>
  <div class="tree-view--wrap">
    <div class="trew-node--add">
      <mt-button
        iconCss="mt-icons mt-icon-icon_solid_Createorder"
        cssClass="e-flat"
        iconPosition="Right"
        @click.native="addNewRootNode"
        >{{ $t('新增根节点') }}</mt-button
      >
      <mt-button
        iconCss="mt-icons mt-icon-icon_solid_Createorder"
        cssClass="e-flat"
        iconPosition="Right"
        @click.native="refresh"
        >{{ $t('重置') }}</mt-button
      >
    </div>
    <mt-common-tree
      v-if="treeViewData.dataSource.length"
      ref="treeView"
      class="tree-view--template"
      :allowEditing="true"
      :fields="treeViewData"
      @onButton="clickCustomButton"
      @nodeEdited="nodeEdited"
      @nodeEditing="nodeEditing"
      @nodeSelected="nodeSelected"
    ></mt-common-tree>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'
import { i18n } from '@/main.js'
import MtCommonTree from '@mtech/common-tree-view'
import '@mtech/common-tree-view/build/esm/bundle.css'

@Component({
  components: {
    MtCommonTree
  }
})
export default class CategoryTree extends Vue {
  @Prop({
    default: ''
  })
  parentId!: string

  treeViewData = {
    // nodeTemplate: function () {
    //   return {
    //     template: Vue.component('common', {
    //       template: `<div class="action-boxs">
    //                   <div>{{MData.name}}</div>
    //                 </div>`,

    //       data() {
    //         return { data: {} }
    //       },
    //       props: {
    //         MData: {
    //           type: Object,
    //           default: () => {}
    //         }
    //       }
    //     })
    //   }
    // },
    dataSource: [],
    id: 'id',
    text: 'name',
    child: 'children'
  }

  // isStartRootNodeFormNull = false // 用户判断是否是从空开始新建根节点
  isEditing = false // 用于判断树节点是否正在编辑的辅助信息
  nodeOperation = [{ text: i18n.t('新增下级') }, { text: i18n.t('删除') }]
  /**
   * 生命周期
   * **/
  async mounted() {
    await this.getCategoryTree()
  }

  refresh() {
    this.$emit('refresh')
  }

  async getCategoryTree() {
    const res = await this.$api.form.getTemplateFind()

    this.treeViewData = Object.assign({}, this.treeViewData, {
      dataSource: res.data
    })
    const category = this.treeViewData.dataSource[0] as { id: string }
    const func: any = this.$refs.treeView
    category?.id && this.$emit('addOptSucces', func)
  }

  // 添加根节点e
  addNewRootNode() {
    // if (this.treeViewData.dataSource.length === 0 && !this.isStartRootNodeFormNull) {
    //   // this.isStartRootNodeFormNull = true
    //   // 只需要根据初始化的datasource维护一次
    //   this.$nextTick(() => {
    //     this.addTreeNode({ parentId: '' })
    //   })
    // } else {
    this.addTreeNode({
      parentId: ''
    })
    // }
  }

  clickCustomButton(event: any) {
    if (event.onBtn.text === i18n.t('新增下级')) {
      this.addTreeNode(event)
    } else if (event.onBtn.text === i18n.t('删除')) {
      this.deleteTreeNode(event)
    }
  }

  addTreeNode(event: any) {
    const treeView: any = this.$refs.treeView
    const instance = treeView?.getCommonMethods()
    const newNodeName = i18n.t('分类名称')
    return this.$api.form
      .addTemplateClassify({
        parentId: event.id,
        name: newNodeName
      })
      .then((res: { code: number; data: { id: any } }) => {
        if (res.code === 200) {
          const { id } = res.data
          // 直接进入编辑
          if (this.treeViewData.dataSource.length === 0) {
            this.treeViewData = Object.assign({}, this.treeViewData, {
              dataSource: [res.data]
            })
          } else {
            instance.addNodes(
              [
                {
                  id: id,
                  name: newNodeName,
                  setOperation: this.nodeOperation
                }
              ],
              event.id
            )
          }
          this.isEditing = true
          instance.beginEdit(id)
        }
      })
  }

  nodeEditing() {
    console.log('nodeEditing')
  }

  nodeEdited(event: any) {
    // if (!this.isEditing) return
    // this.isEditing = false
    const { newText, nodeData } = event
    // const treeView: any = this.$refs.treeView
    // const instance = treeView.getCommonMethods()

    this.$api.form
      .updateTemplateClassify({
        name: newText,
        id: nodeData.id
      })
      .then((res: { code: number; data: { id: any } }) => {
        if (res.code === 200) {
          // ej2 的 updateNode 只能修改 text ,所以将新建节点的ajax前置
          // instance.updateNode(nodeData.id, newText)
        }
      })
  }

  // 删除树节点
  deleteTreeNode(event: any) {
    this.$api.form
      .deleteTemplateClassify({ ids: [event.id] })
      .then((res: { code: number; data: boolean }) => {
        if (res.code) {
          const treeView: any = this.$refs.treeView
          const instance = treeView.getCommonMethods()
          instance.removeNodes([event.id])
        }
      })
  }

  nodeSelected(event: any) {
    this.$emit('nodeSelected', event)
  }
}
</script>
<style lang="scss" scoped>
.tree-view--wrap {
  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }
}
</style>
