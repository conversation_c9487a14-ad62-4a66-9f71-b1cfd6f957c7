<template>
  <div class="notice-hander hander">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
  </div>
</template>
<script>
import { rowDataTemp } from './config/variable'
import { pageConfig, NewRowData } from './config'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig
    }
  },
  methods: {
    actionBegin(args) {
      const { requestType, action, rowData } = args
      if (requestType === 'add') {
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
      } else if (requestType === 'save' && action === 'add') {
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === 'save' && action === 'edit') {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === 'beginEdit') {
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    actionComplete(args) {
      if (args.requestType == 'save') {
        // const { data } = args
        const rowData = rowDataTemp[rowDataTemp.length - 1]
        const params = {
          ...rowData
          // ext: JSON.parse(rowData.ext) ? JSON.parse(rowData.ext) : rowData.ext,
          // parameter: JSON.parse(rowData.parameter)
          //   ? JSON.parse(rowData.parameter)
          //   : rowData.parameter
        }
        // if (
        //   !params.ext ||
        //   !params.parameter ||
        //   !params.route ||
        //   !params.topicName ||
        //   (!params.timeOutSeconds && params.timeOutSeconds !== 0) ||
        //   (!params.status && params.status !== 0)
        // ) {
        //   this.$toast({ content: this.$t('请填写完整！'), type: 'warning' })
        //   return
        // }
        this.$api.dataManage.saveTaskLog(params).then((res) => {
          if (res.code === 200) {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
        // this.$refs.templateRef.refreshCurrentGridData()
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    // 表头操作
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      }
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  // width: 100%;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
