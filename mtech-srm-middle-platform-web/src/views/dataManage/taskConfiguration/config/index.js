import Vue from 'vue'
import { rowDataTemp } from './variable'
// list toolbar
export const toolbar = [
  // { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' }
  // { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  field: null,
  name: null,
  remark: null,
  sortNo: null,
  status: null
}

// list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60',
    allowEditing: false
  },
  {
    field: 'ext',
    headerText: '扩展参数',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return JSON.stringify(data)
      }
    },
    editTemplate: function () {
      return {
        template: Vue.component('extInput', {
          template: `<div>
          <mt-input
          v-model="ext"
          @change="extChange"
        ></mt-input>
        </div>`,
          created() {
            this.ext = this.data.ext ? JSON.stringify(this.data.ext) : ''
          },
          data() {
            return {
              ext: ''
            }
          },
          methods: {
            extChange(e) {
              try {
                rowDataTemp[rowDataTemp.length - 1]['ext'] = e ? JSON.parse(e) : null
              } catch (error) {
                rowDataTemp[rowDataTemp.length - 1]['ext'] = e
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'parameter',
    headerText: '参数',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return JSON.stringify(data)
      }
    },
    editTemplate: function () {
      return {
        template: Vue.component('parameterInput', {
          template: `<div>
          <mt-input
          v-model="parameter"
          @change="parameterChange"
        ></mt-input>
        </div>`,
          created() {
            this.parameter = this.data.parameter ? JSON.stringify(this.data.parameter) : ''
          },
          data() {
            return {
              parameter: ''
            }
          },
          methods: {
            parameterChange(e) {
              try {
                rowDataTemp[rowDataTemp.length - 1]['parameter'] = e ? JSON.parse(e) : null
              } catch (error) {
                rowDataTemp[rowDataTemp.length - 1]['parameter'] = e
              }
            }
          }
        })
      }
    }
  },
  {
    field: 'route',
    headerText: '路由',
    allowEditing: false,
    editTemplate: function () {
      return {
        template: Vue.component('routeInput', {
          template: `<div>
          <mt-input
          v-model="data.route"
          @change="routeChange"
        ></mt-input>
        </div>`,
          methods: {
            routeChange(e) {
              rowDataTemp[rowDataTemp.length - 1]['route'] = e
            }
          }
        })
      }
    }
  },
  {
    field: 'status',
    headerText: '状态',
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: '停用', value: -1, cssClass: ['btns warning'] },
        { text: '进行中', value: 0, cssClass: ['btns warning'] },
        { text: '已完成', value: 1, cssClass: ['btns warning'] },
        { text: '失败', value: 2, cssClass: ['btns warning'] },
        { text: '数据修正', value: 3, cssClass: ['btns warning'] }
      ]
    },
    editTemplate: function () {
      return {
        template: Vue.component('statusSelect', {
          template: `<div>
          <mt-select
          v-model="data.status"
          :data-source="dataSource"
          @change="statusSelectChange"
        ></mt-select>
        </div>`,
          data() {
            return {
              dataSource: [
                { text: '停用', value: -1 },
                { text: '进行中', value: 0 },
                { text: '已完成', value: 1 },
                { text: '失败', value: 2 },
                { text: '数据修正', value: 3 }
              ]
            }
          },
          methods: {
            statusSelectChange(e) {
              // this.data.status = e.value
              // this.$set(this.data, 'status', e.value)
              rowDataTemp[rowDataTemp.length - 1]['status'] = e.value
            }
          }
        })
      }
    }
  },
  {
    field: 'timeOutSeconds',
    headerText: '超时时间(秒)',
    allowEditing: false,
    editTemplate: function () {
      return {
        template: Vue.component('timeOutSecondsInput', {
          template: `<div>
          <mt-input
          v-model="data.timeOutSeconds"
          @change="timeOutSecondsChange"
        ></mt-input>
        </div>`,
          methods: {
            timeOutSecondsChange(e) {
              rowDataTemp[rowDataTemp.length - 1]['timeOutSeconds'] = e
            }
          }
        })
      }
    }
  },
  {
    field: 'topicName',
    headerText: 'MQ主题名称',
    allowEditing: false,
    editTemplate: function () {
      return {
        template: Vue.component('topicNameInput', {
          template: `<div>
          <mt-input
          v-model="data.topicName"
          @change="topicNameChange"
        ></mt-input>
        </div>`,
          methods: {
            topicNameChange(e) {
              rowDataTemp[rowDataTemp.length - 1]['topicName'] = e
            }
          }
        })
      }
    }
  },
  {
    field: 'applicationName',
    headerText: '应用名称',
    allowEditing: false
  },
  {
    field: 'dispatchEndTime',
    headerText: '最近调度结束时间',
    allowEditing: false
  },
  {
    field: 'dispatchStartTime',
    headerText: '最近调度开始时间',
    allowEditing: false
  },
  {
    field: 'dispatchStatus',
    headerText: '调度状态',
    valueConverter: {
      type: 'map',
      fields: { text: 'text', value: 'value' },
      map: [
        { text: '待调度', value: 0, cssClass: ['btns warning'] },
        { text: '调度中', value: 1, cssClass: ['btns warning'] },
        { text: '调度成功', value: 2, cssClass: ['btns warning'] },
        { text: '调度失败', value: 3, cssClass: ['btns warning'] }
      ]
    }
  },
  {
    field: 'executeEndTime',
    headerText: '最近执行结束时间',
    allowEditing: false
  },
  {
    field: 'executeStartTime',
    headerText: '最近执行开始时间',
    allowEditing: false
  },
  {
    field: 'priority',
    headerText: '优先级',
    allowEditing: false
  },
  {
    field: 'remark',
    headerText: '备注',
    allowEditing: false
  },
  {
    field: 'responseContent',
    headerText: '返回内容',
    allowEditing: false
  },
  {
    field: 'spendTime',
    headerText: '耗时(毫秒)',
    allowEditing: false
  },
  {
    field: 'tableName',
    headerText: '表名',
    allowEditing: false
  },
  {
    field: 'taskType',
    headerText: '任务类型',
    allowEditing: false
  }
]

// list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    gridId: '70ce110f-42cc-4d7c-b157-26af2a22f6d9',
    grid: {
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal', // 默认normal模式
        allowEditOnDblClick: true,
        newRowPosition: 'Top'
      },
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '/platform/tenant/es/taskLog/queryBuilder',
        params: {}
      }
    }
  }
]
