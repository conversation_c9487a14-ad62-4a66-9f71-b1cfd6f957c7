<!-- 消息接收配置 -->
<template>
  <div class="notice-hander hander">
    <mt-form>
      <mt-row :gutter="20">
        <mt-col :span="6">
          <mt-form-item :label="$t('应用名称')">
            <mt-select
              v-model="searchForm.applicationName"
              :fields="{ text: 'appName', value: 'appName' }"
              :data-source="applicationNameList"
              @change="applicationNameChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('表名称')">
            <mt-select
              v-model="searchForm.tableName"
              :data-source="tableNameList"
              @change="tableNameChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      @handleClickToolBar="handleClickToolBar"
      @actionComplete="actionComplete"
      @actionBegin="actionBegin"
    />
  </div>
</template>
<script>
import { rowDataTemp } from './config/variable'
import { pageConfig, NewRowData } from './config'
import { i18n } from '@/main.js'
import { cloneDeep } from 'lodash'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig,
      applicationNameList: [],
      tableNameList: [],
      searchForm: {
        applicationName: '',
        tableName: ''
      }
    }
  },
  created() {
    // 获取应用和表列表
    this.$api.dataManage.getAppTableList().then((res) => {
      if (res.code === 200) {
        this.applicationNameList = res.data || []
      }
    })
  },
  methods: {
    applicationNameChange(e) {
      const { itemData } = e
      this.tableNameList = itemData.tableList || []
      // if (itemData.route) {
      //   const url = `/${itemData.route}/tenant/es/field/queryBuilder`
      //   this.$set(this.pageConfig[0]['grid']['asyncConfig'], 'url', url)
      // }
      this.searchForm.tableName = ''
    },
    tableNameChange() {
      this.$set(this.pageConfig[0]['grid']['asyncConfig'], 'params', this.searchForm)
    },
    actionBegin(args) {
      const { requestType, action, rowData } = args
      if (requestType === 'add') {
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        const newRowData = cloneDeep(NewRowData)
        rowDataTemp.push(newRowData)
      } else if (requestType === 'save' && action === 'add') {
        args.data = rowDataTemp[rowDataTemp.length - 1]
        args.rowData = rowDataTemp[rowDataTemp.length - 1]
      } else if (requestType === 'save' && action === 'edit') {
        // 即将保存编辑，保存行编辑后的数据，使数据状态保持
        args.data = rowData
      } else if (requestType === 'beginEdit') {
        rowDataTemp.splice(0, rowDataTemp.length) // 清空数组数据
        rowDataTemp.push(rowData)
      }
    },
    actionComplete(args) {
      if (args.requestType == 'save') {
        // const { data } = args
        const rowData = rowDataTemp[rowDataTemp.length - 1]
        const params = {
          ...this.searchForm,
          ...rowData
        }
        if (
          !params.field ||
          !params.name ||
          (!params.sortNo && params.sortNo !== 0) ||
          (!params.status && params.status !== 0)
        ) {
          this.$toast({ content: this.$t('请填写完整！'), type: 'warning' })
          return
        }
        this.$api.dataManage.saveField(params).then((res) => {
          if (res.code === 200) {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      }
    },
    getRow() {
      //获取编辑或者新增的数据
      let currentRecords = this.$refs.templateRef
        .getCurrentUsefulRef()
        .gridRef.ejsRef.getCurrentViewRecords()
      let row = cloneDeep(this.selectedOtherInfo)
      let info = {}
      currentRecords.some((item) => {
        if (item.addId == this.nowEditRowFlag) {
          Object.assign(item, row)
          info = item
        }
      })
      return info
    },
    // 表头操作
    handleClickToolBar(e) {
      if (e.toolbar.id === 'Add') {
        this.$refs.templateRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
      }
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  // width: 100%;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  .e-content {
    height: calc(100vh - 294px) !important;
  }
}
</style>
