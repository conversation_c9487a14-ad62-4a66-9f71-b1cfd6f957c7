<template>
  <div class="signContract-wrap">
    <mt-loading class="signloading" v-if="showLoading"></mt-loading>
    <mt-dialog ref="dialog" :buttons="buttons" :header="header">
      <mt-form ref="form" :model="approveForm" :rules="formRules">
        <mt-form-item :label="$t('id列表')" prop="importJson" style="padding-top: 18px">
          <mt-input
            type="text"
            :multiline="true"
            :rows="20"
            placeholder="请输入id,多个id之间请使用单个空格或者逗号进行分割"
            v-model="approveForm.importJson"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      approveForm: {
        importJson: ''
      },
      showLoading: false,
      formRules: {
        importJson: [
          { required: true, message: this.$t('请输入同步内容'), trigger: 'blur' },
          { validator: this.importJsonValitor, trigger: 'blur' }
        ]
      }
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    urlRoute() {
      return this.modalData.urlRoute
    },
    tableName() {
      return this.modalData.data.tableName
    },
    importJson() {
      return this.modalData.data.importJson
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    if (this.importJson) {
      this.approveForm.importJson = this.importJson
    }
  },
  methods: {
    importJsonValitor(rule, value, callback) {
      const reg = /^[a-zA-Z0-9,， ]*$/
      if (!reg.test(value)) {
        callback(new Error(this.$t('只能输入数字、英文字母、逗号及空格')))
      } else {
        callback()
      }
    },
    confirm() {
      this.save()
    },
    cancel() {
      this.$emit('cancel-function')
    },

    save() {
      const idList = []
      let arrIndex = 0
      for (let i = 0; i < this.approveForm.importJson.length; i++) {
        let ele = this.approveForm.importJson[i]
        if (ele === ' ' || ele === ',' || ele === '，') {
          arrIndex++
        } else {
          if (idList[arrIndex]) {
            idList[arrIndex] += ele
          } else {
            idList[arrIndex] = ele
          }
        }
      }
      if (!idList.length || !idList.filter((i) => i).length) {
        this.$toast({
          content: this.$t('输入的内容数据格式有误'),
          type: 'warning'
        })
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          try {
            const params = {
              idList: idList.filter((i) => i)
            }
            this.$dialog({
              data: {
                title: this.$t('同步'),
                message: this.$t('是否确定同步以上数据？')
              },
              success: () => {
                this.showLoading = true
                this.$api.dataManage
                  .refreshData(params, this.tableName, this.urlRoute)
                  .then((res) => {
                    if (res.code === 200) {
                      // this.$refs.templateRef.refreshCurrentGridData()
                      this.$toast({ content: this.$t('操作成功'), type: 'success' })
                      this.$emit('confirm-function')
                    }
                  })
                  .finally(() => {
                    this.showLoading = false
                  })
              }
            })
          } catch {
            this.$toast({
              content: this.$t('输入的内容数据格式有误'),
              type: 'warning'
            })
            this.showLoading = false
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.signloading {
  position: fixed;
  top: 0;
  z-index: 9999;
}
</style>
