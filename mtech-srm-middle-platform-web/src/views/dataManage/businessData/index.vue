<!-- 消息接收配置 -->
<template>
  <div class="notice-hander hander">
    <mt-form>
      <mt-row :gutter="20">
        <mt-col :span="6">
          <mt-form-item :label="$t('应用名称')">
            <mt-select
              v-model="searchForm.applicationName"
              :fields="{ text: 'appName', value: 'appName' }"
              :data-source="applicationNameList"
              @change="applicationNameChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="6">
          <mt-form-item :label="$t('表名称')">
            <mt-select
              v-model="searchForm.tableName"
              :data-source="tableNameList"
              @change="tableNameChange"
            ></mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
    <div class="table-area">
      <mt-template-page
        ref="templateRef"
        :template-config="pageConfig"
        :use-tool-template="false"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      />
    </div>
  </div>
</template>
<script>
import { pageConfig } from './config'
import { i18n } from '@/main.js'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  data() {
    return {
      i18n,
      pageConfig: pageConfig,
      applicationNameList: [],
      tableNameList: [],
      searchForm: {
        applicationName: '',
        tableName: ''
      },
      urlRoute: '',
      searchUrl: '/statistics/tenant/data/es/pageQuery'
    }
  },
  created() {
    // 获取应用和表列表
    this.$api.dataManage.getAppTableList().then((res) => {
      if (res.code === 200) {
        this.applicationNameList = res.data || []
        // this.$set(this.pageConfig[0]['grid'], 'columnData', [])
        this.$set(this.pageConfig[0]['grid'], 'asyncConfig', {})
      }
    })
  },
  methods: {
    applicationNameChange(e) {
      const { itemData } = e
      this.tableNameList = itemData.tableList || []
      this.searchForm.tableName = ''
      if (itemData.route) {
        this.urlRoute = itemData.route
      } else {
        this.urlRoute = null
      }
    },
    tableNameChange(e) {
      const { itemData } = e
      this.searchForm.tableName = itemData.value || ''
      if (this.searchForm.tableName) {
        this.columns = []
        this.searchUrl = `/${this.urlRoute}/tenant/data/es/pageQuery`
        this.getColumns()
      }
    },
    //单元格按钮，点击
    handleClickCellTool(e) {
      if (e.tool.id == 'Sync') {
        this.$dialog({
          data: {
            title: this.$t('同步'),
            message: this.$t('是否确定同步该条数据？')
          },
          success: () => {
            this.refreshData([e.data.id])
          }
        })
      } else if (e.tool.id == 'Delete') {
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('是否确定删除该条数据？')
          },
          success: () => {
            this.deleteData([e.data.id])
          }
        })
      }
    },
    deleteData(e) {
      const params = {
        idList: e
      }
      const table = this.searchForm.tableName
      this.$api.dataManage.deleteData(params, table, this.urlRoute).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    rollbackData(e) {
      const params = {
        idList: e
      }
      const table = this.searchForm.tableName
      this.$api.dataManage.rollbackData(params, table, this.urlRoute).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    archiveData(e) {
      const params = {
        idList: e
      }
      const table = this.searchForm.tableName
      this.$api.dataManage.archiveData(params, table, this.urlRoute).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    refreshData(e) {
      const params = {
        idList: e
      }
      const table = this.searchForm.tableName
      this.$api.dataManage.refreshData(params, table, this.urlRoute).then((res) => {
        if (res.code === 200) {
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    getColumns() {
      const params = {
        page: { current: 1, size: 9999 },
        ...this.searchForm
      }
      this.$api.dataManage.getFieldList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          const columns = [
            {
              type: 'checkbox',
              width: '60'
            }
          ]
          // const queryForm = []
          data.forEach((item, index) => {
            const col = {
              searchOptions: { maxQueryValueLength: 5000 },
              field: item.field,
              headerText: item.name,
              width: 150
            }
            if (index === 0) {
              col.cellTools = [
                { id: 'Sync', icon: 'icon_table_restart', title: i18n.t('同步') },
                { id: 'Delete', icon: 'icon_solid_Delete', title: i18n.t('删除') }
              ]
            }
            if (item.clazz === 'datetime') {
              col.searchOptions = {
                elementType: 'date-range',
                operator: 'between',
                serializeValue: (e) => {
                  let obj = e.map((x) => Number(new Date(x.toString())))
                  obj[1] = obj[1] + Number(86400000 - 1440000)

                  //自定义搜索值，规则
                  return obj
                }
              }
            }
            columns.push(col)
            // queryForm.push({
            //   field: item.field,
            //   label: item.name,
            //   operator: 'contains',
            //   type: 'string',
            //   value: null
            // })
            // if (item.status === 1) {
            // }
          })
          // this.queryForm = queryForm
          // this.columns = columns
          const asyncConfig = {
            url: this.searchUrl,
            params: this.searchForm
          }
          // this.$set(this.pageConfig[0]['grid'], 'asyncConfig', asyncConfig)
          // this.$set(this.pageConfig[0]['grid'], 'columnData', columns)
          this.$set(this.pageConfig[0], 'grid', {
            useToolTemplate: false,
            height: 'auto',
            asyncConfig,
            columnData: columns
          })
        }
      })
    },
    // 表头操作
    handleClickToolBar(e) {
      const _selectRows = e.grid.getSelectedRecords()
      if (e.toolbar.id === 'Download') {
        const params = this.$refs.templateRef.getAsyncParams()
        params.page = {
          size: 1000000
        }
        // params.nextSearchAfter = []
        const url = `/${this.urlRoute}/tenant/data/es/exportData`
        if (this.searchForm.applicationName && this.searchForm.tableName && this.urlRoute) {
          this.$api.dataManage.exportData(params, url).then((res) => {
            const { data } = res
            if (data.type === 'application/vnd.ms-excel') {
              // 文件就导出
              this.$toast({
                content: data.msg || this.$t('导出成功'),
                type: 'success'
              })
              download({
                fileName: getHeadersFileName(res),
                blob: data
              })
            }
            if (data.type === 'application/json') {
              const _this = this
              const reader = new FileReader() //创建一个FileReader实例
              reader.readAsText(data, 'utf-8') //读取文件,结果用字符串形式表示
              reader.onload = function () {
                //读取完成后,**获取reader.result**
                const { msg } = JSON.parse(reader.result)
                _this.$toast({
                  content: msg,
                  type: msg === this.$t('系统成功创建导出任务') ? 'success' : 'warning'
                })
              }
              return
            }
          })
        }
      } else if (e.toolbar.id === 'Sync') {
        if (!this.searchForm.tableName) {
          this.$toast({ content: this.$t('请先选择表名称！'), type: 'warning' })
          return
        }
        const params = this.searchForm
        if (_selectRows.length) {
          let importJson = ''
          _selectRows.forEach((itm, idx) => {
            importJson += `${idx > 0 ? ',' : ''}${itm.id}`
          })
          params.importJson = importJson
        }
        this.$dialog({
          modal: () => import('./components/syncDialog.vue'),
          data: {
            title: this.$t('同步'),
            data: params,
            urlRoute: this.urlRoute
          },
          success: () => {
            this.$refs.templateRef.refreshCurrentGridData()
          }
        })
      } else if (e.toolbar.id === 'Delete') {
        if (!this.searchForm.tableName) {
          this.$toast({ content: this.$t('请先选择表名称！'), type: 'warning' })
          return
        }
        if (!_selectRows.length) {
          this.$toast({ content: this.$t('请先选择一行！'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('删除'),
            message: this.$t('是否确定删除选中数据？')
          },
          success: () => {
            const idList = _selectRows.map((i) => i.id)
            this.deleteData(idList)
          }
        })
      } else if (e.toolbar.id === 'Rollback') {
        if (!_selectRows.length) {
          this.$toast({ content: this.$t('请先选择一行！'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('回滚'),
            message: this.$t('是否确定回滚选中数据？')
          },
          success: () => {
            const idList = _selectRows.map((i) => i.id)
            this.rollbackData(idList)
          }
        })
      } else if (e.toolbar.id === 'ArchiveData') {
        if (!_selectRows.length) {
          this.$toast({ content: this.$t('请先选择一行！'), type: 'warning' })
          return
        }
        this.$dialog({
          data: {
            title: this.$t('归档'),
            message: this.$t('是否确定归档选中数据？')
          },
          success: () => {
            const idList = _selectRows.map((i) => i.id)
            this.archiveData(idList)
          }
        })
      }
    }
  }
}
</script>
<style>
.notice-hander .mt-select-index {
  display: inline-block;
}
</style>
<style lang="scss" scope>
.hander {
  // width: 100%;
  // height: 100%;
  // display: flex;
  // flex-direction: column;
  .e-content {
    height: calc(100vh - 294px) !important;
  }
}
</style>
