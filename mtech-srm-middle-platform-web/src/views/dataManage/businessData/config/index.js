// list toolbar
export const toolbar = [
  // { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' }
  { id: 'Download', icon: 'icon_solid_Download', title: '导出' },
  { id: 'Sync', icon: 'icon_table_restart', title: '同步' },
  { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' },
  { id: 'Rollback', icon: 'icon_solid_back', title: '回滚' },
  { id: 'ArchiveData', icon: 'icon_solid_back', title: '归档' },
]

// 新增行固定数据
export const NewRowData = {
  thePrimaryKey: 'add' + Math.random().toString(36).substring(3, 8),
  field: null,
  name: null,
  remark: null,
  sortNo: null,
  status: null
}

// list column
export const columnData = [
  {
    type: 'checkbox',
    width: '60'
  }
  // {
  //   width: 100,
  //   field: 'sortNo',
  //   headerText: '排序序号',
  //   editTemplate: function () {
  //     return {
  //       template: Vue.component('sortNoInput', {
  //         template: `<div>
  //         <mt-input
  //         v-model="data.sortNo"
  //         @change="sortNoChange"
  //       ></mt-input>
  //       </div>`,
  //         methods: {
  //           sortNoChange(e) {
  //             rowDataTemp[rowDataTemp.length - 1]['sortNo'] = e
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'field',
  //   headerText: '字段属性',
  //   editTemplate: function () {
  //     return {
  //       template: Vue.component('fieldInput', {
  //         template: `<div>
  //         <mt-input
  //         v-model="data.field"
  //         @change="fieldChange"
  //       ></mt-input>
  //       </div>`,
  //         methods: {
  //           fieldChange(e) {
  //             rowDataTemp[rowDataTemp.length - 1]['field'] = e
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'name',
  //   headerText: '字段名称',
  //   editTemplate: function () {
  //     return {
  //       template: Vue.component('fieldInput', {
  //         template: `<div>
  //         <mt-input
  //         v-model="data.name"
  //         @change="nameChange"
  //       ></mt-input>
  //       </div>`,
  //         methods: {
  //           nameChange(e) {
  //             rowDataTemp[rowDataTemp.length - 1]['name'] = e
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'status',
  //   headerText: '状态',
  //   valueConverter: {
  //     type: 'map',
  //     fields: { text: 'text', value: 'value' },
  //     map: [
  //       { text: '停用', value: 0, cssClass: ['btns warning'] },
  //       { text: '启用', value: 1, cssClass: ['btns warning'] }
  //     ]
  //   },
  //   editTemplate: function () {
  //     return {
  //       template: Vue.component('statusSelect', {
  //         template: `<div>
  //         <mt-select
  //         v-model="data.status"
  //         :data-source="dataSource"
  //         @change="statusSelectChange"
  //       ></mt-select>
  //       </div>`,
  //         data() {
  //           return {
  //             dataSource: [
  //               { text: '停用', value: 0 },
  //               { text: '启用', value: 1 }
  //             ]
  //           }
  //         },
  //         methods: {
  //           statusSelectChange(e) {
  //             // this.data.status = e.value
  //             // this.$set(this.data, 'status', e.value)
  //             rowDataTemp[rowDataTemp.length - 1]['status'] = e.value
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'remark',
  //   headerText: '备注',
  //   editTemplate: function () {
  //     return {
  //       template: Vue.component('remarkInput', {
  //         template: `<div>
  //         <mt-input
  //         v-model="data.remark"
  //         @change="remarkChange"
  //       ></mt-input>
  //       </div>`,
  //         methods: {
  //           remarkChange(e) {
  //             rowDataTemp[rowDataTemp.length - 1]['remark'] = e
  //           }
  //         }
  //       })
  //     }
  //   }
  // },
  // {
  //   field: 'createTypeName',
  //   headerText: '创建类型',
  //   allowEditing: false
  // }
]

// list pageConfig
export const pageConfig = [
  {
    toolbar: toolbar,
    // gridId: '00fb085b-dcb1-41ee-a343-13e8d02ef4d4',
    grid: {
      // editSettings: {
      //   allowAdding: true,
      //   allowEditing: true,
      //   allowDeleting: true,
      //   mode: 'Normal', // 默认normal模式
      //   allowEditOnDblClick: true,
      //   newRowPosition: 'Top'
      // },
      useToolTemplate: false,
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '',
        params: {}
      }
    }
  }
]
