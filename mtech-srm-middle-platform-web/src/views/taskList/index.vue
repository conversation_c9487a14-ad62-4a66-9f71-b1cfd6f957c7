<template>
  <div class="task-container">
    <div style="background: #fff; padding: 24px; border-radius: 8px">
      <!-- 第一层tabs -->
      <div class="task-tabs">
        <span
          v-for="(item, index) in tabsList"
          :key="item"
          @click="clickTab(index)"
          :class="{ active: currentTabIndex === index }"
          >{{ item }}</span
        >
      </div>
      <!-- 第二层tabs -->
      <div class="list-item-tabs">
        <div
          v-for="(item, index) in todoList"
          :key="item.title"
          :class="['list-item-tabs__tab', currentListTabIndex === index ? 'active' : '']"
          @click="clickListTab(index)"
        >
          <p>{{ item.title }}</p>
          <span>{{ item.number }}</span>
        </div>
        <span class="tabs-bottom" :style="getStyle()" />
      </div>
      <!-- 搜索区域 -->
      <div class="search-input">
        <div class="toggle-tag" @click="isToggle = !isToggle">
          <span>{{ isToggle ? $t('收起') : $t('展开') }}</span>
          <i
            class="mt-icons mt-icon-icon_Sort_up"
            :style="isToggle ? '' : 'transform: rotate(180deg) scale(0.4)'"
          />
          <i
            class="mt-icons mt-icon-icon_Sort_up"
            :style="isToggle ? '' : 'transform: rotate(180deg) scale(0.4)'"
          />
        </div>
        <div v-show="isToggle" class="search-area">
          <div class="input-area">
            <span class="input-field">{{ $t('关键字') }}</span>
            <mt-input v-model="keyword" type="text" :placeholder="$t('请输入关键字查询')" />
          </div>
          <div class="button-group">
            <span @click="reset()">{{ $t('重置') }}</span>
            <span @click="search()">{{ $t('查询') }}</span>
          </div>
        </div>
      </div>
      <!-- 表格 -->
      <mt-data-grid
        ref="dataGrid"
        :column-data="columnData"
        :selection-settings="{ checkboxOnly: true }"
        :data-source="dataSource"
        :allow-sorting="true"
        :allow-scrolling="true"
        :allow-paging="true"
        :page-settings="pageSettings"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      >
      </mt-data-grid>
    </div>
  </div>
</template>

<script>
import MtInput from '@mtech-ui/input'
import { styleList } from './config/index'
import Vue from 'vue'
export default {
  components: {
    MtInput
  },
  data() {
    return {
      tabsList: ['待处理', '已处理', '系统预警'], // 第一层tabs的数据源
      currentTabIndex: 0, // 第一层tabs的下标
      todoList: [
        {
          title: this.$t('全部'),
          number: null
        },
        {
          title: this.$t('待审批'),
          number: null
        },
        {
          title: this.$t('待办任务'),
          number: null
        }
      ], // 第二个tabs的数据源
      currentListTabIndex: 0, // 第二个tabs的下标
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      },
      dataSource: [], // 表格数据
      keyword: '', // 查询关键字
      styleList: styleList, // 第二层tabs样式控制常量
      isToggle: true,
      isPurchase: false // 判断是是否为采方页面
    }
  },
  watch: {
    $route: {
      handler: 'setIsPurchase',
      immediate: true
    }
  },
  computed: {
    // 表格配置根据计算属性动态获取
    columnData() {
      return [
        {
          field: 'no',
          headerText: this.$t('序号'),
          width: '80',
          allowSorting: false,
          template: function () {
            return {
              template: Vue.component('listIndex', {
                template: `<span>{{Number(data.index) + 1}}</span>`
              })
            }
          }
        },
        {
          field: 'todoType',
          headerText: this.$t('类型'),
          width: '100',
          template: function () {
            return {
              template: Vue.component('fileNameOption', {
                template: `<div style="display: flex; align-items: center;"><span style="display: block; width: 6px; height: 6px; margin-right: 8px; border-radius: 50%;" :style="data.todoType == 0 ? 'background: #52C41A;' : 'background: #FFB700;'" />data.todoType == 0 ? '任务' : ${this.$t(
                  '流程'
                )}</div>`,
                data() {
                  return { data: { data: {} } }
                }
              })
            }
          }
        },
        {
          field: 'title',
          width: '180',
          headerText: this.$t('标题'),
          template: function () {
            return {
              template: Vue.component('fileNameOption', {
                template: `<span style="color: #6386C1; cursor: pointer;" @click="linkTo(data.urlRef)">{{data.title}}</span>`,
                data() {
                  return { data: { data: {} } }
                },
                methods: {
                  linkTo(ref) {
                    if (ref) {
                      // window.open(ref, '_blank')
                      const linkArr = ref.split('/#')
                      if (linkArr[1]) {
                        this.$router.push({
                          path: linkArr[1]
                          // query: {
                          //   id: args.id
                          // }
                        })
                      }
                    }
                  }
                }
              })
            }
          }
        },
        {
          field: 'applyUserName',
          width: '100',
          headerText: this.$t('发起人'),
          visible: this.currentTabIndex !== 2 // 预警tab下才没有的项
        },
        {
          field: 'handlerUserName',
          width: '150',
          headerText: this.$t('当前审批人'),
          visible: this.currentTabIndex === 1 // 已处理tab下才有的项
        },
        {
          field: 'handlerUserName',
          width: '100',
          headerText: this.$t('责任人'),
          visible: this.currentTabIndex === 2 // 预警tab下才有的项
        },
        {
          field: 'todoStatus',
          width: '100',
          headerText: this.$t('状态'),
          visible: this.currentTabIndex === 2, // 预警tab下才有的项
          template: function () {
            return {
              template: Vue.component('statusText', {
                template: `<span>{{ data.todoStatus == 1 ? '已处理' : data.todoStatus == 2 ? '已驳回' : data.todoStatus == 3 ? '计划调整中' : '待处理' }}</span>`
              })
            }
          }
        },
        {
          field: 'createTime',
          width: '100',
          headerText: this.$t('创建时间')
        },
        {
          field: 'stayTime',
          width: '100',
          headerText: this.$t('停留时间'),
          visible: this.currentTabIndex === 0 // 待处理tab下才有的项
        },
        {
          field: 'handlerTime',
          width: '100',
          headerText: this.$t('处理时间'),
          visible: this.currentTabIndex === 1 // 已处理tab都有的项
        }
      ]
    }
  },
  created() {
    // 页面初始化时接口请求第二层tabs并赋值，成功回调之后请求列表
    this.clickTab(0)
  },
  methods: {
    setIsPurchase() {
      this.isPurchase = this.$route.path === '/middlePlatform/pur/taskList'
      this.clickTab(0)
    },
    getStyle() {
      return styleList['' + this.currentTabIndex + this.currentListTabIndex]
    },
    reset() {
      this.keyword = ''
      this.pageSettings = {
        ...this.pageSettings,
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0
      }
      this.search()
    },
    getTabCount(params) {
      if (this.isPurchase) {
        // 判断是是否为采方页面
        return this.$api.taskList.todoCount(params)
      }
      return this.$api.taskList.supplierTodoCount(params)
    },
    clickTab(index) {
      // 点击的tab与当前tab下标相同不触发事件
      if (this.currentTabIndex !== index || index == 0) {
        // 点击第一层tab按钮更新第二层tab数据源
        // 0
        // todoGroup = 0 todoTab = 0
        // 1
        // todoGroup = 0 todoTab = 1
        // 2
        // todoGroup = 1
        const params = {
          todoGroup: index === 2 ? 1 : 0
        }
        if (index !== 2) {
          params.todoTab = index
        }
        this.getTabCount(params).then((res) => {
          const { code, data } = res
          if (code == 200) {
            console.log(res)
            this.currentTabIndex = index
            switch (index) {
              case 0:
                this.todoList = [
                  {
                    title: this.$t('全部'),
                    number: data.todoCount
                  },
                  {
                    title: this.$t('待审批'),
                    number: data.flowCount
                  },
                  {
                    title: this.$t('待办任务'),
                    number: data.taskCount
                  }
                ]
                break
              case 1:
                this.todoList = [
                  {
                    title: this.$t('全部'),
                    number: data.doneCount
                  },
                  {
                    title: this.$t('流程'),
                    number: data.flowCount
                  },
                  {
                    title: this.$t('任务'),
                    number: data.taskCount
                  }
                ]
                break
              case 2:
                this.todoList = [
                  {
                    title: this.$t('全部预警'),
                    number: data.totalCount
                  },
                  {
                    title: this.$t('驳回预警'),
                    number: data.backCount
                  },

                  {
                    title: this.$t('超期预警'),
                    number: data.timeOutCount
                  }
                ]
                break
              default:
                break
            }
            // 点击第一层tabs后重置第二层tabs下标并刷新数据
            this.currentListTabIndex = 0
            this.pageSettings = {
              ...this.pageSettings,
              currentPage: 1,
              pageSize: 10,
              totalRecordsCount: 0
            }
            this.search()
          }
        })
      }
    },
    clickListTab(index) {
      // 点击的tab与当前tab下标相同不触发事件
      if (this.currentListTabIndex !== index) {
        // 点击第二层tab按钮更新tab当前下标及数据
        this.currentListTabIndex = index
        this.pageSettings = {
          ...this.pageSettings,
          currentPage: 1,
          pageSize: 10,
          totalRecordsCount: 0
        }
        this.search()
      }
    },
    getTodoQuery(params) {
      if (this.isPurchase) {
        // 判断是否为采方页面
        return this.$api.taskList.todoQuery(params)
      }
      return this.$api.taskList.supplierTodoQuery(params)
    },
    search() {
      console.log('查询', this.keyword)
      // 请求表格数据
      const params = {
        todoGroup: this.currentTabIndex === 2 ? 1 : 0,
        title: this.keyword,
        current: this.pageSettings.currentPage,
        size: this.pageSettings.pageSize
      }
      if (this.currentTabIndex !== 2) {
        params.todoTab = this.currentTabIndex
        if (this.currentListTabIndex === 1) {
          params.todoType = 1
        }
        if (this.currentListTabIndex === 2) {
          params.todoType = 0
        }
      } else {
        if (this.currentListTabIndex === 1) {
          params.todoTab = 3
        }
        if (this.currentListTabIndex === 2) {
          params.todoTab = 2
        }
      }
      // 00
      // todoTab = 0
      // 01
      // todoTab = 0 todoType = 0
      // 02
      // todoTab = 0 todoType = 1
      // 10
      // todoTab = 1
      // 11
      // todoTab = 1 todoType = 0
      // 12
      // todoTab = 1 todoType = 1
      // 20
      // ..
      // 21
      // todoTab = 3
      // 22
      // todoTab = 2
      this.getTodoQuery(params).then((res) => {
        if (res.code === 200) {
          this.dataSource = res.data.records
          this.pageSettings = {
            ...this.pageSettings,
            currentPage: parseInt(res.data.current),
            pageSize: parseInt(res.data.size),
            totalRecordsCount: parseInt(res.data.total)
          }
        }
      })
    },
    // 分页的两个方法
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.search()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    }
  }
}
</script>

<style lang="scss" scoped>
.task-container {
  padding-top: 24px;
}
.task-tabs {
  display: inline-flex;
  border-radius: 4px;
  border: 1px solid #4e5a70;
  margin-bottom: 10px;
  span {
    padding: 11px 16px;
    font-size: 14px;
    color: #4e5a70;
    border-right: 1px solid #4e5a70;
    box-sizing: border-box;
    cursor: pointer;
    &:last-child {
      border-right: 0px;
    }
    &.active {
      color: #fff;
      background: #4e5a70;
    }
  }
}
.list-item-tabs {
  border-bottom: 1px dashed #ececec;
  display: flex;
  &__tab {
    margin: 0 10px;
    padding: 18px 0;
    box-sizing: border-box;
    cursor: pointer;
    &:first-child {
      margin-left: 0;
    }
    display: flex;
    align-items: center;
    p {
      margin-bottom: unset;
      color: #999;
      font-size: 16px;
    }
    span {
      font-size: 12px;
      transform: scale(0.83);
      color: #fff;
      display: block;
      width: 19.2px;
      height: 19.2px;
      background: #f55448;
      border: 1px solid #ffffff;
      line-height: 19.2px;
      text-align: center;
      border-radius: 50%;
      margin: -8px 0 0 2px;
    }
    &.active {
      // padding: 10px 0 8px;
      // border-bottom: 2px solid red;
      p {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  position: relative;
  .tabs-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 33px;
    height: 2px;
    background: #f55448;
    border-radius: 1px;
  }
}
.search-input {
  padding: 10px 0 16px;
  .toggle-tag {
    padding-right: 15px;
    color: #2783fe;
    display: inline-block;
    font-size: 12px;
    position: relative;
    cursor: pointer;
    user-select: none;
    .mt-icons {
      font-size: 12px;
      position: absolute;
      transform: scale(0.4);
      top: -2px;
      left: 26px;
      &:nth-child(2) {
        top: 2px;
      }
    }
  }
  .search-area {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 16px 24px 12px;
    margin-top: 12px;
    .input-area {
      display: flex;
      align-items: center;
      .input-field {
        font-size: 14px;
        color: #333333;
        margin-right: 16px;
        font-family: PingFangSC-Regular;
      }
    }
    .button-group {
      padding-top: 10px;
      span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #2783fe;
        cursor: pointer;
        user-select: none;
      }
    }
  }
}
</style>
