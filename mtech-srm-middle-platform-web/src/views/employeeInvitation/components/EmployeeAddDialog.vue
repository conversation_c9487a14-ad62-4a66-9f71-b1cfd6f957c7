<template>
  <mt-dialog ref="dialog" :header="$t('员工邀请')" :buttons="buttons" @close="hide">
    <mt-form class="form-content--wrap" ref="form" :model="formData" :rules="formRules">
      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('员工姓名')" prop="employeeName">
            <mt-input type="text" v-model="formData.employeeName"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('手机号')" prop="phoneNum">
            <mt-input type="text" v-model="formData.phoneNum"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('邮箱')" prop="email">
            <mt-input type="text" v-model="formData.email"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('集团')">
            <mt-input
              type="text"
              v-model="organizationRootNode.orgName"
              :readonly="true"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('所属公司')" prop="companyOrganizationId">
            <mt-select
              :fields="dictSelectField"
              :dataSource="companyOrganizationList"
              v-model="formData.companyOrganizationId"
              :placeholder="$t('请选择公司名称')"
              @change="changeCompany"
            >
            </mt-select>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('所属部门')" prop="departmentOrganizationId">
            <mt-select
              :fields="dictSelectField"
              :dataSource="departmentOrganizationList"
              v-model="formData.departmentOrganizationId"
              :placeholder="$t('请选择所属部门')"
              @change="changeDepartment"
            >
            </mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('所属岗位')" prop="stationOrganizationId">
            <mt-select
              :fields="dictSelectField"
              :dataSource="stationOrganizationList"
              v-model="formData.stationOrganizationId"
              :placeholder="$t('请选择岗位名称')"
              @change="changeStation"
            >
            </mt-select>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="24">
          <mt-form-item :label="$t('所属角色')" prop="roleIds">
            <mt-checkbox-group
              v-model="formData.roleIds"
              :disabled="false"
              class="form-item--checkbox"
            >
              <mt-checkbox
                v-for="role in employeeRoleList"
                :key="role.id"
                :content="role"
                :fields="checkboxField"
              ></mt-checkbox>
            </mt-checkbox-group>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script lang="ts">
import { i18n } from '@/main.js'
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
@Component({
  components: {}
})
export default class EmployeeAddDialog extends Vue {
  @Prop()
  value!: boolean

  formData: any = {}

  organizationRootNode: any = {}
  companyOrganizationList: any[] = []
  departmentOrganizationList: any[] = []
  stationOrganizationList: any[] = []
  employeeRoleList: any[] = []

  dictSelectField: any = {
    value: 'id',
    text: 'orgName'
  }

  checkboxField = {
    label: 'roleName',
    id: 'id'
  }

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: i18n.t('取消') }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: i18n.t('确认邀请') }
    }
  ]

  formRules = {
    employeeName: [{ required: true, message: i18n.t('请输入员工姓名'), trigger: 'blur' }],
    phoneNum: [{ required: true, validator: this.validatePhoneNum, trigger: 'blur' }],
    email: [{ required: true, validator: this.validateEmail, trigger: 'blur' }],
    companyOrganizationId: [{ required: true, message: i18n.t('请选择所属公司'), trigger: 'blur' }],
    departmentOrganizationId: [
      { required: true, message: i18n.t('请选择所属部门'), trigger: 'blur' }
    ],
    stationOrganizationId: [{ required: true, message: i18n.t('请选择所属岗位'), trigger: 'blur' }],
    roleIds: [{ required: true, message: i18n.t('请选择所属角色'), trigger: 'blur' }]
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  created() {
    this.getChildrenCompanyOrganization()
    this.getorganizationRootNode()
  }

  hide() {
    this.visible = false
    this.formData = {}
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
  }

  handleConfirm() {
    const form: any = this.$refs.form

    form.validate((valid: boolean) => {
      if (valid) {
        this.inviteUser2employee()
      }
    })
  }

  changeCompany(event: any) {
    const { itemData } = event

    this.$set(this.formData, 'departmentOrganizationId', '')
    this.departmentOrganizationList = []
    if (itemData) {
      this.formData.companyOrganizationId = itemData.id
      this.getChildrenDepartmentOrganization(itemData.id)
    }
  }

  changeDepartment(event: any) {
    const { itemData } = event

    this.$set(this.formData, 'stationOrganizationId', '')
    this.stationOrganizationList = []

    if (itemData) {
      this.formData.departmentOrganizationId = itemData.id
      this.getChildrenStationOrganization(itemData.id)
    }
  }

  changeStation(event: any) {
    const { itemData } = event
    this.employeeRoleList = []
    this.$set(this.formData, 'roleIds', [])
    if (itemData) {
      this.formData.stationOrganizationId = itemData.id
      this.roleCriteriaQuery()
    }
  }

  // 验证手机号
  validatePhoneNum(rule: any, value: any, callback: any) {
    const phoneRegex = /^1[3-9]\d{9}$/

    if (!value) {
      callback(new Error(i18n.t('请输入手机号')))
    } else if (!phoneRegex.test(value)) {
      callback(new Error(i18n.t('手机号格式不正确')))
    } else {
      callback()
    }
  }

  validateEmail(rule: any, value: any, callback: any) {
    const emailRegex =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    if (!value) {
      callback(new Error(i18n.t('请输入邮箱地址')))
    } else if (!emailRegex.test(value)) {
      callback(new Error(i18n.t('邮箱地址格式不正确')))
    } else {
      callback()
    }
  }

  //
  // 获取当前组织下公司列表
  private async getorganizationRootNode() {
    const res = await this.$api.employee.getorganizationRootNode()
    this.organizationRootNode = res?.data || {}
  }

  // 获取当前组织下公司列表
  private async getChildrenCompanyOrganization() {
    const res = await this.$api.employee.getChildrenCompanyOrganization({})
    this.companyOrganizationList = res?.data || []
  }

  // 获取当前组织下部门列表
  private async getChildrenDepartmentOrganization(id: string) {
    const res = await this.$api.employee.getChildrenDepartmentOrganization({
      organizationId: id
    })
    this.departmentOrganizationList = res?.data || []
  }

  // 获取当前组织下岗位列表
  private async getChildrenStationOrganization(id: string) {
    const res = await this.$api.employee.getChildrenStationOrganization({
      organizationId: id
    })
    this.stationOrganizationList = res?.data || []
  }

  // 获取角色列表
  private async roleCriteriaQuery() {
    const res = await this.$api.employee.roleCriteriaQuery({
      id: ''
    })
    this.employeeRoleList = res?.data || []
  }

  private inviteUser2employee() {
    const params = {
      email: this.formData.email,
      employeeName: this.formData.employeeName,
      phoneNum: this.formData.phoneNum,
      roleIds: this.formData.roleIds,
      companyOrganizationId: this.formData.companyOrganizationId,
      departmentOrganizationId: this.formData.departmentOrganizationId,
      stationOrganizationId: this.formData.stationOrganizationId
    }

    this.$api.employee
      .inviteUser2employee(params)
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || i18n.t('邀请成功'),
            type: 'success'
          })
          this.hide()
          this.$emit('save')
        } else {
          this.$toast({
            content: res.msg || i18n.t('邀请失败'),
            type: 'error'
          })
        }
      })
      .catch()
  }
}
</script>

<style lang="scss" scoped>
.form-content--wrap {
  margin-top: 20px;
}
.form-item--checkbox {
  margin-top: 10px;
}
</style>
