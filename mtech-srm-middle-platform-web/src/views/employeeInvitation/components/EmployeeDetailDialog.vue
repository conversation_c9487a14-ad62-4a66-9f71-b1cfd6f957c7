<template>
  <mt-dialog ref="dialog" :header="$t('邀请明细')" :buttons="buttons" @close="hide">
    <mt-form class="form-content--wrap">
      <mt-row :gutter="20">
        <mt-col :span="6">
          <img class="photo-image" :src="formData.photo" />
        </mt-col>

        <mt-col :span="18">
          <mt-row :gutter="68">
            <mt-col :span="12">
              <mt-form-item :label="$t('员工姓名')" prop="employeeName">
                <mt-input type="text" :readonly="true" v-model="formData.employeeName"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item :label="$t('手机号')" prop="phoneNum">
                <mt-input type="text" :readonly="true" v-model="formData.phoneNum"></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row :gutter="68">
            <mt-col :span="12">
              <mt-form-item :label="$t('邮箱')" prop="email">
                <mt-input type="text" :readonly="true" v-model="formData.email"></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12"> </mt-col>
          </mt-row>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('集团')" prop="identityTypeCode">
            <mt-input type="text" v-model="formData.enterpriseShortName"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('所属公司')" prop="companyName">
            <mt-input type="text" v-model="formData.companyName"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('所属部门')" prop="departmentName">
            <mt-input type="text" v-model="formData.departmentName"></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('所属岗位')" prop="stationName">
            <mt-input type="text" :readonly="true" v-model="formData.stationName"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="24">
          <mt-form-item :label="$t('所属角色')" prop="rolesName">
            <mt-input type="text" :readonly="true" v-model="formData.rolesName"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="68">
        <mt-col :span="12">
          <mt-form-item :label="$t('邀请状态')" prop="statusId">
            <mt-input type="text" :readonly="true" v-model="formData.statusName"></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import { i18n } from '@/main.js'
@Component({
  components: {}
})
export default class EmployeeAddDialog extends Vue {
  @Prop()
  value!: boolean

  @Prop()
  id!: string

  formData: any = {}

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: i18n.t('取消') }
    },
    {
      click: this.handleConfirm,
      buttonModel: { isPrimary: 'true', content: i18n.t('确认') }
    }
  ]

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  hide() {
    this.visible = false
    this.formData = {}
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {
    const ref: any = this.$refs.dialog

    this.getEmployeeDetail()
    ref.ejsRef.show()
  }

  handleConfirm() {
    this.hide()
    this.formData = {}
  }

  private getEmployeeDetail() {
    this.$api.employee
      .getEmployeeInfo({
        employeeId: this.id
      })
      .then((res: any) => {
        if (res.code === 200) {
          const { statusId } = res.data

          res.data.statusName =
            statusId === '-1'
              ? i18n.t('待审核')
              : statusId === '1'
              ? i18n.t('同意')
              : statusId === '3'
              ? i18n.t('拒绝')
              : ''
          this.formData = res.data
        }
      })
  }
}
</script>

<style lang="scss" scoped>
.form-content--wrap {
  margin-top: 20px;
}

.photo-image {
  width: 100%;
  height: 120px;
  background: #fbfcfd;
  border: 1px dashed #e8e8e8;
}
</style>
