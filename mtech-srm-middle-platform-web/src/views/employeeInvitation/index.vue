<template>
  <div class="employee-invitation">
    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>

    <EmployeeAddDialog v-model="isShowAddDialog" @save="refreshTempaltePage" />
    <EmployeeDetailDialog v-model="isShowDetailDialog" :id="currEmployeeId" />
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import { employeePageConfig } from './config/index'
import EmployeeAddDialog from './components/EmployeeAddDialog.vue'
import EmployeeDetailDialog from './components/EmployeeDetailDialog.vue'

@Component({
  components: {
    EmployeeAddDialog,
    EmployeeDetailDialog
  }
})
export default class EmployeeInvitation extends Vue {
  pageConfig: any[] = employeePageConfig

  isShowAddDialog = false
  isShowDetailDialog = false
  currEmployeeId = ''

  handleClickToolBar(e: any) {
    const { toolbar } = e
    if (toolbar.id === 'Add') {
      this.isShowAddDialog = true
    }
  }

  handleClickCellTool(e: any) {
    const { tool, data } = e
    if (tool.id === 'View') {
      this.showEmployeeDeatilDialog(data)
    }
  }

  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }

  private showEmployeeDeatilDialog(data: any) {
    this.isShowDetailDialog = true
    this.currEmployeeId = data.employeeId
  }
}
</script>
<style lang="scss" scoped>
.employee-invitation {
  height: 100%;
  /deep/ .status {
    line-height: 18px;
    border-radius: 2px;
    padding: 0 4px;
  }

  /deep/ .status-creating {
    background: rgba(237, 161, 51, 0.1);
    color: #eda133;
  }

  /deep/ .status-failed {
    background: rgba(237, 86, 51, 0.1);
    color: #ed5633;
  }

  /deep/ .status-unapproval {
    background: rgba(148, 151, 157, 0.1);
    color: #9a9a9a;
  }

  /deep/ .status-success {
    background: rgba(99, 134, 193, 0.1);
    color: #6386c1;
  }
}
</style>
