import { i18n } from '@/main.js'
import ColumnTimeTemplate from '@/components/ColumnTimeTemplate.vue'

export const employeePageConfig = [
  {
    toolbar: [{ id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增邀请') }],
    useToolTemplate: false,
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'employeeName',
          headerText: i18n.t('员工姓名')
        },
        {
          field: 'phoneNum',
          headerText: i18n.t('手机号')
        },
        {
          field: 'companyName',
          headerText: i18n.t('归属公司')
        },
        {
          field: 'departmentName',
          headerText: i18n.t('部门')
        },
        {
          field: 'stationName',
          headerText: i18n.t('岗位')
        },
        {
          field: 'createUserName',
          headerText: i18n.t('邀请人')
        },
        {
          field: 'createTime',
          headerText: i18n.t('创建时间'),
          template() {
            return {
              template: ColumnTimeTemplate
            }
          }
        },

        {
          field: 'statusId',
          headerText: i18n.t('状态'),
          valueConverter: {
            type: 'map',
            fields: { text: 'label', value: 'status' },
            map: [
              { status: '-1', label: i18n.t('待审核'), cssClass: ['status', 'status-unapproval'] },
              { status: '1', label: i18n.t('同意'), cssClass: ['status', 'status-success'] },
              { status: '3', label: i18n.t('拒绝'), cssClass: ['status', 'status-failed'] }
            ]
          },
          cellTools: [{ id: 'View', icon: 'icon_solid_Submit', title: i18n.t('查看') }]
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/masterDataManagement/tenant/employee/employee-paged-query',
        methods: 'post',
        params: {}
      }
    }
  }
]
