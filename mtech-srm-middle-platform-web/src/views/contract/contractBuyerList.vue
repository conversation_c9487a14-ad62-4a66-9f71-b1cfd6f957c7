<template>
  <div class="buyerContract-wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
      :current-tab="currentTab"
    >
    </mt-template-page>
    <!-- 弹窗 -->
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :style="{ width: '940px' }"
      :header="title"
    >
      <mt-template-page ref="table" :template-config="dialogPageConfig" />
    </mt-dialog>
  </div>
</template>
<script>
import { pageConfig } from './config/contractBuyer'
// import filterSearch from '@/views/contract/components/filterSearch'
// import attachMent from './components/attachMent.vue'
export default {
  components: {
    // attachMent
    // filterSearch
  },
  data() {
    return {
      pageConfig: pageConfig(this),
      currentTab: 0,
      title: this.$t('阶梯报价'),
      dialogPageConfig: [
        {
          toolbar: [[], []],
          useToolTemplate: false,
          useBaseConfig: false,
          grid: {
            allowPaging: false,
            columnData: [
              {
                field: 'stageFrom',
                headerText: this.$t('阶梯起')
              },
              {
                field: 'stageTo',
                headerText: this.$t('阶梯止')
              },
              {
                field: 'untaxedUnitPrice',
                headerText: this.$t('未税单价')
              },
              {
                field: 'taxedUnitPrice',
                headerText: this.$t('含税单价')
              }
            ],
            dataSource: []
          }
        }
      ],
      buttons: [
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  methods: {
    handleClickToolBar(e) {
      const { grid } = e
      const sections = grid.getSelectedRecords()
      if (e.toolbar.id === 'create') {
        if (sections.length === 0) {
          this.$toast({
            content: this.$t('请选择一行数据'),
            type: 'warning'
          })
          return
        }
        this.creatContract(sections)
      }
      if (e.toolbar.id == 'createOrder') {
        if (sections.length === 0) {
          this.$toast({
            content: this.$t('请选择一行数据'),
            type: 'warning'
          })
          return
        }
        this.createOrderRequset(sections)
      }
      if (e.toolbar.id == 'OAProcess') {
        if (sections.length !== 1) {
          this.$toast({
            content: this.$t('请仅选择一行数据'),
            type: 'warning'
          })
          return
        }
        this.linkOA(sections)
      }
      if (e.toolbar.id === 'add') {
        this.addContract()
      }
    },
    linkOA(data) {
      this.$api.contract.queryBuyerListServe
        .getSignOaLink({ contractId: data[0]['id'] })
        .then((res) => {
          const { code, data } = res
          if (code === 200 && data) {
            window.open(decodeURIComponent(data))
          }
        })
    },
    handleClickCellTool(e) {
      const { tool, data } = e
      if (tool.id == 'submit') {
        // 提交
        // this.archiveContract(data);
        this.contractAction('commit', data.id)
      }
      if (tool.id == 'publish') {
        //  发布
        this.contractAction('publish', data.id)
      }
      if (tool.id == 'disable') {
        // 撤销
        this.contractAction('undo', data.id)
      }
      if (tool.id == 'stop') {
        // 作废
        this.contractAction('abandon', data.id)
      }
      if (tool.id == 'cancel_stop') {
        // 取消作废
        this.contractAction('abandon-reverse', data.id)
      }
      if (tool.id == 'sign') {
        // 签订合同
        this.singnContract(data)
      }
      if (tool.id == 'edit') {
        this.contractEdit(data)
      }
      if (tool.id == 'archived') {
        this.archiveContract(data)
      }
    },
    handleClickCellTitle(e) {
      // 标题点击
      const { field, data } = e
      if (e.field == 'contractCode' || e.field == 'contract.contractCode') {
        this.$router.push({
          path: '/middlePlatform/contractPrivew',
          query: {
            contractId: e.field == 'contractCode' ? data.id : data.contract.id
          }
        })
      }
      if (e.field == 'contractIdRelation' || e.field == 'contract.contractIdRelation') {
        this.$router.push({
          path: '/middlePlatform/contractPrivew',
          query: {
            contractId: data.contractIdRelation ?? data.contract.contractIdRelation
          }
        })
      }
      if (e.field == 'pendingitem.purRequestCode') {
        if (data.pendingitem?.businessTypeCode === 'BTTCL006') {
          // 直接跳转
          this.getLogisticsCodeById(data.pendingitem?.purRequestCode)
          return
        }
        this.$router.push({
          path: '/purchase-execute/pr-apply-detail',
          query: {
            id: data.pendingitem?.purRequestCode,
            type: 'view',
            source: 'shopMall',
            businessTypeCode: data.pendingitem?.businessTypeCode
          }
        })
      }
      if (e.field == 'archivedAttachmentList') {
        this.attachMentShow(data.archivedAttachmentList)
      }
      if (e.field == 'pendingitem.stageList' || e.field == 'contractItem.stageList') {
        this.$refs.dialog.ejsRef.show()
        const pending = data.pendingitem?.stageList ?? data.contractItem?.stageList
        this.dialogPageConfig[0].grid.dataSource = pending
      }
    },
    // 物流类通过code查找id(物流类的查询在合同模块)
    getLogisticsCodeById(prCode) {
      this.$api.contract.queryBuyerListServe
        .getLogisticsCodeById({
          docNo: prCode,
          businessTypeCode: 'BTTCL006'
        })
        .then((res) => {
          if (res.data) {
            this.$router.push(
              `/purchase-execute/pr-apply-logistics-detail?id=${res.data.id}&type=view`
            )
          }
        })
    },
    singnContract(data) {
      this.$dialog({
        modal: () => import('./components/signContract.vue'),
        data: {
          title: this.$t('签订'),
          data: data
        },
        success: () => {
          this.$refs.tempaltePageRef.refreshCurrentGridData({
            resetPageParams: false
          })
        }
      })
    },
    archiveContract(data) {
      // 归档
      this.$dialog({
        modal: () => import('./components/archiveContract.vue'),
        data: {
          title: this.$t('归档'),
          data: data
        },
        success: () => {
          this.$refs.tempaltePageRef.refreshCurrentGridData({
            resetPageParams: false
          })
        }
      })
    },
    creatContract(sessions) {
      let supplierArr = []
      let orgArr = []
      let companyArr = []
      let currencyArr = []
      let primyId = []
      let peddingItem = []
      sessions.map((item) => {
        supplierArr = [...supplierArr, item.pendingitem.supplierId]
        orgArr = [...orgArr, item.pendingitem.purOrgId]
        companyArr = [...companyArr, item.company.purCompanyId]
        currencyArr = [...currencyArr, item.pendingitem.currencyId]
        primyId = [...primyId, item.company.companyPrimaryId]
        peddingItem = [...peddingItem, item.pendingitem.pendingitemId]
      })
      if (
        new Set(supplierArr).size > 1 ||
        new Set(orgArr).size > 1 ||
        new Set(companyArr).size > 1 ||
        new Set(currencyArr).size > 1
      ) {
        this.$toast({
          content: this.$t('所选数据的供应商、公司、组织机构和币种均相同的情况下才能创建合同'),
          type: 'warning'
        })
        return
      }
      // 创建时合同校验
      this.$api.contract.queryBuyerListServe
        .checkItemData({ pendingitemIdList: peddingItem })
        .then((res) => {
          if (res.code == 200) {
            this.$router.push({
              path: '/middlePlatform/createContract'
            })
            localStorage.setItem('primaryIds', JSON.stringify(primyId))
          }
        })
    },
    contractAction(type, contractId) {
      // 合同操作
      const message = {
        publish: this.$t('是否确定发布'),
        commit: this.$t('是否确定提交'),
        undo: this.$t('是否确定撤销'),
        abandon: this.$t('是否确定作废'),
        'abandon-reverse': this.$t('是否确定取消作废'),
        sign: this.$t('是否确定签订'),
        archive: this.$t('是否确定归档')
      }

      this.$dialog({
        data: {
          message: message[type],
          title: this.$t('提示')
        },
        success: () => {
          this.$api.contract.queryBuyerListServe.contractAction(type, contractId).then((res) => {
            if (res.code == 200) {
              this.$toast({
                content: res.msg,
                type: 'success'
              })
              this.$refs.tempaltePageRef.refreshCurrentGridData({
                resetPageParams: false
              })
            }
          })
        }
      })
    },
    contractEdit(data) {
      this.$router.push({
        path: '/middlePlatform/contractDetail',
        query: {
          contractId: data.id,
          type: data.logisticsMethodCode === '3' ? 'edit' : null
        }
      })
    },
    addContract() {
      this.$router.push({
        path: '/middlePlatform/contractDetail',
        query: {
          type: 'add'
        }
      })
    },
    async createOrderRequset(data) {
      let vailde = true
      const noVaildeArr = []
      data.map((i) => {
        sessionStorage.setItem(
          'buyerUserInfo',
          JSON.stringify({
            buyerUserName: i.contract.purName.split('-')[3]
              ? i.contract.purName.split('-')[3]
              : i.contract.purName.split('-')[1]
          })
        )
        if (i.contract.status !== 5 && i.contract.status !== 6) {
          vailde = false
        }
        if (i.contractItem.purRequirementRemainNum <= 0) {
          noVaildeArr.push(i.contract.contractCode)
        }
      })
      if (!vailde) {
        this.$toast({
          content: this.$t('只有已签订或已归档的明细才可以创建订单'),
          type: 'warning'
        })
        return
      }
      if (noVaildeArr.length) {
        this.$toast({
          content: this.$t('当前有合同明细采购数量已经全部转化为订单了，不允许再次创建采购订单'),
          type: 'warning'
        })
        return
      }
      const contractItemIdList = data.map((item) => item?.contractItem?.id)
      const contractCodeList = data.map((item) => item?.contract?.contractCode)
      let res = await this.$api.contract.queryBuyerListServe
        .createOrderCheck({
          contractCodeList
        })
        .catch((error) => {
          console.log(error)
        })
      if (res.code !== 200) return
      this.$api.contract.queryBuyerListServe.createOrder({ contractItemIdList }).then((res) => {
        this.$router.push({
          path: '/purchase-execute/purchase-coordination-detail',
          query: {
            type: 1,
            source: 4,
            key: res.data.key
          }
        })
      })
    },
    attachMentShow(data) {
      this.$dialog({
        modal: () => import('./components/privewAttachment.vue'),
        data: {
          title: this.$t('归档附件'),
          data: data
        },
        success: () => {}
      })
    },
    confirm() {
      this.$refs.dialog.ejsRef.hide()
    }
  },
  created() {
    if (this.$route.query.tab) {
      this.currentTab = this.$route.query.tab
    }
  }
}
</script>
<style lang="scss" scoped>
.buyerContract-wrap {
  height: 100%;
}
/deep/ .href {
  color: #00469c;
  cursor: pointer;
  font-weight: bold;
}
/deep/ .status {
  font-size: 12px;
  padding: 4px;
  border-radius: 2px;
}
/deep/ .info {
  background-color: rgba(99, 134, 193, 0.1);
  color: #6386c1;
}
/deep/ .warning {
  background-color: rgba(237, 161, 51, 0.1);
  color: rgba(237, 161, 51, 1);
}
/deep/ .success {
  background-color: rgba(138, 204, 64, 0.1);
  color: rgba(138, 204, 64, 1);
}
/deep/ .error {
  background-color: rgba(237, 86, 51, 0.1);
  color: rgba(237, 86, 51, 1);
}
/deep/ .disabled {
  background-color: rgba(154, 154, 154, 0.1);
  color: rgba(154, 154, 154, 1);
}
/deep/ .text {
  position: relative;
  padding-left: 14px;
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    margin-top: -4px;
    left: 0;
  }
}
/deep/ .text_success {
  color: #6386c1;
  &::before {
    background-color: #6386c1;
  }
}
/deep/ .text_waring {
  color: #eda133;
  &::before {
    background-color: #eda133;
  }
}
/deep/ .text_fail {
  color: #ed5633;
  &::before {
    background-color: #ed5633;
  }
}
/deep/ .text_gray {
  color: #9a9a9a;
  &::before {
    background-color: #9a9a9a;
  }
}
/deep/ .column-tool {
  margin-top: 8px;
}
</style>
