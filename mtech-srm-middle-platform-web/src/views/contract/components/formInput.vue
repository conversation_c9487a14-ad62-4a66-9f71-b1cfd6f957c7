// 头部表单组件
<template>
  <div class="form-box">
    <div class="headr-box">
      <div>
        <div v-if="!childObj.createStatus">
          <span v-if="inputObj.status === 0" class="span-status blue">{{ $t('草稿') }}</span>
          <span v-if="inputObj.status === 1" class="span-status cancel">{{ $t('待发布') }}</span>
          <span v-if="inputObj.status === 2" class="span-status cancel">{{ $t('待反馈') }}</span>
          <span v-if="inputObj.status === 3" class="span-status green">{{ $t('反馈正常') }}</span>
          <span v-if="inputObj.status === 4" class="span-status red">{{ $t('反馈异常') }}</span>
          <span v-if="inputObj.status === 5" class="span-status green">{{ $t('已签订') }}</span>
          <span v-if="inputObj.status === 6" class="span-status green">{{ $t('已归档') }}</span>
          <span class="headr-text">{{ $t('更新人') }}：{{ inputObj.updateUserName }}</span>
          <span class="headr-text headr-time"
            >{{ $t('更新时间') }}：{{
              $moment(inputObj.updateTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span
          >
        </div>
      </div>
      <div class="headr-btn">
        <span @click="backOff">{{ $t('返回') }}</span>
        <!-- 供方 -->
        <span
          class="accept"
          v-if="inputObj.status === 2 && !childObj.createStatus && !childObj.seeStatus"
          @click="accept"
          >{{ $t('接受') }}</span
        >
        <span
          class="reject"
          v-if="inputObj.status === 2 && !childObj.createStatus && !childObj.seeStatus"
          @click="refuse"
          >{{ $t('拒绝') }}</span
        >
        <span v-if="childObj.createStatus && !childObj.seeStatus" @click="draft">{{
          $t('保存草稿')
        }}</span>
        <span v-if="childObj.createStatus && !childObj.seeStatus" @click="publish">{{
          $t('发布')
        }}</span>
        <!-- <span v-if="childObj.createStatus && !childObj.seeStatus" @click="submitBtn">{{
          $t('提交')
        }}</span> -->
        <span @click="away"
          >{{ $t('收起')
          }}<span
            :style="{
              transform: formInput ? 'rotate(180deg)' : 'rotate(0deg)',
              marginTop: formInput ? '-5px' : '2px'
            }"
            ><i class="mt-icons mt-icon-MT_DownArrow"></i></span
        ></span>
      </div>
    </div>
    <div class="headr-border" v-if="!formInput"></div>
    <mt-form ref="ruleForm" :model="inputObj" :rules="rules" class="form-input">
      <div class="demo-block">
        <mt-form-item
          :class="inputDisabled.contractCodeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同编号')"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.contractCode"
            :disabled="inputDisabled.contractCodeDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('保存后自动生成')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contractNameDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同名称')"
          style="color: #f0f"
          prop="contractName"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.contractName"
            :disabled="inputDisabled.contractNameDisabled"
            :show-clear-button="true"
            type="text"
            maxlength="64"
            :placeholder="$t('请输入合同名称')"
          ></mt-input>
        </mt-form-item>

        <mt-form-item
          :class="inputDisabled.supplierCodeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商编号')"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierCode"
            :disabled="inputDisabled.supplierCodeDisabled"
            :show-clear-button="true"
            type="text"
            style="padding: 0"
            :placeholder="$t('请输入供应商编号')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.supplierNameDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商名称')"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierName"
            :disabled="inputDisabled.supplierNameDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contractCategoryDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同类别')"
          prop="contractCategoryCode"
        >
          <mt-DropDownTree
            v-if="fieldsStatus"
            :placeholder="$t('请选择合同类别')"
            :popup-height="300"
            :fields="fields"
            id="baseTreeSelect"
            v-model="inputObj.contractCategoryCode"
            :readonly="inputDisabled.contractCategoryDisabled"
            @select="selectTree"
            :allow-filtering="true"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.orgGroupDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('采购组织')"
        >
          <mt-input
            v-model="inputObj.purOrgName"
            :disabled="inputDisabled.orgGroupDisabled"
            :show-clear-button="true"
            type="text"
          ></mt-input>
          <!-- <mt-DropDownTree
            v-if="purchaseStatus"
            :placeholder="$t('请选择采购组织编号')"
            :popupHeight="300"
            :fields="purchaseFields"
            id="baseTreeSelect"
            v-model="inputObj.purOrgId"
            :readonly="inputDisabled.orgGroupDisabled"
            @select="purchaseSelect"
            :allowFiltering="true"
          ></mt-DropDownTree> -->
          <!-- <mt-select
            v-model="inputObj.purOrgId"
            :disabled="inputDisabled.orgGroupDisabled"
            :showClearButton="true"
            :dataSource="purchaseFields.dataSource"
            :placeholder="$t('请选择采购组织编号')"
          ></mt-select> -->
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.companyDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('公司')"
        >
          <!-- <mt-input
            itemLabelStyle="left"
            v-model="inputObj.purCompanyName"
            :disabled="inputDisabled.companyDisabled"
            :showClearButton="true"
            type="text"
            :placeholder="$t('请输入公司')"
          ></mt-input> -->
          <mt-input
            v-model="inputObj.purCompanyName"
            :disabled="inputDisabled.companyDisabled"
            :show-clear-button="true"
            type="text"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.buyerGroupDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('采购组')"
        >
          <mt-select
            v-model="inputObj.purchaseGroupId"
            :disabled="inputDisabled.buyerGroupDisabled"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="selectObj.buyerGroup"
            :placeholder="$t('请选择采购组')"
            @change="changeBuyerGroup"
          ></mt-select>
        </mt-form-item>
        <!-- 22222222222222222222222222222222222222暂无下拉框数据 @change="relationContract"-->
        <mt-form-item
          :class="inputDisabled.agreementDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('关联合同/协议')"
        >
          <!-- <mt-select
            v-model="inputObj.contractCodeRelation"
            :disabled="inputDisabled.agreementDisabled"
            :showClearButton="true"
            :dataSource="selectObj.relationArr"
            :placeholder="$t('请选择公司')"
            :allowFiltering="true"
          ></mt-select> -->

          <filterSearch
            v-model="inputObj.contractIdRelation"
            :disabled="inputDisabled.agreementDisabled"
          ></filterSearch>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.dataArrDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同类型')"
          prop="contractTypeId"
        >
          <mt-select
            :data-source="selectObj.dataArr"
            :show-clear-button="true"
            :placeholder="$t('请选择合同类型')"
            :disabled="inputDisabled.dataArrDisabled"
            v-model="inputObj.contractTypeId"
            @change="changeContractType"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.moneyArrDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('币种')"
        >
          <mt-input
            v-model="inputObj.currencyName"
            :disabled="inputDisabled.moneyArrDisabled"
            :show-clear-button="true"
            type="text"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="amountMoney"
          :class="inputDisabled.notTotalDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同总金额（未税）')"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.untaxedTotalPrice"
            :disabled="inputDisabled.notTotalDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入合同总金额（未税）')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="amountMoney"
          :class="inputDisabled.totalDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同总金额（含税）')"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.taxedTotalPrice"
            :disabled="inputDisabled.totalDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入合同总金额（含税）')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.timeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同生效时间')"
          prop="effectiveBeginTime"
        >
          <mt-date-time-picker
            :format="formatVal"
            v-model="inputObj.effectiveBeginTime"
            :placeholder="$t('请选择日期和时间')"
            :disabled="inputDisabled.timeDisabled"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.timeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同终止时间')"
          prop="effectiveEndTime"
        >
          <mt-date-time-picker
            :format="formatVal"
            v-model="inputObj.effectiveEndTime"
            :placeholder="$t('请选择日期和时间')"
            :disabled="inputDisabled.timeDisabled"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contactsDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商联系人')"
        >
          <!-- <mt-input
            itemLabelStyle="left"
            v-model="inputObj.supplierContactName"
            :disabled="inputDisabled.contactsDisabled"
            :showClearButton="true"
            type="text"
            :placeholder="$t('请输入供应商联系人')"
          ></mt-input> -->
          <mt-select
            item-label-style="left"
            :data-source="selectObj.supplierContactList"
            :disabled="inputDisabled.contactsDisabled"
            :show-clear-button="true"
            :placeholder="$t('请输入供应商联系人')"
            v-model="inputObj.supplierContactName"
            @change="changeSupplierContact"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.telephoneDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商联系电话')"
          prop="supplierContactPhone"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierContactPhone"
            :disabled="inputDisabled.telephoneDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商联系电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.mailboxDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商邮箱')"
          prop="supplierContactMail"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierContactMail"
            :disabled="inputDisabled.mailboxDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.mailboxDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商备用邮箱')"
          prop="supplierSpareEmail"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierSpareEmail"
            :disabled="inputDisabled.mailboxDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商备用邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="inputObj.contractCategoryCode && contractCategoryItemCode !== '41'"
          :class="inputDisabled.conditionDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('付款条件')"
        >
          <mt-select
            :data-source="selectObj.condition"
            :disabled="inputDisabled.conditionDisabled"
            :show-clear-button="true"
            :placeholder="$t('请选择付款条件')"
            v-model="inputObj.paymentItemId"
            @change="changeCondition"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="inputObj.contractCategoryCode && contractCategoryItemCode !== '41'"
          prop="accountPeriod"
          :class="inputDisabled.accountPeriodDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('账期天数')"
        >
          <mt-inputNumber
            item-label-style="left"
            v-model="inputObj.accountPeriod"
            :disabled="inputDisabled.accountPeriodDisabled"
            :show-clear-button="true"
            :min="1"
            type="text"
            :placeholder="$t('请输入账期天数')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contractTemplateDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同模板')"
        >
          <mt-select
            :data-source="selectObj.contractTemplate"
            v-model="inputObj.contractTemplateId"
            @change="templateChangeHandle"
            :disabled="inputDisabled.contractTemplateDisabled"
            :show-clear-button="true"
            :placeholder="$t('请选择合同模板')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.establishmentDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('是否立项')"
        >
          <mt-select
            :data-source="selectObj.isEstablishment"
            :disabled="inputDisabled.establishmentDisabled"
            :show-clear-button="true"
            :placeholder="$t('请选择是否立项')"
            v-model="inputObj.established"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="isRelationCompany"
          :class="inputDisabled.isRelationCompanyDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('是否关联公司')"
        >
          <mt-select
            :data-source="selectObj.relationCompanyList"
            :disabled="inputDisabled.isRelationCompanyDisabled"
            :show-clear-button="true"
            :placeholder="$t('是否关联公司')"
            v-model="inputObj.isRelationCompany"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.buyerDisabled ? 'label-style form-item' : 'form-item'"
          style="width: 450px"
          :label="$t('采购员')"
        >
          <mt-select
            :data-source="selectObj.buyer"
            :show-clear-button="true"
            :placeholder="$t('请选择采购员')"
            v-model="inputObj.purId"
            :disabled="inputDisabled.buyerDisabled"
            @change="changeBuyer"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.timeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('质保期')"
          prop="qaTimeLimitValue"
        >
          <mt-date-time-picker
            :format="formatVal"
            v-model="inputObj.qaTimeLimitValue"
            :min="new Date(new Date().getTime() + 86400000)"
            :placeholder="$t('请选择质保期')"
            :disabled="inputDisabled.timeDisabled"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          v-if="businessTypeCode === 'BTTCL006'"
          class="label-style form-item"
          :label="$t('需求类型')"
        >
          <mt-input v-model="inputObj.demandTypeName" disabled type="text"></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="businessTypeCode === 'BTTCL006'"
          class="label-style form-item"
          :label="$t('运输方式')"
        >
          <mt-input v-model="inputObj.logisticsMethodName" disabled type="text"></mt-input>
        </mt-form-item>
        <div class="demo-content">
          <mt-form-item
            :class="
              inputDisabled.taxationDescribeDisabled ? 'label-style item-content' : 'item-content'
            "
            :label="$t('税费承担描述')"
          >
            <mt-input
              :rows="2"
              type="text"
              :placeholder="$t('税费承担描述内容')"
              :disabled="inputDisabled.taxationDescribeDisabled"
              :show-clear-button="true"
              :multiline="!inputDisabled.taxationDescribeDisabled"
              v-model="inputObj.taxesDesc"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            :class="inputDisabled.remarksDisabled ? 'label-style item-content' : 'item-content'"
            :label="$t('备注')"
          >
            <mt-input
              :rows="2"
              type="text"
              :placeholder="$t('备注内容')"
              v-model="inputObj.memo"
              :disabled="inputDisabled.remarksDisabled"
              :show-clear-button="true"
              :multiline="!inputDisabled.remarksDisabled"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
        </div>
      </div>
      <div class="row" v-if="!childObj.statusBtn">
        <div class="col-xs-12 col-sm-12 col-lg-12 col-md-12">
          <mt-form-item
            class="form-item"
            label="$t('反馈备注')"
            :class="inputDisabled.responseMemoDisabled ? 'label-style form-item' : 'form-item'"
          >
            <mt-input
              :rows="2"
              type="text"
              :placeholder="$t('备注内容')"
              v-model="inputObj.supplierReply"
              :multiline="!inputDisabled.responseMemoDisabled"
              :disabled="inputDisabled.responseMemoDisabled"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
        </div>
      </div>
    </mt-form>
  </div>
</template>
<script>
import filterSearch from '../components/filterSearch.vue'
import Bus from '../../../utils/bus'
export default {
  components: {
    filterSearch
  },
  props: {
    childObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    businessTypeCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputObj: {},
      inputDisabled: this.childObj.inputDisabled,
      // 表单
      rules: {
        contractTypeId: [{ required: true, message: this.$t('请选择合同类型'), trigger: 'blur' }],
        contractCategoryCode: [{ required: true, message: this.$t('请选择类别'), trigger: 'blur' }],
        supplierContactPhone: [{ validator: this.validatePhoneNum, trigger: 'blur' }],
        supplierContactMail: [{ required: true, validator: this.validateEmail, trigger: 'blur' }],
        supplierSpareEmail: [{ validator: this.validateEmail, trigger: 'blur' }],
        effectiveBeginTime: [{ required: true, validator: this.beginTimeRules, trigger: 'blur' }],
        effectiveEndTime: [{ required: true, validator: this.endTimeRules, trigger: 'blur' }],
        qaTimeLimitValue: [{ required: true, message: this.$t('请选择质保期'), trigger: 'blur' }],
        contractName: [{ required: true, message: this.$t('请输入合同名称'), trigger: 'blur' }],
        accountPeriod: [{ required: true, message: this.$t('请输入账期天数'), trigger: 'blur' }],
        isRelationCompany: [
          { required: true, message: this.$t('请选择是否关联公司'), trigger: 'blur' }
        ]
      },
      // 弹窗
      dialogObj: {
        title: '',
        message: '',
        formObj: ''
      },
      formInput: true,
      // select数据
      selectObj: {
        dataArr: [],
        moneyArr: [],
        condition: [],
        buyer: [],
        contractTemplate: [],
        isEstablishment: [],
        buyerGroup: [],
        relationArr: [],
        supplierContactList: [],
        relationCompanyList: [
          { text: this.$t('否'), value: 0 },
          { text: this.$t('是'), value: 1 }
        ]
      },
      fields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      fieldsStatus: false,
      formatVal: 'yyyy-MM-dd HH:mm',
      amountMoney: true,
      recursionData: [],
      recursionObj: [],
      employeeId: '',
      // 采购组织
      purchaseFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      purchaseStatus: false,
      purchaseData: [],
      // 公司
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      companyStatus: false,
      companyData: [],
      purOrgIds: '',
      purCompanyIds: '',
      itemKing: false,
      companyKing: false,
      moneyKing: false,
      contractCategoryItemCode: null
    }
  },
  async created() {
    this.inputObj = JSON.parse(JSON.stringify(this.childObj.obj))
    this.purOrgIds = JSON.parse(JSON.stringify(this.inputObj.purOrgId))
    this.purCompanyIds = JSON.parse(JSON.stringify(this.inputObj.purCompanyId))
    if (this.childObj.createStatus) {
      this.getQueryUserData()
      const fullYearLastDay = this.$moment().year(this.$moment().year()).endOf('year').valueOf()
      this.inputObj.effectiveEndTime = this.$moment(fullYearLastDay).format('YYYY-MM-DD HH:mm:ss')
    }
    this.getBuyerGroup()
    this.getSupplierContact()
    this.getBuyer()
    this.termPayment()
    this.contractType()
    this.contractTemplate()
    this.isEstablishmentData()
    this.contractTypeTree()
    this.inputObj.purCompanyId = []
    this.inputObj.purCompanyId.push(this.purCompanyIds)
  },
  methods: {
    // 校验
    beginTimeRules(rule, value, callback) {
      const effective = new Date(this.inputObj.effectiveEndTime).getTime()
      if (!value) {
        callback(new Error(this.$t('请选择生效时间')))
      } else if (effective < new Date(value).getTime() && this.inputObj.effectiveEndTime) {
        callback(new Error(this.$t('生效时间不能大于终止时间')))
      } else {
        callback()
      }
    },
    endTimeRules(rule, value, callback) {
      const effective = new Date(this.inputObj.effectiveBeginTime).getTime()
      if (!value) {
        callback(new Error(this.$t('请选择终止时间')))
      } else if (effective > new Date(value).getTime() && this.inputObj.effectiveBeginTime) {
        callback(new Error(this.$t('终止时间不能小于生效时间')))
      } else {
        callback()
      }
    },
    validatePhoneNum(rule, value, callback) {
      const phoneRegex = /^1[3-9]\d{9}$/
      let isUnValidMobile = this.inputObj.countryCode !== 'CN'
      if (value && !phoneRegex.test(value) && !isUnValidMobile) {
        callback(new Error(this.$t('手机号格式不正确')))
      } else {
        callback()
      }
    },
    validateEmail(rule, value, callback) {
      const emailRegex =
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      if (value && !emailRegex.test(value)) {
        callback(new Error(this.$t('邮箱地址格式不正确')))
      } else {
        callback()
      }
    },
    // 返回
    backOff() {
      this.$router.go(-1)
    },
    // 供方
    // 接受
    accept() {
      this.dialogObj.title = this.$t('接受')
      this.dialogObj.message = this.$t('确定要接受吗？')
      this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
        .utc()
        .format()
      this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime).utc().format()
      this.dialogObj.formObj = this.inputObj
      this.$emit('dialogChild', this.dialogObj)
      this.$parent.accept()
    },
    // 拒绝
    refuse() {
      this.dialogObj.title = this.$t('拒绝')
      this.dialogObj.message = this.$t('确定要拒绝吗？')
      this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
        .utc()
        .format()
      this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime).utc().format()
      this.dialogObj.formObj = this.inputObj
      this.$emit('dialogChild', this.dialogObj)
      this.$parent.refuse()
    },
    // 保存草稿
    draft() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('保存草稿')
          this.dialogObj.message = this.$t('确定要保存草稿吗？')
          // this.inputObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          // delete this.inputObj.contractCategoryCodeStr
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = JSON.parse(JSON.stringify(this.inputObj))

          this.dialogObj.formObj.qaTimeLimitValue = new Date(
            this.inputObj.qaTimeLimitValue
          ).getTime()
          this.dialogObj.formObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.draft()
        } else {
          return false
        }
      })
    },
    // 提交
    submitBtn() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('提交')
          this.dialogObj.message = this.$t('确定要提交吗？')
          // this.inputObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          // delete this.inputObj.contractCategoryCodeStr
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = JSON.parse(JSON.stringify(this.inputObj))

          this.dialogObj.formObj.qaTimeLimitValue = new Date(
            this.inputObj.qaTimeLimitValue
          ).getTime()
          this.dialogObj.formObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.submitBtn()
        } else {
          return false
        }
      })
    },
    // 发布
    publish() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('发布')
          this.dialogObj.message = this.$t('确定要发布吗？')
          // this.inputObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          // delete this.inputObj.contractCategoryCodeStr
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = JSON.parse(JSON.stringify(this.inputObj))

          this.dialogObj.formObj.qaTimeLimitValue = new Date(
            this.inputObj.qaTimeLimitValue
          ).getTime()
          this.dialogObj.formObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.publish()
        } else {
          return false
        }
      })
    },
    // 收起
    away() {
      if (this.formInput) {
        this.$refs.ruleForm.$el.style.height = '0px'
        this.$refs.ruleForm.$el.style.transition = 'height 0.2s'
        this.$refs.ruleForm.$el.style.overflow = 'hidden'
        this.$refs.ruleForm.$el.style.marginTop = '0px'
      } else {
        this.$refs.ruleForm.$el.style.height = '363px'
        this.$refs.ruleForm.$el.style.transition = 'height 0.2s'
        this.$refs.ruleForm.$el.style.marginTop = '45px'
      }
      this.formInput = !this.formInput
    },
    // 获取select数据接口
    // 采购组
    getBuyerGroup() {
      this.$api.contract.querySelectData
        .getBuyerGroupSelect({ groupTypeCode: 'BG001CG' })
        .then((res) => {
          if (res.code === 200) {
            let obj = ''
            res.data.forEach((item) => {
              obj = {
                text: '',
                value: '',
                code: ''
              }
              obj.text = item.groupName
              obj.value = item.id
              obj.code = item.groupCode
              this.selectObj.buyerGroup.push(obj)
              if (obj.code === 'F01') {
                this.inputObj.purchaseGroupId = obj.value
              }
            })
            // 如果创建合同时没有默认选中第一个   当未选中时
            if (this.childObj.createStatus) {
              this.inputObj.purchaseGroupId = this.selectObj.buyerGroup.find(
                (obj) => obj.code === 'F01'
              ).value
              if (!this.inputObj.purchaseGroupId) {
                this.inputObj.purchaseGroupId = this.selectObj.buyerGroup[0].value
              }
            }
          }
        })
    },
    // 采购员
    getBuyer() {
      const params = {
        fuzzyName: this.childObj.obj.purName
      }
      this.$api.contract.querySelectData.getBuyer(params).then((res) => {
        if (res.code === 200) {
          let obj = ''
          res.data.forEach((item) => {
            if (item?.employeeId == this.employeeId) {
              // this.inputObj.purId = this.employeeId
              this.inputObj.purId = item.uid
            }
            obj = {
              text: '',
              value: ''
            }
            obj.text =
              // item.companyOrgName +
              // '-' +
              // item.departmentOrgName +
              // '-' +
              item.employeeCode + '-' + item.employeeName
            obj.value = item.uid
            this.selectObj.buyer.push(obj)
          })
        }
      })
    },
    // 采购员
    getSupplierContact() {
      const params = {
        supplierId: this.childObj.obj.supplierId
      }
      this.$api.contract.querySelectData
        .findBusinessPartnerContactBySupplierIdApi(params)
        .then((res) => {
          if (res.code === 200) {
            let obj = ''
            res.data.forEach((item) => {
              obj = {
                text: `${item.name}-${item.duty}`,
                value: item.name,
                mobile: item.mobile,
                email: item.email
              }
              this.selectObj.supplierContactList.push(obj)
            })
          }
        })
    },
    // 货币
    getMoney() {
      this.$api.contract.querySelectData.getCurrency().then((res) => {
        if (res.code === 200) {
          this.selectObj.moneyArr = res.data.map((item) => {
            return {
              text: item.currrencyCode + '-' + item.currencyName,
              value: item.id
            }
          })
        }
      })
    },
    // 获取付款条件
    termPayment() {
      this.$api.contract.querySelectData.conditionType({ dictCode: 'PaymentType' }).then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.name
            obj.value = item.id
            this.selectObj.condition.push(obj)
          })
          // 如果创建合同时没有默认选中第一个   当未选中时
          if (this.childObj.createStatus) {
            this.inputObj.paymentItemId = this.selectObj.condition[0].value
          }
        }
      })
    },
    // 合同类型
    contractType() {
      this.$api.contract.querySelectData.conditionType({ dictCode: 'contractType' }).then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.name
            obj.value = item.id
            this.selectObj.dataArr.push(obj)
          })
          // 如果创建合同时没有默认选中第一个   当未选中时
          if (this.childObj.createStatus) {
            this.inputObj.contractTypeId = this.selectObj.dataArr[0].value
          }
        }
      })
    },
    // 合同模板
    contractTemplate() {
      this.$api.contract.querySelectData.contractTemplate().then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.label
            obj.value = item.value
            this.selectObj.contractTemplate.push(obj)
          })
          // 如果创建合同时没有默认选中第一个   当未选中时
          if (this.childObj.createStatus) {
            this.inputObj.contractTemplateId = this.selectObj.contractTemplate[0].value
          }
        }
      })
    },
    // 是否立项
    isEstablishmentData() {
      this.$api.contract.querySelectData.isEstablishment().then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.label
            obj.value = item.value
            this.selectObj.isEstablishment.push(obj)
          })
        }
      })
    },
    // 采购组组织请求数据
    queryPurchaseFields() {
      // let obj = {
      //   orgLevelCode: "ORG02",
      //   orgType: "ORG001PRO",
      //   tenantId: ''
      // }
      // if(this.childObj.detailObj) {
      //   obj.tenantId = this.childObj.detailObj.tenantId
      // }else{
      //   obj.tenantId = this.$store.state.user.tenantId
      // }
      const obj = {
        orgId: ''
      }
      if (this.childObj.detailObj) {
        obj.orgId = this.childObj.detailObj.purCompanyId
      } else {
        obj.orgId = this.childObj.obj.purCompanyId
      }
      this.$api.contract.querySelectData.queryPurchase(obj).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            item.value = item.id
            item.text = item.organizationName
          })
          this.purchaseFields.dataSource = res.data
          this.purchaseStatus = true
        }
      })
    },
    // 公司请求数据
    queryCompanyFields() {
      const obj = {
        orgType: 'ORG001PRO',
        tenantId: ''
      }
      if (this.childObj.detailObj) {
        obj.tenantId = this.childObj.detailObj.tenantId
      } else {
        obj.tenantId = this.$store.state.user.tenantId
      }

      this.$api.contract.querySelectData.queryCompany(obj).then((res) => {
        if (res.code === 200) {
          this.companyFields.dataSource = res.data
          this.companyStatus = true
        }
      })
    },
    // 公司
    recursionCompany(data) {
      data.forEach((item) => {
        this.companyData.push(item)
        if (item.children) {
          this.recursionCompany(item.children)
        }
      })
    },
    // 合同类别
    contractTypeTree() {
      this.$api.contract.querySelectData
        .conditionType({ dictCode: 'contractCategory' })
        .then((res) => {
          if (res.code === 200) {
            this.fields.dataSource = res.data
            this.inputObj.contractCategoryCodeStr = ''
            this.inputObj.contractCategoryName = ''
            this.recursionTree(this.fields.dataSource)
            this.fieldsStatus = true
          }
        })
    },
    // 合同类别选中时
    selectTree(data) {
      this.recursionObj = []
      this.inputObj.contractCategoryCodeStr = ''
      this.inputObj.contractCategoryName = ''
      this.recursionObj.push(data.itemData)
      this.recursionId(data.itemData.parentID)
      this.recursionObj = this.recursionObj.reverse()
      this.recursionData.forEach((i) => {
        if (data.itemData.id === i.id) {
          this.contractCategoryItemCode = i.itemCode
          if (i.itemCode === '42') {
            this.inputObj.contractHroStatus = 1
          } else {
            this.inputObj.contractHroStatus = 0
          }
        }
      })
      this.recursionObj.forEach((item, index) => {
        if (index !== this.recursionObj.length - 1) {
          this.inputObj.contractCategoryCodeStr += item.id + '-'
          this.inputObj.contractCategoryName += item.name + '-'
        } else {
          this.inputObj.contractCategoryCodeStr += item.id
          this.inputObj.contractCategoryName += item.text
        }
      })
    },
    // 递归平铺数据
    recursionTree(data) {
      data.forEach((item) => {
        if (
          item.itemCode === '41' &&
          (!this.inputObj.contractCategoryCode || !this.inputObj.contractCategoryCode.length)
        ) {
          this.$set(this.inputObj, 'contractCategoryCode', [item.id])
          this.$set(this.inputObj, 'contractHroStatus', 0)
          if (item.id) {
            this.inputObj.contractCategoryCodeStr += item.id
            this.inputObj.contractCategoryName += item.name
          }
          this.contractCategoryItemCode = '41'
        }
        this.recursionData.push(item)
        if (item.children) {
          this.recursionTree(item.children)
        }
      })
    },
    // 获取选中合同类别的父级数据  递归
    recursionId(id) {
      this.recursionData.forEach((item) => {
        if (item.id === id) {
          this.recursionObj.push(item)
          if (item.parentId !== '0') {
            this.recursionId(item.parentId)
          }
        }
      })
    },
    // 合同类型名称
    changeContractType(data) {
      this.inputObj.contractTypeName = data.itemData.text
      if (this.inputObj.contractTypeName === this.$t('框架协议')) {
        this.amountMoney = false
      } else {
        this.amountMoney = true
      }
    },
    // 采购组
    changeBuyerGroup(data) {
      this.inputObj.purchaseGroupName = data.itemData.text
      this.inputObj.purchaseGroupCode = data.itemData.code
    },
    // 采购员
    changeBuyer(data) {
      this.inputObj.purName = data.itemData.text
    },
    // 付款条件
    changeCondition(data) {
      this.inputObj.paymentItemName = data.itemData.text
    },
    // 供应商联系人
    changeSupplierContact(data) {
      this.inputObj.supplierContactPhone = data.itemData.mobile
      this.inputObj.supplierContactMail = data.itemData.email
    },
    // 关联合同名称
    contractNameRelations(data) {
      this.inputObj.contractNameRelation = data
    },
    // 获取用户信息
    getQueryUserData() {
      this.employeeId = this.$store.state?.user?.employeeId
      // this.inputObj.purId = this.$store.state?.user?.employeeId;
    },
    templateChangeHandle(e) {
      const itemData = e.itemData
      this.inputObj.contractTemplateName = itemData.text
    }
  },
  mounted() {
    // 关联合同名称
    Bus.$on('contractNameRelation', this.contractNameRelations)
    if (this.inputObj.contractCategoryCode) {
      this.inputObj.contractCategoryCodeStr = this.inputObj.contractCategoryCode
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  width: 100%;
  padding: 20px 0 20px 0;
  background: #fff;
  .headr-box {
    margin-bottom: 25px;
    padding: 0 20px 0 20px;
    display: flex;
    justify-content: space-between;
    .span-status {
      display: inline-block;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      padding: 2px;
      border-radius: 2px;
      margin-right: 20px;
    }
    .cancel {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
    .green {
      color: #8acc40;
      background: rgba(138, 204, 64, 0.1);
    }
    .red {
      color: #ed5633;
      background: rgba(237, 86, 51, 0.1);
    }
    // .ash {
    //   color: #9a9a9a;
    //   background: rgba(154, 154, 154, 0.1);
    // }
    .blue {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    .headr-text {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }
    .headr-time {
      margin-left: 13px;
    }
    .headr-btn {
      display: flex;
      span {
        display: inline-block;
        margin-left: 40px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      span:nth-of-type(1) {
        margin-left: 0;
      }
      span:nth-last-of-type(1) {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(99, 134, 193, 1);
      }
    }
  }
  .headr-border {
    border-top: 1px solid#E8E8E8;
    height: 20px;
  }
  .form-input {
    // height: 389px;
    padding: 0 20px 0 30px;
    margin-top: 45px;
  }
}
.demo-block {
  display: flex;
  // margin-bottom: 10px;
  flex-wrap: wrap;
  .demo-input {
    margin-bottom: 10px;
    margin-left: 10px;
  }
  .title-text {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
  .form-item {
    width: 225px;
    margin-bottom: 15px;
    margin-right: 10px;
  }
}
.demo-content {
  width: 100%;
  display: flex;
  .item-content {
    width: calc(50% - 10px);
  }
  .item-content:nth-of-type(2) {
    margin-left: 20px;
  }
}
.input-item {
  padding-right: 20px;
}
.col-xs-12,
.col-sm-12,
.col-lg-6,
.col-md-6 {
  margin-bottom: 15px;
}
/deep/ input.e-disabled {
  min-height: 30px !important;
  height: 30px;
  background: #fff !important;
}
/deep/ span.e-disabled {
  height: 31px;
  background: #fff !important;
}
/deep/ span.e-multi-line-input {
  height: auto !important;
  background: #fff !important;
}
/deep/ textarea.e-disabled {
  background: #fff !important;
}
/deep/ .label {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
}

.label-style /deep/ .label {
  color: rgba(154, 154, 154, 1);
}
.label-style /deep/#baseTreeSelect {
  color: rgba(154, 154, 154, 1);
}
.accept {
  color: #8acc40 !important;
}
.reject {
  color: #ed5633 !important;
}
</style>
