<template>
  <div>
    <div class="buyerContract-wrap">
      <mt-template-page
        ref="tempaltePageRef"
        :templateConfig="attachMentPageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        @actionComplete="actionComplete"
      >
        <mt-template-page
          slot="slot-1"
          ref="templateRef1"
          :currentTab="currentTab"
          :template-config="FilePageConfig"
          @handleSelectTab="handleSelectTabSlot"
          @handleClickToolBar="handleClickToolBarSlot"
          @handleClickCellTool="handleClickCellToolSlot"
          @handleClickCellTitle="handleClickCellTitleSlot"
        ></mt-template-page>
      </mt-template-page>
      <!-- 弹窗 -->
      <mt-dialog
        ref="dialog"
        css-class="create-proj-dialog"
        :buttons="buttons"
        :style="{ width: '940px' }"
        :header="title"
      >
        <mt-template-page ref="table" :template-config="dialogPageConfig" />
      </mt-dialog>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from '@mtech/vue-property-decorator'
import axios from 'axios'
import {
  attachMentPageConfig,
  FilePageConfig,
  addAttachMentPageConfig
} from '../config/attachMent.js'
import { i18n } from '@/main'
const Bus: any = require('@/utils/bus.js').default
@Component({})
export default class attachMent extends Vue {
  @Prop()
  tag!: number // 1 采方 2 供方  3查看

  @Prop()
  data!: Array<any> // 标的明细数据

  @Prop()
  contractTempId!: string // 合同临时id

  @Prop()
  businessTypeCode!: string // 业务类型

  @Prop()
  logisticsMethodCode!: string // 物流方式

  @Prop({
    default: () => {
      return { contractId: '', providerType: 0 }
    }
  })
  contract!: any // 上传、下载删除的 合同信息

  buttons = [
    { click: this.confirm, buttonModel: { isPrimary: i18n.t('true'), content: i18n.t('确定') } }
  ]
  title = i18n.t('阶梯价格')
  dialogPageConfig = [
    {
      toolbar: [[], []],
      useToolTemplate: false,
      useBaseConfig: false,
      grid: {
        allowPaging: false,
        columnData: [
          {
            field: 'stageFrom',
            headerText: i18n.t('阶梯起')
          },
          {
            field: 'stageTo',
            headerText: i18n.t('阶梯止')
          },
          {
            field: 'priceUnit',
            headerText: i18n.t('价格单位')
          },
          {
            field: 'untaxedUnitPrice',
            headerText: i18n.t('未税单价')
          },
          {
            field: 'taxedUnitPrice',
            headerText: i18n.t('含税单价')
          }
        ],
        dataSource: []
      }
    }
  ]

  currentTab = 0
  supplierReplay = []

  get type() {
    return this.$route.query?.type
  }
  get attachMentPageConfig() {
    const attachMents =
      this.logisticsMethodCode === '3' && this.tag === 1
        ? addAttachMentPageConfig(this.tag, this.logisticsMethodCode, this.type)
        : attachMentPageConfig(this.tag, this.logisticsMethodCode)
    attachMents[0].grid.dataSource = this.data
    return attachMents
  }

  get FilePageConfig() {
    let contractId = this.contractId
    if (!contractId) {
      contractId = this.contractTempId
    }
    return FilePageConfig(contractId, this.tag, this.currentTab)
  }

  get contractId() {
    // 合同id
    return this.contract.contractId || this.$route.query?.contractId
  }

  get providerType() {
    //  上传类型
    return this.contract.providerType
  }

  handleClickToolBar(e: any): void {
    // const { grid, toolbar } = e
    if (e.toolbar.id === 'upload') {
      this.uploadFile()
    }
    const tempaltePageRef: any = this.$refs.tempaltePageRef
    if (!tempaltePageRef?.getCurrentUsefulRef) {
      return
    }
    if (e.toolbar.id === 'add') {
      // const item = {
      //   addId: 'add' + Math.random().toString(36).substr(3, 8)
      // }
      tempaltePageRef.getCurrentUsefulRef().gridRef.ejsRef.addRecord()
    } else if (e.toolbar.id === 'delete') {
      tempaltePageRef.getCurrentUsefulRef().gridRef.ejsRef.deleteRecord()
    }
  }
  actionComplete() {}
  getTableData() {
    const tempaltePageRef: any = this.$refs.tempaltePageRef
    if (!tempaltePageRef?.getCurrentUsefulRef) {
      return
    }
    const data = tempaltePageRef.getCurrentUsefulRef().gridRef.ejsRef.getCurrentViewRecords()
    return data
  }
  handleClickCellTool() {
    // const { tool, data } = e as any
    // if (tool.id == 'submit') {
    // }
  }

  handleClickCellTitle(e: any) {
    // 标题点击}
    const { field, data } = e

    if (field === 'stageList') {
      const dialog: any = this.$refs.dialog
      dialog.ejsRef.show()
      this.dialogPageConfig[0].grid.dataSource = data.stageList
    }
  }

  handleSelectTabSlot(e: any) {
    this.currentTab = e
  }

  handleClickToolBarSlot(e: any) {
    const { grid, toolbar } = e as any
    const { id } = toolbar as any
    const sections = grid.getSelectedRecords()

    if (id === 'upload') {
      if (!this.contractId && !this.contractTempId) {
        this.$toast({
          content: i18n.t('数据异常'),
          type: 'warning'
        })
        return
      }
      this.uploadFile()
    }
    if (sections.length === 0 && id != 'upload') {
      this.$toast({
        content: i18n.t('请选择一条数据'),
        type: 'warning'
      })
      return
    }
    if (id === 'del') {
      const sections: any = grid.getSelectedRecords()
      if (sections.length <= 0) {
        this.$toast({
          content: i18n.t('请选择一行数据'),
          type: 'warning'
        })
        return
      }
      this.deleteFile(sections)
    }
    if (id === 'download') {
      this.download(sections)
    }
  }

  handleClickCellToolSlot(e: any) {
    const { tool, data } = e as any
    if (tool.id == 'del') {
      this.deleteFile([data])
    }
    if (tool.id == 'download') {
      this.download([data])
    }
  }

  handleClickCellTitleSlot(e: any) {
    if (e.field == 'fileName') {
      let params = {
        id: e.data.sysFileId || e.data.id,
        useType: 1
      }
      this.$api.messageCenter.getFilePreview(params).then((res) => {
        window.open(
          // `${res.data}/onlinePreview?url=${e.data.remoteUrl || e.data.url}`
          `${res.data}`
        )
      })
    }
  }
  uploadFile() {
    console.log(this.tag)
    this.$dialog({
      modal: () => import('./uploadDialog.vue'),
      data: {
        title: i18n.t('上传附件')
      },
      success: (data) => {
        this.requestFile(data)
      }
    })
  }

  deleteFile(sections: any) {
    sections.map((item) => {
      this.$api.contract.uploadFile.delete(item.id).then((res) => {
        if (res.code == 200) {
          this.$toast({
            content: i18n.t('删除成功'),
            type: 'success'
          })
          const ref = this.$refs.templateRef1 as any
          ref.refreshCurrentGridData()
        }
      })
    })
  }

  download(sections) {
    sections.map((item) => {
      axios
        .get(`/api/contract/attachment/download/${item.id}`, { responseType: 'blob' })
        .then((res) => {
          const filename = item.fileName
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.download = filename
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href) // 释放URL 对象
          document.body.removeChild(link)
        })
    })
  }

  requestFile(files) {
    const formDatas = new FormData()
    let contractId = this.contractId
    let isContractTempId = '0'
    if (!contractId) {
      contractId = this.contractTempId
      isContractTempId = '1'
    }
    formDatas.append('contractId', contractId)
    formDatas.append('isContractTempId', isContractTempId)
    formDatas.append('provider', this.providerType)
    formDatas.append('file', files[0])
    this.$store.commit('startLoading')
    this.$api.contract.uploadFile.upload(formDatas).then((res) => {
      this.$store.commit('endLoading')
      if (res.code == 200) {
        this.$toast({
          content: i18n.t('上传成功'),
          type: 'success'
        })
        const ref = this.$refs.templateRef1 as any
        ref.refreshCurrentGridData()
      }
    })
  }

  getSupplierReplay(e) {
    const { contractItemMemo } = e // 如果传过来的值等于空 那么直接删掉当前行号
    const keyArr = []
    this.supplierReplay.map((item) => {
      keyArr.push(item.contractItemId)
    })
    if (!contractItemMemo) {
      if (keyArr.indexOf(e.contractItemId) != -1) {
        this.supplierReplay.splice(keyArr.indexOf(e.contractItemId), 1)
      }
    }
    if (contractItemMemo) {
      if (keyArr.indexOf(e.contractItemId) != -1) {
        this.supplierReplay[keyArr.indexOf(e.contractItemId)].contractItemMemo = e.contractItemMemo
      } else {
        this.supplierReplay.push(e)
      }
    }
    const parent: any = this.$parent
    parent.getSupplierReplay(this.supplierReplay)
  }

  confirm() {
    const ejsRef: any = this.$refs.dialog
    ejsRef.ejsRef.hide()
  }

  created() {
    Bus.$on('SupplierReplay', this.getSupplierReplay)
  }
}
</script>
<style lang="scss" scoped>
.buyerContract-wrap {
  height: 500px;
}
</style>
