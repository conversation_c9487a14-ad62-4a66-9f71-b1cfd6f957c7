// 头部表单组件
<template>
  <div class="form-box">
    <div class="headr-box">
      <div>
        <div v-if="!childObj.createStatus">
          <span v-if="inputObj.status === 0" class="span-status blue">{{ $t('草稿') }}</span>
          <span v-if="inputObj.status === 1" class="span-status cancel">{{ $t('待发布') }}</span>
          <span v-if="inputObj.status === 2" class="span-status cancel">{{ $t('待反馈') }}</span>
          <span v-if="inputObj.status === 3" class="span-status green">{{ $t('反馈正常') }}</span>
          <span v-if="inputObj.status === 4" class="span-status red">{{ $t('反馈异常') }}</span>
          <span v-if="inputObj.status === 5" class="span-status green">{{ $t('已签订') }}</span>
          <span v-if="inputObj.status === 6" class="span-status green">{{ $t('已归档') }}</span>
        </div>
      </div>
      <div class="headr-btn">
        <span @click="backOff">{{ $t('返回') }}</span>
        <template>
          <span @click="draft">{{ $t('保存草稿') }}</span>
        </template>
        <template v-if="!childObj.seeStatus">
          <template v-if="childObj.headerObj.status === 1 || childObj.headerObj.status == 4">
            <span @click="release">{{ $t('发布') }}</span>
            <span @click="submitBtn('save')">{{ $t('保存') }}</span>
            <span @click="revoke">{{ $t('撤销') }}</span>
          </template>
          <template v-if="childObj.headerObj.status === 0">
            <span @click="release">{{ $t('发布') }}</span>
            <span @click="revoke">{{ $t('撤销') }}</span>
          </template>
        </template>
        <span @click="away"
          >{{ $t('收起')
          }}<span
            :style="{
              transform: formInput ? 'rotate(180deg)' : 'rotate(0deg)',
              marginTop: formInput ? '-5px' : '2px'
            }"
            ><i class="mt-icons mt-icon-MT_DownArrow"></i></span
        ></span>
      </div>
    </div>
    <div class="headr-border" v-if="!formInput"></div>
    <mt-form ref="ruleForm" :model="inputObj" :rules="rules" class="form-input">
      <div class="demo-block">
        <mt-form-item class="label-style form-item" :label="$t('合同编号')">
          <mt-input
            item-label-style="left"
            v-model="inputObj.contractCode"
            disabled
            :show-clear-button="true"
            type="text"
            :placeholder="$t('系统自动生成')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contractNameDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同名称')"
          style="color: #f0f"
          prop="contractName"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.contractName"
            :disabled="inputDisabled.contractNameDisabled"
            :show-clear-button="true"
            type="text"
            maxlength="64"
            :placeholder="$t('请输入名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('供应商名称')" prop="supplierCode">
          <RemoteAutocomplete
            v-model="inputObj.supplierCode"
            url="/masterDataManagement/tenant/supplier/queryInfoAndContact"
            :placeholder="$t('请选择')"
            :fields="{
              text: 'supplierName',
              value: 'supplierCode'
            }"
            :search-fields="['supplierCode', 'supplierName']"
            @change="supplierChange"
          ></RemoteAutocomplete>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('合同类别')" prop="contractCategoryCode">
          <mt-DropDownTree
            v-if="fieldsStatus"
            :placeholder="$t('请选择合同类别')"
            :popup-height="300"
            :fields="contractCategoryFields"
            id="baseTreeSelect"
            v-model="inputObj.contractCategoryCode"
            :readonly="inputDisabled.contractCategoryDisabled"
            @select="selectTree"
            :allow-filtering="true"
          ></mt-DropDownTree>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('公司')" prop="purCompanyId">
          <mt-select
            ref="companyRef"
            v-model="inputObj.purCompanyId"
            :data-source="purCompanyOptions"
            :allow-filtering="true"
            :placeholder="$t('请选择公司')"
            @change="purCompanyChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('采购组织')" prop="purOrgId">
          <mt-select
            ref="purOrgRef"
            v-model="inputObj.purOrgId"
            :popup-height="400"
            :data-source="buyerOrgOptions"
            :allow-filtering="true"
            :placeholder="$t('请选择采购组织')"
            @change="purOrgChange"
          ></mt-select>
        </mt-form-item>

        <mt-form-item
          :class="inputDisabled.buyerGroupDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('采购组')"
          prop="purchaseGroupId"
        >
          <mt-select
            v-model="inputObj.purchaseGroupId"
            :disabled="inputDisabled.buyerGroupDisabled"
            :show-clear-button="true"
            :allow-filtering="true"
            :data-source="selectObj.buyerGroup"
            :placeholder="$t('请选择采购组')"
            @change="changeBuyerGroup"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.agreementDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('关联合同/协议')"
        >
          <filterSearch
            v-model="inputObj.contractIdRelation"
            :disabled="inputDisabled.agreementDisabled"
          ></filterSearch>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.dataArrDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同类型')"
          prop="contractTypeId"
        >
          <mt-select
            :data-source="selectObj.dataArr"
            :show-clear-button="true"
            :placeholder="$t('请选择合同类型')"
            :disabled="inputDisabled.dataArrDisabled"
            v-model="inputObj.contractTypeId"
            @change="changeContractType"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('币种')">
          <mt-select
            :data-source="selectObj.moneyArr"
            :show-clear-button="true"
            :placeholder="$t('请选择币种')"
            v-model="inputObj.currencyId"
            :allow-filtering="true"
            @change="currencyChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('合同总金额（未税）')">
          <mt-inputNumber
            item-label-style="left"
            v-model="inputObj.untaxedTotalPrice"
            :show-clear-button="true"
            :min="1"
            type="text"
            :placeholder="$t('请输入合同总金额（未税）')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('合同总金额（含税）')">
          <mt-inputNumber
            item-label-style="left"
            v-model="inputObj.taxedTotalPrice"
            :show-clear-button="true"
            :min="1"
            type="text"
            :placeholder="$t('请输入合同总金额（未税）')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.timeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('生效时间')"
          prop="effectiveBeginTime"
        >
          <mt-date-time-picker
            :format="formatVal"
            v-model="inputObj.effectiveBeginTime"
            :placeholder="$t('请选择日期和时间')"
            :disabled="inputDisabled.timeDisabled"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.timeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('终止时间')"
          prop="effectiveEndTime"
        >
          <mt-date-time-picker
            :format="formatVal"
            v-model="inputObj.effectiveEndTime"
            :placeholder="$t('请选择日期和时间')"
            :disabled="inputDisabled.timeDisabled"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contactsDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商联系人')"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierContactName"
            :disabled="inputDisabled.contactsDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商联系人')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.telephoneDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商联系电话')"
          prop="supplierContactPhone"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierContactPhone"
            :disabled="inputDisabled.telephoneDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商联系电话')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.mailboxDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商邮箱')"
          prop="supplierContactMail"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierContactMail"
            :disabled="inputDisabled.mailboxDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.mailboxDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('供应商备用邮箱')"
          prop="supplierSpareEmail"
        >
          <mt-input
            item-label-style="left"
            v-model="inputObj.supplierSpareEmail"
            :disabled="inputDisabled.mailboxDisabled"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入供应商备用邮箱')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item
          v-if="
            inputObj.contractCategoryCode &&
            inputObj.contractCategoryName !== '非采采购合同' &&
            inputObj.contractCategoryName !== $t('非采采购合同')
          "
          :class="inputDisabled.conditionDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('付款条件')"
        >
          <mt-select
            :data-source="selectObj.condition"
            :disabled="inputDisabled.conditionDisabled"
            :show-clear-button="true"
            :placeholder="$t('请选择付款条件')"
            v-model="inputObj.paymentItemId"
            @change="changeCondition"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="
            inputObj.contractCategoryCode &&
            inputObj.contractCategoryName !== '非采采购合同' &&
            inputObj.contractCategoryName !== $t('非采采购合同')
          "
          prop="accountPeriod"
          :class="inputDisabled.accountPeriodDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('账期天数')"
        >
          <mt-inputNumber
            item-label-style="left"
            v-model="inputObj.accountPeriod"
            :disabled="inputDisabled.accountPeriodDisabled"
            :show-clear-button="true"
            :min="1"
            type="text"
            :placeholder="$t('请输入账期天数')"
          ></mt-inputNumber>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.contractTemplateDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('合同模板')"
        >
          <mt-select
            :data-source="selectObj.contractTemplate"
            v-model="inputObj.contractTemplateId"
            :disabled="inputDisabled.contractTemplateDisabled"
            :show-clear-button="true"
            @change="templateChangeHandle"
            :placeholder="$t('请选择合同模板')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.establishmentDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('是否立项')"
        >
          <mt-select
            :data-source="selectObj.isEstablishment"
            :disabled="inputDisabled.establishmentDisabled"
            :show-clear-button="true"
            :placeholder="$t('请选择是否立项')"
            v-model="inputObj.established"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          prop="isRelationCompany"
          :class="inputDisabled.isRelationCompanyDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('是否关联公司')"
        >
          <mt-select
            :data-source="selectObj.relationCompanyList"
            :disabled="inputDisabled.isRelationCompanyDisabled"
            :show-clear-button="true"
            :placeholder="$t('是否关联公司')"
            v-model="inputObj.isRelationCompany"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.buyerDisabled ? 'label-style form-item' : 'form-item'"
          style="width: 450px"
          :label="$t('采购员')"
        >
          <mt-select
            :data-source="selectObj.buyer"
            :show-clear-button="true"
            :placeholder="$t('请选择采购员')"
            v-model="inputObj.purId"
            :disabled="inputDisabled.buyerDisabled"
            @change="changeBuyer"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          :class="inputDisabled.timeDisabled ? 'label-style form-item' : 'form-item'"
          :label="$t('质保期')"
          prop="qaTimeLimitValue"
        >
          <mt-date-time-picker
            :format="formatVal"
            v-model="inputObj.qaTimeLimitValue"
            :placeholder="$t('请选择质保期')"
            :min="new Date(new Date().getTime() + 86400000)"
            :disabled="inputDisabled.timeDisabled"
          ></mt-date-time-picker>
        </mt-form-item>
        <mt-form-item class="form-item" :label="$t('需求类型')">
          <mt-select
            ref="demandTypeRef"
            v-model="inputObj.demandType"
            :data-source="selectObj.demandTypeList"
            :allow-filtering="true"
            filter-type="Contains"
            :show-clear-button="false"
            :fields="{ text: 'dictName', value: 'dictCode' }"
            :placeholder="$t('请选择需求类型')"
            @change="changeDemandType"
          ></mt-select>
        </mt-form-item>
        <mt-form-item class="label-style form-item" :label="$t('运输方式')">
          <mt-input v-model="inputObj.logisticsMethodName" disabled type="text"></mt-input>
        </mt-form-item>
        <div class="demo-content">
          <mt-form-item
            :class="
              inputDisabled.taxationDescribeDisabled ? 'label-style item-content' : 'item-content'
            "
            :label="$t('税费承担描述')"
          >
            <mt-input
              :rows="1"
              type="text"
              :placeholder="$t('税费承担描述内容')"
              v-model="inputObj.taxesDesc"
              :disabled="inputDisabled.taxationDescribeDisabled"
              :multiline="!inputDisabled.taxationDescribeDisabled"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
          <mt-form-item
            :class="inputDisabled.remarksDisabled ? 'label-style item-content' : 'item-content'"
            :label="$t('备注')"
          >
            <mt-input
              :rows="1"
              type="text"
              :placeholder="$t('备注内容')"
              v-model="inputObj.memo"
              :disabled="inputDisabled.remarksDisabled"
              :show-clear-button="!inputDisabled.remarksDisabled"
              maxlength="128"
            ></mt-input>
          </mt-form-item>
        </div>
      </div>
    </mt-form>
  </div>
</template>
<script>
import filterSearch from '../components/filterSearch.vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import Bus from '../../../utils/bus'
export default {
  components: {
    filterSearch,
    RemoteAutocomplete
  },
  props: {
    childObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    businessTypeCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputObj: {},
      inputDisabled: {},
      // 表单
      rules: {
        contractTypeId: [
          {
            required: true,
            message: this.$t('请选择合同类型'),
            trigger: 'blur'
          }
        ],
        supplierCode: [
          {
            required: true,
            message: this.$t('请选择供应名称'),
            trigger: 'blur'
          }
        ],
        contractCategoryCode: [{ required: true, message: this.$t('请选择类别'), trigger: 'blur' }],
        purCompanyId: [{ required: true, message: this.$t('请选择公司'), trigger: 'blur' }],
        purOrgId: [{ required: true, message: this.$t('请选择采购组织'), trigger: 'blur' }],
        purchaseGroupId: [{ required: true, message: this.$t('请选择采购组'), trigger: 'blur' }],
        supplierContactPhone: [{ validator: this.validatePhoneNum, trigger: 'blur' }],
        supplierContactMail: [{ validator: this.validateEmail, trigger: 'blur' }],
        effectiveBeginTime: [{ required: true, validator: this.beginTimeRules, trigger: 'blur' }],
        effectiveEndTime: [{ required: true, validator: this.endTimeRules, trigger: 'blur' }],
        contractName: [
          {
            required: true,
            message: this.$t('请输入合同名称'),
            trigger: 'blur'
          }
        ],
        accountPeriod: [{ required: true, message: this.$t('请输入账期天数'), trigger: 'blur' }],
        isRelationCompany: [
          { required: true, message: this.$t('请选择是否关联公司'), trigger: 'blur' }
        ]
      },
      // 弹窗
      dialogObj: {
        title: '',
        message: '',
        formObj: ''
      },
      formInput: true,
      // select数据
      selectObj: {
        dataArr: [],
        moneyArr: [],
        condition: [],
        buyer: [],
        contractTemplate: [],
        isEstablishment: [],
        buyerGroup: [],
        relationArr: [],
        relationCompanyList: [
          { text: this.$t('否'), value: 0 },
          { text: this.$t('是'), value: 1 }
        ],
        demandTypeList: []
      },
      contractCategoryFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      formatVal: 'yyyy-MM-dd HH:mm:ss',
      amountMoney: true,
      recursionData: [],
      recursionObj: [],
      // 采购组织
      purchaseFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      purchaseStatus: false,
      purchaseData: [],
      // 公司
      companyFields: {
        dataSource: [],
        value: 'id',
        text: 'name',
        child: 'children'
      },
      companyStatus: false,
      companyData: [],
      purOrgIds: '',
      purCompanyIds: '',
      itemKing: false,
      companyKing: false,
      moneyKing: false,
      purCompanyOptions: [],
      buyerOrgOptions: [],
      fieldsStatus: false
    }
  },
  computed: {
    isAdd() {
      return this.$route.query?.type === 'add'
    }
  },
  watch: {
    childObj: {
      handler(newVal) {
        this.inputObj = newVal?.obj || {}
        this.inputDisabled = newVal?.inputDisabled || {}
      },
      deep: true
    }
  },
  async created() {
    await this.contractTypeTree()
    await this.initDictItems()
    await this.getCompany()
    await this.getBuyerGroup()
    await this.getContractType()
    await this.contractTemplate()
    await this.isEstablishmentData()
    await this.getBuyer()
    this.termPayment()
    this.getMoney()

    this.setDefaultData()
  },
  mounted() {
    // 关联合同名称
    Bus.$on('contractNameRelation', this.contractNameRelations)
    if (this.inputObj.contractCategoryCode) {
      this.inputObj.contractCategoryCodeStr = this.inputObj.contractCategoryCode
    }
  },
  methods: {
    // 初始化设置默认值
    setDefaultData() {
      let defaultData = {
        logisticsMethodCode: '3',
        logisticsMethodName: '铁运',
        established: 0, // 是否立项默认【否】
        isRelationCompany: 0 // 是否关联公司默认【否】
      }
      // 采购组默认【非采部】
      defaultData.purchaseGroupId = this.selectObj.buyerGroup.find(
        (obj) => obj.code === 'F01'
      ).value
      // 合同类型默认【合同】（第一项）
      defaultData.contractTypeId = this.selectObj.dataArr[0].value
      // 合同类型默认【自有合同模板】（第一项）
      defaultData.contractTemplateId = this.selectObj.contractTemplate[0].value
      // 采购员默认当前采购
      defaultData.purId = this.selectObj.buyer[0].value
      this.inputObj = { ...this.inputObj, ...defaultData }
    },
    // -------------- 顶部按钮操作：start-------------------
    // 返回
    backOff() {
      if (this.$route.query?.type === 'be-reconciled') {
        this.$router.push({
          path: '/purchase-execute/be-reconciled'
        })
      } else if (this.$route.query?.type === 'statement-detail') {
        let sourceQuery = JSON.parse(this.$route.query?.sourceQuery)
        this.$router.push({
          path: '/purchase-execute/statement-detail',
          query: {
            ...sourceQuery
          }
        })
      } else if (this.$route.query?.type === 'invoice-detail') {
        let sourceQuery = JSON.parse(this.$route.query?.sourceQuery)
        this.$router.push({
          path: '/purchase-execute/invoice-detail',
          query: {
            ...sourceQuery
          }
        })
      } else {
        this.$router.push({
          path: '/middlePlatform/contractBuyerList',
          query: {
            tab: 1
          }
        })
      }
    },
    // 接受
    accept() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('接受')
          this.dialogObj.message = this.$t('确定要接受吗？')
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = this.inputObj
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.accept()
        } else {
          return false
        }
      })
    },
    // 拒绝
    refuse() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('拒绝')
          this.dialogObj.message = this.$t('确定要拒绝吗？')
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = this.inputObj
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.refuse()
        } else {
          return false
        }
      })
    },
    // 保存草稿
    draft() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('保存草稿')
          this.dialogObj.message = this.$t('确定要保存草稿吗？')
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = JSON.parse(JSON.stringify(this.inputObj))

          this.dialogObj.formObj.qaTimeLimitValue = new Date(
            this.inputObj.qaTimeLimitValue
          ).getTime()
          this.dialogObj.formObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.save()
        } else {
          return false
        }
      })
    },
    // 撤销
    revoke() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('保存撤销')
          this.dialogObj.message = this.$t('确定要撤销吗？')
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.inputObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          // delete this.inputObj.contractCategoryCodeStr
          this.dialogObj.formObj = this.inputObj
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.revoke()
        } else {
          return false
        }
      })
    },
    // 发布
    release() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = this.$t('保存发布')
          this.dialogObj.message = this.$t('确定要发布吗？')
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          this.dialogObj.formObj = JSON.parse(JSON.stringify(this.inputObj))
          this.dialogObj.formObj.qaTimeLimitValue = new Date(
            this.inputObj.qaTimeLimitValue
          ).getTime()
          this.dialogObj.formObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.release()
        } else {
          return false
        }
      })
    },
    // 提交
    submitBtn(type = 'submit') {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dialogObj.title = type === 'save' ? this.$t('保存') : this.$t('提交')
          this.dialogObj.message =
            type === 'save' ? this.$t('确定要保存吗？') : this.$t('确定要提交吗？')
          this.inputObj.effectiveBeginTime = this.$moment(this.inputObj.effectiveBeginTime)
            .utc()
            .format()
          this.inputObj.effectiveEndTime = this.$moment(this.inputObj.effectiveEndTime)
            .utc()
            .format()
          // this.inputObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          // delete this.inputObj.contractCategoryCodeStr
          this.dialogObj.formObj = JSON.parse(JSON.stringify(this.inputObj))

          this.dialogObj.formObj.qaTimeLimitValue = new Date(
            this.inputObj.qaTimeLimitValue
          ).getTime()
          this.dialogObj.formObj.contractCategoryCode = this.inputObj.contractCategoryCodeStr
          this.$emit('dialogChild', this.dialogObj)
          this.$parent.submitBtn(type)
        } else {
          return false
        }
      })
    },
    // 收起
    away() {
      if (this.formInput) {
        this.$refs.ruleForm.$el.style.height = '0px'
        this.$refs.ruleForm.$el.style.transition = 'height 0.2s'
        this.$refs.ruleForm.$el.style.overflow = 'hidden'
        this.$refs.ruleForm.$el.style.marginTop = '0px'
      } else {
        this.$refs.ruleForm.$el.style.height = '363px'
        this.$refs.ruleForm.$el.style.transition = 'height 0.2s'
        this.$refs.ruleForm.$el.style.marginTop = '45px'
      }
      this.formInput = !this.formInput
    },
    // -------------- 顶部按钮操作：end-------------------
    // -------------- 选择框切换：start-------------------
    supplierChange(e) {
      const obj = {
        supplierName: e.itemData?.supplierName,
        supplierEnterpriseId: e.itemData?.supplierEnterpriseId,
        supplierContactName: e.itemData?.contactName,
        supplierContactPhone: e.itemData?.contactMobile,
        supplierContactMail: e.itemData?.contactMail
      }
      this.inputObj = { ...this.inputObj, ...obj }
    },
    purCompanyChange(e) {
      this.$set(this.inputObj, 'purCompanyName', e.itemData?.orgName)
      this.$set(this.inputObj, 'purCompanyCode', e.itemData?.orgCode)
      // 获取采购组织
      this.getGroup(e.value)
    },
    purOrgChange(e) {
      this.$set(this.inputObj, 'purOrgName', e.itemData?.organizationName)
      this.$set(this.inputObj, 'purOrgCode', e.itemData?.organizationCode)
    },
    // 币种切换
    currencyChange(e) {
      this.$set(this.inputObj, 'currencyName', e.itemData?.currencyName)
    },
    // 合同类别选中时
    selectTree(data) {
      this.recursionObj = []
      this.inputObj.contractCategoryCodeStr = ''
      this.inputObj.contractCategoryName = ''
      this.recursionObj.push(data.itemData)
      this.recursionId(data.itemData.parentID)
      this.recursionObj = this.recursionObj.reverse()
      this.recursionData.forEach((i) => {
        if (data.itemData.id === i.id) {
          if (i.itemCode === '42') {
            this.inputObj.contractHroStatus = 1
          } else {
            this.inputObj.contractHroStatus = 0
          }
        }
      })
      this.recursionObj.forEach((item, index) => {
        if (index !== this.recursionObj.length - 1) {
          this.inputObj.contractCategoryCodeStr += item.id + '-'
          this.inputObj.contractCategoryName += item.name + '-'
        } else {
          this.inputObj.contractCategoryCodeStr += item.id
          this.inputObj.contractCategoryName += item.text
        }
      })
    },
    // 采购组
    changeBuyerGroup(data) {
      this.inputObj.purchaseGroupName = data.itemData.text
      this.inputObj.purchaseGroupCode = data.itemData.code
    },
    // 采购员
    changeBuyer(data) {
      this.inputObj.purName = data.itemData.text
    },
    changeDemandType(e) {
      this.inputObj.demandTypeName = e.itemData.dictName
    },
    // 付款条件
    changeCondition(data) {
      this.inputObj.paymentItemName = data.itemData.text
    },
    contractNameRelations(data) {
      this.inputObj.contractNameRelation = data
    },
    templateChangeHandle(e) {
      const itemData = e.itemData
      this.inputObj.contractTemplateName = itemData.text
    },
    // 合同类型名称
    changeContractType(data) {
      this.inputObj.contractTypeName = data.itemData.text
      if (this.inputObj.contractTypeName === this.$t('框架协议')) {
        this.amountMoney = false
      } else {
        this.amountMoney = true
      }
    },
    // -------------- 选择框切换：end-------------------
    // -------------- 数据校验：start-------------------
    beginTimeRules(rule, value, callback) {
      const effective = new Date(this.inputObj.effectiveEndTime).getTime()
      if (!value) {
        callback(new Error(this.$t('请选择生效时间')))
      } else if (effective < new Date(value).getTime() && this.inputObj.effectiveEndTime) {
        callback(new Error(this.$t('生效时间不能大于终止时间')))
      } else {
        callback()
      }
    },
    endTimeRules(rule, value, callback) {
      const effective = new Date(this.inputObj.effectiveBeginTime).getTime()
      if (!value) {
        callback(new Error(this.$t('请选择终止时间')))
      } else if (effective > new Date(value).getTime() && this.inputObj.effectiveBeginTime) {
        callback(new Error(this.$t('终止时间不能小于生效时间')))
      } else {
        callback()
      }
    },
    validatePhoneNum(rule, value, callback) {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (value && !phoneRegex.test(value)) {
        callback(new Error(this.$t('手机号格式不正确')))
      } else {
        callback()
      }
    },
    validateEmail(rule, value, callback) {
      const emailRegex =
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      if (value && !emailRegex.test(value)) {
        callback(new Error(this.$t('邮箱地址格式不正确')))
      } else {
        callback()
      }
    },
    // -------------- 数据校验：end-------------------

    // -------------- 初始化获取数据：start ---------------
    // 初始化获取字典数据
    async initDictItems() {
      let codeList = [
        { code: 'LOGISTICS_DEMAND_TYPE', type: 'string' } // 需求类型
      ]
      await this.$api.contract.querySelectData.getNumDictListAllByCode(codeList).then((res) => {
        if (res.code === 200) {
          // 需求类型
          this.selectObj.demandTypeList = res.data.LOGISTICS_DEMAND_TYPE
        }
      })
    },
    // 合同类别
    async contractTypeTree() {
      await this.$api.contract.querySelectData
        .conditionType({ dictCode: 'contractCategory' })
        .then((res) => {
          if (res.code === 200) {
            this.contractCategoryFields.dataSource = res.data
            this.fieldsStatus = true
            this.recursionTree(this.contractCategoryFields.dataSource)
            // 设置默认值
            if (this.inputObj.contractCategoryCode) {
              const strCode = this.inputObj.contractCategoryCode
              this.inputObj.contractCategoryCode = []
              if (typeof strCode === 'string') {
                this.inputObj.contractCategoryCode.push(strCode)
              } else {
                this.inputObj.contractCategoryCode = strCode
              }
            }
          }
        })
    },
    // 公司
    async getCompany() {
      await this.$api.contract.querySelectData
        .OrgFindSpecifiedChildrenLevelOrgs({
          organizationLevelCodes: ['ORG02', 'ORG01'],
          orgType: 'ORG001PRO',
          includeItself: true,
          organizationIds: []
        })
        .then((res) => {
          if (res.code === 200) {
            this.purCompanyOptions = res.data.map((item) => {
              return {
                ...item,
                text: item.orgCode + '-' + item.orgName,
                value: item.id
              }
            })
          }
        })
    },
    // 采购组织
    async getGroup(purCompanyId) {
      const companyId = purCompanyId || this.inputObj.purCompanyId
      if (!companyId) {
        return
      }
      await this.$api.contract.querySelectData
        .getOrganizateByOrgId({ orgId: companyId })
        .then((res) => {
          this.buyerOrgOptions = res.data.map((item) => {
            return {
              ...item,
              text: item.organizationCode + '-' + item.organizationName,
              value: item.id
            }
          })
        })
    },

    // 采购组
    async getBuyerGroup() {
      await this.$api.contract.querySelectData
        .getBuyerGroupSelect({ groupTypeCode: 'BG001CG' })
        .then((res) => {
          if (res.code === 200) {
            let obj = ''
            res.data.forEach((item) => {
              obj = {
                text: '',
                value: '',
                code: ''
              }
              obj.text = item.groupName
              obj.value = item.id
              obj.code = item.groupCode
              this.selectObj.buyerGroup.push(obj)
            })
          }
        })
    },

    // 合同类型
    async getContractType() {
      await this.$api.contract.querySelectData
        .conditionType({ dictCode: 'contractType' })
        .then((res) => {
          let obj = ''
          if (res.code === 200) {
            res.data.forEach((item) => {
              obj = {
                text: '',
                value: ''
              }
              obj.text = item.name
              obj.value = item.id
              this.selectObj.dataArr.push(obj)
            })
          }
        })
    },

    // 获取付款条件
    termPayment() {
      this.$api.contract.querySelectData.conditionType({ dictCode: 'PaymentType' }).then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.name
            obj.value = item.id
            this.selectObj.condition.push(obj)
          })
        }
      })
    },
    // 合同模板
    async contractTemplate() {
      await this.$api.contract.querySelectData.contractTemplate().then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.label
            obj.value = item.value
            this.selectObj.contractTemplate.push(obj)
          })
        }
      })
    },
    // 是否立项
    async isEstablishmentData() {
      await this.$api.contract.querySelectData.isEstablishment().then((res) => {
        let obj = ''
        if (res.code === 200) {
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.label
            obj.value = item.value
            this.selectObj.isEstablishment.push(obj)
          })
        }
      })
    },
    // 采购员
    async getBuyer() {
      let purName = this.childObj.obj?.purName
      if (purName?.split('-')[1]) {
        purName = purName.split('-')[1]
      }
      const params = {
        fuzzyName: purName
      }
      await this.$api.contract.querySelectData.getBuyer(params).then((res) => {
        if (res.code === 200) {
          let obj = ''
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.text = item.employeeCode + '-' + item.employeeName
            obj.value = item.uid
            this.selectObj.buyer.push(obj)
          })
        }
      })
    },
    // 采购组组织请求数据
    queryPurchaseFields() {
      // let obj = {
      //   orgLevelCode: "ORG02",
      //   orgType: "ORG001PRO",
      //   tenantId: ''
      // }
      // if(this.childObj.detailObj) {
      //   obj.tenantId = this.childObj.detailObj.tenantId
      // }else{
      //   obj.tenantId = this.$store.state.user.tenantId
      // }
      const obj = {
        orgId: this.childObj.detailObj.purCompanyId
      }
      this.$api.contract.querySelectData.queryPurchase(obj).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            item.value = item.id
            item.text = item.organizationName
          })
          this.purchaseFields.dataSource = res.data
          this.purchaseStatus = true
        }
      })
    },
    // 公司请求数据
    queryCompanyFields() {
      const obj = {
        orgType: 'ORG001PRO',
        tenantId: ''
      }
      if (this.childObj.detailObj) {
        obj.tenantId = this.childObj.detailObj.tenantId
      } else {
        obj.tenantId = this.$store.state.user.tenantId
      }
      this.$api.contract.querySelectData.queryCompany(obj).then((res) => {
        if (res.code === 200) {
          this.companyFields.dataSource = res.data
        }
      })
    },
    // 公司
    recursionCompany(data) {
      data.forEach((item) => {
        this.companyData.push(item)
        if (item.children) {
          this.recursionCompany(item.children)
        }
      })
    },
    // 货币
    getMoney() {
      const obj = {
        tenantId: '10000'
      }

      this.$api.contract.querySelectData.getMoneySelect(obj).then((res) => {
        if (res.code === 200) {
          let obj = ''
          res.data.forEach((item) => {
            obj = {
              text: '',
              value: ''
            }
            obj.currencyName = item.currencyName
            obj.text = item.currencyCode + '-' + item.currencyName
            obj.value = item.id
            this.selectObj.moneyArr.push(obj)
          })
        }
      })
    },
    // -------------- 初始化获取数据：end --------------
    // -------------- 工具函数：start --------------------
    // 递归平铺数据(默认设置非采合同)
    recursionTree(data) {
      data.forEach((item) => {
        if (
          item.itemCode === '41' &&
          (!this.inputObj.contractCategoryCode || !this.inputObj.contractCategoryCode.length)
        ) {
          this.$set(this.inputObj, 'contractCategoryCode', [item.id])
          this.$set(this.inputObj, 'contractCategoryCodeStr', item.id)
          this.$set(this.inputObj, 'contractCategoryName', item.name)
          this.$set(this.inputObj, 'contractHroStatus', 0)
        }
        this.recursionData.push(item)
        if (item.children) {
          this.recursionTree(item.children)
        }
      })
    },
    // 获取选中合同类别的父级数据  递归
    recursionId(id) {
      this.recursionData.forEach((item) => {
        if (item.id === id) {
          this.recursionObj.push(item)
          if (item.parentId !== '0') {
            this.recursionId(item.parentId)
          }
        }
      })
    }
    // -------------- 工具函数：start -------------------
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  width: 100%;
  padding: 20px 0 20px 0;
  background: #fff;
  .headr-box {
    margin-bottom: 25px;
    padding: 0 20px 0 20px;
    display: flex;
    justify-content: space-between;
    .span-status {
      display: inline-block;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      padding: 2px;
      border-radius: 2px;
      margin-right: 20px;
    }
    .cancel {
      color: #eda133;
      background: rgba(237, 161, 51, 0.1);
    }
    .green {
      color: #8acc40;
      background: rgba(138, 204, 64, 0.1);
    }
    .red {
      color: #ed5633;
      background: rgba(237, 86, 51, 0.1);
    }
    // .ash {
    //   color: #9a9a9a;
    //   background: rgba(154, 154, 154, 0.1);
    // }
    .blue {
      color: #6386c1;
      background: rgba(99, 134, 193, 0.1);
    }
    .headr-text {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }
    .headr-time {
      margin-left: 13px;
    }
    .headr-btn {
      display: flex;
      span {
        display: inline-block;
        margin-left: 40px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      span:nth-of-type(1) {
        margin-left: 0;
      }
      span:nth-last-of-type(1) {
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(99, 134, 193, 1);
      }
    }
  }
  .headr-border {
    border-top: 1px solid#E8E8E8;
    height: 20px;
  }
  .form-input {
    // height: 389px;
    padding: 0 20px 0 30px;
    margin-top: 45px;
  }
}
.demo-block {
  display: flex;
  // margin-bottom: 10px;
  flex-wrap: wrap;
  .demo-input {
    margin-bottom: 10px;
    margin-left: 10px;
  }
  .title-text {
    font-size: 14px;
    font-family: PingFangSC;
    font-weight: normal;
    color: rgba(154, 154, 154, 1);
  }
  .form-item {
    width: 225px;
    margin-bottom: 15px;
    margin-right: 10px;
  }
  .form-item:nth-of-type(1) {
    margin-left: 0px;
  }
}
.demo-content {
  width: 100%;
  display: flex;
  .item-content {
    width: calc(50% - 10px);
  }
  .item-content:nth-of-type(2) {
    margin-left: 20px;
  }
}
.input-item {
  padding-right: 20px;
}
.col-xs-12,
.col-sm-12,
.col-lg-6,
.col-md-6 {
  margin-bottom: 15px;
}
/deep/ input.e-disabled {
  min-height: 30px !important;
  height: 30px;
  background: #fff !important;
}
/deep/ span.e-disabled {
  height: 31px;
  background: #fff !important;
}
/deep/ span.e-multi-line-input {
  height: auto !important;
  background: #fff !important;
}
/deep/ textarea.e-disabled {
  background: #fff !important;
}
/deep/ .label {
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: normal;
  color: rgba(41, 41, 41, 1);
}

.label-style /deep/ .label {
  color: rgba(154, 154, 154, 1);
}
.label-style /deep/#baseTreeSelect {
  color: rgba(154, 154, 154, 1);
}
</style>
