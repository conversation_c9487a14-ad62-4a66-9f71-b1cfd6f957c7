<template>
  <mt-input
    item-label-style="left"
    :show-clear-button="true"
    @change="change"
    v-model="data.supplierReply"
    type="text"
    :placeholder="$t('请输入反馈备注')"
    max-length="128"
  ></mt-input>
</template>
<script>
import Bus from '../../../utils/bus'
export default {
  data() {
    return {}
  },
  methods: {
    change(e) {
      Bus.$emit('SupplierReplay', { contractItemId: this.data.id, contractItemMemo: e })
    }
  }
}
</script>
