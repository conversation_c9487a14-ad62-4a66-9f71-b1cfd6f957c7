<template>
  <div class="signContract-wrap">
    <mt-loading class="signloading" v-if="showLoading"></mt-loading>
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <mt-form ref="signContractRef" :model="signForm">
        <mt-form-item :label="$t('签订方式')" style="width: 100%">
          <mt-radio v-model="signForm.approvalType" :data-source="typeData"></mt-radio>
        </mt-form-item>
        <mt-form-item v-if="signForm.approvalType === '0'" :label="$t('审批流程')">
          <mt-radio v-model="signForm.itemCode" :data-source="processList"></mt-radio>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      signForm: {
        approvalType: '0',
        itemCode: ''
      },
      typeData: [
        {
          label: this.$t('签字审批'),
          value: '1'
        },
        {
          label: this.$t('盖章审批'),
          value: '0'
        }
      ],
      showLoading: false,
      processList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    contractId() {
      return this.modalData.data.id
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    getProcessList() {
      this.$api.contract.querySelectData
        .conditionType({
          dictCode: 'oacontractid'
        })
        .then((res) => {
          if (res.code === 200) {
            this.processList = res.data.map((i) => {
              return {
                label: i.itemName,
                value: i.itemCode
              }
            })
          }
        })
    },
    confirm() {
      this.save()
    },
    cancel() {
      this.$emit('cancel-function')
    },

    save() {
      this.showLoading = true
      this.$api.contract.queryBuyerListServe
        .sign(this.contractId, this.signForm)
        .then((res) => {
          if (res.code == 200) {
            this.$emit('confirm-function')
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    }
  },
  created() {
    this.getProcessList()
  }
}
</script>
<style lang="scss" scoped>
.signloading {
  position: fixed;
  top: 0;
  z-index: 9999;
}
</style>
