<template>
  <div class="archive-wrap">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <mt-form ref="archiveContractRef" :model="archiveForm" :rules="archiveFormRules">
        <mt-form-item :label="$t('上传合同盖章附件')" prop="picUrl" class="fullwidth">
          <Upload :multiple="true"></Upload>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
import Upload from './upload.vue'
import Bus from '../../../utils/bus'
export default {
  components: {
    Upload
  },
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      archiveForm: {
        fileUrl: ''
      },
      archiveFormRules: {
        picUrl: [{ required: true, message: this.$t('请选上传合同盖章附件'), trigger: 'blur' }]
      },
      typeData: [
        {
          label: this.$t('签字审批'),
          value: '1'
        },
        {
          label: this.$t('盖章审批'),
          value: '2'
        }
      ],
      asyncSettings: {
        saveUrl: 'https://ej2.syncfusion.com/services/api/uploadbox/Save',
        removeUrl: 'https://ej2.syncfusion.com/services/api/uploadbox/Remove'
      },
      fileList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    contractId() {
      return this.modalData.data.id
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    Bus.$on('getFileData', (data) => {
      this.fileList = data
    })
  },
  methods: {
    confirm() {
      this.save()
    },
    cancel() {
      this.$emit('cancel-function')
    },
    save() {
      if (this.fileList.length == 0) {
        this.$toast({ content: this.$t('请上传归档附件'), type: 'warning' })
      } else {
        const formData = new FormData()
        this.fileList.map((item) => {
          formData.append('files', item)
        })
        this.$emit('confirm-function')
        this.$store.commit('startLoading')
        this.$api.contract.queryBuyerListServe.archive(this.contractId, formData).then(() => {
          this.$store.commit('endLoading')
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        })
      }
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped>
.fullwidth {
  width: 100% !important;
}
</style>
