<template>
  <div class="upload-wrap">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <mt-template-page
        ref="tempaltePageRef"
        :template-config="privewConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
      >
      </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
import { privewConfig } from '../config/contractBuyer'
import axios from 'axios'
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      signForm: {
        type: '2'
      }
      // _files: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    privewConfig() {
      privewConfig[0].grid.dataSource = this.modalData.data
      return privewConfig
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.save()
    },
    cancel() {
      this.$emit('cancel-function')
    },

    save() {
      this.$emit('confirm-function')
    },
    handleClickToolBar() {},
    handleClickCellTool() {},
    handleClickCellTitle(e) {
      const { data, field } = e
      // 标题点击
      if (field == 'fileName') {
        this.download(data)
      }
    },
    download({ id, fileName }) {
      axios.get(`/api/contract/attachment/download/${id}`, { responseType: 'blob' }).then((res) => {
        const filename = fileName
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.download = filename
        document.body.appendChild(link)
        link.click()
        URL.revokeObjectURL(link.href) // 释放URL 对象
        document.body.removeChild(link)
      })
    }
  },
  created() {}
}
</script>
<style lang="scss" scoped>
/deep/ .href {
  color: #00469c;
  cursor: pointer;
  font-weight: bold;
}
</style>
