<template>
  <div class="left-box">
    <div class="table-box" ref="tableRef">
      <div class="head-box">
        <p>{{ $t('待签列表') }}</p>
        <div class="search-box">
          <mt-input
            class="e-input"
            type="text"
            :placeholder="$t('请输入合同名称')"
            v-model="search"
            @blur="searchData"
          />
          <span v-if="!btnStatus" @click="searchData" class="icon-search"
            ><MtIcon name="icon_search"
          /></span>
        </div>
      </div>
      <div class="table-list">
        <div>
          <div
            class="list-obj"
            v-for="(item, index) in mtPageData.records"
            :key="index"
            @click="switchClik(item)"
          >
            <p>
              <span class="span-code">{{ item.id }}</span>
              <span v-if="item.status === 4" class="span-status red">{{ $t('反馈异常') }}</span>
              <span v-if="item.status === 2" class="span-status cancel">{{ $t('待反馈') }}</span>
              <span v-if="item.status === 3" class="span-status green">{{ $t('反馈正常') }}</span>
              <span v-if="item.status === 0" class="span-status blue">{{ $t('草稿') }}</span>
              <span v-if="item.status === 1" class="span-status blue">{{ $t('待发布') }}</span>
              <span v-if="item.status === 5" class="span-status green">{{ $t('已签订') }}</span>
              <span v-if="item.status === 6" class="span-status green">{{ $t('已归档') }}</span>
            </p>
            <p>{{ item.contractName }}</p>
          </div>
        </div>
        <mt-page
          :page-settings="pageSettings"
          :total-pages="mtPageData.pages"
          @currentChange="goToPage"
        ></mt-page>
      </div>
    </div>
    <div class="table-btn" @click="away" ref="btnRef">
      <span v-if="!btnStatus"><MtIcon name="icon_arrow_left" class="icon-btn" /></span>
      <span v-if="btnStatus"><MtIcon name="icon_arrow_right" class="icon-btn" /></span>
    </div>
  </div>
</template>

<script>
import MtPage from '@mtech-ui/page'
export default {
  props: {
    sidebarUrl: {
      type: String,
      default: ''
    }
  },
  components: {
    MtPage
  },
  data() {
    return {
      search: '',
      list: [
        {
          code: 'dsf2353453453',
          status: 1,
          text: '办公用品采购 - 生产型采购'
        },
        {
          code: 'dsf2353453453',
          status: 5,
          text: '办公用品采购 - 生产型采购'
        },
        {
          code: 'dsf2353453453',
          status: 4,
          text: '办公用品采购 - 生产型采购'
        }
      ],
      pageSettings: { pageSize: 10, pageCount: 1, pageSizes: [10], contractName: '' },
      btnStatus: true,
      mtPageData: [],
      sidebarListUrl: this.sidebarUrl
    }
  },
  created() {
    this.sidebar(this.pageSettings)
  },
  methods: {
    away() {
      if (!this.btnStatus) {
        this.$refs.tableRef.style.width = '0px'
        this.$refs.tableRef.style.height = '0px'
        this.$refs.tableRef.style.transition = 'width 0.2s'
        this.$refs.tableRef.style.overflow = 'hidden'
        this.$refs.tableRef.style.border = '0px'
        this.$refs.btnRef.style.marginLeft = '-20px'
      } else {
        this.$refs.tableRef.style.width = '280px'
        this.$refs.tableRef.style.height = '100%'
        this.$refs.tableRef.style.transition = 'width 0.2s'
        this.$refs.btnRef.style.marginLeft = '0px'
        this.$refs.tableRef.style.border = '1px solid rgba(232, 232, 232, 1)'
      }
      this.btnStatus = !this.btnStatus
    },
    // 点击切换右边的数据
    switchClik(res) {
      if (this.sidebarUrl === '/supplier/simple/page') {
        this.$router.push({
          path: '/middlePlatform/feedbackContract',
          query: { id: res.id }
        })
        this.$parent.detailsData()
      } else {
        this.$router.push({
          path: '/middlePlatform/contractPrivew',
          query: { contractId: res.id }
        })
        this.$parent.getContractDetail(res.id)
      }
    },
    // 分页切换
    goToPage(num) {
      this.pageData = ''
      this.pageSettings.pageCount = num
      this.sidebar(this.pageSettings)
    },
    // 请求左侧栏数据
    sidebar(param) {
      const resParam = {
        page: {
          current: param.pageCount,
          size: param.pageSize
        },
        contractName: param.contractName
      }
      this.$api.contract.querySupplierListServe
        .sidebarDetailsList(this.sidebarListUrl, resParam)
        .then((res) => {
          if (res.code === 200) {
            this.mtPageData = res.data
            this.pageStatus = true
          }
        })
    },
    // 搜索
    searchData() {
      this.pageSettings.contractName = this.search
      this.sidebar(this.pageSettings)
    }
  }
}
</script>

<style lang="scss" scoped>
.input-item {
  padding-right: 20px;
}
.left-box {
  width: 100%;
  height: 100%;
  display: flex;
}
.table-box {
  width: 0px;
  height: 0;
  // margin-right: 12px;
  background: #ffffff;
  // border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
  .head-box {
    width: calc(100 - 40px);
    height: 109px;
    padding: 20px;
    border-bottom: 1px solid #e8e8e8;
    .search-box {
      display: flex;
      position: relative;
      margin-top: 20px;
    }
    .icon-search {
      position: absolute;
      top: 5px;
      left: 5px;
      color: #98aac3;
      cursor: pointer;
    }
    p {
      font-size: 20px;
      font-family: PingFangSC;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }
  }
}
.table-list {
  width: 100%;
  height: calc(100% - 109px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .list-obj {
    cursor: pointer;
    margin-top: 30px;
    margin-left: 20px;
    p {
      .span-code {
        display: inline-block;
        height: 20px;
        font-size: 14px;
        font-family: PingFangSC;
        font-weight: 500;
        color: rgba(0, 70, 156, 1);
        margin-right: 10px;
        line-height: 20px;
      }
      .span-status {
        display: inline-block;
        height: 20px;
        font-size: 12px;
        font-family: PingFangSC;
        font-weight: 500;
        padding: 0 2px 0 2px;
        border-radius: 2px;
        margin-right: 20px;
        line-height: 20px;
      }
      .cancel {
        color: #eda133;
        background: rgba(237, 161, 51, 0.1);
      }
      .green {
        color: #8acc40;
        background: rgba(138, 204, 64, 0.1);
      }
      .red {
        color: #ed5633;
        background: rgba(237, 86, 51, 0.1);
      }
      // .ash {
      //   color: #9a9a9a;
      //   background: rgba(154, 154, 154, 0.1);
      // }
      .blue {
        color: #6386c1;
        background: rgba(99, 134, 193, 0.1);
      }
    }
    p:nth-of-type(2) {
      height: 17px;
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      line-height: 17px;
    }
  }
}
.table-btn {
  width: 12px;
  height: 60px;
  background: rgba(0, 70, 156, 0.3);
  border-radius: 0 4px 4px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 90px;
  cursor: pointer;
  margin-left: -20px;
  .icon-btn {
    width: 4px;
    height: 8px;
    color: rgba(255, 255, 255, 1);
  }
}
::v-deep .mt-pagertemplate .mt-page-jump {
  display: none;
}
::v-deep .mt-pagertemplate .mt-pagesizes {
  display: none;
}
::v-deep.mt-input .e-control.e-textbox.e-lib {
  padding-left: 20px;
}
</style>
