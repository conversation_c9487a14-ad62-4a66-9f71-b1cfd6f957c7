<template>
  <!-- 合同创建 -->
  <div>
    <div class="feedback-box">
      <!-- <div class="left-signed">
      <signedList></signedList>
    </div> -->
      <div class="right-list">
        <formInput
          :child-obj="childObj"
          :businessTypeCode="businessTypeCode"
          @dialogChild="dialogParent"
          v-if="isShow"
        ></formInput>
        <attachMent
          v-if="isShow"
          :tag="1"
          :businessTypeCode="businessTypeCode"
          :data="childObj.obj.contractDetailVoList"
          :contract-temp-id="contractTempId"
          :logisticsMethodCode="logisticsMethodCode"
        ></attachMent>
        <mt-dialog
          ref="dialog"
          :open="onOpen"
          @save="confirm"
          @close="cancel"
          :buttons="buttons"
          css-class="create-proj-dialog"
          width="500"
          height="300"
          :header="header"
        >
          <div class="dialog-content">{{ dialogObj.message }}</div>
        </mt-dialog>
      </div>
    </div>
    <mt-loading v-if="loading"></mt-loading>
  </div>
</template>
<script>
import attachMent from './components/attachMent.vue'
import formInput from './components/formInput.vue'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    attachMent,
    formInput
  },
  data() {
    const contractTempId = `${new Date().getTime()}${Math.floor((Math.random() + 0.1) * 1000000)}`
    return {
      contractTempId,
      childObj: {
        obj: {
          contractCode: null, // 合同编码
          contractId: null, // 合同编码
          contractName: null, // 合同名称
          supplierCode: null, // 供应商编号
          supplierName: null, // 供应商名称
          purOrgId: null, // 采购组织
          purOrgName: null, // 采购组织名称
          purCompanyId: null, // 公司id
          purCompanyName: null, // 公司
          countryCode: null, // 国家code
          purchaseGroupId: null, // 采购组
          purchaseGroupName: null, // 采购组name
          contractIdRelation: null, // 关联合同
          paymentItemId: null, // 付款周期
          paymentItemName: null, // 付款周期Name
          contractTypeId: null, // 合同类型
          contractTypeName: null, // 合同类型名称
          currencyId: null, // 币种
          currencyName: null, // 币种名字
          purId: null, // 采购员
          purName: null, // 采购员Name
          effectiveBeginTime: null, // 生效日期
          effectiveEndTime: null, // 结束日期
          qaTimeLimitValue: null, // 质保期
          supplierId: null, // 供应商id
          supplierContactId: null, // 供应商编码
          supplierContactName: null, // 供应商名称
          supplierContactPhone: null, // 供应商电话
          supplierContactMail: null, // 供应商邮箱
          supplierSpareEmail: null, // 供应商备用邮箱
          // purchaseOrgName: null,          //组织机构名称
          memo: null, // 备注
          contractTemplateId: null, // 合同模板
          contractTemplateName: null, // 合同模板名称
          taxesDesc: null, // 运费描述
          established: null, // 是否立项
          taxedTotalPrice: null, // 含税价格
          untaxedTotalPrice: null, // 不含税
          purchaseGroupCode: null, // 采购组
          contractCategoryCode: [], // 合同类别
          contractCategoryName: null, // 合同类别名称
          updateTime: null, // 更新时间
          updateUserName: null, // 更新人
          status: null, // 状态
          accountPeriod: null, // 账期天数
          isRelationCompany: null, // 是否关联公司
          demandTypeName: null, // 需求类型
          logisticsMethodName: null // 运输方式
        },
        // disabled 控制
        inputDisabled: {
          contractCodeDisabled: true,
          contractNameDisabled: false,
          supplierCodeDisabled: true,
          supplierNameDisabled: true,
          orgGroupDisabled: true,
          companyDisabled: true,
          buyerGroupDisabled: false,
          agreementDisabled: false,
          dataArrDisabled: false,
          moneyArrDisabled: true,
          notTotalDisabled: true,
          totalDisabled: true,
          conditionDisabled: false,
          buyerDisabled: false,
          contactsDisabled: false,
          timeDisabled: false,
          telephoneDisabled: false,
          mailboxDisabled: false,
          contractTemplate: false,
          taxationDescribeDisabled: false,
          remarksDisabled: false,
          establishmentDisabled: false,
          contractCategoryDisabled: false,
          responseMemoDisabled: false,
          accountPeriodDisabled: false,
          isRelationCompanyDisabled: false
        },
        // 顶部信息
        headerObj: {
          updatePeople: this.$t('张三'),
          updateTime: '2022',
          status: 3
        },
        statusBtn: true, // 判断是供方还是采方
        createStatus: true, // 判断是创建还是编辑
        seeStatus: false // 判断是查看还是编辑与创建
      },
      dialogObj: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      isShow: false,
      commitStatus: '',
      uploadFileSet: [],
      loading: false,
      businessTypeCode: '',
      logisticsMethodCodeMap: {
        1: 'pendingAnnualLogisticsSeaItemList', // 海运
        3: 'annualLogisticsRailwayItemList', // 铁运
        5: 'annualLogisticsTrunkItemList' // 物流
      },
      logisticsMethodCode: ''
    }
  },
  computed: {
    header() {
      return this.dialogObj.title
    }
  },
  mounted() {
    this.detailsData()
    this.createContractData = utils.debounce(this.createContractData, 800)
    this.createContractCommit = utils.debounce(this.createContractCommit, 800)
  },
  methods: {
    dialogParent(data) {
      this.dialogObj = data
    },
    // 保存草稿
    draft() {
      this.commitStatus = 'draft'
      this.onOpen()
    },
    // 提交
    submitBtn() {
      this.commitStatus = 'submit'
      this.onOpen()
    },
    // 提交
    publish() {
      this.commitStatus = 'publish'
      this.onOpen()
    },
    // 拒绝
    onOpen() {
      this.$refs.dialog.ejsRef.show()
    },
    cancel() {
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      if (this.commitStatus === 'submit') {
        this.createContractCommit()
      } else if (this.commitStatus === 'publish') {
        this.createContractPublish()
      } else {
        this.createContractData()
      }
    },
    // 请求数据
    initTable() {},
    // 获取创建详情数据
    detailsData() {
      const param = JSON.parse(localStorage.getItem('primaryIds'))
      this.$api.contract.queryBuyerListServe.createView(param).then((res) => {
        if (res.code === 200) {
          const data = res.data
          const _logisticsMethod = this.logisticsMethodCodeMap[data.logisticsMethodCode]
          for (const i in this.childObj.obj) {
            this.childObj.obj[i] = data[i]
          }
          this.childObj.obj.contractDetailVoList =
            res.data?.contractItemVoList[0]?.businessTypeCode === 'BTTCL006'
              ? res.data[_logisticsMethod] || []
              : res.data.contractItemVoList
          this.childObj.obj.contractDetailVoList?.forEach((item, i) => {
            item.index = i + 1
          })
          // this.childObj.obj.contractDetailVoList = res.data.contractItemVoList
          this.childObj.obj.companyPrimaryIdList = param
          this.childObj.obj.demandTypeName = res.data?.demandTypeName
          this.childObj.obj.logisticsMethodName = res.data?.logisticsMethodName
          this.businessTypeCode = res.data?.contractItemVoList[0]?.businessTypeCode
          this.logisticsMethodCode = res.data.logisticsMethodCode
          // this.childObj.obj.contractCategoryCode = []
          // this.childObj.obj.contractCategoryCode.push(res.data.contractCategoryCode)
          this.isShow = true
        }
      })
    },
    // 创建合同 保存草稿
    createContractData() {
      delete this.dialogObj.formObj.contractDetailVoList
      delete this.dialogObj.formObj.contractCategoryCodeStr
      this.loading = true
      const params = {
        ...this.dialogObj.formObj,
        contractTempId: this.contractTempId
      }
      // this.dialogObj.formObj.contractCategoryCode = this.dialogObj.formObj.contractCategoryCode
      this.$api.contract.queryBuyerListServe
        .createContractSave(params)
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            this.$toast({
              content: res.msg || this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.dialog.ejsRef.hide()
            // this.$router.go(-1)
            this.$router.push({
              path: '/middlePlatform/contractDetail',
              query: {
                contractId: res.data.contractId
              }
            })
          } else {
            this.$toast({
              content: res.msg || this.$t('操作失败'),
              type: 'error'
            })
            this.$refs.dialog.ejsRef.hide()
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 提交
    createContractCommit() {
      delete this.dialogObj.formObj.contractDetailVoList
      delete this.dialogObj.formObj.contractCategoryCodeStr
      this.loading = true
      const params = {
        ...this.dialogObj.formObj,
        contractTempId: this.contractTempId
      }
      // this.dialogObj.formObj.contractCategoryCode = this.dialogObj.formObj.contractCategoryCode
      this.$api.contract.queryBuyerListServe
        .createContractCommit(params)
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            this.$toast({
              content: res.msg || this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.dialog.ejsRef.hide()
            this.$router.go(-1)
          } else {
            this.$toast({
              content: res.msg || this.$t('操作失败'),
              type: 'error'
            })
            this.$refs.dialog.ejsRef.hide()
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 发布
    createContractPublish() {
      delete this.dialogObj.formObj.contractDetailVoList
      delete this.dialogObj.formObj.contractCategoryCodeStr
      this.loading = true
      const params = {
        ...this.dialogObj.formObj,
        contractTempId: this.contractTempId
      }
      // this.dialogObj.formObj.contractCategoryCode = this.dialogObj.formObj.contractCategoryCode
      this.$api.contract.queryBuyerListServe
        .createContractPublish(params)
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            this.$toast({
              content: res.msg || this.$t('操作成功'),
              type: 'success'
            })
            this.$refs.dialog.ejsRef.hide()
            this.$router.go(-1)
          } else {
            this.$toast({
              content: res.msg || this.$t('操作失败'),
              type: 'error'
            })
            this.$refs.dialog.ejsRef.hide()
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    actionUpladFile(n) {
      this.uploadFileSet = n
    }
  }
}
</script>
<style lang="scss" scoped>
.feedback-box {
  display: flex;
  .left-signed {
    width: 292px;
    // height: calc(100% - 40px);
    // margin: 20px 20px 0 0;
  }
  .right-list {
    // width: calc(100% - 280px);
    overflow-x: auto;
  }
}
.mt-loading {
  position: fixed;
  top: 0;
  z-index: 9999;
}
</style>
