<template>
  <!-- 供方-合同管理 -->
  <div class="supplier-box">
    <mt-template-page
      ref="tempaltePageRef"
      @handleClickCellTitle="handleClickCellTitle"
      :template-config="pageConfig"
    >
    </mt-template-page>
  </div>
</template>
<script>
import { pageConfig } from './config/contractSupplier'
export default {
  data() {
    return {
      pageConfig: pageConfig(this)
    }
  },
  methods: {
    // 跳到详情页
    handleClickCellTitle(e) {
      if (e.field == 'contractCode') {
        this.$router.push({
          path: '/middlePlatform/feedbackContract',
          query: { id: e.data.id }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-box {
  width: 100%;
  height: 100%;
}
::v-deep .href {
  color: #00469c;
  cursor: pointer;
  font-weight: bold;
}
::v-deep .cancel {
  font-size: 12px;
  padding: 2px;
  color: #eda133;
  background: rgba(237, 161, 51, 0.1);
}
::v-deep .green {
  font-size: 12px;
  padding: 2px;
  color: #8acc40;
  background: rgba(138, 204, 64, 0.1);
}
::v-deep .red {
  font-size: 12px;
  padding: 2px;
  color: #ed5633;
  background: rgba(237, 86, 51, 0.1);
}
::v-deep .ash {
  font-size: 12px;
  padding: 2px;
  color: #9a9a9a;
  background: rgba(154, 154, 154, 0.1);
}
::v-deep .text {
  position: relative;
  padding-left: 14px;
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    margin-top: -4px;
    left: 0;
  }
}
::v-deep .text_success {
  color: #6386c1;
  &::before {
    background-color: #6386c1;
  }
}
::v-deep .text_waring {
  color: #eda133;
  &::before {
    background-color: #eda133;
  }
}
::v-deep .text_fail {
  color: #ed5633;
  &::before {
    background-color: #ed5633;
  }
}
</style>
