<template>
  <!-- 合同反馈 -->
  <div class="feedback-box">
    <div class="left-signed">
      <signedList v-if="childObj.obj.status !== 2" :sidebar-url="sidebarUrl"></signedList>
    </div>
    <div class="right-list">
      <formInput
        v-if="isShow"
        :businessTypeCode="businessTypeCode"
        :child-obj="childObj"
        @dialogChild="dialogParent"
      ></formInput>
      <attachMent
        v-if="isShow"
        :tag="tagNum"
        :data="childObj.obj.contractDetailVoList"
        :logisticsMethodCode="logisticsMethodCode"
        :contract="contract"
      ></attachMent>
      <mt-dialog
        ref="dialog"
        :open="onOpen"
        @save="confirm"
        @close="cancel"
        :buttons="buttons"
        css-class="create-proj-dialog"
        width="500"
        height="300"
        :header="header"
      >
        <div class="dialog-content" style="font-size: 16px">{{ dialogObj.message }}</div>
      </mt-dialog>
    </div>
  </div>
</template>
<script>
import attachMent from './components/attachMent.vue'
import formInput from './components/supplierFormInput.vue'
import signedList from './components/signedList.vue'
export default {
  components: {
    attachMent,
    formInput,
    signedList
  },
  data() {
    return {
      childObj: {
        obj: {
          contractId: null, // 合同id
          contractCode: null, // 合同编码
          contractName: null, // 合同名称
          supplierCode: null, // 供应商编号
          supplierName: null, // 供应商名称
          purOrgId: null, // 采购组织
          purOrgName: null, // 采购组织名称
          purCompanyId: null, // 公司id
          purCompanyName: null, // 公司
          purchaseGroupId: null, // 采购组
          contractIdRelation: null, // 关联合同
          contractNameRelation: null,
          paymentItemId: null, // 付款周期
          paymentItemName: null,
          contractTypeId: null, // 合同类型
          currencyId: null, // 币种
          currencyName: null, // 币种
          purId: null, // 采购员
          effectiveBeginTime: null, // 生效日期
          effectiveEndTime: null, // 结束日期
          supplierContactId: null, // 供应商编码
          supplierContactName: null, // 供应商名称
          supplierContactPhone: null, // 供应商电话
          supplierContactMail: null, // 供应商邮箱
          supplierSpareEmail: null, // 供应商备用邮箱
          // purchaseOrgName: null,          //组织机构名称
          memo: null, // 备注
          contractTemplateId: null, // 合同模板
          taxesDesc: null, // 运费描述
          established: null, // 是否立项
          taxedTotalPrice: null, // 含税价格
          untaxedTotalPrice: null, // 不含税
          contractCategoryName: null,
          purchaseGroupCode: null, // 采购组
          supplierReply: null, // 反馈备注
          hetongleibie: null,
          updateTime: null, // 更新时间
          updateUserName: null, // 更新人
          status: null, // 状态,
          purchaseGroupName: null,
          contractTypeName: null,
          contractTemplateName: null,
          purName: null,
          accountPeriod: null,
          isRelationCompany: null,
          demandTypeName: null,
          logisticsMethodName: null
        },
        // disabled 控制
        inputDisabled: {
          contractCodeDisabled: true,
          contractNameDisabled: true,
          supplierCodeDisabled: true,
          supplierNameDisabled: true,
          orgGroupDisabled: true,
          buyerGroupDisabled: true,
          companyDisabled: true,
          agreementDisabled: true,
          dataArrDisabled: true,
          moneyArrDisabled: true,
          notTotalDisabled: true,
          totalDisabled: true,
          conditionDisabled: true,
          buyerDisabled: true,
          contactsDisabled: true,
          timeDisabled: true,
          telephoneDisabled: true,
          mailboxDisabled: true,
          contractTemplate: true,
          taxationDescribeDisabled: true,
          remarksDisabled: true,
          establishmentDisabled: true,
          contractCategoryDisabled: true,
          contractTemplateDisabled: true,
          responseMemoDisabled: false,
          accountPeriodDisabled: true,
          isRelationCompanyDisabled: true
        },
        // 顶部信息,
        statusBtn: false,
        seeStatus: false,
        createStatus: false, // 判断是创建还是编辑
        detailObj: {}
      },
      dialogObj: '',
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { content: this.$t('确定') }
        }
      ],
      isShow: false,
      openNum: 0,
      contract: {
        contractId: this.$route.query.id,
        providerType: 1
      },
      // 附件反馈备注
      feedbackArr: [],
      sidebarUrl: '/supplier/simple/page',
      tagNum: 2,
      businessTypeCode: '',
      logisticsMethodCodeMap: {
        1: 'pendingAnnualLogisticsSeaItemList', // 海运
        3: 'annualLogisticsRailwayItemList', // 铁运
        5: 'annualLogisticsTrunkItemList' // 物流
      },
      logisticsMethodCode: ''
    }
  },
  computed: {
    header() {
      return this.dialogObj.title
    }
  },
  created() {
    this.detailsData()
  },
  methods: {
    getSupplierReplay(data) {
      this.feedbackArr = data
    },
    dialogParent(data) {
      this.dialogObj = data
    },
    // 接受
    accept() {
      this.openNum = 1
      this.onOpen()
    },
    // 拒绝
    refuse() {
      this.openNum = 0
      this.onOpen()
    },
    // 拒绝
    onOpen() {
      this.$refs.dialog.ejsRef.show()
    },
    cancel() {
      // this.$emit("cancel-function");
      this.$refs.dialog.ejsRef.hide()
    },
    confirm() {
      const obj = {
        contractId: this.dialogObj.formObj.contractId,
        supplierReply: this.dialogObj.formObj.supplierReply,
        contractItemMemoDtoList: this.feedbackArr
      }
      if (this.openNum === 1) {
        // 接受
        this.action('accept', obj)
      } else {
        // 拒绝
        this.action('reject', obj)
      }
    },
    // 请求数据
    initTable() {},
    // 获取详情数据
    detailsData() {
      this.$api.contract.querySupplierListServe
        .supplierDetails(this.$route.query.id)
        .then((res) => {
          if (res.code === 200) {
            this.childObj.detailObj = res.data
            const data = res.data
            const _logisticsMethod = this.logisticsMethodCodeMap[data.logisticsMethodCode]
            for (const i in this.childObj.obj) {
              this.childObj.obj[i] = data[i]
            }
            // 判断是详情还是编辑，详情就禁用可以操作的
            if (this.childObj.obj.status === 2) {
              this.tagNum = 2
              this.childObj.inputDisabled.responseMemoDisabled = false
            } else {
              this.tagNum = 3
              this.childObj.inputDisabled.responseMemoDisabled = true
            }
            // this.childObj.obj.contractDetailVoList = res.data.contractItemVoList
            this.childObj.obj.contractDetailVoList =
              res.data?.contractItemVoList[0]?.businessTypeCode === 'BTTCL006'
                ? res.data[_logisticsMethod] || []
                : res.data.contractItemVoList
            this.childObj.obj.contractDetailVoList?.forEach((item, i) => {
              item.index = i + 1
            })
            // 截取合同类型最后一个 '-'后的id
            const strCode = res.data.contractCategoryCode ?? ''
            const strIndex = strCode.lastIndexOf('\-')
            this.childObj.obj.contractCategoryCode = []
            this.childObj.obj.contractCategoryCode.push(
              strCode.substring(strIndex + 1, strCode.length)
            )
            this.businessTypeCode = res.data?.contractItemVoList[0]?.businessTypeCode
            this.logisticsMethodCode = res.data?.logisticsMethodCode
            this.isShow = true
          }
        })
    },
    // 接受与拒绝
    action(val, param) {
      this.$api.contract.querySupplierListServe.contractSupplierAction(val, param).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || this.$t('操作成功'),
            type: 'success'
          })
          this.$refs.dialog.ejsRef.hide()
          this.$router.go(-1)
        } else {
          this.$toast({
            content: res.msg || this.$t('操作失败'),
            type: 'error'
          })
          this.$refs.dialog.ejsRef.hide()
          this.$router.go(-1)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.feedback-box {
  display: flex;
  .left-signed {
    width: 292px;
    // height: calc(100% - 40px);
    // margin: 20px 20px 0 0;
  }
  .right-list {
    // width: calc(100% - 280px);
    overflow-x: auto;
  }
}
</style>
