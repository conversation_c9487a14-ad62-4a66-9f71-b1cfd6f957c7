<template>
  <div class="contractDetail">
    <!-- 新增/编辑合同: 不在之前代码些兼容逻辑，铁运特殊处理 -->
    <formBuyerAdd
      v-if="type === 'add' || type === 'edit'"
      :child-obj="childObj"
      @dialogChild="dialogParent"
    ></formBuyerAdd>
    <formInput
      v-else-if="inputStatus"
      :child-obj="childObj"
      @dialogChild="dialogParent"
    ></formInput>
    <attachMent
      ref="attactMent"
      :tag="1"
      :data="bdData"
      :contract="contract"
      v-if="showAttachMent"
      :logisticsMethodCode="logisticsMethodCode"
    ></attachMent>
  </div>
</template>
<script>
import formInput from '@/views/contract/components/formBuyer.vue'
import formBuyerAdd from '@/views/contract/components/formBuyerAdd.vue'
import attachMent from '@/views/contract/components/attachMent'
import { utils } from '@mtech-common/utils'
export default {
  components: {
    formInput,
    formBuyerAdd,
    attachMent
  },
  data() {
    return {
      data: [],
      childObj: {
        obj: {
          contractSourceTypeCode: null, // 合同来源
          contractSourceType: null, // 合同来源
          businessTypeId: null, // 业务类型
          businessTypeCode: null, // 业务类型
          businessTypeName: null, // 业务类型
          contractCode: null, // 合同编码
          contractId: null, // 合同编码
          contractName: null, // 合同名称
          supplierEnterpriseId: null, // 供应商
          supplierEnterpriseCode: null, // 供应商
          supplierEnterpriseName: null, // 供应商
          supplierCode: null, // 供应商编号
          supplierName: null, // 供应商名称
          purOrgId: null, // 采购组织
          purOrgName: null, // 采购组织名称
          purCompanyId: null, // 公司id
          purCompanyName: null, // 公司
          purchaseGroupId: null, // 采购组
          purchaseGroupCode: null, // 采购组
          purchaseGroupName: null, // 采购组name
          contractIdRelation: null, // 关联合同
          paymentItemId: null, // 付款周期
          paymentItemName: null, // 付款周期Name
          contractTypeId: null, // 合同类型
          contractTypeName: null, // 合同类型名称
          currencyId: null, // 币种
          currencyName: null,
          purId: null, // 采购员
          purName: null, // 采购员Name
          effectiveBeginTime: null, // 生效日期
          effectiveEndTime: null, // 结束日期
          qaTimeLimitValue: null, // 质保期
          supplierContactId: null, // 供应商编码
          supplierContactName: null, // 供应商名称
          supplierContactPhone: null, // 供应商电话
          supplierContactMail: null, // 供应商邮箱
          supplierSpareEmail: null, // 供应商备用邮箱
          // purchaseOrgName: null,          //组织机构名称
          memo: null, // 备注
          contractTemplateId: null, // 合同模板
          contractTemplateName: null, // 合同模板名称
          taxesDesc: null, // 运费描述
          established: null, // 是否立项
          taxedTotalPrice: null, // 含税价格
          untaxedTotalPrice: null, // 不含税
          contractCategoryCode: null, // 合同类别
          contractCategoryName: null, // 合同类别
          updateTime: null, // 更新时间
          updateUserName: null, // 更新人
          status: null, // 状态
          accountPeriod: null, // 账期天数
          isRelationCompany: null, // 是否关联公司
          demandType: null, // 需求类型
          logisticsMethodName: null //运输方式
        },
        // disabled 控制
        inputDisabled: {
          contractCodeDisabled: true,
          contractNameDisabled: false,
          supplierCodeDisabled: true,
          supplierNameDisabled: true,
          orgGroupDisabled: true,
          companyDisabled: true,
          buyerGroupDisabled: false,
          agreementDisabled: false,
          dataArrDisabled: false,
          moneyArrDisabled: true,
          notTotalDisabled: true,
          totalDisabled: true,
          conditionDisabled: false,
          buyerDisabled: false,
          contactsDisabled: false,
          timeDisabled: false,
          telephoneDisabled: false,
          mailboxDisabled: false,
          contractTemplate: false,
          taxationDescribeDisabled: false,
          remarksDisabled: false,
          responseMemoDisabled: true,
          establishmentDisabled: false,
          contractCategoryDisabled: false,
          contractTemplateDisabled: false,
          accountPeriodDisabled: false,
          isRelationCompanyDisabled: false
        },
        // 验证
        rules: {
          dataArr: [{ required: true, message: this.$t('请选择合同类型'), trigger: 'blur' }]
        },
        // 顶部信息
        headerObj: {
          updateUserName: '',
          updateTime: '',
          status: ''
        },
        statusBtn: true,
        createStatus: false,
        detailObj: {}
      },
      dialogObj: '',
      contract: {
        contractId: this.$route.query.contractId,
        providerType: 0
      },
      bdData: [],
      inputStatus: false,
      businessTypeCode: '',
      logisticsMethodCodeMap: {
        1: 'pendingAnnualLogisticsSeaItemList', // 海运
        3: 'annualLogisticsRailwayItemList', // 铁运
        5: 'annualLogisticsTrunkItemList' // 物流
      },
      logisticsMethodCode: ''
    }
  },
  computed: {
    type() {
      return this.$route.query.type
    },
    showAttachMent() {
      return this.bdData.length > 0 || ['add', 'edit'].includes(this.type)
    }
  },
  methods: {
    // 铁运新增
    initRailwayAddData() {
      this.logisticsMethodCode = '3'
    },
    dialogParent(data) {
      this.dialogObj = data
    },
    getContractDetail(contractId) {
      this.$api.contract.contractDetailBuyer.getDetail(contractId).then((res) => {
        if (res.code == 200) {
          const data = res.data
          const _logisticsMethod = this.logisticsMethodCodeMap[data.logisticsMethodCode]
          this.childObj.detailObj = res.data
          for (const i in this.childObj.obj) {
            this.childObj.obj[i] = data[i]
          }
          this.bdData =
            res.data?.contractItemVoList[0]?.businessTypeCode === 'BTTCL006'
              ? res.data[_logisticsMethod] || []
              : res.data.contractItemVoList
          this.childObj.headerObj.updateTime = res.data.updateTime
          this.childObj.headerObj.updatePeople = res.data.updateUserName
          this.childObj.headerObj.status = res.data.status
          this.logisticsMethodCode = res.data.logisticsMethodCode
          this.inputStatus = true
        }
      })
    },
    toVoid() {
      this.$dialog({
        data: {
          title: this.$t('作废'),
          message: this.$t('是否确定作废该条合同？')
        },
        success: () => {
          this.toVoidRequest()
        }
      })
    },
    toVoidRequest() {
      this.$api.contract.queryBuyerListServe
        .contractAction('abandon', this.$route.query.contractId)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('作废成功'),
              type: 'success'
            })
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }
        })
    },
    revoke() {
      this.$dialog({
        data: {
          title: this.$t('撤销'),
          message: this.$t('是否确定撤销该条合同？')
        },
        success: () => {
          this.revokeRequest()
        }
      })
    },
    revokeRequest() {
      delete this.dialogObj.formObj.contractCategoryCodeStr
      this.$api.contract.queryBuyerListServe
        .contractAction('undo', this.$route.query.contractId)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('作废成功'),
              type: 'success'
            })
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }
        })
    },
    // 物流新增保存
    save() {
      this.$dialog({
        data: {
          title: this.$t('保存'),
          message: this.$t('是否确定保存？')
        },
        success: () => {
          this.saveRequest()
        }
      })
    },
    saveRequest() {
      let params = {
        ...this.dialogObj.formObj,
        annualLogisticsRailwayItemList: this.$refs.attactMent?.getTableData() || []
      }
      this.$api.contract.contractDetailBuyer.saveOrUpdate(params).then((res) => {
        if (res.code === 200) {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
          if (this.type === 'add') {
            this.$router.replace({
              path: '/middlePlatform/contractDetail',
              query: {
                type: 'edit',
                contractId: res.data
              }
            })
          }

          this.getContractDetail(res.data)
        }
      })
    },
    draft() {
      this.$dialog({
        data: {
          title: this.$t('保存草稿'),
          message: this.$t('是否确定保存草稿？')
        },
        success: () => {
          this.draftRequest()
        }
      })
    },
    draftRequest() {
      if (this.dialogObj.formObj?.contractSourceTypeCode !== 1) {
        delete this.dialogObj.formObj.contractCategoryCodeStr
        this.$api.contract.contractDetailBuyer.save(this.dialogObj.formObj).then(() => {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        })
      } else {
        let params = {
          ...this.dialogObj.formObj,
          createContractDirectlyMtPendingItemDtoList: this.bdData
        }
        params.purCompanyId = params.purCompanyId[0]
        delete params.contractCategoryCodeStr
        this.$api.contract.contractDetailBuyer.createDraftContractDirectlyApi(params).then(() => {
          this.$toast({
            content: this.$t('保存成功'),
            type: 'success'
          })
        })
      }
    },
    submitBtn(type = 'submit') {
      this.$dialog({
        data: {
          title: type === 'save' ? this.$t('保存') : this.$t('提交'),
          message: type === 'save' ? this.$t('确定要保存吗？') : this.$t('确定要提交吗？')
        },
        success: () => {
          this.submitBtnRequest(type)
        }
      })
    },
    submitBtnRequest(type) {
      delete this.dialogObj.formObj.contractCategoryCodeStr
      this.$api.contract.contractDetailBuyer.submit(this.dialogObj.formObj).then(() => {
        if (type !== 'save') {
          setTimeout(() => {
            this.$toast({
              content: this.$t('提交成功'),
              type: 'success'
            })
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }, 500)
          return
        }
        this.$toast({
          content: this.$t('保存成功'),
          type: 'success'
        })
      })
    },
    release() {
      this.$dialog({
        data: {
          title: this.$t('发布'),
          message: this.$t('是否确定发布？')
        },
        success: () => {
          this.releaseRequest()
        }
      })
    },
    releaseRequest() {
      if (this.dialogObj.formObj?.contractSourceTypeCode !== 1) {
        delete this.dialogObj.formObj.contractCategoryCodeStr
        this.$api.contract.contractDetailBuyer.publish(this.dialogObj.formObj).then(() => {
          this.$toast({
            content: this.$t('发布成功'),
            type: 'success'
          })
          setTimeout(() => {
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }, 500)
        })
      } else {
        let params = {
          ...this.dialogObj.formObj,
          createContractDirectlyMtPendingItemDtoList: this.bdData
        }
        params.purCompanyId = params.purCompanyId[0]
        delete params.contractCategoryCodeStr
        this.$api.contract.contractDetailBuyer.publishContractDirectlyApi(params).then(() => {
          this.$toast({
            content: this.$t('发布成功'),
            type: 'success'
          })
          setTimeout(() => {
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }, 500)
        })
      }
    }
  },
  created() {
    const contractId = this.$route.query.contractId
    if (contractId) {
      this.getContractDetail(contractId)
    }
    if (['add', 'edit'].includes(this.type)) {
      this.initRailwayAddData()
    }
  },
  mounted() {
    // 作废
    this.toVoidRequest = utils.debounce(this.toVoidRequest, 800)
    // 保存
    this.draftRequest = utils.debounce(this.draftRequest, 800)
    // 提交
    this.submitBtnRequest = utils.debounce(this.submitBtnRequest, 800)
    // 发布
    this.releaseRequest = utils.debounce(this.releaseRequest, 800)
  }
}
</script>
<style lang="scss" scoped></style>
