<template>
  <div class="contractDetail layouts">
    <div class="left-signed">
      <signedList :sidebar-url="sidebarUrl"></signedList>
    </div>
    <div class="right-list">
      <formInput
        :child-obj="childObj"
        :businessTypeCode="businessTypeCode"
        @dialogChild="dialogParent"
        v-if="inputStatus"
      ></formInput>
      <attachMent
        :tag="3"
        :data="bdData"
        :businessTypeCode="businessTypeCode"
        :logisticsMethodCode="logisticsMethodCode"
        :contract="contract"
        v-if="bdData.length > 0"
      ></attachMent>
    </div>
  </div>
</template>
<script>
import formInput from '@/views/contract/components/formBuyer.vue'
import attachMent from '@/views/contract/components/attachMent'
import signedList from './components/signedList.vue'
export default {
  components: {
    formInput,
    attachMent,
    signedList
  },
  data() {
    return {
      data: [],
      childObj: {
        obj: {
          contractCode: null, // 合同编码
          contractId: null, // 合同编码
          contractName: null, // 合同名称
          supplierCode: null, // 供应商编号
          supplierName: null, // 供应商名称
          purOrgId: null, // 采购组织
          purOrgName: null, // 采购组织名称
          purCompanyId: null, // 公司id
          purCompanyName: null, // 公司
          purchaseGroupId: null, // 采购组
          purchaseGroupName: null, // 采购组name
          contractIdRelation: null, // 关联合同
          paymentItemId: null, // 付款周期
          paymentItemName: null, // 付款周期Name
          contractTypeId: null, // 合同类型
          contractTypeName: null, // 合同类型名称
          currencyId: null, // 币种
          currencyName: null,
          purId: null, // 采购员
          purName: null, // 采购员Name
          effectiveBeginTime: null, // 生效日期
          effectiveEndTime: null, // 结束日期
          qaTimeLimitValue: null, // 质保期
          supplierContactId: null, // 供应商编码
          supplierContactName: null, // 供应商名称
          supplierContactPhone: null, // 供应商电话
          supplierContactMail: null, // 供应商邮箱
          supplierSpareEmail: null, // 供应商备用邮箱
          // purchaseOrgName: null,          //组织机构名称
          memo: null, // 备注
          contractTemplateId: null, // 合同模板
          taxesDesc: null, // 运费描述
          established: null, // 是否立项
          taxedTotalPrice: null, // 含税价格
          untaxedTotalPrice: null, // 不含税
          purchaseGroupCode: null, // 采购组
          contractCategoryCode: [], // 合同类别
          contractCategoryName: null, // 合同类别
          updateTime: null, // 更新时间
          updateUserName: null, // 更新人
          status: null, // 状态
          accountPeriod: null, // 账期天数
          isRelationCompany: null, // 是否关联公司
          supplierReply: null, // 反馈备注
          demandTypeName: null,
          logisticsMethodName: null
        },
        // disabled 控制
        inputDisabled: {
          contractCodeDisabled: true,
          contractNameDisabled: true,
          supplierCodeDisabled: true,
          supplierNameDisabled: true,
          orgGroupDisabled: true,
          companyDisabled: true,
          buyerGroupDisabled: true,
          agreementDisabled: true,
          dataArrDisabled: true,
          moneyArrDisabled: true,
          notTotalDisabled: true,
          totalDisabled: true,
          conditionDisabled: true,
          buyerDisabled: true,
          contactsDisabled: true,
          timeDisabled: true,
          telephoneDisabled: true,
          mailboxDisabled: true,
          contractTemplate: true,
          taxationDescribeDisabled: true,
          remarksDisabled: true,
          responseMemoDisabled: true,
          establishmentDisabled: true,
          contractCategoryDisabled: true,
          contractTemplateDisabled: true,
          accountPeriodDisabled: true,
          isRelationCompanyDisabled: true
        },
        // 验证
        rules: {
          dataArr: [{ required: true, message: this.$t('请选择合同类型'), trigger: 'blur' }]
        },
        // 顶部信息
        headerObj: {
          updatePeople: '',
          updateTime: '',
          status: 0
        },
        statusBtn: true,
        createStatus: false,
        seeStatus: true,
        detailObj: {}
      },
      dialogObj: '',
      contract: {
        contractId: this.$route.query.contractId,
        providerType: 0
      },
      bdData: [],
      sidebarUrl: '/purchase/simple/page',
      inputStatus: false,
      logisticsMethodCodeMap: {
        1: 'pendingAnnualLogisticsSeaItemList', // 海运
        3: 'annualLogisticsRailwayItemList', // 铁运
        5: 'annualLogisticsTrunkItemList' // 物流
      },
      logisticsMethodCode: ''
    }
  },
  methods: {
    dialogParent(data) {
      this.dialogObj = data
    },
    getContractDetail(contractId) {
      this.$api.contract.contractDetailBuyer.getPrview(contractId).then((res) => {
        if (res.code == 200) {
          const data = res.data
          const _logisticsMethod = this.logisticsMethodCodeMap[data.logisticsMethodCode]
          this.childObj.detailObj = res.data
          for (const i in this.childObj.obj) {
            this.childObj.obj[i] = data[i]
          }
          this.bdData =
            res.data?.contractItemVoList[0]?.businessTypeCode === 'BTTCL006'
              ? res.data[_logisticsMethod] || []
              : res.data.contractItemVoList
          this.bdData?.forEach((item, i) => {
            item.index = i + 1
          })
          this.childObj.headerObj.updateTime = res.data.updateTime
          this.childObj.headerObj.updatePeople = res.data.updateUserName
          this.childObj.headerObj.status = res.data.status
          this.businessTypeCode = res.data?.contractItemVoList[0]?.businessTypeCode
          this.logisticsMethodCode = res.data?.logisticsMethodCode
          this.inputStatus = true
        }
      })
    },
    toVoid() {
      this.$dialog({
        data: {
          title: this.$t('作废'),
          message: this.$t('是否确定作废该条合同？')
        },
        success: () => {
          this.toVoidRequest()
        }
      })
    },
    toVoidRequest() {
      this.$api.contract.queryBuyerListServe
        .contractAction('abandon', this.$route.query.contractId)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('作废成功'),
              type: 'success'
            })
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }
        })
    },
    revoke() {
      this.$dialog({
        data: {
          title: this.$t('撤销'),
          message: this.$t('是否确定撤销该条合同？')
        },
        success: () => {
          this.revokeRequest()
        }
      })
    },
    revokeRequest() {
      this.$api.contract.queryBuyerListServe
        .contractAction('undo', this.$route.query.contractId)
        .then((res) => {
          if (res.code == 200) {
            this.$toast({
              content: this.$t('作废成功'),
              type: 'success'
            })
            this.$router.push({
              path: '/middlePlatform/contractBuyerList',
              query: {
                tab: 1
              }
            })
          }
        })
    }
  },
  created() {
    const contractId = this.$route.query.contractId
    if (contractId) {
      this.getContractDetail(contractId)
    }
  }
}
</script>
<style lang="scss" scoped>
.layouts {
  display: flex;
  .left-signed {
    width: 292px;
    // height: calc(100% - 40px);
    // margin: 20px 20px 0 0;
  }
  .right-list {
    // width: calc(100% - 280px);
    overflow-x: auto;
  }
}
</style>
