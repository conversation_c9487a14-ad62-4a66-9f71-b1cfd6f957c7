import { i18n } from '@/main.js'
import moment from 'moment'
import Vue from 'vue'
const listToolBarBuyer = [
  {
    id: 'create',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建合同')
  }
]

const allListTool = [
  {
    id: 'OAProcess',
    icon: 'icon_solid_Createorder',
    title: i18n.t('OA审批进度')
  },
  {
    id: 'add',
    icon: 'icon_solid_Createorder',
    title: i18n.t('新增') // 支持干线
  }
]

const contractListTool = [
  {
    id: 'createOrder',
    icon: 'icon_solid_Createorder',
    title: i18n.t('创建采购订单')
  }
]
// 待签列表
const waitingListColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'pendingitem.purchaseRequestNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'pendingitem.purRequestCode',
    headerText: i18n.t('申请单号'),
    cssClass: ['href']
  },
  {
    field: 'pendingitem.purRequestName',
    headerText: i18n.t('申请名称')
  },
  {
    field: 'pendingitem.sourceName',
    headerText: i18n.t('来源')
  },
  {
    field: 'pendingitem.sourceCode',
    headerText: i18n.t('寻源单号')
  },
  {
    field: 'pendingitem.customProjectName',
    headerText: i18n.t('项目名称')
  },
  {
    field: 'pendingitem.supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'pendingitem.supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'pendingitem.itemCode',
    headerText: i18n.t('物料/品相编码')
  },
  {
    field: 'pendingitem.itemName',
    headerText: i18n.t('物料/品相名称')
  },
  {
    field: 'pendingitem.spec',
    headerText: i18n.t('规格型号')
  },
  {
    field: 'pendingitem.categoryName',
    headerText: i18n.t('品类')
  },
  {
    field: 'pendingitem.purRequirementName',
    headerText: i18n.t('需求名称')
  },
  {
    field: 'pendingitem.purRequirementDesc',
    headerText: i18n.t('需求描述')
  },
  {
    field: 'company.purCompanyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'pendingitem.purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'pendingitem.purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'pendingitem.stepQuote',
    headerText: i18n.t('是否阶梯'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'pendingitem.stageType',
    headerText: i18n.t('阶梯方式'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: {
        0: i18n.t('数量'),
        1: i18n.t('时间'),
        2: i18n.t('金额')
      }
    }
  },
  {
    field: 'pendingitem.stageList',
    headerText: i18n.t('阶梯报价'),
    template: () => {
      return {
        template: Vue.component('stagePrice', {
          template: `
        <div >
            <div @click='click' style="color:#005ca9; cursor: pointer;">{{ i18n.t("查看") }}</div>
        </div>
                   `,
          data: function () {
            return {
              data: {},
              i18n
            }
          },
          methods: {
            click() {
              this.$parent.$emit('handleClickCellTitle', {
                field: 'pendingitem.stageList',
                data: { ...this.data }
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'pendingitem.priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'pendingitem.unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'pendingitem.purUnitName',
    headerText: i18n.t('采购单位名称')
  },
  {
    field: 'pendingitem.conversionRate',
    headerText: i18n.t('转换率（%）'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return data * 100
      }
    }
  },
  {
    field: 'pendingitem.untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'pendingitem.untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },
  {
    field: 'pendingitem.taxRate',
    headerText: i18n.t('税率（%）'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return data * 100
      }
    }
  },
  {
    field: 'pendingitem.taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'pendingitem.taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  {
    field: 'pendingitem.currencyName',
    headerText: i18n.t('币种')
  },
  {
    field: 'pendingitem.validStartTime',
    headerText: i18n.t('价格开始时间')
  },
  {
    field: 'pendingitem.validEndTime',
    headerText: i18n.t('价格结束时间')
  },
  {
    field: 'pendingitem.requiredDeliveryDate',
    headerText: i18n.t('交期')
  },
  {
    field: 'pendingitem.stockSite',
    headerText: i18n.t('库存地点')
  }
]

// 合同列表
const allListColumnData = [
  {
    width: 50,
    type: 'checkbox'
  },
  {
    field: 'contractCode',
    headerText: i18n.t('合同编码'),
    width: 240,
    cellTools: [
      {
        id: 'edit',
        icon: 'icon_Editor',
        title: i18n.t('编辑'),
        visibleCondition: (data) => {
          return (
            (data.status == 0 || data.status == 1 || data.status == 4) &&
            data.effectiveStatus != 2 &&
            data.effectiveStatus != 3
          )
        }
      }
    ]
  },
  {
    field: 'contractName',
    headerText: i18n.t('合同名称')
  },
  // {
  //   field: 'contractIdRelation',
  //   headerText: i18n.t('关联合同编码'),
  //   width: 240,
  //   cssClass: ['href']
  // },
  // {
  //   field: 'contractNameRelation',
  //   headerText: i18n.t('关联合同名称')
  // },
  {
    field: 'status',
    width: 200,
    headerText: i18n.t('状态'),
    cellTools: [
      // {
      //   id: 'submit',
      //   icon: 'icon_Share_2',
      //   title: i18n.t('提交'),
      //   visibleCondition: (data) => {
      //     return (
      //       data.approvalStatus !== 0 &&
      //       data.status == 0 &&
      //       data.effectiveStatus != 2 &&
      //       data.effectiveStatus != 3
      //     )
      //   }
      // },
      {
        id: 'disable',
        icon: 'icon_list_recall',
        title: i18n.t('撤销'),
        visibleCondition: (data) => {
          return (
            data.approvalStatus !== 0 &&
            (data.status == 3 || data.status == 4 || data.status == 0 || data.status == 1) &&
            data.effectiveStatus != 2 &&
            data.effectiveStatus != 3
          )
        }
      },
      {
        id: 'publish',
        icon: 'icon_solid_finish',
        title: i18n.t('发布'),
        visibleCondition: (data) => {
          return (
            data.approvalStatus !== 0 &&
            (data.status == 1 || data.status == 4) &&
            data.effectiveStatus != 2 &&
            data.effectiveStatus != 3
          )
        }
      },
      {
        id: 'sign',
        icon: 'a-icon_list_concludeandsign',
        title: i18n.t('签订'),
        visibleCondition: (data) => {
          return (
            data.status == 3 &&
            data.approvalStatus != 0 &&
            data.approvalStatus != 1 &&
            data.effectiveStatus != 2 &&
            data.effectiveStatus != 3
          )
        }
      },
      {
        id: 'stop',
        icon: 'icon_solid_delete_2',
        title: i18n.t('作废'),
        visibleCondition: (data) => {
          //  合同状态:0=草稿,1=待发布,2=待反馈,3=反馈正常,4=反馈异常,5=已签订,6=已归档
          return (
            data.approvalStatus !== 0 &&
            (data.status == 3 || data.status == 4 || data.status == 5 || data.status == 6) &&
            data.effectiveStatus != 2 &&
            data.effectiveStatus != 3
          )
        }
      },
      {
        id: 'cancel_stop',
        icon: 'icon_solid_delete_2',
        title: i18n.t('取消作废'),
        visibleCondition: (data) => {
          // 生效状态:0=未生效,1=生效,2=已撤销,3=已作废，4=已失效
          return data.approvalStatus !== 0 && data.effectiveStatus == 3
        }
      },
      {
        id: 'archived',
        icon: 'a-icon_list_placeonfile',
        title: i18n.t('归档'),
        visibleCondition: (data) => {
          // 生效状态:0=未生效,1=生效,2=已撤销,3=已作废，4=已失效
          return (
            data.approvalStatus !== 0 &&
            (data.status == 5 || data.status == 6) &&
            data.effectiveStatus != 2 &&
            data.effectiveStatus != 3
          )
        }
      }
    ],
    valueConverter: {
      type: 'map',
      fields: {
        text: 'label',
        value: 'status'
      },
      map: [
        {
          status: 0,
          label: i18n.t('草稿'),
          cssClass: ['status info']
        },
        {
          status: 1,
          label: i18n.t('待发布'),
          cssClass: ['status warning']
        },
        {
          status: 2,
          label: i18n.t('待反馈'),
          cssClass: ['status warning']
        },
        {
          status: 3,
          label: i18n.t('反馈正常'),
          cssClass: ['status success']
        },
        {
          status: 4,
          label: i18n.t('反馈异常'),
          cssClass: ['status error']
        },
        {
          status: 5,
          label: i18n.t('已签订'),
          cssClass: ['status success']
        },
        {
          status: 6,
          label: i18n.t('已归档'),
          cssClass: ['status success']
        }
      ]
    }
  },
  {
    field: 'approvalStatus',
    headerText: i18n.t('审批状态'),
    valueConverter: {
      type: 'map',
      fields: {
        text: 'label',
        value: 'status'
      },
      map: [
        {
          status: 0,
          label: i18n.t('待审批'),
          cssClass: ['text text_waring']
        },
        {
          status: 1,
          label: i18n.t('审批通过'),
          cssClass: ['text text_success']
        },
        {
          status: 2,
          label: i18n.t('驳回'),
          cssClass: ['text text_fail']
        },
        {
          status: 3,
          label: i18n.t('未提交'),
          cssClass: ['text text_waring']
        },
        {
          status: 4,
          label: i18n.t('废弃'),
          cssClass: ['text text_fail']
        }
      ]
    }
  },
  {
    field: 'effectiveStatus',
    headerText: i18n.t('生效状态'),
    valueConverter: {
      type: 'map',
      fields: {
        text: 'label',
        value: 'status'
      },
      map: [
        {
          status: 0,
          label: i18n.t('未生效'),
          cssClass: ['text text_gray']
        },
        {
          status: 1,
          label: i18n.t('已生效'),
          cssClass: ['text text_success']
        },
        {
          status: 2,
          label: i18n.t('已撤销'),
          cssClass: ['text text_fail']
        },
        {
          status: 3,
          label: i18n.t('已作废'),
          cssClass: ['text text_gray']
        },
        {
          status: 4,
          label: i18n.t('已失效'),
          cssClass: ['text text_gray']
        }
      ]
    }
  },
  {
    field: 'logisticsMethodCode',
    headerText: i18n.t('运输方式'),
    valueConverter: {
      type: 'map',
      // fields: {
      //   text: 'label',
      //   value: 'status'
      // },
      map: [
        {
          value: '1',
          text: i18n.t('海运'),
          cssClass: ''
        },
        {
          value: '3',
          text: i18n.t('铁运'),
          cssClass: ''
        },
        {
          value: '5',
          text: i18n.t('干线'),
          cssClass: ''
        }
      ]
    }
  },
  {
    field: 'contractTypeName',
    headerText: i18n.t('合同类型')
  },
  {
    field: 'contractCategoryName',
    headerText: i18n.t('合同类别'),
    width: 200
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },

  {
    field: 'purCompanyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purGroupName',
    headerText: i18n.t('采购组')
  },
  {
    field: 'qaTimeLimitValue',
    headerText: i18n.t('质保期'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'signTime',
    headerText: i18n.t('签订时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'effectiveBeginTime',
    headerText: i18n.t('合同生效时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'effectiveEndTime',
    headerText: i18n.t('合同终止时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'archivedAttachmentList',
    headerText: i18n.t('归档附件'),
    cssClass: ['href'],
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (data.length > 0) {
          return i18n.t('查看附件列表')
        }
      }
    }
  },
  {
    field: 'supplierReply',
    headerText: i18n.t('反馈备注')
  },
  {
    field: 'taxesDesc',
    headerText: i18n.t('税费承担描述')
  },
  {
    field: 'memo',
    headerText: i18n.t('备注')
  }
]
// 合同明细
const contractlistColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'contract.contractCode',
    headerText: i18n.t('合同编码'),
    width: 240,
    cssClass: ['href']
  },
  {
    field: 'contract.contractName',
    headerText: i18n.t('合同名称')
  },
  {
    field: 'contract.status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: {
        text: 'label',
        value: 'status'
      },
      map: [
        {
          status: 0,
          label: i18n.t('草稿'),
          cssClass: ['status info']
        },
        {
          status: 1,
          label: i18n.t('待发布'),
          cssClass: ['status warning']
        },
        {
          status: 2,
          label: i18n.t('待反馈'),
          cssClass: ['status warning']
        },
        {
          status: 3,
          label: i18n.t('反馈正常'),
          cssClass: ['status success']
        },
        {
          status: 4,
          label: i18n.t('反馈异常'),
          cssClass: ['status error']
        },
        {
          status: 5,
          label: i18n.t('已签订'),
          cssClass: ['status success']
        },
        {
          status: 6,
          label: i18n.t('已归档'),
          cssClass: ['status success']
        }
      ]
    }
  },
  {
    field: 'contract.effectiveStatus',
    headerText: i18n.t('生效状态'),
    valueConverter: {
      type: 'map',
      fields: {
        text: 'label',
        value: 'status'
      },
      map: [
        {
          status: 0,
          label: i18n.t('未生效'),
          cssClass: ['text text_gray']
        },
        {
          status: 1,
          label: i18n.t('已生效'),
          cssClass: ['text text_success']
        },
        {
          status: 2,
          label: i18n.t('已撤销'),
          cssClass: ['text text_fail']
        },
        {
          status: 3,
          label: i18n.t('已作废'),
          cssClass: ['text text_gray']
        },
        {
          status: 4,
          label: i18n.t('已失效'),
          cssClass: ['text text_gray']
        }
      ]
    }
  },
  {
    field: 'contractItem.purRequirementRealNum',
    headerText: i18n.t('已建订单'),
    // ignore: true,
    searchOptions: {
      renameField: 'item.purRequirementRealNum',
      elementType: 'select',
      // operator: 'in',
      multiple: true,
      dataSource: [
        {
          text: '是',
          value: 1
        },
        {
          text: '否',
          value: 0
        }
      ]
    },
    // valueConverter: {
    //   type: 'function',
    //   filter: (data) => {
    //     return data <= 0 ? '是' : '否'
    //   }
    // },,
    template: () => {
      return {
        template: Vue.component('stagePrice', {
          template: `
        <div >
            <span v-if="data.contractItem.purRequirementRealNum > 0">是</span>
            <span v-else>否</span>
        </div>
                   `
        })
      }
    }
    // valueConverter: {
    //   type: 'map',
    //   fields: {
    //     text: 'label',
    //     value: 'status'
    //   },
    //   map: [
    //     {
    //       status: 0,
    //       label: i18n.t('否'),
    //       cssClass: ['text text_gray']
    //     },
    //     {
    //       status: 1,
    //       label: i18n.t('是'),
    //       cssClass: ['text text_success']
    //     }
    //   ]
    // }
  },
  {
    field: 'contractItem.sourceCode',
    headerText: i18n.t('寻源单号'),
    searchOptions: {
      renameField: 'item.sourceCode'
    }
  },
  {
    field: 'contract.customProjectName',
    headerText: i18n.t('项目名称')
  },
  {
    field: 'contract.contractIdRelation',
    headerText: i18n.t('关联合同编码'),
    width: 240,
    cssClass: ['href']
  },

  {
    field: 'pendingItem.purRequestCode',
    headerText: i18n.t('关联采购申请号'),
    cssClass: ['href'],
    searchOptions: {
      renameField: 'pending.purRequestCode'
    }
  },
  {
    field: 'contract.businessTypeName',
    headerText: i18n.t('关联申请业务类型')
  },
  {
    field: 'company.purCompanyCode',
    headerText: i18n.t('公司编码')
  },
  {
    field: 'company.purCompanyName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'contract.purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'contract.purGroupName',
    headerText: i18n.t('采购组')
  },

  {
    field: 'contract.purName',
    headerText: i18n.t('采购员')
  },
  {
    field: 'contractItem.lineNo',
    headerText: i18n.t('行号')
  },
  {
    field: 'contract.supplierCode',
    headerText: i18n.t('供应商编码')
  },
  {
    field: 'contract.supplierName',
    headerText: i18n.t('供应商名称')
  },
  {
    field: 'contractItem.stepQuote',
    headerText: i18n.t('是否阶梯'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: {
        0: i18n.t('否'),
        1: i18n.t('是')
      }
    }
  },
  {
    field: 'contractItem.stageType',
    headerText: i18n.t('阶梯方式'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: {
        0: i18n.t('数量'),
        1: i18n.t('时间'),
        2: i18n.t('金额')
      }
    }
  },
  {
    field: 'contractItem.stageList',
    headerText: i18n.t('阶梯报价'),
    template: () => {
      return {
        template: Vue.component('stagePrice', {
          template: `
        <div >
            <div @click='click' style="color:#005ca9; cursor: pointer;">{{ i18n.t("查看") }}</div>
        </div>
                   `,
          data: function () {
            return {
              data: {},
              i18n
            }
          },
          methods: {
            click() {
              this.$parent.$emit('handleClickCellTitle', {
                field: 'contractItem.stageList',
                data: { ...this.data }
              })
            }
          }
        })
      }
    }
  },
  {
    field: 'contractItem.itemCode',
    headerText: i18n.t('物料/品相编码')
  },
  {
    field: 'contractItem.itemName',
    headerText: i18n.t('物料/品相名称')
  },
  {
    field: 'pendingItem.purRequirementName',
    headerText: i18n.t('需求名称'),
    searchOptions: {
      renameField: 'pending.purRequirementName'
    }
  },
  {
    field: 'pendingItem.purRequirementDesc',
    headerText: i18n.t('需求描述'),
    searchOptions: {
      renameField: 'pending.purRequirementDesc'
    }
  },
  {
    field: 'pendingItem.purRequirementNum',
    headerText: i18n.t('需求数量'),
    searchOptions: {
      renameField: 'pending.purRequirementNum'
    }
  },
  {
    field: 'contractItem.purRequirementRemainNum',
    headerText: i18n.t('可创建数量')
  },
  {
    field: 'contractItem.priceUnit',
    headerText: i18n.t('价格单位')
  },
  {
    field: 'contractItem.unitName',
    headerText: i18n.t('基本单位')
  },
  {
    field: 'contractItem.purUnitName',
    headerText: i18n.t('采购单位名称')
  },
  {
    field: 'contractItem.conversionRate',
    headerText: i18n.t('转换率（%）'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return data * 100
      }
    }
  },
  {
    field: 'contractItem.taxRate',
    headerText: i18n.t('税率（%）'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return data * 100
      }
    }
  },
  {
    field: 'contractItem.untaxedUnitPrice',
    headerText: i18n.t('未税单价')
  },
  {
    field: 'contractItem.untaxedTotalPrice',
    headerText: i18n.t('未税总价')
  },

  {
    field: 'contractItem.taxedUnitPrice',
    headerText: i18n.t('含税单价')
  },
  {
    field: 'contractItem.taxedTotalPrice',
    headerText: i18n.t('含税总价')
  },
  {
    field: 'contract.effectiveBeginTime',
    headerText: i18n.t('合同生效时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'contract.effectiveEndTime',
    headerText: i18n.t('合同终止时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'contractItem.priceValidStartTime',
    headerText: i18n.t('价格开始时间')
  },
  {
    field: 'contractItem.priceValidEndTime',
    headerText: i18n.t('价格结束时间')
  },
  {
    field: 'contractItem.requiredDeliveryDate',
    headerText: i18n.t('交期')
  },
  {
    field: 'contractItem.stockSite',
    headerText: i18n.t('库存地点')
  },
  {
    field: 'company.siteName',
    headerText: i18n.t('地点/工厂')
  }
]
export const pageConfig = (_t) => [
  {
    title: i18n.t('待签列表'),
    toolbar: listToolBarBuyer,
    useToolTemplate: false,
    gridId: '6750815e-0ea6-11ee-b83a-525400cfb560',
    grid: {
      columnData: waitingListColumnData,
      // dataSource: watingListData,
      asyncConfig: {
        url: _t.$api.contract.queryBuyerListServe.waitingListApi
      }
    }
  },
  {
    title: i18n.t('合同列表'),
    useToolTemplate: false,
    toolbar: allListTool,
    gridId: '675082a8-0ea6-11ee-89ef-525400cfb560',
    grid: {
      columnData: allListColumnData,
      asyncConfig: {
        url: _t.$api.contract.queryBuyerListServe.contractListApi
      }
      //  dataSource: allListData,
    }
  },
  {
    title: i18n.t('合同明细档案'),
    toolbar: contractListTool,
    useToolTemplate: false,
    gridId: '6750837a-0ea6-11ee-a999-525400cfb560',
    grid: {
      columnData: contractlistColumnData,
      asyncConfig: {
        url: _t.$api.contract.queryBuyerListServe.contractItemApi
      }
      //  dataSource: contractListData
    }
  }
]

const privewColum = [
  {
    headerText: i18n.t('文件名'),
    field: 'fileName',
    cssClass: ['href']
  },
  {
    headerText: i18n.t('文件类型'),
    field: 'fileType'
  },
  {
    headerText: i18n.t('文件大小'),
    field: 'fileSize',
    template: () => {
      return {
        template: Vue.component('fileSize', {
          data() {
            return {
              data: {}
            }
          },
          template:
            "<span>{{ (data.fileSize/1024)>1024?(data.fileSize/1024/1024).toFixed('2')+'MB':(data.fileSize/1024).toFixed('2')+'KB'}}</span>"
        })
      }
    }
  },
  {
    headerText: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    headerText: i18n.t('创建时间'),
    field: 'createTime',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
]

const toolbarEmpty = {
  useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [[], []]
}
export const privewConfig = [
  {
    toolbar: toolbarEmpty,
    useToolTemplate: false,
    grid: {
      allowPaging: false,
      columnData: privewColum,
      dataSource: []
    }
  }
]
