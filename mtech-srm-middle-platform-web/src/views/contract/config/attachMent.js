import { i18n } from '@/main.js'
import Vue from 'vue'
import moment from 'moment'
import simpleInput from '../components/simpleInput.vue'
import dayjs from 'dayjs'
const raiwayModeList = [
  { value: 'SOC', text: 'SOC' },
  { value: 'COC', text: 'COC' },
  { value: 'DTD', text: '门到站' }
]
// 海运
const seaColumnData = () => {
  return [
    {
      field: 'index',
      headerText: i18n.t('序号')
    },
    {
      field: 'countryName',
      headerText: i18n.t('国家')
    },
    {
      field: 'polName',
      headerText: i18n.t('POL')
    },
    {
      field: 'podName',
      headerText: i18n.t('POD')
    },
    {
      field: 'lineName',
      headerText: i18n.t('TCL LINE')
    },
    {
      field: 'mainLineName',
      headerText: i18n.t('MAIN LINE')
    },
    {
      field: 'transportTermsName',
      headerText: i18n.t('运输条款')
    },
    {
      field: 'tradeTermsName',
      headerText: i18n.t('贸易条款')
    },
    {
      field: 'receivePlace',
      headerText: i18n.t('收货地')
    },
    {
      field: 'endAddress',
      headerText: i18n.t('目的地')
    },
    {
      field: 'endTransportMethodName',
      headerText: i18n.t('目的港运输方式')
    },
    {
      field: 'untaxedTotalPrice',
      headerText: i18n.t('未税总价')
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('含税总价')
    },
    {
      field: 'startTime',
      headerText: i18n.t('价格开始时间')
    },
    {
      field: 'endTime',
      headerText: i18n.t('价格结束时间')
    },
    {
      field: 'purchaseApplyCode',
      headerText: i18n.t('申请单号')
    },
    {
      field: 'rfxCode',
      headerText: i18n.t('寻源单号')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称')
    }
  ]
}

// 铁运
const railwayColumnData = (type) => {
  return [
    {
      type: 'checkbox',
      width: 50
    },
    {
      field: 'index',
      headerText: i18n.t('序号'),
      width: ['edit', 'add'].includes(type) ? 0 : 100,
      allowEditing: false
    },
    {
      field: 'railwayMode',
      headerText: i18n.t('铁运模式'),
      valueAccessor: (field, data) => {
        return raiwayModeList.find((item) => item.value === data[field])?.text
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-select
              v-model={scoped.railwayMode}
              fields={{ text: 'text', value: 'value' }}
              dataSource={raiwayModeList}
              onChange={(e) => {
                scoped.railwayModeName = e.itemData.text
              }}
            />
          </div>
        )
      }
    },
    {
      field: 'startPlaceName',
      headerText: i18n.t('起运地')
    },
    {
      field: 'loadingAddr',
      headerText: i18n.t('装货地')
    },
    {
      field: 'domesticStartStationName',
      headerText: i18n.t('国内始发站')
    },
    {
      field: 'endStationName',
      headerText: i18n.t('目的站')
    },
    {
      field: 'pickupLocation',
      headerText: i18n.t('提箱地点')
    },
    {
      field: 'untaxedTotalPrice',
      headerText: i18n.t('未税总价'),
      editorRender(h, scoped) {
        scoped.quantity = scoped.quantity ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.untaxedTotalPrice}
              min={0}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('含税总价'),
      editorRender(h, scoped) {
        scoped.quantity = scoped.quantity ?? 0
        return (
          <div>
            <mt-input-number
              v-model={scoped.taxedTotalPrice}
              min={0}
              placeholder={i18n.t('请输入')}
            />
          </div>
        )
      }
    },
    {
      field: 'priceValidStartTime',
      headerText: i18n.t('价格开始日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data.priceValidStartTime) {
                  return dayjs(this.data.priceValidStartTime).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-date-picker
              v-model={scoped.priceValidStartTime}
              placeholder={i18n.t('请选择')}
              show-clear-button={false}
            />
          </div>
        )
      }
    },
    {
      field: 'priceValidEndTime',
      headerText: i18n.t('价格结束日期'),
      template: () => {
        return {
          template: Vue.component('headers', {
            template: `<span>{{ getTimeFmt() }}</span>`,
            methods: {
              getTimeFmt() {
                if (this.data.priceValidEndTime) {
                  return dayjs(this.data.priceValidEndTime).format('YYYY-MM-DD')
                }
                return ''
              }
            }
          })
        }
      },
      editorRender(h, scoped) {
        return (
          <div>
            <mt-date-picker
              v-model={scoped.priceValidEndTime}
              placeholder={i18n.t('请选择')}
              show-clear-button={false}
            />
          </div>
        )
      }
    }
  ]
}
// 干线
const trunkColumnData = () => {
  return [
    {
      field: 'index',
      headerText: i18n.t('序号'),
      allowEditing: false
    },
    {
      field: 'deliverAddr',
      headerText: i18n.t('发货站')
    },
    {
      field: 'productline',
      headerText: i18n.t('产品线')
    },
    {
      field: 'routeName',
      headerText: i18n.t('线路')
    },
    {
      field: 'destProvinceName',
      headerText: i18n.t('目的地省')
    },
    {
      field: 'destCityName',
      headerText: i18n.t('目的地市')
    },
    {
      field: 'destAreaName',
      headerText: i18n.t('目的地区/县')
    },
    {
      field: 'untaxedTotalPrice',
      headerText: i18n.t('未税总价')
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('含税总价')
    },
    {
      field: 'taxRate',
      headerText: i18n.t('税率')
    },
    {
      field: 'startTime',
      headerText: i18n.t('价格开始时间')
    },
    {
      field: 'endTime',
      headerText: i18n.t('价格结束时间')
    },
    {
      field: 'purchaseApplyCode',
      headerText: i18n.t('申请单号')
    },
    {
      field: 'rfxCode',
      headerText: i18n.t('寻源单号')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称')
    }
  ]
}

const columnData = (tag, logisticsMethodCode, type) => {
  let replay = []
  if (tag == 2) {
    replay = [
      {
        field: 'replyMemo',
        headerText: i18n.t('反馈备注'),
        template: function () {
          return {
            template: simpleInput
          }
        }
      }
    ]
  } else {
    replay = [
      {
        field: 'replyMemo',
        headerText: i18n.t('反馈备注'),
        allowEditing: false
      }
    ]
  }
  if (logisticsMethodCode === '1') {
    // 海运
    return seaColumnData().concat(replay)
  }
  if (logisticsMethodCode === '3') {
    // 铁运
    return railwayColumnData(type).concat(replay)
  }
  if (logisticsMethodCode === '5') {
    // 干线
    return trunkColumnData().concat(replay)
  }
  return [
    {
      field: 'lineNo',
      headerText: i18n.t('行号')
    },
    {
      field: 'itemCode',
      headerText: i18n.t('物料/品相编码')
    },
    {
      field: 'itemName',
      headerText: i18n.t('物料/品相名称')
    },
    {
      field: 'categoryName',
      headerText: i18n.t('品类名称')
    },
    {
      field: 'spec',
      headerText: i18n.t('规格型号')
    },
    {
      field: 'customProjectName',
      headerText: i18n.t('项目名称')
    },
    {
      field: 'purRequirementName',
      headerText: i18n.t('需求名称')
    },
    {
      field: 'purRequirementDesc',
      headerText: i18n.t('需求描述')
    },
    {
      field: 'purRequirementNum',
      headerText: i18n.t('需求数量')
    },

    {
      field: 'siteName',
      headerText: i18n.t('地点/工厂')
    },
    {
      field: 'priceUnit',
      headerText: i18n.t('价格单位')
    },
    {
      field: 'unitName',
      headerText: i18n.t('基本单位')
    },
    {
      field: 'purUnitName',
      headerText: i18n.t('采购单位名称')
    },
    {
      field: 'conversionRate',
      headerText: i18n.t('转换率（%）'),
      valueConverter: {
        type: 'function',
        filter: (data) => {
          if (!data) return
          return data * 100
        }
      }
    },
    {
      field: 'taxRate',
      headerText: i18n.t('税率（%）'),
      valueConverter: {
        type: 'function',
        filter: (data) => {
          if (!data) return
          return data * 100
        }
      }
    },
    {
      field: 'stepQuote',
      headerText: i18n.t('是否阶梯'),
      valueConverter: {
        type: 'map',
        fields: { text: 'label', value: 'status' },
        map: {
          0: i18n.t('否'),
          1: i18n.t('是')
        }
      }
    },
    {
      field: 'stageType',
      headerText: i18n.t('阶梯方式'),
      valueConverter: {
        type: 'map',
        fields: { text: 'label', value: 'status' },
        map: {
          0: i18n.t('数量'),
          1: i18n.t('时间'),
          2: i18n.t('金额')
        }
      }
    },
    {
      field: 'stageList',
      headerText: i18n.t('阶梯报价'),
      template: () => {
        return {
          template: Vue.component('stagePrice', {
            template: `
        <div >
            <div @click='click' style="color:#005ca9; cursor: pointer;">{{ i18n.t("查看") }}</div>
        </div>
                   `,
            data: function () {
              return {
                data: {},
                i18n
              }
            },
            methods: {
              click() {
                this.$parent.$emit('handleClickCellTitle', {
                  field: 'stageList',
                  data: { ...this.data }
                })
              }
            }
          })
        }
      }
    },
    {
      field: 'untaxedUnitPrice',
      headerText: i18n.t('未税单价')
    },
    {
      field: 'untaxedTotalPrice',
      headerText: i18n.t('未税总价')
    },

    {
      field: 'taxedUnitPrice',
      headerText: i18n.t('含税单价')
    },
    {
      field: 'taxedTotalPrice',
      headerText: i18n.t('含税总价')
    },
    {
      field: 'priceValidStartTime',
      headerText: i18n.t('价格开始时间')
    },
    {
      field: 'priceValidEndTime',
      headerText: i18n.t('价格结束时间')
    },
    {
      field: 'stockSite',
      headerText: i18n.t('库存地址')
    },
    {
      field: 'requiredDeliveryDate',
      headerText: i18n.t('交期')
    }
  ].concat(replay)
}
const toolbar = {
  useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [[], []]
}
const editToolbar = {
  useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    [
      {
        id: 'add',
        icon: 'icon_solid_upload',
        title: i18n.t('新增')
      },
      {
        id: 'delete',
        icon: 'icon_list_download',
        title: i18n.t('删除')
      }
    ],
    []
  ]
}

const toolBarFileGf = (tag) => {
  let tools = []
  // 1是采方 2是供方 3查看
  if (tag == 1) {
    tools = [
      {
        id: 'upload',
        icon: 'icon_solid_upload',
        title: i18n.t('上传')
      },
      {
        id: 'download',
        icon: 'icon_list_download',
        title: i18n.t('下载')
      },
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  } else if (tag == 2 || tag == 3) {
    tools = [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  } else {
    tools = []
  }
  return {
    useBaseConfig: false,
    tools: [tools, ['refresh']]
  }
}
const toolBarFileCf = (tag) => {
  // 1是采方 2是供方
  let tools = []
  // 1是采方 2是供方
  if (tag == 2) {
    tools = [
      {
        id: 'upload',
        icon: 'icon_solid_upload',
        title: i18n.t('上传')
      },
      {
        id: 'download',
        icon: 'icon_list_download',
        title: i18n.t('下载')
      },
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除')
      }
    ]
  } else if (tag == 1 || tag == 3) {
    tools = [
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  } else {
    tools = []
  }
  return {
    useBaseConfig: false,
    tools: [tools, ['refresh']]
  }
}
const columnDataFileCf = (tag) => [
  {
    type: 'checkbox',
    width: 50
  },
  {
    // tag:1 只可以删除自己上传的附件，其他的只有下载的权限
    headerText: i18n.t('文件名'),
    field: 'fileName',
    cellTools: [
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => {
          return tag == 1
        }
      },
      {
        id: 'download',
        icon: 'icon_list_download',
        title: i18n.t('下载'),
        visibleCondition: () => {
          return tag == 1 || tag == 3
        }
      }
    ]
  },
  {
    headerText: i18n.t('文件类型'),
    field: 'fileType'
  },
  {
    headerText: i18n.t('文件大小'),
    field: 'fileSize',
    template: () => {
      return {
        template: Vue.component('fileSize', {
          data() {
            return {
              data: {}
            }
          },
          template:
            "<span>{{ (data.fileSize/1024)>1024?(data.fileSize/1024/1024).toFixed('2')+'MB':(data.fileSize/1024).toFixed('2')+'KB'}}</span>"
        })
      }
    }
  },
  {
    headerText: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    headerText: i18n.t('创建时间'),
    field: 'createTime',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
]
const columnDataFileGf = (tag) => [
  {
    type: 'checkbox',
    width: 50
  },
  {
    // tag:1 只可以删除自己上传的附件，其他的只有下载的权限
    headerText: i18n.t('文件名'),
    width: 350,
    field: 'fileName',
    cellTools: [
      {
        id: 'del',
        icon: 'icon_solid_Delete',
        title: i18n.t('删除'),
        visibleCondition: () => {
          return tag == 2
        }
      },
      {
        id: 'download',
        icon: 'icon_solid_Createorder',
        title: i18n.t('下载'),
        visibleCondition: () => {
          return tag == 2 || tag == 3
        }
      }
    ]
  },
  {
    headerText: i18n.t('文件类型'),
    field: 'fileType'
  },
  {
    headerText: i18n.t('文件大小'),
    field: 'fileSize',
    template: () => {
      return {
        template: Vue.component('fileSize', {
          data() {
            return {
              data: {}
            }
          },
          template:
            "<span>{{ (data.fileSize/1024)>1024?(data.fileSize/1024/1024).toFixed('2')+'MB':(data.fileSize/1024).toFixed('2')+'KB'}}</span>"
        })
      }
    }
  },
  {
    headerText: i18n.t('创建人'),
    field: 'createUserName'
  },
  {
    headerText: i18n.t('创建时间'),
    field: 'createTime',
    valueConverter: {
      type: 'function',
      filter: (data) => {
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
]
export const addAttachMentPageConfig = (tag, logisticsMethodCode, type) => {
  const config = [
    {
      title: i18n.t('标的明细'),
      useToolTemplate: false,
      toolbar: editToolbar,
      isUseCustomEditor: true,
      grid: {
        columnData: columnData(tag, logisticsMethodCode, type),
        editSettings: {
          allowEditing: true,
          allowAdding: true,
          allowDeleting: true,
          newRowPosition: 'Top'
        }
      }
    }
  ]
  if (type === 'edit') {
    config.push({ title: i18n.t('附件') })
  }
  return config
}
export const attachMentPageConfig = (tag, logisticsMethodCode) => [
  {
    title: i18n.t('标的明细'),
    useToolTemplate: false,
    toolbar,
    grid: {
      columnData: columnData(tag, logisticsMethodCode)
      // dataSource
    }
  },
  {
    title: i18n.t('附件')
  }
]
export const FilePageConfig = (contractId, tag, e) => [
  {
    title: i18n.t('采方'),
    useToolTemplate: false,
    toolbar: toolBarFileGf(tag),
    grid: {
      columnData: columnDataFileCf(tag),
      allowPaging: false,
      asyncConfig: {
        url: '/contract/attachment',
        methods: 'get',
        params: {
          contractId: contractId,
          attachmentType: e
        },
        recordsPosition: 'data'
      }
    }
  },
  {
    title: i18n.t('供方'),
    useToolTemplate: false,
    toolbar: toolBarFileCf(tag),
    grid: {
      columnData: columnDataFileGf(tag),
      allowPaging: false,
      asyncConfig: {
        url: '/contract/attachment',
        methods: 'get',
        params: {
          contractId: contractId,
          attachmentType: e
        },
        recordsPosition: 'data'
      }
      // dataSource: dataSourceFile
    }
  }
]
