import { i18n } from '@/main.js'
import moment from 'moment'
export const listColumnData = [
  {
    field: 'contractCode',
    headerText: i18n.t('合同编码'),
    width: 250,
    cellTools: []
  },
  {
    field: 'contractName',
    headerText: i18n.t('合同名称'),
    cssClass: ''
  },
  // {
  //   field: 'contractIdRelation',
  //   headerText: i18n.t('关联合同编号'),
  //   cssClass: ''
  // },
  // {
  //   field: 'contractNameRelation',
  //   headerText: i18n.t('关联合同名称')
  // },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: [
        { status: 2, label: i18n.t('待反馈'), cssClass: ['btns cancel'] },
        { status: 3, label: i18n.t('反馈正常'), cssClass: ['btns green'] },
        { status: 4, label: i18n.t('反馈异常'), cssClass: ['btns red'] },
        { status: 5, label: i18n.t('已签订'), cssClass: ['btns green'] },
        { status: 6, label: i18n.t('已归档'), cssClass: ['btns green'] }
      ]
    }
  },
  {
    field: 'approvalStatus',
    headerText: i18n.t('审批状态'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'approvalStatus' },
      map: [
        { approvalStatus: 0, label: i18n.t('待审批'), cssClass: ['text text_waring'] },
        { approvalStatus: 1, label: i18n.t('审批完成'), cssClass: ['text text_success'] },
        { approvalStatus: 2, label: i18n.t('驳回'), cssClass: ['text text_fail'] },
        {
          approvalStatus: 3,
          label: i18n.t('未提交'),
          cssClass: ['text text_waring']
        }
      ]
    }
  },
  {
    field: 'supplierReply',
    headerText: i18n.t('反馈备注')
  },
  {
    field: 'memo',
    headerText: i18n.t('备注')
  },
  {
    field: 'contractTypeName',
    headerText: i18n.t('合同类型')
  },
  {
    field: 'contractCategoryName',
    headerText: i18n.t('合同类别')
  },
  {
    field: 'supplierCode',
    headerText: i18n.t('供应商编号')
  },
  {
    field: 'supplierName',
    headerText: i18n.t('供应商名称')
  },

  {
    field: 'purOrgName',
    headerText: i18n.t('采购组织')
  },
  {
    field: 'purCompanyName',
    headerText: i18n.t('公司')
  },
  {
    field: 'purGroupName', // 1
    headerText: i18n.t('采购组')
  },
  {
    field: 'createUserName',
    headerText: i18n.t('创建人')
  },
  {
    field: 'createTime',
    headerText: i18n.t('创建时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'signTime',
    headerText: i18n.t('签订时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'effectiveBeginTime',
    headerText: i18n.t('合同生效时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  },
  {
    field: 'effectiveEndTime',
    headerText: i18n.t('合同终止时间'),
    valueConverter: {
      type: 'function',
      filter: (data) => {
        if (!data) return
        return moment(data).format('YYYY-MM-DD HH:mm:ss')
      }
    }
  }
]
export const pageConfig = (_t) => [
  {
    toolbar: listToolBar1,
    useToolTemplate: false,
    gridId: '67505c2e-0ea6-11ee-b86d-525400cfb560',
    grid: {
      columnData: listColumnData,
      // dataSource:listData,
      asyncConfig: {
        url: _t.$api.contract.querySupplierListServe.supplierList
      }
    }
  }
]
console.log(pageConfig, 77777)
export const listToolBar1 = {
  useBaseConfig: true, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [[], ['Refresh', 'filter', 'setting']]
}
