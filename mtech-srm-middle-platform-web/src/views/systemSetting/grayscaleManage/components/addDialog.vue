<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form class="dialogForm" ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="title" :label="$t('灰度配置标题')">
        <mt-input v-model="formData.title" :placeholder="$t('灰度配置标题')"></mt-input>
      </mt-form-item>
      <mt-form-item prop="code" :label="$t('灰度配置编码')">
        <mt-input v-model="formData.code" :placeholder="$t('灰度配置编码')"></mt-input>
      </mt-form-item>
      <mt-form-item prop="allowType" :label="$t('允许类型')">
        <mt-select
          :allow-filtering="true"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="allowTypeList"
          v-model="formData.allowType"
          :placeholder="$t('允许类型')"
          @change="allowTypeChange"
        >
        </mt-select>
      </mt-form-item>
      <mt-form-item prop="noUserAllowType" :label="$t('查询用户为空允许类型')">
        <mt-select
          :allow-filtering="true"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="allowTypeList"
          v-model="formData.noUserAllowType"
          :placeholder="$t('查询用户为空允许类型')"
          @change="noUserAllowTypeChange"
        >
        </mt-select>
      </mt-form-item>
      <mt-form-item prop="grayType" :label="$t('灰度类型')">
        <mt-select
          :allow-filtering="true"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="grayTypeList"
          v-model="formData.grayType"
          :placeholder="$t('灰度类型')"
          @change="grayTypeChange"
        >
        </mt-select>
      </mt-form-item>
      <mt-form-item prop="scaleValue" :label="$t('灰度比例') + '%'">
        <mt-input v-model="formData.scaleValue" :placeholder="$t('请输入')"></mt-input>
      </mt-form-item>
      <mt-form-item
        v-if="formData.grayType === 'customize'"
        prop="canaryConfigUserSaveRequestList"
        :label="$t('用户')"
      >
        <mt-multi-select
          v-model="formData.canaryConfigUserSaveRequestList"
          id="dropDownTreeCom"
          style="width: 100%"
          :fields="{
            text: 'employeeName',
            value: 'account'
          }"
          :data-source="userArrList"
          filter-bar-:placeholder="$t('Search')"
          :allow-filtering="true"
          :filtering="getUserList"
          :placeholder="$t('用户')"
          :filter-bar-placeholder="$t('请输入用户名称进行搜索')"
          :no-records-template="noRecordsTemplate"
        ></mt-multi-select>
      </mt-form-item>
      <mt-form-item prop="status" :label="$t('配置状态')">
        <!-- <mt-input v-model="formData.grayType" :placeholder="$t('灰度类型')"></mt-input> -->
        <mt-select
          :allow-filtering="true"
          :fields="{ text: 'text', value: 'value' }"
          :data-source="statusList"
          v-model="formData.status"
          :placeholder="$t('配置状态')"
        >
        </mt-select>
      </mt-form-item>
      <mt-form-item prop="remark" :label="$t('备注')" class="full-width">
        <mt-input v-model="formData.remark" :placeholder="$t('请输入')"></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
import { grayTypeList, statusList, allowTypeList } from '../config/index'
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      grayTypeList,
      statusList,
      allowTypeList,
      dialogTitle: this.$t('新增'),
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        title: '',
        code: '',
        grayType: '',
        allowType: '',
        noUserAllowType: '',
        scaleValue: '',
        canaryConfigUserSaveRequestList: [], // 用户已选列表id
        status: 0,
        remark: '',
        version: 0
      },

      editType: 'add',
      userArrList: [], // 用户可选列表
      noRecordsTemplate: this.$t('请输入用户名称进行搜索'),

      rules: {
        title: [
          {
            required: true,
            message: this.$t('灰度配置标题不能为空'),
            trigger: 'blur'
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('灰度配置编码不能为空'),
            trigger: 'blur'
          }
        ],
        grayType: [
          {
            required: true,
            message: this.$t('灰度类型不能为空'),
            trigger: 'blur'
          }
        ],
        allowType: [
          {
            required: true,
            message: this.$t('允许类型不能为空'),
            trigger: 'blur'
          }
        ],
        noUserAllowType: [
          {
            required: true,
            message: this.$t('查询用户为空允许类型'),
            trigger: 'blur'
          }
        ],
        scaleValue: [
          {
            required: true,
            message: this.$t('灰度比例不能为空'),
            trigger: 'blur'
          }
        ],
        // canaryConfigUserSaveRequestList: [
        //   {
        //     required: true,
        //     message: this.$t('用户不能为空'),
        //     trigger: 'blur'
        //   }
        // ],
        status: [
          {
            required: true,
            message: this.$t('配置状态不能为空'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  mounted() {
    if (this.dialogData && this.dialogData.formData) {
      this.formData = this.dialogData.formData
      const params = {
        id: this.formData.id,
        page: {
          current: 1,
          size: 999
        }
      }
      this.$api.systemSetting.getGrayUserList(params).then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.userArrList = data.records.map((i) => {
            return {
              ...i,
              employeeName: i.account + '-' + i.accountName
            }
          })
          this.formData.canaryConfigUserSaveRequestList = data.records.map((i) => i.account)
        }
      })
    }
    if (this.dialogData && this.dialogData.editType) {
      this.editType = this.dialogData.editType
      this.dialogTitle = this.$t('编辑')
    }
    this.$refs.dialog.ejsRef.show()
  },
  watch: {},
  methods: {
    noUserAllowTypeChange(val) {
      const { itemData } = val
      this.formData.noUserAllowTypeName = itemData.text
    },
    allowTypeChange(val) {
      const { itemData } = val
      this.formData.allowTypeName = itemData.text
    },
    // 获取用户列表
    getUserList(val) {
      // let params = {
      //   fuzzyName: val.text
      //   // orgLevelCode: 'ORG05',
      //   // orgType: 'ORG001ADM'
      // }
      // this.$api.systemSetting.getBuyerList(params).then((res) => {
      const params = {
        condition: '',
        defaultRules: [],
        page: {
          current: 1,
          size: 100
        },
        pageFlag: false,
        rules: [
          {
            field: 'userName',
            operator: 'contains',
            type: 'string',
            value: val.text
          }
        ],
        tenantId: 0
      }
      this.$api.messageReceiving.userPagedQuery(params).then((res) => {
        const { code, data } = res
        if (code == 200 && data != null && data.records) {
          // const userInfo = sessionStorage.userInfo ? JSON.parse(sessionStorage.userInfo) : {}
          const userArrList = data.records.map((item) => {
            item.account = item.userCode
            item.accountName = item.userName
            item.userId = item.id
            // item.id = item.uid
            item.employeeName = item.account + '-' + item.accountName
            item.userTenantId = item.defaultTenantId
            // if (userInfo.tenantId && userInfo.tenantName) {
            //   item.userTenantName = userInfo.tenantName
            // }
            return item
          })
          const newArr = userArrList.concat(this.userArrList)
          let map = new Map()
          for (let item of newArr) {
            map.set(item.userId, item)
          }
          this.userArrList = [...map.values()]
          this.noRecordsTemplate = this.$t('没有找到记录')
        }
      })
    },
    grayTypeChange(val) {
      const { itemData } = val
      this.formData.grayTypeName = itemData.text
    },
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
      this.$emit('handleAddDialogShow', false)
    },
    handleConfirm() {
      const _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          const canaryConfigUserSaveRequestList = []
          let map = new Map()
          for (let item of _this.userArrList) {
            map.set(item.account, item)
          }
          if (
            _this.formData &&
            _this.formData.canaryConfigUserSaveRequestList &&
            _this.formData.canaryConfigUserSaveRequestList.length
          ) {
            for (let item of _this.formData.canaryConfigUserSaveRequestList) {
              canaryConfigUserSaveRequestList.push(map.get(item))
            }
          }
          const params = [{ ..._this.formData, canaryConfigUserSaveRequestList }]
          this.$api.systemSetting.grayBatchSave(params).then((res) => {
            const { code, msg } = res
            if (code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('handleAddDialogShow', false)
              this.$emit('confirmSuccess', _this.formData)
            } else {
              this.$toast({ content: msg, type: 'warning' })
            }
          })
        }
      })
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>

<style>
.create-proj-dialog .e-dlg-content {
  padding-top: 20px !important;
}
.dialogForm .mt-form-item textarea {
  height: 400px !important;
}
</style>
