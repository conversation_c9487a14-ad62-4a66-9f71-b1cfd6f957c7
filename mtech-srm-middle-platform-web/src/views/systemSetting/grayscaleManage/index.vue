<template>
  <div class="task-center">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
    ></mt-template-page>

    <add-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></add-dialog>
  </div>
</template>

<script>
import { pageConfig } from './config'
import addDialog from './components/addDialog.vue'
export default {
  components: {
    addDialog
  },
  data() {
    return {
      pageConfig: pageConfig,
      type: '0',
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickToolBar(e) {
      const { toolbar, grid } = e
      const sections = grid.getSelectedRecords()
      if (toolbar.id == 'Add') {
        // 新增
        this.dialogData = null
        this.handleAddDialogShow(true)
      } else if (toolbar.id === 'Edit') {
        // 编辑
        if (sections && sections.length === 1) {
          this.dialogData = {
            formData: sections[0],
            editType: 'edit'
          }
        } else {
          this.$toast({ content: this.$t('请只选择一行进行编辑'), type: 'warning' })
          return
        }
        this.handleAddDialogShow(true)
      } else if (toolbar.id == 'Enable') {
        // 启用
      } else if (toolbar.id == 'Disable') {
        // 禁用
      } else if (toolbar.id == 'Delete') {
        // 删除
        if (sections && sections.length < 1) {
          this.$toast({ content: this.$t('请选择需要删除的数据'), type: 'warning' })
          return
        }
        const idList = sections.map((i) => i.id)
        this.grayBatchDelete(idList)
      }
    },
    grayBatchDelete(idList) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('删除后将不可恢复，是否继续？')
        },
        success: () => {
          this.$api.systemSetting.grayBatchDelete({ idList }).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.templateRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    // 查看确认
    confirmSuccess() {
      this.$refs.templateRef.refreshCurrentGridData()
    },
    // 查看弹框
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>
<style lang="scss">
.task-center {
  // height: 100%;
  .mt-select-index {
    float: left;
  }
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
