import { i18n } from '@/main.js'

export const statusList = [
  { value: 0, text: i18n.t('停用'), cssClass: '' },
  { value: 1, text: i18n.t('启用'), cssClass: '' }
]
export const allowTypeList = [
  { value: 'allow', text: i18n.t('允许名单'), cssClass: '' },
  { value: 'block', text: i18n.t('屏蔽名单'), cssClass: '' }
]
export const grayTypeList = [
  { value: 'complete', text: i18n.t('全量'), cssClass: '' },
  { value: 'scale', text: i18n.t('按比例'), cssClass: '' },
  { value: 'customize', text: i18n.t('自定义'), cssClass: '' }
]
const columnData = [
  {
    type: 'checkbox',
    width: 50
  },
  // {
  //   field: 'templateName',
  //   headerText: '模板名称',
  //   width: 220,
  //   cellTools: [
  //     {
  //       id: 'preview',
  //       icon: 'icon_outline_Preview',
  //       title: i18n.t('预览')
  //     },
  //     {
  //       id: 'view',
  //       icon: 'icon_Share_2',
  //       title: i18n.t('查看')
  //     },
  //     {
  //       id: 'upload',
  //       icon: 'icon_solid_upload',
  //       title: i18n.t('上传')
  //     },
  //     {
  //       id: 'download',
  //       icon: 'icon_solid_Download',
  //       title: i18n.t('下载')
  //     }
  //   ]
  // },
  {
    field: 'title',
    headerText: '灰度配置标题'
  },
  {
    field: 'code',
    headerText: '灰度配置编码'
  },
  {
    field: 'allowType',
    headerText: '允许类型',
    ignore: true,
    valueConverter: {
      type: 'map',
      map: allowTypeList
    }
  },
  {
    field: 'grayType',
    headerText: '灰度类型',
    searchOptions: {
      elementType: 'select',
      dataSource: grayTypeList,
      fields: { text: 'text', value: 'value' }
    },
    valueConverter: {
      type: 'map',
      map: grayTypeList
    }
  },
  {
    field: 'scaleValue',
    headerText: '灰度比例%',
    ignore: true
  },
  {
    field: 'status',
    headerText: '配置状态',
    ignore: true,
    valueConverter: {
      type: 'map',
      map: statusList
    }
  },
  {
    field: 'remark',
    headerText: '备注',
    ignore: true
  }
]
// list pageConfig
export const pageConfig = [
  {
    toolbar: [
      { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
      { id: 'Edit', icon: 'icon_solid_edit', title: '编辑' },
      // { id: 'Enable', icon: 'icon_table_enable', title: '启用' },
      // { id: 'Disable', icon: 'icon_table_disable', title: '停用' },
      { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
    ],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    gridId: 'c9400a6b-9c6b-46db-9390-c40178f3a583',
    grid: {
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '/iam/tenant/_canary_config/queryBuilder',
        params: {}
      }
    }
  }
]
