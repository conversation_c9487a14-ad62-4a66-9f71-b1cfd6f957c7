import { i18n } from '@/main.js'

export const statusList = [
  { value: 0, text: i18n.t('进行中'), cssClass: '' },
  { value: 1, text: i18n.t('已完成'), cssClass: '' },
  { value: 2, text: i18n.t('失败'), cssClass: '' }
]
// list toolbar
// export const toolbar = [{ id: 'Add', icon: 'icon_solid_Createorder', title: '新增' }]
// list column
const columnData = [
  {
    field: 'templateName',
    headerText: '模板名称',
    width: 220,
    cellTools: [
      {
        id: 'preview',
        icon: 'icon_outline_Preview',
        title: i18n.t('预览')
      },
      {
        id: 'view',
        icon: 'icon_Share_2',
        title: i18n.t('查看')
      },
      {
        id: 'upload',
        icon: 'icon_solid_upload',
        title: i18n.t('上传')
      },
      {
        id: 'download',
        icon: 'icon_solid_Download',
        title: i18n.t('下载')
      }
    ]
  },
  {
    field: 'templateCode',
    headerText: '模板编码'
  },
  {
    field: 'templatePath',
    headerText: '模板路径'
  },
  {
    field: 'version',
    headerText: '版本号'
  },
  // {
  //   field: 'templateSetting',
  //   headerText: '模板配置'
  // },
  {
    field: 'templateType',
    headerText: '模板类型'
  }
]
// list pageConfig
export const pageConfig = [
  {
    toolbar: [],
    useToolTemplate: false, //此项不使用预置的表格操作按钮'新增、编辑、删除'
    grid: {
      height: 'auto',
      columnData,
      asyncConfig: {
        url: '/srm-purchase-execute/admin/printTemplate/config/pageQuery',
        params: {}
      }
    }
  }
]
