<template>
  <mt-dialog
    ref="dialog"
    css-class="create-proj-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
  >
    <mt-form class="dialogForm" ref="ruleForm" :model="formData" :rules="rules">
      <mt-form-item prop="remark" :label="$t('渲染数据')" class="full-width">
        <mt-input
          :multiline="true"
          v-model="formData.mockDataList"
          :placeholder="$t('渲染数据')"
        ></mt-input>
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogTitle: '',
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.handleConfirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      formData: {
        mockDataList: null
      },

      rules: {
        mockDataList: [
          {
            required: true,
            message: this.$t('渲染数据不能为空'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$set(this.formData, 'mockDataList', this.dialogData.mockDataList || '')
  },
  watch: {},
  methods: {
    handleClose() {
      this.$refs.dialog.ejsRef.hide()
      this.$emit('handleAddDialogShow', false)
    },
    handleConfirm() {
      const _this = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let _mockDataList = this.formData.mockDataList
          try {
            _mockDataList = _mockDataList ? JSON.parse(_mockDataList) : []
          } catch (e) {
            this.$toast({
              content: this.$t('文件格式错误'),
              type: 'warning'
            })
            return
          }

          let params = {
            mockDataList: _mockDataList,
            renderType: 'html',
            templateCode: this.dialogData.templateCode
          }
          this.$api.systemSetting.templateRender(params).then((res) => {
            if (res?.data?.type === 'application/json') {
              const reader = new FileReader()
              reader.readAsText(res?.data, 'utf-8')
              reader.onload = function () {
                const readerRes = reader.result
                const resObj = JSON.parse(readerRes)
                _this.$toast({
                  content: resObj.msg,
                  type: 'error'
                })
              }
              return
            }
            this.$emit('handleAddDialogShow', false)
            this.$emit('confirmSuccess', res.data)
          })
        }
      })
    },
    onOpen(args) {
      args.preventFocus = true
    }
  }
}
</script>

<style>
.create-proj-dialog .e-dlg-content {
  padding-top: 20px !important;
}
.dialogForm .mt-form-item textarea {
  height: 400px !important;
}
</style>
