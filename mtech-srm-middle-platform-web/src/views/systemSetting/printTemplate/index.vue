<template>
  <div class="task-center">
    <mt-template-page
      ref="templateRef"
      :hidden-tabs="true"
      :template-config="pageConfig"
      @handleClickCellTool="handleClickCellTool"
    ></mt-template-page>

    <view-dialog
      v-if="addDialogShow"
      :dialog-data="dialogData"
      :type="this.type"
      @handleAddDialogShow="handleAddDialogShow"
      @confirmSuccess="confirmSuccess"
    ></view-dialog>
  </div>
</template>

<script>
import { pageConfig } from './config'
import viewDialog from './components/viewDialog.vue'
import { download, getHeadersFileName } from '@/utils/utils'
export default {
  components: {
    viewDialog
  },
  data() {
    return {
      pageConfig: pageConfig,
      type: '0',
      addDialogShow: false,
      dialogData: null
    }
  },

  methods: {
    handleClickCellTool(e) {
      if (e.tool.id == 'preview') {
        this.handlePreview(e.data)
      } else if (e.tool.id === 'view') {
        this.handleView(e.data)
      } else if (e.tool.id == 'upload') {
        this.handleUpload(e.data)
      } else if (e.tool.id == 'download') {
        this.handleDownload(e.data)
      }
    },
    // 预览
    handlePreview(data) {
      this.$api.systemSetting.templatePreview({ templateCode: data.templateCode }).then((res) => {
        const content = res.data
        this.htmlRender(content)
      })
    },
    htmlRender(content) {
      let pdfUrl = window.URL.createObjectURL(
        new Blob([content], { type: 'text/html;charset=utf-8' })
      )
      window.open(pdfUrl)
    },
    pdfRender(content) {
      let pdfUrl = window.URL.createObjectURL(
        new Blob([content], { type: 'text/html;charset=utf-8' })
      )
      let date = new Date().getTime()
      let ifr = document.createElement('iframe')
      ifr.style.frameborder = 'no'
      ifr.style.display = 'none'
      ifr.style.pageBreakBefore = 'always'
      ifr.setAttribute('id', 'printPdf' + date)
      ifr.setAttribute('name', 'printPdf' + date)
      ifr.src = pdfUrl
      document.body.appendChild(ifr)
      this.doPrint('printPdf' + date)
      window.URL.revokeObjectURL(ifr.src)
    },
    // 查看
    handleView(data) {
      this.addDialogShow = true
      this.dialogData = {
        templateCode: data.templateCode,
        mockDataList: data.mockDataList?.length ? JSON.stringify(data.mockDataList) : ''
      }
    },
    // 上传
    handleUpload(data) {
      this.$dialog({
        modal: () => import('./components/uploadDialog.vue'),
        data: {
          title: this.$t('上传')
        },
        success: (res) => {
          this.requestFile(data, res)
        }
      })
    },
    requestFile(data, files) {
      let { version, templateCode } = data
      version = Number(version) + 1
      const formDatas = new FormData()
      formDatas.append('version', version)
      formDatas.append('templateCode', templateCode)
      formDatas.append('file', files[0])

      this.$store.commit('startLoading')
      this.$api.systemSetting.templateUpload(formDatas).then((res) => {
        this.$store.commit('endLoading')
        if (res.code == 200) {
          this.$toast({
            content: this.$t('上传成功'),
            type: 'success'
          })
          this.$refs.templateRef.refreshCurrentGridData()
        }
      })
    },
    // 下载
    handleDownload(data) {
      this.$api.systemSetting.templateDownload({ templateCode: data.templateCode }).then((res) => {
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      })
    },
    // 打印预览
    doPrint(val) {
      let ordonnance = document.getElementById(val).contentWindow
      setTimeout(() => {
        ordonnance.print()
      }, 100)
    },
    // 查看确认
    confirmSuccess(e) {
      this.pdfRender(e)
    },
    // 查看弹框
    handleAddDialogShow(flag) {
      this.addDialogShow = flag
    }
  }
}
</script>
<style lang="scss">
.task-center {
  // height: 100%;
  .mt-select-index {
    float: left;
  }
  .e-content {
    height: calc(100vh - 230px) !important;
  }
}
</style>
