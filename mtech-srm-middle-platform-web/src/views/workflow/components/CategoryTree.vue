<template>
  <div class="tree-view--wrap">
    <div class="trew-node--add">
      <mt-button
        iconCss="mt-icons mt-icon-icon_solid_Createorder"
        cssClass="e-flat"
        iconPosition="Right"
        @click.native="addNewRootNode"
        >{{ $t('新增根节点') }}</mt-button
      >
    </div>
    <mt-common-tree
      v-if="treeViewData.dataSource.length || isStartRootNodeFormNull"
      ref="treeView"
      class="tree-view--template"
      :allowEditing="true"
      :fields="treeViewData"
      @onButton="clickCustomButton"
      @nodeEdited="nodeEdited"
      @nodeSelected="nodeSelected"
    ></mt-common-tree>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'
import { i18n } from '@/main.js'
import MtCommonTree from '@mtech/common-tree-view'
import '@mtech/common-tree-view/build/esm/bundle.css'

@Component({
  components: {
    MtCommonTree
  }
})
export default class CategoryTree extends Vue {
  @Prop({
    default: ''
  })
  type!: string

  treeViewData = {
    dataSource: [],
    id: 'id',
    text: 'name',
    child: 'subChild'
  }

  isStartRootNodeFormNull = false // 用户判断是否是从空开始新建根节点
  isUpdating = false // 用于判断树节点是否处于更新文本的状态
  nodeOperation = [{ text: i18n.t('新增下级') }, { text: i18n.t('删除') }]
  /**
   * 生命周期
   * **/
  async created() {
    await this.getCategoryTree()
    const category = this.treeViewData.dataSource[0] as { id: string }
    category?.id &&
      this.$emit('nodeSelected', {
        id: category?.id
      })
  }

  async getCategoryTree() {
    const foamtNodeData = (node: any) => {
      if (!node) return

      node.setOperation = this.nodeOperation
      node.subChild.forEach((n: any) => {
        foamtNodeData(n)
      })
    }

    try {
      const res = await this.$api.workflow.categoryTree({
        group: this.type
      })
      if (res.code === 200 && res.data) {
        const list: any[] = res?.data || []
        list.forEach((v) => {
          foamtNodeData(v)
        })
        this.treeViewData.dataSource = list as never[]
      }
    } catch (error) {
      console.error(error)
    }
  }

  // 添加根节点e
  addNewRootNode() {
    if (this.treeViewData.dataSource.length === 0 && !this.isStartRootNodeFormNull) {
      this.isStartRootNodeFormNull = true // 只需要根据初始化的datasource维护一次
      this.$nextTick(() => {
        this.addTreeNode({ id: 0 })
      })
    } else {
      this.addTreeNode({
        id: 0
      })
    }
  }

  clickCustomButton(event: any) {
    if (event.onBtn.text === i18n.t('新增下级')) {
      this.addTreeNode(event)
    } else if (event.onBtn.text === i18n.t('删除')) {
      this.deleteTreeNode(event)
    }
  }

  addTreeNode(event: any) {
    const treeView: any = this.$refs.treeView
    const instance = treeView?.getCommonMethods()
    const newNodeName = i18n.t('分类名称')

    return this.$api.workflow
      .addCategoryNode({
        group: this.type,
        name: newNodeName,
        pid: event.id
      })
      .then((res: { code: number; data: { id: any } }) => {
        if (res.code === 200) {
          const { id } = res.data
          instance.addNodes(
            [
              {
                id: id,
                name: newNodeName,
                setOperation: this.nodeOperation
              }
            ],
            event.id
          )
          // 直接进入编辑
          instance.beginEdit(id)
        }
      })
  }

  nodeEdited(event: any) {
    if (this.isUpdating) {
      this.isUpdating = false
      return
    }
    const { newText, nodeData } = event
    const treeView: any = this.$refs.treeView
    const instance = treeView?.getCommonMethods()

    this.$api.workflow
      .updateCategoryNode({
        name: newText,
        id: nodeData.id
      })
      .then((res: { code: number; data: { id: any } }) => {
        if (res.code === 200) {
          this.isUpdating = true
          // ej2 的 updateNode 只能修改 text ,所以将新建节点的ajax前置
          instance.updateNode(nodeData.id, newText)
        }
      })
  }

  // 删除树节点
  deleteTreeNode(event: any) {
    const id = event.id
    this.$api.workflow
      .deleteCategoryTreeNode({ id })
      .then((res: { code: number; data: boolean }) => {
        if (res.code === 200 && res.data) {
          const treeView: any = this.$refs.treeView
          const instance = treeView?.getCommonMethods()
          instance.removeNodes([id])
          this.$emit('nodeDelete', id)
        }
      })
  }

  nodeSelected(event: any) {
    this.$emit('nodeSelected', event.nodeData)
  }
}
</script>
<style lang="scss" scoped>
.tree-view--wrap {
  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }
}
</style>
