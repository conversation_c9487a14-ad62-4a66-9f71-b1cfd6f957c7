<template>
  <mt-side-bar
    ref="sidebar"
    position="Right"
    type="Over"
    width="800px"
    zIndex="1010"
    v-show="value"
    :showBackdrop="true"
  >
    <div class="workflow-server-edit">
      <div class="header">
        <span class="title">{{ title }}</span>
        <mt-icon class="btn" name="icon_Close_1" @click.native="close"></mt-icon>
      </div>

      <mt-form ref="form" class="content" :model="formData" :rules="formRules">
        <div class="title">{{ $t('基础信息') }}</div>

        <mt-form-item :label="$t('接口编码')" prop="key">
          <mt-input v-model="formData.key" :disabled="isEdit" :placeholder="$t('请输入接口编码')" />
        </mt-form-item>

        <mt-form-item :label="$t('接口描述')" prop="desc">
          <mt-input v-model="formData.desc" :placeholder="$t('请输入接口描述')" />
        </mt-form-item>

        <mt-form-item :label="$t('接口名称')" prop="name">
          <mt-input v-model="formData.name" :placeholder="$t('请输入接口名称')" />
        </mt-form-item>

        <mt-form-item :label="$t('接口地址')" prop="url">
          <mt-input v-model="formData.url" :placeholder="$t('接口地址')" />
        </mt-form-item>

        <mt-form-item :label="$t('返回结果')" prop="resultJson">
          <mt-input
            v-model="formData.resultJson"
            :multiline="true"
            :rows="2"
            :placeholder="$t('服务描述')"
          />
        </mt-form-item>

        <div class="title" style="margin: 0">
          <span>{{ $t('参数信息') }}</span>
        </div>

        <mt-template-page
          class="table--wrap"
          ref="tempaltePageRef"
          :hiddenTabs="true"
          :templateConfig="pageConfig"
          @handleClickToolBar="handleClickToolBar"
          @handleClickCellTool="handleClickCellTool"
        >
        </mt-template-page>
      </mt-form>

      <div class="footer">
        <mt-button class="footer-btn" cssClass="e-flat" @click="close">{{ $t('取消') }}</mt-button>
        <mt-button class="footer-btn" cssClass="e-flat" :isPrimary="true" @click="handleConfirm">{{
          $t('确定')
        }}</mt-button>
      </div>
      <WorkflowInterfaceParam
        v-model="isShowDialog"
        :data="interfaceParamData"
        @save="saveInterfaceParam"
        @add="addInterfaceParam"
      />
    </div>
  </mt-side-bar>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import { flowInterfaceParamConfig } from '../config'
import WorkflowInterfaceParam from './WorkflowInterfaceParam.vue'

@Component({
  components: {
    WorkflowInterfaceParam
  }
})
export default class WorkflowInterfaceEdit extends Vue {
  @Prop({
    default: false
  })
  value!: boolean

  @Prop({
    default: ''
  })
  categoryId!: string

  @Prop({
    type: String
  })
  id!: string

  isShowDialog = false
  currentParamIndex: string | undefined

  pageConfig: any[] = flowInterfaceParamConfig

  formData: any = {}

  formRules = {
    key: [{ required: true, message: '请输入服务编码', trigger: 'blur' }],
    name: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
    url: [{ required: true, message: '请输入接口地址', trigger: 'blur' }],
    desc: [{ required: true, message: '请输入描述内容', trigger: 'blur' }],
    resultJson: [{ required: true, message: '请输入返回结果', trigger: 'blur' }]
  }

  get isEdit() {
    return !!this.id
  }

  get title() {
    return this.isEdit ? '编辑' : '新增'
  }

  get interfaceParamData() {
    if (this.currentParamIndex) {
      return this.pageConfig[0].grid.dataSource[Number(this.currentParamIndex)]
    }
    return undefined
  }

  @Watch('value')
  onWatchValue(val: boolean) {
    if (val) {
      this.show()
    } else {
      this.close()
    }
  }

  @Watch('id')
  onWatchId(val: string) {
    this.resetFormData()
    if (val) {
      this.getInterfaceDetail(val)
    }
  }

  private getInterfaceDetail(id: string) {
    this.$api.workflow
      .flowInterfacInfo({
        id
      })
      .then((res: any) => {
        const data = res.data
        if (res.code === 200 && data) {
          data.paramJson = JSON.parse(data.paramJson || '[]')
          this.formData = data
          this.pageConfig[0].grid.dataSource.push(...data.paramJson)
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '详情获取失败',
          type: 'error'
        })
      })
  }

  show() {
    const sidebar: any = this.$refs.sidebar
    sidebar.show()
  }

  close() {
    const sidebar: any = this.$refs.sidebar
    sidebar.hide()
    this.$emit('input', false)
  }

  private resetFormData() {
    this.formData = {
      id: '',
      key: '',
      desc: '',
      name: '',
      url: '',
      resultJson: '',
      paramJson: ''
    }
    this.pageConfig[0].grid.dataSource = []
  }

  handleClickToolBar(e: any) {
    const { grid, toolbar } = e
    if (e.toolbar.id === 'Add') {
      this.showInterfaceParam()
      return
    }

    const sections = grid.getSelectedRowIndexes()
    if (sections.length === 0) {
      this.$toast({
        content: '请先选择接口参数',
        type: 'warning'
      })
      return
    }

    if (toolbar.id === 'Delete') {
      this.deleteInterfaceParam(sections)
    }
  }

  handleClickCellTool(e: any) {
    const { tool, componentData } = e
    if (tool.id === 'edit') {
      this.editRowData(componentData.index)
    } else if (tool.id === 'delete') {
      this.deleteRowData(componentData.index)
    }
  }

  showInterfaceParam() {
    this.isShowDialog = true
    this.currentParamIndex = undefined
  }

  deleteInterfaceParam(ids: number[]) {
    this.pageConfig[0].grid.dataSource = this.pageConfig[0].grid.dataSource.filter(
      (v: any, index: number) => {
        return !ids.includes(index)
      }
    )
  }

  editRowData(index: string) {
    this.isShowDialog = true
    this.currentParamIndex = index
  }

  deleteRowData(index: string) {
    this.deleteInterfaceParam([Number(index)])
  }

  saveInterfaceParam(data: any) {
    const index = Number(this.currentParamIndex)
    this.pageConfig[0].grid.dataSource.splice(index, 1, data)
  }

  addInterfaceParam(data: any) {
    this.pageConfig[0].grid.dataSource.unshift(data)
  }

  handleConfirm() {
    ;(this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        if (this.isEdit) {
          this.updateInterface()
        } else {
          this.addInterface()
        }
      } else {
        this.$toast({
          content: '输入有误，请检查',
          type: 'warning'
        })
      }
    })
  }

  updateInterface() {
    const paramJson = this.pageConfig[0].grid.dataSource

    this.$api.workflow
      .flowInterfacUpdate({
        categoryId: this.categoryId,
        paramJson: JSON.stringify(paramJson),
        resultJson: this.formData.resultJson,
        key: this.formData.key,
        url: this.formData.url,
        desc: this.formData.desc,
        id: this.formData.id,
        name: this.formData.name
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '服务更新成功',
            type: 'success'
          })
          this.$emit('update', res.data)
          this.close()
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '服务更新失败',
          type: 'error'
        })
      })
  }

  addInterface() {
    const paramJson = this.pageConfig[0].grid.dataSource
    this.$api.workflow
      .flowInterfaceAdd({
        categoryId: this.categoryId,
        desc: this.formData.desc,
        name: this.formData.name,
        key: this.formData.key,
        paramJson: JSON.stringify(paramJson),
        resultJson: this.formData.resultJson,
        url: this.formData.url
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '接口新增成功',
            type: 'success'
          })
          this.$emit('add', res.data)
          this.close()
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '接口新增失败',
          type: 'error'
        })
      })
  }
}
</script>

<style lang="scss" scoped>
.workflow-server-edit {
  display: flex;
  height: 100%;
  flex-direction: column;
  overflow: auto;

  .header {
    height: 60px;
    padding: 0 20px;
    background: #f3f3f3;
    border-radius: 8px 8px 0 0;
    font-size: 16px;
    line-height: 60px;
    color: #292929;
    .btn {
      position: absolute;
      right: 20px;
      top: 18px;
      cursor: pointer;
    }
  }

  .content {
    flex: 1;
    padding: 20px;

    .title {
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 20px;
      &::before {
        content: '';
        border-left: 3px solid rgba(0, 70, 156, 1);
        border-radius: 5px 0 0 5px;
        margin-right: 8px;
      }
    }

    .table--wrap {
      height: 350px;
    }
  }

  .footer {
    height: 60px;
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
    text-align: right;
    padding: 0 20px;

    .footer-btn {
      margin-top: 20px;
      margin-right: 20px;
    }
  }
}
</style>
