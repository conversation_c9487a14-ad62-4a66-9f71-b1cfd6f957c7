<template>
  <mt-dialog ref="dialog" :header="$t('选择服务')" :buttons="buttons" width="900px" z-index="1011">
    <mt-row class="mt-row--wrap">
      <mt-col :span="6" class="content-border tree-view--wrap">
        <mt-treeView
          v-if="serviceClassify.dataSource.length"
          :fields="serviceClassify"
          @nodeSelected="nodeSelected"
        ></mt-treeView>
      </mt-col>

      <mt-col :span="16" class="content-border">
        <div class="search">
          <mt-icon name="icon_search"></mt-icon>
          <mt-input
            class="search-input"
            v-model="searchText"
            :placeholder="$t('请输入搜索内容')"
            floatLabelType="Never"
            @keyup.native.enter="search"
          >
          </mt-input>
        </div>
        <div class="list">
          <mt-DataGrid
            class="data-grid--content"
            :dataSource="formDataList"
            :columnData="columnData"
            :allowPaging="true"
            :pageSettings="pageSettings"
            @currentChange="changePageCount"
            @sizeChange="changePageSize"
            @rowSelected="getSelectedRecords"
            ref="eventDataGrid"
          ></mt-DataGrid>
        </div>
      </mt-col>
    </mt-row>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class WorkflowRemoatServerAdd extends Vue {
  @Prop({
    default: false
  })
  value!: boolean

  buttons = [
    {
      click: this.hide,
      buttonModel: { isPrimary: 'true', content: '取消' }
    },
    {
      click: this.save,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  // 树状数据
  serviceClassify = {
    dataSource: [],
    id: 'id',
    text: 'name',
    child: 'subChild'
  }

  // 列表
  columnData = [
    // { width: '60', type: 'checkbox' },
    { field: 'name', headerText: '接口名称' },
    { field: 'desc', headerText: '接口描述' },
    { field: 'url', headerText: '接口地址' }
  ]

  // 服务列表
  formDataList = []

  // 查询的关键字
  searchText = ''
  // 当前被选中的树节点
  currentSelectedNode: any = {}
  // 当前被选中的服务
  currentService: any = {}

  pageSettings = { pageSize: 10, pageCount: 1, pageSizes: [10, 20, 30, 50], totalRecordsCount: 6 }

  @Watch('value')
  onWatchValue(val: boolean) {
    if (val) {
      this.getCategoryTree({
        group: 'interface'
      })
      ;(this.$refs.dialog as any).ejsRef.show()
    } else {
      ;(this.$refs.dialog as any).ejsRef.hide()
    }
  }

  hide() {
    this.$emit('input', false)
  }

  save() {
    this.$emit('save', this.currentService)
    this.hide()
  }

  search() {
    this.getFlowService({
      categoryId: this.currentSelectedNode.id,
      current: 1,
      size: this.pageSettings.pageSize,
      name: this.searchText
    })
  }

  nodeSelected(event: any) {
    const { nodeData } = event
    this.currentSelectedNode = nodeData
    this.searchText = ''
    const params = {
      categoryId: nodeData.id,
      current: 1,
      size: this.pageSettings.pageSize
    }
    this.getFlowService(params)
  }

  goToPage(num: number) {
    this.changePageCount(num)
  }

  changePageCount(count: number) {
    const params = {
      categoryId: this.currentSelectedNode.id,
      current: count,
      size: this.pageSettings.pageSize
    }
    this.getFlowService(params)
  }

  changePageSize(size: number) {
    const params = {
      categoryId: this.currentSelectedNode.id,
      current: this.pageSettings.pageCount,
      size: size
    }
    this.getFlowService(params)
  }

  getSelectedRecords(event: any) {
    // TODO：如何保持页面的选中高亮状态
    this.currentService = event.data
  }

  // 获取分类树
  getCategoryTree(params: any) {
    this.$api.workflow
      .categoryTree(params)
      .then((res: { code: number; data: any }) => {
        if (res.code === 200) {
          const list: never[] = res.data || []
          this.$set(this.serviceClassify, 'dataSource', [])
          this.$nextTick().then(() => {
            this.$set(this.serviceClassify, 'dataSource', [...list])
          })
        }
      })
      .catch()
  }

  getFlowService(params: any) {
    this.$api.workflow
      .flowInterfacePage({
        ...params
      })
      .then((res: { code: number; data: { records: never[] } }) => {
        if (res.code === 200) {
          this.formDataList = res.data.records || []
        }
      })
      .catch()
  }
}
</script>

<style lang="scss" scoped>
.mt-row--wrap {
  margin-top: 24px;

  .tree-view--wrap {
    margin-right: 24px;
  }

  .search {
    border-bottom: 1px solid #e8e8e8;
    padding: 6px 14px 0;

    .search-input {
      width: calc(100% - 25px);
    }

    ::v-deep .e-input-group {
      border: none;
    }
  }

  .list {
    padding: 12px 24px;
    height: 320px;
    overflow: auto;
  }
}

.content-border {
  border: 1px solid #e8e8e8;
  border-radius: 4px 0 0 4px;
  height: 390px;
}
</style>
