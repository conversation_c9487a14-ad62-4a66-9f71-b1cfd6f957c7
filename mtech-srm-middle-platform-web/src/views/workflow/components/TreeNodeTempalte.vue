<template>
  <div class="treeviewdiv">
    <span class="treeName">{{ data.name }}</span>
    <span style="float: right">
      <mt-icon name="icon_solid_add" class="icon icon-zoomin"></mt-icon>
      <mt-icon name="icon_solid_delete_2" class="icon icon-zoomout"></mt-icon>
    </span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  }
}
</script>

<style scoped lang="scss">
.treeviewdiv {
  width: 100%;
  .icon {
    margin-right: 24px;
    font-size: 16px;
  }
  .icon-zoomin {
    color: #6386c1;
  }
  .icon-zoomout {
    color: #ed5633;
  }
}
</style>
