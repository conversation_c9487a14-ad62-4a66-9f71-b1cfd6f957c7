<template>
  <mt-side-bar
    ref="sidebar"
    position="Right"
    type="Over"
    width="800px"
    zIndex="1010"
    :showBackdrop="true"
    v-show="visible"
  >
    <div class="workflow-server-edit">
      <div class="header">
        <span class="title">{{ title }}</span>
        <mt-icon class="btn" name="icon_Close_1" @click.native="close"></mt-icon>
      </div>

      <mt-form ref="form" class="content" :model="formData" :rules="formRules">
        <div class="title">{{ $t('基础信息') }}</div>

        <mt-form-item :label="$t('服务编码')" prop="id" v-if="isEdit">
          <mt-input v-model="formData.id" :disabled="isEdit" :placeholder="$t('请输入服务编码')" />
        </mt-form-item>

        <mt-form-item :label="$t('服务分类')" prop="categoryName" v-if="isEdit">
          <mt-input
            v-model="formData.categoryName"
            :disabled="isEdit"
            :placeholder="$t('请输入服务分类')"
          />
        </mt-form-item>

        <mt-form-item :label="$t('服务名称')" prop="name">
          <mt-input v-model="formData.name" :placeholder="$t('请输入服务名称')" />
        </mt-form-item>

        <mt-form-item :label="$t('服务类型')" prop="type">
          <mt-select
            v-model="formData.type"
            :data-source="selectOptions"
            float-label-type="Never"
            :placeholder="$t('请选择服务类型')"
          ></mt-select>
        </mt-form-item>

        <mt-form-item :label="$t('服务描述')" prop="desc">
          <mt-input
            v-model="formData.desc"
            :multiline="true"
            :rows="2"
            :placeholder="$t('服务描述')"
          />
        </mt-form-item>

        <!-- 服务类型选择远程调用 -->
        <div v-show="formData.type === 'RemoteInvoke'">
          <div class="title">
            <span>{{ $t('接口信息') }}</span>
            <div class="interface-btn interface-btn--add" @click="showServerDialog">
              <mt-icon class="icon-btn" name="icon_solid_add" />{{ $t('添加') }}
            </div>
            <div class="interface-btn interface-btn--delete" @click="deleteServeInterface">
              <mt-icon class="icon-btn" name="icon_solid_delete_2" />{{ $t('删除') }}
            </div>
          </div>

          <div v-if="formData.interfaceName">
            <mt-form-item :label="$t('接口名称')" prop="interfaceName">
              <mt-input
                v-model="formData.interfaceName"
                :disabled="true"
                :placeholder="$t('接口名称')"
              />
            </mt-form-item>

            <mt-form-item :label="$t('接口描述')" prop="interfaceDesc">
              <mt-input
                v-model="formData.interfaceDesc"
                :disabled="true"
                :placeholder="$t('接口描述')"
              />
            </mt-form-item>

            <mt-form-item :label="$t('接口地址')" prop="interfaceUrl">
              <mt-input
                v-model="formData.interfaceUrl"
                :disabled="true"
                :placeholder="$t('接口地址')"
              />
            </mt-form-item>
          </div>

          <p v-else class="interface-null-tip">
            {{ $t('添加成功后展示接口信息') }}
          </p>
        </div>

        <!-- 服务类型选择条件表达式， -->
        <div v-show="formData.type === 'RuleScript'">
          <mt-form-item :label="$t('脚本类型')" prop="contentType">
            <mt-select
              v-model="formData.contentType"
              :data-source="scriptContentType"
              float-label-type="Never"
              :placeholder="$t('请选择脚本类型')"
            ></mt-select>
          </mt-form-item>

          <mt-form-item :label="$t('脚本内容')" prop="content">
            <mt-input
              v-model="formData.content"
              :multiline="true"
              :rows="2"
              :placeholder="$t('脚本内容')"
            />
          </mt-form-item>
        </div>

        <!-- 其他服务类型 -->
        <div
          v-show="
            [
              'CountersignFinishExpression',
              'ExecScript',
              'WhereExpression',
              'ErrorCode',
              'ApprovalScript'
            ].includes(formData.type)
          "
        >
          <mt-form-item :label="$t('表达式')" prop="content">
            <mt-input
              v-model="formData.content"
              :multiline="true"
              :rows="2"
              :placeholder="$t('内容')"
            />
          </mt-form-item>
        </div>
      </mt-form>

      <div class="footer">
        <mt-button class="footer-btn" cssClass="e-flat" @click="close">{{ $t('取消') }}</mt-button>
        <mt-button class="footer-btn" cssClass="e-flat" :isPrimary="true" @click="handleConfirm">{{
          $t('确定')
        }}</mt-button>
      </div>

      <WorkflowRemoatServerAdd v-model="isShowDialog" @save="addServeInterface" />
    </div>
  </mt-side-bar>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'
import WorkflowRemoatServerAdd from './WorkflowRemoatServerAdd.vue'

@Component({
  components: {
    WorkflowRemoatServerAdd
  }
})
export default class WorkflowServeEdit extends Vue {
  @Prop({
    default: false
  })
  value!: boolean

  @Prop({
    default: ''
  })
  categoryId!: string

  @Prop({
    type: String
  })
  id!: string

  isShowDialog = false

  formData: any = {
    id: '',
    categoryId: '',
    categoryName: '',
    name: '',
    type: '',
    desc: '',
    contentType: '',
    content: '',
    interfaceName: '',
    interfaceDesc: '',
    interfaceUrl: ''
  }

  get formRules() {
    const formRules: any = {
      // id: [{ required: true, message: '请输入服务编码', trigger: 'blur' }],
      name: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
      type: [{ required: true, message: '请选择服务类型', trigger: 'blur' }],
      desc: [{ required: true, message: '请输入描述内容', trigger: 'blur' }]
    }

    if (this.formData.type === 'RemoteInvoke') {
      formRules.interfaceName = [{ required: true, message: '请输入接口名称', trigger: 'blur' }]
      formRules.interfaceDesc = [{ required: true, message: '请输入接口描述', trigger: 'blur' }]
      formRules.interfaceUrl = [{ required: true, message: '请输入接口地址', trigger: 'blur' }]
    } else if (this.formData.type === 'RuleScript') {
      formRules.contentType = [{ required: true, message: '请选择脚本分类', trigger: 'blur' }]
      formRules.content = [{ required: true, message: '请输入脚本内容', trigger: 'blur' }]
    } else {
      // formRules.content = [{ required: true, message: '请输入脚本内容', trigger: 'blur' }]
    }

    return formRules
  }

  // 脚本类型
  scriptContentType = [
    {
      text: 'Javascript',
      value: 'javascript'
    }
  ]

  selectOptions = [
    {
      text: '会签完成条件',
      value: 'CountersignFinishExpression'
    },
    {
      text: '执行脚本',
      value: 'ExecScript'
    },
    {
      text: '远程调用',
      value: 'RemoteInvoke'
    },
    {
      text: '规则脚本',
      value: 'RuleScript'
    },
    {
      text: '条件表达式',
      value: 'WhereExpression'
    },
    {
      text: '边界错误码',
      value: 'ErrorCode'
    },
    {
      text: '审批脚本',
      value: 'ApprovalScript'
    }
  ]

  get visible() {
    if (this.value) {
      this.show()
    }
    return this.value
  }

  set visible(v: boolean) {
    if (!v) {
      const sidebar: any = this.$refs.sidebar
      sidebar.hide()
    }
    this.$emit('input', v)
  }

  get isEdit() {
    return !!this.id
  }

  get title() {
    return this.isEdit ? '编辑' : '新增'
  }

  @Watch('id')
  onWatchId(val: string) {
    this.resetFormData()
    if (val) {
      this.getServeDetail(val)
    }
  }

  private getServeDetail(id: string) {
    this.$api.workflow
      .flowServiceInfo({
        id
      })
      .then((res: any) => {
        if (res.code === 200 && res.data) {
          this.formData = {
            ...res.data,
            type: res.data?.type?.value
          }
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '详情获取失败',
          type: 'error'
        })
      })
  }

  private show() {
    const sidebar: any = this.$refs.sidebar
    sidebar.show()
  }

  close() {
    this.visible = false
  }

  showServerDialog() {
    this.isShowDialog = true
  }

  // 添加远程调用服务接口
  addServeInterface(server: any) {
    const { name = '', desc = '', url = '', id = '' } = server
    this.formData.interfaceId = id
    this.formData.interfaceName = name
    this.formData.interfaceUrl = url
    this.formData.interfaceDesc = desc
  }

  // 删除远程调用服务接口
  deleteServeInterface() {
    this.$set(this.formData, 'interfaceName', undefined)
    // this.formData.interfaceName = ''
    this.formData.interfaceUrl = ''
    this.formData.interfaceDesc = ''
  }

  handleConfirm() {
    ;(this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        if (this.isEdit) {
          this.updateServer()
        } else {
          this.addServer()
        }
      } else {
        this.$toast({
          content: '输入有误，请检查',
          type: 'warning'
        })
      }
    })
  }

  private updateServer() {
    const param = this.formatParam()
    param.id = this.id

    this.$api.workflow
      .flowServiceUpdate(param)
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '服务更新成功',
            type: 'success'
          })
          this.$emit('update', res.data)
          this.close()
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '服务更新失败',
          type: 'error'
        })
      })
  }

  private addServer() {
    this.$api.workflow
      .flowServiceAdd(this.formatParam())
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: res.msg || '服务新增成功',
            type: 'success'
          })
          this.$emit('add', res.data)
          this.close()
        }
      })
      .catch((error: any) => {
        this.$toast({
          content: error.msg || '服务新增失败',
          type: 'error'
        })
      })
  }

  private formatParam() {
    const param: any = {
      categoryId: this.categoryId,
      content: this.formData.content,
      desc: this.formData.desc,
      name: this.formData.name,
      type: this.formData.type
    }
    if (this.formData.type === 'RemoteInvoke') {
      param.content = this.formData.interfaceId
    } else if (this.formData.type === 'RuleScript') {
      param.contentType = this.formData.contentType
    }

    return param
  }

  private resetFormData() {
    this.formData = {
      id: '',
      categoryId: '',
      categoryName: '',
      name: '',
      type: '',
      desc: '',
      contentType: '',
      content: '',
      interfaceName: '',
      interfaceDesc: '',
      interfaceUrl: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-server-edit {
  display: flex;
  height: 100%;
  flex-direction: column;
  overflow: auto;

  .header {
    height: 60px;
    padding: 0 20px;
    background: #f3f3f3;
    border-radius: 8px 8px 0 0;
    font-size: 16px;
    line-height: 60px;
    color: #292929;
    .btn {
      position: absolute;
      right: 20px;
      top: 18px;
      cursor: pointer;
    }
  }

  .content {
    flex: 1;
    padding: 20px;

    .title {
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 20px;
      &::before {
        content: '';
        border-left: 3px solid rgba(0, 70, 156, 1);
        border-radius: 5px 0 0 5px;
        margin-right: 8px;
      }
    }
    .interface-null-tip {
      text-align: center;
      color: #9a9a9a;
    }

    .interface-btn {
      float: right;
      cursor: pointer;

      .icon-btn {
        margin-right: 6px;
      }
    }

    .interface-btn--add {
      color: #6386c1;
    }

    .interface-btn--delete {
      margin-right: 20px;
      color: #ed5633;
    }
  }

  .footer {
    height: 60px;
    border-radius: 0 0 0 8px;
    box-shadow: inset 0 1px 0 0 rgba(232, 232, 232, 1);
    text-align: right;
    padding: 0 20px;

    .footer-btn {
      margin-top: 20px;
      margin-right: 20px;
    }
  }
}
</style>
