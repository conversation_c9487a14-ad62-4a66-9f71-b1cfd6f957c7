<template>
  <div class="workflow-controller">
    <mt-form ref="form" :model="controllerData" :rules="formRules">
      <mt-row justify="space-between" type="flex" :gutter="20">
        <mt-col :span="8">
          <mt-form-item :label="`${moduleName}${$t('编码')}`" prop="key">
            <mt-input
              v-model="controllerData.key"
              :placeholder="$t('请输入')"
              :readonly="idDisabledKey"
              :showClearButton="!idDisabledKey"
            />
          </mt-form-item>
        </mt-col>
        <mt-col :span="8">
          <mt-form-item :label="`${moduleName}${$t('名称')}`" prop="name">
            <mt-input v-model="controllerData.name" :placeholder="$t('请输入')" />
          </mt-form-item>
        </mt-col>
        <mt-col :span="8">
          <mt-form-item :label="$t('版本号')" prop="version">
            <mt-input
              v-model="controllerData.version"
              :placeholder="$t('请输入版本号')"
              :readonly="true"
              :showClearButton="false"
            />
          </mt-form-item>
        </mt-col>
        <mt-col style="flex: 0 0 230px">
          <mt-form-item>
            <mt-button
              v-if="hasSaveBtn"
              class="btn--revert"
              iconCss="mt-icons mt-icon-icon_solid_Save1"
              cssClass="e-flat"
              @click.native="handleController('save')"
              >{{ $t('保存') }}</mt-button
            >
            <mt-button
              class="btn--revert"
              iconCss="mt-icons mt-icon-icon_solid_Release"
              cssClass="e-flat"
              :disabled="!canPublishBtn"
              @click.native="handleController('publish')"
              >{{ $t('发布') }}</mt-button
            >
            <mt-button
              class="btn--revert"
              iconCss="mt-icons mt-icon-icon_solid_close"
              cssClass="e-flat"
              @click.native="handleController('close')"
              >{{ $t('关闭') }}</mt-button
            >
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class WorkflowController extends Vue {
  @Prop({
    type: Object
  })
  formData!: any

  @Prop({
    type: Boolean,
    default: false
  })
  isEdit!: boolean

  @Prop()
  moduleName!: string

  get idDisabledKey() {
    return !!this.formData.key
  }

  get hasSaveBtn() {
    return !(Number(this.formData.version) > 0 && this.isEdit)
  }

  get canPublishBtn() {
    return !!this.formData.id
  }

  @Watch('formData', { immediate: true })
  onWatchFormData() {
    this.controllerData = {
      ...this.formData
    }
  }

  @Watch('formData.version')
  onWatchFormDataVersion(val: any) {
    if (val) {
      this.$set(this.controllerData, 'version', val)
    }
  }

  controllerData = {
    key: '',
    name: '',
    version: ''
  }

  formRules = {
    key: [{ require: true, validator: this.validFormKey, trigger: 'blur' }],
    name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
  }

  created() {
    // this.$api.workflow.addFlowTemplateValid()
  }

  handleController(type: string) {
    if (type === 'close') {
      this.$emit('control', type, this.controllerData)
      return
    }
    ;(this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        this.$emit('control', type, this.controllerData)
      }
    })
  }

  private validFormKey(rule: any, value: any, callback: any) {
    if (!value) {
      callback(new Error('请输入模板编码'))
    } else if (!/^[a-zA-Z][A-Za-z0-9_]*/.test(value)) {
      callback(new Error('字母开头，后接数字、字母或下划线'))
    } else {
      callback()
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-controller {
  background: #f5f5f5;
  padding: 20px 20px 0 20px;
  border: 1px solid rgba(232, 232, 232, 1);
  border-radius: 8px 8px 0 0;
}
</style>
