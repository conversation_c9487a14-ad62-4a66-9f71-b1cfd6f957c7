<template>
  <mt-dialog
    ref="dialog"
    :header="$t('新增')"
    :zIndex="1011"
    width=""
    :buttons="buttons"
    @close="hide"
  >
    <mt-form ref="form" class="content" :model="formData" :rules="formRules">
      <mt-form-item :label="$t('参数名称')" prop="id">
        <mt-input v-model="formData.name" :placeholder="$t('请输入参数名称')" />
      </mt-form-item>

      <mt-form-item :label="$t('参数类型')" prop="type">
        <mt-input v-model="formData.type" :placeholder="$t('请输入参数类型')" />
      </mt-form-item>

      <mt-form-item :label="$t('参数描述')" prop="desc">
        <mt-input
          v-model="formData.desc"
          :multiline="true"
          :rows="2"
          :placeholder="$t('参数描述')"
        />
      </mt-form-item>
    </mt-form>
  </mt-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class WorkflowInterfaceParam extends Vue {
  @Prop()
  value!: boolean

  @Prop()
  data!: any

  buttons = [
    {
      click: this.hide,
      buttonModel: { content: '取消' }
    },
    {
      click: this.save,
      buttonModel: { isPrimary: 'true', content: '确定' }
    }
  ]

  formData = {
    name: '',
    desc: '',
    type: ''
  }

  formRules = {
    desc: [{ required: true, message: '请输入参数描述', trigger: 'blur' }],
    name: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
    type: [{ required: true, message: '请输入参数类型', trigger: 'blur' }]
  }

  get visible() {
    return this.value
  }

  set visible(v: boolean) {
    this.$emit('input', v)
  }

  get isEdit() {
    return this.data && this.data.name
  }

  @Watch('visible')
  onWatchVisible(val: boolean) {
    if (val) {
      this.show()
    }
  }

  hide() {
    this.visible = false
    const ref: any = this.$refs.dialog
    ref.ejsRef.hide()
  }

  show() {
    this.resetFormData()
    const ref: any = this.$refs.dialog
    ref.ejsRef.show()
    if (this.data) {
      this.formData = { ...this.data }
    }
  }

  save() {
    ;(this.$refs.form as any).validate((valid: boolean) => {
      if (valid) {
        if (this.isEdit) {
          this.$emit('save', this.formData)
        } else {
          this.$emit('add', this.formData)
        }
        this.hide()
      } else {
        this.$toast({
          content: '输入有误，请检查',
          type: 'warning'
        })
      }
    })
  }

  private resetFormData() {
    this.formData = {
      name: '',
      desc: '',
      type: ''
    }
  }
}
</script>

<style lang="scss" scoped></style>
