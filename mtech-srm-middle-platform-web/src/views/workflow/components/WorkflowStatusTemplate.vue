<template>
  <div class="grid-edit-column">
    <div v-if="data.version > 0" class="status publish--status">{{ $t('已发布') }}</div>
    <div v-else class="status unpublish--status">{{ $t('草稿') }}</div>
    <div class="column-tool mt-flex flow-id-template">
      <div
        v-for="item in buttons"
        :key="item.id"
        class="template-svg"
        :name="item.icon"
        @click="handleClickCellTool(item)"
      >
        <i :class="['mt-icons', 'mt-icon-' + item.icon]"></i>
        <span class="icon-title">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import Bus from '@/utils/bus'

export default {
  name: 'WorkflowStatusTemplate',
  data() {
    return {
      data: {},
      buttons: [{ id: 'Publish', icon: 'icon_Share_2', title: this.$t('发布') }]
    }
  },
  mounted() {},
  methods: {
    handleClickCellTool(item) {
      if (this.data.type === 'instance') {
        Bus.$emit('instance-handleClickCellTool', {
          tool: item,
          data: this.data
        })
      } else {
        Bus.$emit('workflow-handleClickCellTool', {
          tool: item,
          data: this.data
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}

.grid-edit-column {
  padding-left: 10px;

  .status {
    font-size: 12px;
    line-height: 12px;
    padding: 4px;
    border-radius: 2px;
    text-align: center;
    display: inline-block;
    margin-bottom: 2px;
  }

  .publish--status {
    color: #9baac1;
    background: rgba(245, 246, 248, 1);
  }

  .unpublish--status {
    color: #6386c1;
    background: rgba(238, 242, 249, 1);
  }

  .column-tool {
    color: #6386c1;
    .template-svg {
      cursor: pointer;
      margin-left: 10px;
      line-height: 1;
      &:first-of-type {
        margin-left: 0;
      }
      .mt-icons,
      .icon-title {
        font-size: 12px;
      }
      .icon-title {
        font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system',
          'BlinkMacSystemFont';
      }
    }
  }
}
</style>
