<template>
  <div class="home">
    <mt-row justify="space-between" type="flex">
      <mt-col :span="23">
        <mt-select
          :data-source="historyList"
          :fields="field"
          :show-clear-button="true"
          :allow-filtering="true"
          :placeholder="$t('请选择历史版本')"
          @change="changeVersion"
        ></mt-select>
      </mt-col>
      <mt-col style="width: 180px">
        <mt-button
          class="btn--revert"
          icon-css="mt-icons mt-icon-icon_solid_Filter"
          css-class="e-flat"
          icon-position="Right"
          @click.native="revert"
          >{{ $t('回滚') }}</mt-button
        >

        <mt-button
          class="btn--close"
          icon-css="mt-icons mt-icon-icon_solid_close"
          css-class="e-flat"
          icon-position="Right"
          @click="close"
          >{{ $t('关闭') }}</mt-button
        >
      </mt-col>
    </mt-row>
    <div>
      <img :src="historyImg" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class WorkFlowHistory extends Vue {
  get flowId() {
    return this.$route.params.id || ''
  }

  get from() {
    return this.$route.query.from
  }

  field = {
    text: 'name',
    value: 'id'
  }

  historyList = []
  versionId = ''
  historyImg = ''

  created() {
    this.getHistoryList(this.flowId)
  }

  getHistoryList(id: string) {
    this.$api.workflow
      .tempateHistoryPage({
        templateId: id,
        current: 1,
        size: 999
      })
      .then((res: any) => {
        this.historyList = res.data?.records || []
      })
  }

  changeVersion(event: any) {
    this.versionId = event.value
    this.getVersionImg(event.value)
  }

  // 获取版本图片
  getVersionImg(id: string) {
    this.$api.workflow
      .templateHistoryImg({
        id: id
      })
      .then((res: any) => {
        const blob = new Blob([res.data], { type: 'image/svg+xml' })
        const url = URL.createObjectURL(blob)
        // const image = document.createElement('img')
        // image.src = url

        this.historyImg = url
      })
  }

  revert() {
    if (!this.versionId) return

    this.$api.workflow
      .rollbackHistory({
        historyId: this.versionId
      })
      .then((res: any) => {
        if (res.code === 200) {
          this.$toast({
            content: '回退成功',
            type: 'success'
          })
        }
      })
      .catch((err: any) => {
        this.$toast({
          content: err.msg || '回退失败',
          type: 'error'
        })
      })
  }

  close() {
    if (this.from === 'template') {
      this.$router.push('/middlePlatform/workflowtemplate')
    } else {
      this.$router.push('/middlePlatform/workflowprocess')
    }
  }
}
</script>

<style lang="scss" scoped>
.btn--close {
  /deep/ button {
    color: #ed5633 !important;
  }
}
.btn--revert {
  /deep/ button {
    color: #4f5b6d !important;
  }
}
</style>
