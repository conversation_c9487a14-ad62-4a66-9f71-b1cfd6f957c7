<template>
  <div class="work-flow-list">
    <CategoryTree class="tree-view--wrap" type="service" @nodeSelected="nodeSelected" />

    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>

    <WorkflowServeEdit
      v-model="isShowSidebar"
      :id="currentEditServeId"
      :categoryId="currentCategory.id"
      @add="refreshTempaltePage"
      @update="refreshTempaltePage"
    />
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import CategoryTree from './components/CategoryTree.vue'
import WorkflowServeEdit from './components/WorkflowServeEdit.vue'

import { flowServePageConfig } from './config'

@Component({
  components: {
    CategoryTree,
    WorkflowServeEdit
  }
})
export default class WorkflowServe extends Vue {
  pageConfig: any[] = flowServePageConfig

  isShowSidebar = false
  currentCategory: any = {} // 当前选择的分类
  currentEditServeId = '' // 当前正在编辑的服务ID

  mounted() {}

  nodeSelected(nodeData: any) {
    this.getTemplatePage(nodeData)
  }

  // 获取分类下的模板列表
  getTemplatePage(nodeData: any) {
    this.currentCategory = nodeData || {}
    this.pageConfig[0].grid.asyncConfig.query = Object.assign(
      {},
      this.pageConfig[0].grid.asyncConfig.query,
      { categoryId: nodeData.id }
    )
  }

  handleClickToolBar(e: any) {
    const { grid, toolbar } = e
    if (e.toolbar.id === 'Add') {
      this.showServerSidebar()
      return
    }

    const sections = grid.getSelectedRecords()
    if (sections.length === 0) {
      this.$toast({
        content: '请先选择服务',
        type: 'warning'
      })
      return
    }
    const idList = sections.map((v: { id: any }) => v.id)

    if (toolbar.id === 'Delete') {
      this.deleteTemplate(idList)
    }
  }

  handleClickCellTool(e: any) {
    const { tool, data } = e
    if (tool.id === 'edit') {
      this.showServerSidebar(data.id)
    } else if (tool.id === 'delete') {
      this.deleteTemplate([e.data.id])
    }
  }

  // 打开新增接口侧边栏
  showServerSidebar(id?: string) {
    this.isShowSidebar = true
    this.currentEditServeId = id || ''
  }

  // 删除模板
  deleteTemplate(idList: string[]) {
    this.$api.workflow
      .flowServiceDelete({
        ids: idList
      })
      .then((res: { code: number }) => {
        if (res.code === 200) {
          this.refreshTempaltePage()
          this.$toast({
            content: '删除成功',
            type: 'success'
          })
        }
      })
      .catch((err: { msg: any }) => {
        this.$toast({
          content: err.msg || '删除失败',
          type: 'error'
        })
      })
  }

  refreshTempaltePage() {
    const ref = this.$refs.tempaltePageRef as any
    ref.refreshCurrentGridData()
  }
}
</script>
<style lang="scss" scoped>
.work-flow-list {
  width: 100%;
  height: 100%;
  display: flex;
}

.tree-view--wrap {
  flex: 0 0 300px;
  background: #fff;
  border-right: 1px solid #eee;

  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }

  ::v-deep .e-list-text {
    width: 100% !important;
  }

  /deep/ .e-list-text {
    width: 100% !important;
  }
}

.data-grid--wrap {
  flex: 1;
  overflow: hidden;
}
</style>
