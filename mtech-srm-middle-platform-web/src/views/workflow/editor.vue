<template>
  <div class="workflow-edit">
    <WorkflowController
      @control="handleController"
      :form-data="formData"
      :moduleName="moduleName"
      :is-edit="true"
    />
    <MtechBpmnEditor :id="templateId" ref="editor"> </MtechBpmnEditor>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import MtechBpmnEditor from '@digis/digis-bpmn-editor'
import WorkflowController from './components/WorkflowController.vue'

@Component({
  components: {
    MtechBpmnEditor,
    WorkflowController
  }
})
export default class WorkFlowEditor extends Vue {
  get templateId() {
    return this.$route.params.id || ''
  }

  get type() {
    return this.$route.query.type
  }

  get moduleName() {
    if (this.type === 'template') {
      return '模板'
    } else if (this.type === 'instance') {
      return '流程'
    } else {
      return ''
    }
  }

  formData: any = {
    id: '',
    key: '',
    name: '',
    version: ''
  }

  created() {
    this.getTemplateData(this.templateId)
  }

  getTemplateData(id: string) {
    this.$api.workflow
      .getTemplateData({
        id: id
      })
      .then((res: any) => {
        this.formData = {
          ...res.data
        }
      })
  }

  handleController(type: string, formData: any) {
    if (type === 'save' && this.formData.id) {
      this.save(this.formData.id, formData)
    }
    if (type === 'publish') {
      this.publish(formData)
    } else if (type === 'close') {
      this.close()
    }
  }

  save(id: string, formData: any) {
    const editor = this.$refs.editor as any
    editor.getXML().then((res: any) => {
      this.$api.workflow
        .saveFlowTemplate({
          id: id,
          name: formData.name,
          data: res
        })
        .then((res: any) => {
          this.formData.id = res.data.id
          this.formData.key = res.data.key
          this.$toast({
            content: '保存成功',
            type: 'success'
          })
        })
        .catch((err: any) => {
          this.$toast({
            content: err.msg || '保存失败',
            type: 'error'
          })
        })
    })
  }

  publish(formData: any) {
    if (!this.formData.id) {
      this.$toast({
        content: '请先保存模板，然后发布',
        type: 'warning'
      })
      return
    }

    const editor = this.$refs.editor as any
    editor.getXML().then((res: any) => {
      this.$api.workflow
        .publishTemplate({
          id: this.formData.id,
          name: formData.name,
          data: res
        })
        .then((res: any) => {
          this.formData.version = res.data.version
          this.$toast({
            content: '发布成功',
            type: 'success'
          })
        })
        .catch((err: any) => {
          this.$toast({
            content: err.msg || '发布失败',
            type: 'error'
          })
        })
    })
  }

  close() {
    if (this.type === 'template') {
      this.$router.push('/middlePlatform/workflowtemplate')
    } else if (this.type === 'instance') {
      this.$router.push('/middlePlatform/workflowprocess')
    } else {
      this.$router.push('/middlePlatform/workflowtemplate')
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-edit {
  width: 100%;
  height: calc(100% - 86px);
}
</style>
