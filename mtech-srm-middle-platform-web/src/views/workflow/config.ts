// import Vue from 'vue'
// import ListRowTemplate from '@/components/pageComponents/workflow/ListRowTemplate.vue'
import WorkflowStatusTemplate from './components/WorkflowStatusTemplate.vue'

export const strategyListToolBar = [
  [
    { id: 'Add', icon: 'icon_solid_Createorder', title: '新增' },
    // { id: 'Paste', icon: 'icon_solid_edit', title: '复制' },
    { id: 'Delete', icon: 'icon_solid_Delete', title: '删除' }
  ],
  ['Setting']
]

export const componentConfig = [
  {
    title: '',
    toolbar: ['add', 'delete', { id: 'version', icon: 'icon_solid_Submit', title: '查看历史版本' }],
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'key',
          headerText: '模板编码',
          cellTools: [
            // 用户单元格按钮，详见celltools备注。
            'edit',
            'delete'
          ]
        },
        {
          field: 'name',
          headerText: '模板名称'
        },
        {
          field: 'status',
          headerText: '状态',
          template() {
            return {
              template: WorkflowStatusTemplate
            }
          }
        },
        {
          field: 'version',
          headerText: '版本号'
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/flow/tenant/template/queryBuilder',
        query: {
          category: '',
          type: 'TemplateModel'
        }
      }
    }
  }
]

export const flowInstancePageConfig = [
  {
    title: '',
    toolbar: ['add', 'delete', { id: 'version', icon: 'icon_solid_Submit', title: '查看历史版本' }],
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'key',
          headerText: '模板编码',
          cellTools: [
            // 用户单元格按钮，详见celltools备注。
            'edit',
            'delete'
          ]
        },
        {
          field: 'name',
          headerText: '模板名称'
        },
        {
          field: 'status',
          headerText: '状态',
          template() {
            return {
              template: WorkflowStatusTemplate
            }
          }
        },
        {
          field: 'version',
          headerText: '版本号'
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/flow/tenant/template/queryBuilder',
        query: {
          category: '',
          type: 'ProcessModel'
        },
        serializeList(list: any[]) {
          return list.map((v) => {
            v.type = 'instance'
            return v
          })
        }
      }
    }
  }
]

export const flowServePageConfig = [
  {
    title: '',
    toolbar: ['add', 'delete'],
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'id',
          headerText: '服务编码',
          cellTools: ['edit', 'delete']
        },
        {
          field: 'name',
          headerText: '服务名称'
        },
        {
          field: 'type.label',
          headerText: '服务类型'
        },
        {
          field: 'desc',
          headerText: '服务描述'
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/flow/tenant/flowService/queryBuilder',
        query: { categoryId: '' }
      }
    }
  }
]

export const flowInterfacePageConfig = [
  {
    toolbar: ['add', 'delete'],
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'id',
          headerText: '接口ID',
          cellTools: ['edit', 'delete']
        },
        {
          field: 'name',
          headerText: '接口名称'
        },
        {
          field: 'desc',
          headerText: '接口描述'
        },
        {
          field: 'url',
          headerText: '接口地址'
        }
      ],
      dataSource: [],
      asyncConfig: {
        url: '/flow/tenant/flowInterface/queryBuilder',
        query: {}
      }
    }
  }
]

export const flowInterfaceParamConfig = [
  {
    toolbar: ['add', 'delete'],
    grid: {
      columnData: [
        {
          width: '40',
          type: 'checkbox'
        },
        {
          field: 'name',
          headerText: '参数名称',
          cellTools: ['edit', 'delete']
        },
        {
          field: 'type',
          headerText: '参数类型'
        },
        {
          field: 'desc',
          headerText: '参数描述'
        }
      ],
      dataSource: []
    }
  }
]
