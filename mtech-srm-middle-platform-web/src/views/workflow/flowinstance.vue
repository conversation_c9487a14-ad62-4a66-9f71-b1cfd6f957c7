<template>
  <div class="work-flow-list">
    <CategoryTree class="tree-view--wrap" type="processModel" @nodeSelected="nodeSelected" />

    <mt-template-page
      ref="tempaltePageRef"
      :hiddenTabs="true"
      :templateConfig="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    >
    </mt-template-page>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from '@mtech/vue-property-decorator'
import CategoryTree from './components/CategoryTree.vue'
import { flowInstancePageConfig } from './config'
// import Bus from '@/utils/bus.js'
const Bus: any = require('@/utils/bus.js').default

@Component({
  components: {
    CategoryTree
  }
})
export default class WorkflowList extends Vue {
  pageConfig: any[] = flowInstancePageConfig

  currentPublishTemplate!: any // 当前将要发布的模板
  currentCategory!: any // 当前选择的分类

  mounted() {
    Bus.$on('instance-handleClickCellTool', this.clickPublish)
  }

  nodeSelected(nodeData: any) {
    this.getTemplatePage(nodeData)
  }

  // 获取分类下的模板列表
  getTemplatePage(nodeData: any) {
    this.currentCategory = nodeData || {}
    this.pageConfig[0].grid.asyncConfig.query = Object.assign(
      {},
      this.pageConfig[0].grid.asyncConfig.query,
      { category: nodeData.id }
    )
  }

  clickPublish(event: any) {
    if (event.tool.id === 'Publish') {
      this.currentPublishTemplate = event.data
      this.$dialog({
        data: {
          title: '提示',
          message: '是否确认发布？'
        },
        success: () => this.confirmPublishTemplate()
      })
    }
  }

  confirmPublishTemplate() {
    this.$api.workflow
      .publishTemplateById({
        id: this.currentPublishTemplate.id
      })
      .then((res: { code: number; data: any }) => {
        if (res.code === 200) {
          this.currentPublishTemplate = {
            ...res.data
          }
        }
      })
      .catch((err: { msg: any }) => {
        this.$toast({
          content: err.msg || '发布错误',
          type: 'error'
        })
      })
  }

  handleClickToolBar(e: any) {
    const { grid, toolbar } = e
    if (e.toolbar.id === 'Add') {
      this.addTemplate()
    }

    const sections = grid.getSelectedRecords()
    if (sections.length === 0) {
      return
    }
    const idList = sections.map((v: { id: any }) => v.id)

    if (toolbar.id === 'version') {
      this.viewHistory(idList)
    } else if (toolbar.id === 'Delete') {
      this.deleteTemplate(idList)
    }
  }

  handleClickCellTool(e: any) {
    const { tool, data } = e
    if (tool.id === 'edit') {
      this.editTemplate(data.id)
    } else if (tool.id === 'delete') {
      this.deleteTemplate([e.data.id])
    }
  }

  // 查看历史版本
  viewHistory(idList: string[]) {
    if (idList.length === 1) {
      const id = idList[0]
      this.$router.push('/middlePlatform/workflowhistory/' + id)
    } else {
      this.$toast({
        content: '一次只能选择一个流程进行查看',
        type: 'warning'
      })
    }
  }

  // 删除流程
  deleteTemplate(idList: string[]) {
    this.$api.workflow
      .flowInterfaceDelete({
        ids: idList
      })
      .then((res: { code: number }) => {
        if (res.code === 200) {
          const ref = this.$refs.tempaltePageRef as any
          ref.refreshCurrentGridData()
          this.$toast({
            content: '删除成功',
            type: 'success'
          })
        }
      })
      .catch((err: { msg: any }) => {
        this.$toast({
          content: err.msg || '删除失败',
          type: 'error'
        })
      })
  }

  // 编辑模板
  editTemplate(id: string) {
    if (id) {
      this.$router.push('/middlePlatform/workfloweditor/' + id + '?type=instance')
    }
  }

  // 新增版本
  addTemplate() {
    this.$router.push({
      path: `/middlePlatform/workflowadd/${this.currentCategory.id}`,
      query: {
        type: 'instance'
      }
    })
  }
}
</script>
<style lang="scss" scoped>
.work-flow-list {
  width: 100%;
  height: 100%;
  display: flex;
}

.tree-view--wrap {
  flex: 0 0 300px;
  background: #fff;
  border-right: 1px solid #eee;

  .trew-node--add {
    line-height: 50px;
    padding: 0 20px;
    color: #4f5b6d;
  }

  .tree-view--template {
    width: 100%;
  }

  ::v-deep .e-list-text {
    width: 100% !important;
  }

  /deep/ .e-list-text {
    width: 100% !important;
  }
}

.data-grid--wrap {
  flex: 1;
  overflow: hidden;
}
</style>
