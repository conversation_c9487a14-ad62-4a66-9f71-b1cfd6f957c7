<template>
  <div class="list-item">
    <div v-if="todoList.length" class="list-item-content">
      <div class="list-item-title">
        <div>
          <img src="../../../assets/预警.png" class="dontlist-icon" />
          {{ $t('预警') }}
          <span class="list-item-tabs__more" @click="routerTo()">{{ $t('查看更多') }} ></span>
        </div>
      </div>
      <div class="list-item-tabs">
        <div
          v-for="(item, index) in todoList"
          :key="item.todoTab"
          :class="['list-item-tabs__tab', currentTabIndex === index ? 'active' : '']"
          @click="clickTab(index)"
        >
          <p>{{ getTabName(item.todoTab) }}</p>
          <span>{{ item.count }}</span>
        </div>
      </div>
      <div v-if="listContent && listContent.length" class="list-item-data">
        <template v-for="(item, index) in listContent">
          <div v-if="index < 4" :key="index" class="data-row">
            <div class="data-row-area" @click="linkTo(item.urlRef)">
              <span class="data-row-icon" :class="currentTabIndex === 1 ? 'bg-grey' : ''" />
              <span class="data-row-content">{{ item.title }}</span>
            </div>
            <span class="data-row-time">{{ getTime(item) }}</span>
          </div>
        </template>
      </div>
      <div v-else class="list-no-data">{{ $t('暂无数据') }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    todoList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      listContent: [],
      currentTabIndex: 0
    }
  },
  watch: {
    todoList: {
      handler(newVal) {
        if (newVal) {
          this.currentTabIndex = 0
          this.listContent = this.todoList[0]['record']
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getTabName(todoTab) {
      // 获取tab名
      switch (todoTab) {
        case 0:
          return this.$t('待处理列表')
        case 1:
          return this.$t('已处理列表')
        case 2:
          return this.$t('超期预警')
        case 3:
          return this.$t('驳回预警')
        default:
          return ''
      }
    },
    getTime(item) {
      if (item.todoTab === 1) {
        // 已处理列表显示处理时间
        return item.handlerTime.substr(0, 11)
      }
      // 显示创建时间
      return item.createTime.substr(0, 11)
    },
    clickTab(index) {
      if (this.currentTabIndex !== index) {
        // 点击tab按钮更新tab当前下标及数据
        this.currentTabIndex = index
        this.listContent = this.todoList[index]['record']
      }
    },
    routerTo() {
      console.log('前往任务列表')
      if (this.$route.path === '/main/dashboard') {
        this.$router.push(`/middlePlatform/pur/taskList`)
      } else {
        this.$router.push(`/middlePlatform/sup/taskList`)
      }
    },
    linkTo(ref) {
      if (ref) {
        // window.open(ref, '_blank')
        const linkArr = ref.split('/#')
        if (linkArr[1]) {
          this.$router.push({
            path: linkArr[1]
            // query: {
            //   id: args.id
            // }
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.list-item {
  // max-width: calc(50% - 16px);
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
  // &-header {
  //   background: #ccc;
  //   display: flex;
  //   justify-content: space-between;
  //   padding: 5px 10px;
  //   &__title {
  //     font-weight: bold;
  //   }
  //   &__icon {
  //     cursor: pointer;
  //   }
  // }
  &-content {
    padding: 0 24px 24px;
  }

  .list-item-title {
    border-bottom: 1px dashed #e9e9e9;
    font-weight: 500;
    font-size: 16px;
    div {
      padding: 16px 0;
    }
    .list-item-tabs__more {
      position: unset;
      float: right;
      color: darkgrey;
    }
  }
  &-tabs {
    display: flex;
    &__tab {
      margin: 0 10px;
      box-sizing: border-box;
      cursor: pointer;
      &:first-child {
        margin-left: 0;
      }
      display: flex;
      align-items: center;
      p {
        margin-bottom: unset;
        padding: 16px 0;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #999999;
      }
      span {
        font-size: 12px;
        transform: scale(0.83);
        color: #fff;
        display: block;
        width: 19.2px;
        height: 19.2px;
        background: #f55448;
        border: 1px solid #ffffff;
        line-height: 17px;
        text-align: center;
        border-radius: 50%;
        margin: -8px 0 0 2px;
      }
      &.active {
        p {
          padding: 16px 0 14px;
          border-bottom: 2px solid red;
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
    position: relative;
    &__more {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #3678fe;
      position: absolute;
      right: 0;
      bottom: 19px;
      cursor: pointer;
      user-select: none;
    }
  }
  &-data {
    padding-top: 3px;
    min-height: 150px;
    .data-row {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      span {
        display: block;
      }
      &-icon {
        display: block;
        width: 6px;
        height: 6px;
        background: #3678fe;
        border-radius: 50%;
        margin-right: 8px;
      }
      .bg-grey {
        background: #d8d8d8;
      }
      &-area {
        display: flex;
        align-items: center;
        cursor: pointer;
        &:hover {
          .data-row-icon {
            background: #3678fe;
          }
        }
      }
      &-content {
        max-width: calc(100% - 14px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
      &-time {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 12px;
        padding: 4px 0 0 14px;
      }
    }
  }
  .list-no-data {
    min-height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #d8d8d8;
  }
}
</style>
