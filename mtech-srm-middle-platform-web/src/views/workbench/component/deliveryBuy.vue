<template>
  <div v-if="todoList.length" class="announcement-list">
    <div class="announcement-list-content">
      <div class="list-item-header">
        <div class="list-item-title">
          <img
            v-if="todoList[0].taskGroupName === $t('送货协同')"
            src="../../../assets/送货协同.png"
            class="dontlist-icon"
          />
          <img
            v-if="todoList[0].taskGroupName === $t('委外')"
            src="../../../assets/委外合同.png"
            class="dontlist-icon"
          />

          {{ todoList[0].taskGroupName }}
        </div>
      </div>
      <div class="list-item-bottom">
        <div
          style="width: 25%; cursor: pointer"
          v-for="(item, index) in todoList"
          :key="index"
          @click="linkToDetail(item, index)"
        >
          <div class="list-item-bottom-new">{{ item.totalCount }}</div>
          <div class="list-item-bottom-new-two">{{ item.taskBusinessName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    todoList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    linkToDetail(args, index) {
      console.log('详情', args, index)
      this.$router.push({
        path: `/${args.urlRefFront}`,
        query: {
          from:
            args.taskBusinessType === 'ORDER_DELIVERY_PRO' ? 'mydelivery' + index : 'mytodo' + index
        }
      })
      sessionStorage.setItem('todoDetail', JSON.stringify(args.requestParam))
    }
  }
}
</script>

<style lang="scss" scoped>
.announcement-list {
  // max-width: calc(50% - 16px);
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
  // padding: 0 24px 4px;
  .list-item-bottom {
    text-align: center;
    display: flex;
    .list-item-bottom-new {
      font-size: 30px;
      font-weight: 700;
    }
    .list-item-bottom-new-two {
      margin-top: 5px;
      font-size: 12px;
      font-weight: 500;
    }
  }
  .list-item {
    &-content {
      padding: 0 24px 4px;
    }
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
    }
    &-title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 0;
    }
    &-more {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #3678fe;
      cursor: pointer;
      user-select: none;
    }
  }
  .list-item-content {
    overflow: hidden;
  }
  .list-item-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    .sort-number {
      font-family: Helvetica;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }
    .clock-img {
      display: block;
      min-width: 40px;
      max-width: 40px;
      height: 40px;
      background: #eef2fd;
      border-radius: 8px;
      margin: 0 8px;
    }
    .list-detail {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      width: calc(100% - 65px);
      .list-title {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        color: #333333;
      }
      .list-time {
        font-size: 12px;
        color: #999999;
      }
    }
  }
  .list-no-data {
    min-height: 198px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #d8d8d8;
  }
}
</style>
