<template>
  <div class="announcement-list">
    <div class="announcement-list-content">
      <div class="list-item-header">
        <div class="list-item-title">
          <img src="../../../assets/通知公告.png" class="dontlist-icon" />
          {{ $t('通知公告') }}
        </div>
        <span class="list-item-more" @click="linkTo()">{{ $t('查看更多') }} ></span>
      </div>
      <div v-if="announcementList && announcementList.length" class="list-item-content">
        <template v-for="(item, index) in announcementList">
          <div v-if="index < 4" :key="index" class="list-item-area" @click="linkToDetail(item)">
            <!-- <span class="sort-number">{{ index + 1 }}</span> -->
            <!-- <img src="./../../../assets/icon_anounce.png" class="clock-img" /> -->
            <div class="list-detail">
              <!-- <span :class="[index < 0 ? 'list-title-bold' : 'list-title']"> -->
              <span :class="[item.hasView === 0 ? 'list-title-bold' : 'list-title']">
                <span class="list-title-dian">·</span>
                {{ item.title }}
              </span>
              <span class="list-time">{{ item.releaseTime }}</span>
            </div>
          </div>
        </template>
      </div>
      <div v-else class="list-no-data">{{ $t('暂无数据') }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    announcementList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    linkTo() {
      console.log('前往系统公告')
      if (this.$route.path === '/main/dashboard') {
        this.$router.push(`/middlePlatform/pur/system-notice`)
      } else {
        this.$router.push(`/middlePlatform/sup/system-notice`)
      }
    },
    linkToDetail(args) {
      console.log('详情', args)
      if (this.$route.path === '/main/dashboard') {
        this.$router.push({
          path: '/middlePlatform/pur/system-notice/detail',
          query: {
            id: args.id
          }
        })
      } else {
        this.$router.push({
          path: '/middlePlatform/sup/system-notice/detail',
          query: {
            id: args.id
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.announcement-list {
  // max-width: calc(50% - 16px);
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;

  .list-item-title {
    line-height: 30px;
    .dontlist-icon {
      margin-bottom: 3px;
    }
  }
  // padding: 0 24px 4px;
  .list-item {
    &-content {
      padding: 0 24px 4px;
    }
    &-header {
      .list-item-more {
        color: darkgrey;
      }
      display: flex;
      justify-content: space-between;
      align-items: center;
      /* padding: 0 24px; */
      width: 88%;
      margin: 0 auto;
      border-bottom: 1px dashed #e9e9e9;
    }
    &-title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 0;
    }
    &-more {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #3678fe;
      cursor: pointer;
      user-select: none;
    }
  }
  .list-item-content {
    padding-top: 15px;
    overflow: hidden;
  }
  .list-item-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer;
    .sort-number {
      font-family: Helvetica;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }
    .clock-img {
      display: block;
      min-width: 40px;
      max-width: 40px;
      height: 40px;
      background: #eef2fd;
      border-radius: 8px;
      margin: 0 8px;
    }
    .list-detail {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      width: calc(100% - 0px);
      .list-title {
        // display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        width: 60%;
        float: left;
        .list-title-dian {
          font-weight: 600;
        }
        color: gray;
        font-weight: 400;
      }
      .list-title-bold {
        .list-title-dian {
          font-weight: 600;
          color: red;
        }
        // display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 600;
        font-size: 14px;
        width: 60%;
        float: left;
        color: #333333;
      }
      .list-time {
        font-size: 12px;
        width: 30%;
        float: right;
        color: #999999;
      }
    }
  }
  .list-no-data {
    min-height: 198px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #d8d8d8;
  }
}
</style>
