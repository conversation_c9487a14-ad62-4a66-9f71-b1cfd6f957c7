<template>
  <div class="announcement-list">
    <div class="announcement-list-content">
      <div class="list-item-header">
        <div class="list-item-title">{{ $t('待处理列表') }}</div>
        <!-- <span class="list-item-more" @click="linkTo()">更多</span> -->
      </div>

      <div class="list-item-content">
        <template v-for="(item, index) in todoNewList">
          <div v-if="index < 7" :key="index" class="list-item-area" @click="linkToDetail(item)">
            <!-- <span class="sort-number">{{ index + 1 }}</span> -->
            <!-- <img src="./../../../assets/icon_anounce.png" class="clock-img" /> -->
            <div class="data-row-area">
              <span class="data-row-icon" />
              <span class="data-row-content">{{ item.taskBusinessName }}</span>
              <p>{{ item.totalCount }}</p>

              <!-- <span class="list-title">{{ item. }} {{ item.totalCount }}</span> -->
              <!-- <span class="list-time">{{ item.releaseTime }}</span> -->
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
// import { queryParams } from '@syncfusion/ej2-base'

export default {
  props: {
    todoNewList: {
      type: Object,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    linkToDetail(args) {
      console.log('详情', args)
      this.$router.push({
        path: `/${args.urlRefFront}`,
        query: { from: 'mytodo' }
      })
      sessionStorage.setItem('todoDetail', JSON.stringify(args.requestParam))

      // if (this.$route.path === '/main/dashboard') {
      //   this.$router.push({
      //     path: '/middlePlatform/pur/system-notice/detail',
      //     query: {
      //       id: args.id
      //     }
      //   })
      // } else {
      //   this.$router.push({
      //     path: '/middlePlatform/sup/system-notice/detail',
      //     query: {
      //       id: args.id
      //     }
      //   })
      // }
    }
  }
}
</script>

<style lang="scss" scoped>
.announcement-list {
  // max-width: calc(50% - 16px);
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #fff;
  // padding: 0 24px 4px;
  .list-item {
    &-content {
      padding: 0 24px 4px;
    }
    &-header {
      display: flex;
      border-bottom: 1px dashed #e9e9e9;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
    }
    &-title {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 0;
    }
    &-more {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #3678fe;
      cursor: pointer;
      user-select: none;
    }
  }
  .list-item-content {
    overflow: hidden;
  }
  .data-row {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
  }
  .data-row-area {
    display: flex;
    p {
      font-size: 12px;
      transform: scale(0.83);
      color: #fff;
      display: block;
      width: 19.2px;
      height: 19.2px;
      background: #f55448;
      border: 1px solid #ffffff;
      line-height: 17px;
      text-align: center;
      border-radius: 50%;
      margin: -8px 0 0 2px;
    }
  }
  .data-row-icon {
    background: #3678fe;
    border-radius: 50%;
    display: block;
    margin-top: 8px;
    height: 6px;
    margin-right: 8px;
    width: 6px;
  }
  .list-item-area {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-top: 6px;
    cursor: pointer;
    .sort-number {
      font-family: Helvetica;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }
    .clock-img {
      display: block;
      min-width: 40px;
      max-width: 40px;
      height: 40px;
      background: #eef2fd;
      border-radius: 8px;
      margin: 0 8px;
    }
    .list-detail {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      width: calc(100% - 65px);
      .list-title {
        display: block;
        overflow: hidden;
        color: dodgerblue;
        font-size: 20px;
        text-overflow: ellipsis;
        white-space: nowrap;

        // color: #333333;
      }
      .list-time {
        font-size: 12px;
        color: #999999;
      }
    }
  }
  .bg-grey {
    background: #d8d8d8;
  }
  .list-no-data {
    min-height: 198px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #d8d8d8;
  }
}
</style>
