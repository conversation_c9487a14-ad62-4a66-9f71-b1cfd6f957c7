<template>
  <div style="height: 100%">
    <mt-template-page
      ref="mainTemplatePage"
      :template-config="projectListPageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <!-- <mt-horizontal-list
      :data-source="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    ></mt-horizontal-list> -->
    <mt-dialog
      ref="addDialog"
      :header="dialogTitle"
      :buttons="buttons"
      css-class="dialog-form-flex"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="name" :label="$t('项目名称')">
          <mt-input
            v-model="ruleForm.name"
            :show-clear-button="true"
            :placeholder="$t('项目名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { projectListPageConfig } from './config'
export default {
  data() {
    return {
      projectListPageConfig,
      dialogTitle: this.$t('新增项目'),
      dialogId: '',
      projectSelect: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        name: ''
      },
      rules: {
        name: [{ required: true, message: this.$t('请填写项目名称'), trigger: 'blur' }]
      },
      params: { current: 1, size: 10 },
      pageConfig: [
        {
          title: this.$t('项目列表'), // tab单项
          toolbar: [
            // 表格顶部toolbar
            ['Add', 'Delete', 'Options'],
            ['Filter', 'Export', 'Refresh', 'Setting']
          ],
          grid: {
            // 表格配置
            dataSource: [], // 数据源
            columnData: [
              // 列数据
              {
                width: '50',
                type: 'checkbox'
              },
              {
                field: 'projectName',
                headerText: this.$t('项目名称'),
                width: '150',
                isCustomColumn: true, // 用户自定义列
                cellTools: [
                  // 用户单元格按钮
                  'edit',
                  'delete'
                ]
              },
              {
                field: 'createTime',
                width: '150',
                headerText: this.$t('创建时间')
              },
              {
                field: 'updateTime',
                width: '150',
                headerText: this.$t('修改时间')
              }
            ]
          }
        }
      ]
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.user
    }
  },
  methods: {
    hide() {
      this.$refs.addDialog.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dialogTitle === this.$t('增加项目')) {
            const params = {
              projectName: this.ruleForm.name,
              tenantId: parseInt(this.userInfo.accountId)
            }
            this.$api.ruleEng.addProject(params).then(() => {
              this.refreshMain()
              this.$refs.addDialog.ejsRef.hide()
            })
          } else {
            const params = {
              projectName: this.ruleForm.name,
              id: this.dialogId
            }
            this.$api.ruleEng.updataProject(params).then(() => {
              this.refreshMain()
              this.$refs.addDialog.ejsRef.hide()
            })
          }
        } else {
          return false
        }
      })
    },
    async getProjectPage() {
      await this.$api.ruleEng.getProjectPage(this.params).then((r) => {
        if (r.code === 200) {
          this.pageConfig[0].grid.dataSource = [...r.data.records]
        }
      })
    },
    // 刷新main列表
    refreshMain() {
      this.$refs.mainTemplatePage.refreshCurrentGridData()
    },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      if (e.tabIndex === 0) {
        if (e.toolbar.id === 'Add') {
          this.dialogTitle = this.$t('增加项目')
          this.ruleForm.name = ''
          this.$refs.addDialog.ejsRef.show()
        } else if (e.toolbar.id === 'Delete') {
          console.log(e.grid.getSelectedRecords())
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 0) {
            this.$toast({
              content: this.$t('请至少选择一条数据进行操作！'),
              type: 'warning'
            })
          } else {
            const p = []
            arr.forEach((item) => {
              p.push(item.id)
            })
            this.deleteProject(p)
          }
        } else if (e.toolbar.id === 'Refresh') {
          this.refreshMain()
        }
      }
    },
    // 删除规则方法
    deleteProject(arr) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？')
        },
        success: () => {
          this.$api.ruleEng.deleteProject({ ids: arr }).then(() => {
            this.refreshMain()
          })
        }
      })
    },
    handleClickCellTool(a) {
      // 单元格 图标点击 tool
      console.log('use-handleClickCellTool', a)

      if (a.tool.id === 'edit') {
        this.$refs.addDialog.ejsRef.show()
        this.dialogTitle = this.$t('编辑项目')
        this.dialogId = a.data.id
        this.ruleForm.name = a.data.projectName
      } else if (a.tool.id === 'delete') {
        this.deleteProject([a.data.id])
      }
    },
    handleClickCellTitle() {
      // console.log('use-handleClickCellTool', a)
      // this.$router.push({ name: 'objectDetails', params: a.data })
    }
  }
}
</script>
