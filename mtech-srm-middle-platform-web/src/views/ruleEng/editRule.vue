<template>
  <div class="code-mirror-div">
    <div class="tool-bar">
      <div class="box1">
        <div
          v-if="!isEdit"
          style="
            font-size: 24px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(35, 43, 57, 1);
            margin-right: 20px;
          "
        >
          {{ code_title }}
        </div>
        <mt-input
          v-if="isEdit"
          v-model="code_title"
          width="300"
          maxlength="20"
          type="text"
          @blur="isEdit = false"
        ></mt-input>
        <mt-button
          icon-css="mt-icons mt-icon-edit-06"
          css-class="e-flat"
          style="margin-left: 10x"
          @click="setStyle"
          >{{ $t('编辑') }}</mt-button
        >
        <!-- <mt-button :is-primary="true" icon-css="mt-icons mt-icon-M_Bookmark" css-class="e-flat" style="margin-left: 10x" @click="getValue"
          >{{ $t("标签") }}</mt-button
        > -->
      </div>
      <div class="box2">
        <div style="display: flex">
          <div class="box21">
            <mt-row>
              <mt-col :span="9" style="font-size: 14px; padding-bottom: 5px"
                >{{ $t('发布状态') }}：</mt-col
              >
              <mt-col :span="9"
                ><mt-select v-model="arrValue0" :data-source="dataArr0"></mt-select
              ></mt-col>
              <mt-col :span="4"><div class="line"></div></mt-col>
            </mt-row>
          </div>
          <div class="box21">
            <mt-row class="">
              <mt-col :span="9" style="font-size: 14px; padding-bottom: 5px"
                >{{ $t('可用状态') }}：</mt-col
              >
              <mt-col :span="9"
                ><mt-select v-model="arrValue1" :data-source="dataArr1"></mt-select
              ></mt-col>
              <mt-col :span="4"><div class="line"></div></mt-col>
            </mt-row>
          </div>
          <div class="box21">
            <mt-row>
              <mt-col :span="9" style="font-size: 14px; padding-bottom: 5px"
                >{{ $t('共享状态') }}：</mt-col
              >
              <mt-col :span="9"
                ><mt-select v-model="arrValue2" :data-source="dataArr2"></mt-select
              ></mt-col>
            </mt-row>
          </div>
        </div>
        <div style="display: flex">
          <mt-button
            icon-css="mt-icons mt-icon-M_PV_Save"
            css-class="e-flat"
            style="margin-left: 10x"
            @click="saveRule"
            >{{ $t('保存') }}</mt-button
          >
          <mt-button
            icon-css="mt-icons mt-icon-delete-05"
            css-class="e-flat"
            style="margin-left: 10x"
            @click="deleteRule"
            >{{ $t('删除') }}</mt-button
          >
          <mt-button
            icon-css="mt-icons mt-icon-icon_solid_close"
            css-class="e-flat dangerClass"
            style="margin-left: 50x"
            @click="$router.go(-1)"
            >{{ $t('关闭') }}</mt-button
          >
        </div>
      </div>
    </div>
    <div style="display: flex; width: 100%; margin-top: 3px">
      <div class="left-box">
        <div class="left-top-box">
          <div style="height: 36px; line-height: 36px; overflow: hidden">
            <span
              style="
                font-size: 16px;
                font-family: PingFangSC;
                font-weight: 600;
                color: rgba(35, 43, 57, 1);
                float: left;
              "
              >{{ $t('对象库') }}</span
            ><span
              style="
                font-size: 12px;
                font-family: PingFangSC;
                font-weight: normal;
                color: rgba(154, 154, 154, 1);
                float: right;
              "
              >{{ $t('点击对象或拖动对象至文本域') }}</span
            >
          </div>
          <div class="e-input-group">
            <input
              v-model="objectData"
              class="e-input"
              type="text"
              :placeholder="$t('请输入文本')"
            />
            <span
              class="mt-icons mt-icon-MT_Search"
              style="color: #979797; line-height: 30px"
              @click="serchObject"
            ></span>
          </div>
        </div>
        <div class="left-top-tree">
          <mt-treeView
            ref="treeview"
            :fields="filedsIcon"
            :allow-drag-and-drop="true"
            :node-template="templateData"
            @nodeDragStop="nodeDragStop"
            @nodeDragging="nodeDrag"
          ></mt-treeView>
        </div>
      </div>
      <div class="code-box">
        <div class="code-tab">
          <div>{{ $t('编辑文本域') }}</div>
          <div>{{ `v${version}` }}</div>
        </div>
        <code-mirror-editor
          ref="cmEditor"
          :cm-theme="cmTheme"
          :cm-mode="cmMode"
          :auto-format-json="autoFormatJson"
          :json-indentation="jsonIndentation"
        ></code-mirror-editor>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
// 使用时需要根据CodeMirrorEditor.vue的实际存放路径，调整from后面的组件路径，以便正确引用
import CodeMirrorEditor from './codeEditor'
export default {
  name: 'EditRule',
  components: {
    CodeMirrorEditor
  },
  data() {
    return {
      objectData: '',
      dataArr0: [
        { text: this.$t('未发布'), value: 0 },
        { text: this.$t('已发布'), value: 1 }
      ],
      dataArr1: [
        { text: this.$t('不可用'), value: 1 },
        { text: this.$t('可用'), value: 0 }
      ],
      dataArr2: [
        { text: this.$t('不共享'), value: 0 },
        { text: this.$t('共享'), value: 1 }
      ],
      arrValue0: 0,
      arrValue1: 0,
      arrValue2: 0,
      code_title: this.$t('标题'),
      version: '1',
      isEdit: false,
      nodeClickData: '',
      cmTheme: '3024-day', // codeMirror主题
      // codeMirror主题选项
      cmThemeOptions: [
        'default',

        '3024-day',

        '3024-night',

        'abcdef',

        'ambiance',

        'ayu-dark',

        'ayu-mirage',

        'base16-dark',

        'base16-light',

        'bespin',

        'blackboard',

        'cobalt',

        'colorforth',

        'darcula',

        'dracula',

        'duotone-dark',

        'duotone-light',

        'eclipse',

        'elegant',

        'erlang-dark',

        'gruvbox-dark',

        'hopscotch',

        'icecoder',

        'idea',

        'isotope',

        'scsser-dark',

        'liquibyte',

        'lucario',

        'material',

        'material-darker',

        'material-palenight',

        'material-ocean',

        'mbo',

        'mdn-like',

        'midnight',

        'monokai',

        'moxer',

        'neat',

        'neo',

        'night',

        'nord',

        'oceanic-next',

        'panda-syntax',

        'paraiso-dark',

        'paraiso-light',

        'pastel-on-dark',

        'railscasts',

        'rubyblue',

        'seti',

        'shadowfox',

        'solarized dark',

        'solarized light',

        'the-matrix',

        'tomorrow-night-bright',

        'tomorrow-night-eighties',

        'ttcn',

        'twilight',

        'vibrant-ink',

        'xq-dark',

        'xq-light',

        'yeti',

        'yonce',

        'zenburn'
      ],
      cmEditorMode: 'java', // 编辑模式
      // 编辑模式选项
      cmEditorModeOptions: [
        'default',
        'json',
        'sql',
        'javascript',
        'css',
        'xml',
        'html',
        'yaml',
        'markdown',
        'python'
      ],
      cmMode: 'javascript', // codeMirror模式
      jsonIndentation: 2, // json编辑模式下，json格式化缩进 支持字符或数字，最大不超过10，默认缩进2个空格
      autoFormatJson: true, // json编辑模式下，输入框失去焦点时是否自动格式化，true 开启， false 关闭
      templateData: () => {
        const _this = this
        return {
          template: Vue.component('EmpTemplate', {
            data() {
              return {
                data: {}
              }
            },
            methods: {
              getData() {
                console.log(this)
                // if (this.data.isUnDrag) {
                // } else {
                //   _this.$refs.cmEditor.setLine(this.data.name)
                // }
                if (!this.data.isUnDrag) {
                  _this.$refs.cmEditor.setLine(this.data.name)
                }
              }
            },
            template: `<span>
                            <span>
                              <div @click='getData'>
                                <span>{{data.name}}</span>
                                <span v-if='data.fieldType'>:({{data.fieldType}})</span>
                                <span v-if='data.fieldDesc'>({{data.fieldDesc}})</span>
                              </div>
                            </span>
                          </span>`
          })
        }
      },
      filedsIcon: {
        id: 'id',
        text: 'name',
        child: 'children',
        // id: 'nodeId',
        // text: 'nodeText',
        // child: 'nodeChild',
        dataSource: [
          // {
          //   nodeId: '04',
          //   nodeText: 'Pictures',
          //   icon: 'folder',
          //   expanded: true,
          //   nodeChild: [
          //     {
          //       nodeId: '04-01',
          //       nodeText: 'Camera Roll',
          //       expanded: true,
          //       icon: 'folder',
          //       nodeChild: [
          //         {
          //           nodeId: '04-01-01',
          //           nodeText: 'WIN_20160726_094117.JPG'
          //         },
          //         {
          //           nodeId: '04-01-02',
          //           nodeText: 'WIN_20160726_094118.JPG'
          //         }
          //       ]
          //     },
          //     { nodeId: '04-02', nodeText: 'Wind.jpg', icon: 'folder' },
          //     { nodeId: '04-03', nodeText: 'Stone.jpg', icon: 'folder' }
          //   ]
          // },
          // {
          //   nodeId: '05',
          //   nodeText: 'Downloads',
          //   icon: 'folder',
          //   nodeChild: [
          //     { nodeId: '05-01', nodeText: 'UI-Guide.pdf' },
          //     { nodeId: '05-02', nodeText: 'Tutorials.zip' },
          //     { nodeId: '05-03', nodeText: 'Game.exe' },
          //     { nodeId: '05-04', nodeText: 'TypeScript.7z' }
          //   ]
          // }
        ],
        iconCss: 'icon',
        imageUrl: 'image'
      }
    }
  },

  computed: {
    userInfo() {
      return this.$store.state.user
    }
  },
  created() {
    this.getObjectTree('')
  },
  mounted() {
    if (this.$route.params.id !== 1) {
      this.$api.ruleEng.getRule(this.$route.params.id).then((r) => {
        this.setValue(r.data.ruleCode)
        this.arrValue0 = r.data.deployState
        this.arrValue1 = r.data.enabled
        this.arrValue2 = r.data.ruleShared
        this.code_title = r.data.ruleName
        this.version = r.data.ruleVersion
      })
    }
  },
  methods: {
    // 搜索对象树状
    serchObject() {
      this.getObjectTree(this.objectData)
    },
    getObjectTree(params) {
      this.$api.ruleEng.getObjectTree(params).then((r) => {
        this.filedsIcon.dataSource = r.data
        this.filedsIcon.dataSource.forEach((e) => {
          e.isUnDrag = true
        })
        // this.$refs.treeview.ejsRef.refreshNode()
        const chatInfo = JSON.parse(JSON.stringify(this.filedsIcon))
        this.filedsIcon = {}
        this.filedsIcon = JSON.parse(JSON.stringify(chatInfo))
      })
    },
    nodeClicked(args) {
      if (args.node.ariaLevel !== '1' && args.name !== null) {
        // this.setValue(args.nodeData.text)

        console.log('nodeClicked', args)
        this.$refs.cmEditor.setLine(args.node.textContent)
      }
    },
    nodeDrag(args) {
      if (args.draggedNodeData.parentID === null) {
        args.dropIndicator = 'e-no-drop'
      } else {
        if (
          args.target.className === 'CodeMirror-scroll' ||
          args.target.className === 'cm-def' ||
          args.target.className === ' CodeMirror-line '
        ) {
          args.dropIndicator = 'e-drop-in'
        } else {
          args.dropIndicator = 'e-no-drop'
        }
      }
    },
    nodeDragStop(args) {
      console.log(args)
      if (args.draggedNodeData.parentID !== null) {
        if (
          args.target.className === 'CodeMirror-scroll' ||
          args.target.className === 'cm-def' ||
          args.target.className === ' CodeMirror-line '
        ) {
          // this.setValue(args.draggedNodeData.text)
          this.$refs.cmEditor.setLine(args.draggedNodeData.text.split(':')[0])
        } else {
          args.cancel = true
        }
      }
    },
    // 切换编辑模式事件处理函数
    onEditorModeChange(value) {
      switch (value) {
        case 'json':
          this.cmMode = 'application/json'
          break
        case 'sql':
          this.cmMode = 'sql'
          break
        case 'javascript':
          this.cmMode = 'javascript'
          break
        case 'xml':
          this.cmMode = 'xml'
          break
        case 'css':
          this.cmMode = 'css'
          break
        case 'html':
          this.cmMode = 'htmlmixed'
          break
        case 'yaml':
          this.cmMode = 'yaml'
          break
        case 'markdown':
          this.cmMode = 'markdown'
          break
        case 'python':
          this.cmMode = 'python'
          break
        default:
          this.cmMode = 'application/json'
      }
    },
    // 修改样式（不推荐，建议参考<style>中的样式，提前配置好样式）
    setStyle() {
      // let styleStr = 'position: absolute; top: 80px; left: 50px; right: 200px; bottom: 20px; padding: 2px; height: auto;'
      // this.$refs.cmEditor.setStyle(styleStr)
      this.isEdit = true
    },
    saveRule() {
      if (this.code_title == '') {
        this.$toast({
          content: this.$t('标题不能为空'),
          type: 'warning'
        })
        return
      }
      const params = {
        ruleCode: this.$refs.cmEditor.getValue(),
        ruleName: this.code_title,
        ruleShared: this.arrValue2,
        tenantId: parseInt(this.userInfo.accountId)
      }
      if (this.$route.params.id === 1) {
        this.$api.ruleEng.addRule(params, this.arrValue2).then((r) => {
          console.log(r)
          this.$router.go(-1)
        })
      } else {
        // 修改规则
        const params2 = {
          deployState: this.arrValue0,
          enabled: this.arrValue1,
          id: this.$route.params.id,
          ruleCode: this.$refs.cmEditor.getValue(),
          ruleName: this.code_title || this.$t('标题'),
          tenantId: parseInt(this.userInfo.accountId)
        }
        this.$api.ruleEng.updateRule(params2).then((r) => {
          console.log(r)
          this.$router.go(-1)
        })
      }
    },
    // 获取内容
    getValue() {
      const content = this.$refs.cmEditor.getValue()
      console.log(content)
    },
    // 修改内容
    setValue(e) {
      const content = this.$refs.cmEditor.getValue()
      const jsonValue = `${e}
${content}`
      this.$refs.cmEditor.setValue(jsonValue)
    },
    deleteRule() {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？')
        },
        success: () => {
          this.$api.ruleEng.deleteRule({ ids: [this.$route.params.id] }).then(() => {
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.CodeMirror {
  width: 100%;
  height: 100%;
}
.e-btn-sb-icons {
  font-family: 'button-icons';
  line-height: 1;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.code-mirror-div {
  .e-ddl.e-input-group.e-control-wrapper .e-ddl-icon::before {
    content: '\e82a';
  }
  .e-float-input.e-control-wrapper.e-no-float-label.e-input-group.e-ddl.e-lib.e-keyboard {
    border: none;
  }
  .e-ddl.e-input-group .e-dropdownlist {
    color: #eda133;
  }
  .mt-row {
    display: flex;
    height: 50px;
    flex-direction: row;
    align-items: center;
  }
  .mt-icon-MT_Search {
    color: #f9f9f9;
  }
}
</style>

<style lang="scss" scoped>
/deep/ .vue-codemirror {
  height: 100%;
}
.box2 {
  .dangerClass {
    color: #ff0020 !important;
  }
}
/deep/ .e-treeview .e-ul {
  overflow: hidden;
}
.left-box {
  height: calc(100vh - 210px);
  background: #ffffff;
  .left-top-box {
    padding: 0 20px;
    width: 300px;
    height: 84px;
    background: rgba(255, 255, 255, 1);
    box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
  }
  .left-top-box2 {
    width: 260px;
    height: 26px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid #e8e8e8;
    border-radius: 2px;
  }
  .left-top-tree {
    width: 300px;
    height: calc(100vh - 290px);
    overflow: auto;
  }
}
.code-box {
  position: relative;
  flex: 1;
  /* height: 100%; */
  width: 100%;
  overflow: hidden;
  height: calc(100vh - 224px);
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.08);
  border-radius: 3px;
  margin: 10px 0 10px 10px;
  .code-tab {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    height: 44px;
    padding: 10px;
    background: #fafafa;
    box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
  }
}
.code-mirror-div {
  padding: 2px;
  background: #f9f9f9;
}
.tool-bar {
  top: 20px;
  height: 100px;
  background: rgba(255, 255, 255, 1);
  box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
  background: #ffffff;
  .box1 {
    display: flex;
    align-items: center;
    padding: 10px 0 0 10px;
    height: 50px;
  }
  .box2 {
    display: flex;
    height: 50px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-left: 10px;
    .box21 {
      width: 250px;
      margin-right: 10px;
      display: flex;
      // height: 40px;
      flex-direction: row;
      align-items: center;
      .line {
        width: 1px;
        height: 16px;
        background: rgba(232, 232, 232, 1);
        float: right;
        margin-bottom: 5px;
      }
      .hCenter {
        display: flex;
        height: 50px;
        flex-direction: row;
        align-items: center;
      }
    }
  }
}
</style>
