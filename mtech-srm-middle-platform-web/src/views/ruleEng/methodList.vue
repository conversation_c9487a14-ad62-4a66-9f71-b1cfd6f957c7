<template>
  <div style="height: 100%">
    <mt-template-page
      :hidden-tabs="true"
      ref="mainTemplatePage"
      :template-config="methodListPageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
    </mt-template-page>
    <mt-dialog
      ref="addDialog"
      :header="dialogTitle"
      :buttons="buttons"
      css-class="dialog-form-flex"
    >
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="name" :label="$t('方法名称')">
          <mt-input
            v-model="ruleForm.name"
            :show-clear-button="true"
            :placeholder="$t('方法名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="project" :label="$t('所属项目')">
          <mt-select
            v-model="ruleForm.project"
            :data-source="projectSelect"
            :show-clear-button="true"
            :placeholder="$t('所属项目')"
            :allow-filtering="true"
            :fields="{ text: 'projectName', value: 'id' }"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
import { methodListPageConfig } from './config'
export default {
  data() {
    return {
      dialogTitle: this.$t('新增方法'),
      dialogId: '',
      MethodSelect: [],
      projectSelect: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        project: '',
        name: ''
      },
      rules: {
        project: [{ required: true, message: this.$t('请选择项目'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('请填写方法名称'), trigger: 'blur' }]
      },
      params: { current: 1, size: 10 },
      methodListPageConfig,
      pageConfig: [
        {
          title: this.$t('方法列表'), // tab单项
          toolbar: [
            // 表格顶部toolbar
            ['Add', 'Delete', 'Options'],
            ['Filter', 'Export', 'Refresh', 'Setting']
          ],
          grid: {
            // 表格配置
            dataSource: [], // 数据源
            columnData: [
              // 列数据
              {
                width: '50',
                type: 'checkbox'
              },
              {
                field: 'methodName',
                headerText: this.$t('方法名称'),
                width: '150',
                isCustomColumn: true, // 用户自定义列
                cellTools: [
                  // 用户单元格按钮
                  'edit',
                  'delete'
                ]
              },
              {
                field: 'projectName',
                width: '150',
                headerText: this.$t('所属项目')
              },
              {
                field: 'createTime',
                width: '150',
                headerText: this.$t('创建时间')
              },
              {
                field: 'updateTime',
                width: '150',
                headerText: this.$t('修改时间')
              }
            ]
          }
        }
      ]
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.user
    }
  },
  mounted() {
    this.getProjectList()
  },
  methods: {
    // 刷新main列表
    refreshMain() {
      this.$refs.mainTemplatePage.refreshCurrentGridData()
    },
    async getProjectList() {
      await this.$api.ruleEng.getProjectList().then((r) => {
        this.projectSelect = [...r.data]
      })
    },
    hide() {
      this.$refs.addDialog.ejsRef.hide()
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dialogTitle === this.$t('新增方法')) {
            const params = {
              methodName: this.ruleForm.name,
              projectId: this.ruleForm.project
            }
            this.$api.ruleEng.addMethod(params).then(() => {
              this.refreshMain()
              this.$refs.addDialog.ejsRef.hide()
            })
          } else {
            const params = {
              methodName: this.ruleForm.name,
              id: this.dialogId,
              projectId: this.ruleForm.project
            }
            this.$api.ruleEng.updataMethod(params).then(() => {
              this.refreshMain()
              this.$refs.addDialog.ejsRef.hide()
            })
          }
        } else {
          return false
        }
      })
    },
    async getMethodPage() {
      await this.$api.ruleEng.getMethodPage(this.params).then((r) => {
        if (r.code === 200) {
          this.pageConfig[0].grid.dataSource = [...r.data.records]
        }
      })
    },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      if (e.tabIndex === 0) {
        if (e.toolbar.id === 'Add') {
          this.dialogTitle = this.$t('新增方法')
          this.ruleForm.name = ''
          this.ruleForm.project = ''
          this.$refs.addDialog.ejsRef.show()
        } else if (e.toolbar.id === 'Delete') {
          console.log(e.grid.getSelectedRecords())
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 0) {
            this.$toast({
              content: this.$t('请至少选择一条数据进行操作！'),
              type: 'warning'
            })
          } else {
            const p = []
            arr.forEach((item) => {
              p.push(item.id)
            })
            this.deleteMethod(p)
          }
        }
      }
    },
    // 删除规则方法
    deleteMethod(arr) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？')
        },
        success: () => {
          this.$api.ruleEng.deleteMethod({ ids: arr }).then(() => {
            this.refreshMain()
          })
        }
      })
    },
    handleClickCellTool(a) {
      // 单元格 图标点击 tool
      console.log('use-handleClickCellTool', a)

      if (a.tool.id === 'edit') {
        this.$refs.addDialog.ejsRef.show()
        this.dialogTitle = this.$t('编辑方法')
        this.dialogId = a.data.id
        this.ruleForm.name = a.data.methodName
        this.ruleForm.project = a.data.projectId
      } else if (a.tool.id === 'delete') {
        this.deleteMethod([a.data.id])
      }
    },
    handleClickCellTitle() {
      // console.log('use-handleClickCellTool', a)
      // this.$router.push({ name: 'objectDetails', params: a.data })
    }
  }
}
</script>
