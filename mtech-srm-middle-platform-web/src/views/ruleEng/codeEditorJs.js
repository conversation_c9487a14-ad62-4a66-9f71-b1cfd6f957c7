import Vue from 'vue'
import { codemirror } from 'vue-codemirror'

import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/blackboard.css'

/* eslint-disable */
import 'codemirror/mode/javascript/javascript.js'

import 'codemirror/mode/xml/xml.js'

import 'codemirror/mode/htmlmixed/htmlmixed.js'

import 'codemirror/mode/css/css.js'

import 'codemirror/mode/yaml/yaml.js'

import 'codemirror/mode/sql/sql.js'

import 'codemirror/mode/python/python.js'

import 'codemirror/mode/markdown/markdown.js'

import 'codemirror/addon/hint/show-hint.css'

import 'codemirror/addon/hint/show-hint.js'

import 'codemirror/addon/hint/javascript-hint.js'

import 'codemirror/addon/hint/xml-hint.js'

import 'codemirror/addon/hint/css-hint.js'

import 'codemirror/addon/hint/html-hint.js'

import 'codemirror/addon/hint/sql-hint.js'

import 'codemirror/addon/hint/anyword-hint.js'

import 'codemirror/addon/lint/lint.css'

import 'codemirror/addon/lint/lint.js'

import 'codemirror/addon/lint/json-lint'

// import 'codemirror/addon/lint/javascript-lint.js'

// require('script-loader!jsonlint')

import 'codemirror/addon/fold/foldcode.js'

import 'codemirror/addon/fold/foldgutter.js'

import 'codemirror/addon/fold/foldgutter.css'

import 'codemirror/addon/fold/brace-fold.js'

import 'codemirror/addon/fold/xml-fold.js'

import 'codemirror/addon/fold/comment-fold.js'

import 'codemirror/addon/fold/markdown-fold.js'

import 'codemirror/addon/fold/indent-fold.js'

import 'codemirror/addon/edit/closebrackets.js'

import 'codemirror/addon/edit/closetag.js'

import 'codemirror/addon/edit/matchtags.js'

import 'codemirror/addon/edit/matchbrackets.js'

import 'codemirror/addon/selection/active-line.js'

import 'codemirror/addon/search/jump-to-line.js'

import 'codemirror/addon/dialog/dialog.js'

import 'codemirror/addon/dialog/dialog.css'

import 'codemirror/addon/search/searchcursor.js'

import 'codemirror/addon/search/search.js'

import 'codemirror/addon/display/autorefresh.js'

import 'codemirror/addon/selection/mark-selection.js'

import 'codemirror/addon/search/match-highlighter.js'
Vue.use(codemirror)
Vue.component('Codemirror', codemirror)
