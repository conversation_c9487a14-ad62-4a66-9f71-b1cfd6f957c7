<template>
  <div style="height: 100%">
    <div class="tool-bar">
      <div class="box1">
        <div
          style="
            font-size: 24px;
            font-family: PingFangSC;
            font-weight: 500;
            color: rgba(35, 43, 57, 1);
            padding: 10px;
          "
        >
          {{ $route.params.modelName }}
        </div>
        <!-- <mt-input v-if="isEdit" v-model="code_title" width="300" maxlength="20" type="text" @blur="isEdit = false"></mt-input> -->
        <!-- <mt-button icon-css="mt-icons mt-icon-edit-06" css-class="e-flat" style="margin-left: 10x" @click="setStyle">{{ $t("编辑") }}</mt-button> -->
        <!-- <mt-button :is-primary="true" icon-css="mt-icons mt-icon-M_Bookmark" css-class="e-flat" style="margin-left: 10x" @click="getValue"
          >{{ $t("标签") }}</mt-button
        > -->
      </div>
      <div class="box2">
        <div style="display: flex">
          <div class="box21">
            <mt-row>
              <mt-col :span="9" style="font-size: 14px; padding-bottom: 5px"
                >{{ $t('所属项目') }}：</mt-col
              >
              <mt-col :span="9" style="overflow: hidden"
                ><span style="color: #00469c">{{ $route.params.projectName }}</span></mt-col
              >
              <mt-col :span="4"><div class="line"></div></mt-col>
            </mt-row>
          </div>
          <div class="box21" style="width: 300px">
            <mt-row class="">
              <mt-col :span="6" style="font-size: 14px; padding-bottom: 5px"
                >{{ $t('创建时间') }}：</mt-col
              >
              <mt-col :span="12"
                ><span style="font-weight: 600">{{ $route.params.createTime }}</span></mt-col
              >
              <mt-col :span="4"><div class="line"></div></mt-col>
            </mt-row>
          </div>
          <div class="box21" style="width: 300px">
            <mt-row>
              <mt-col :span="6" style="font-size: 14px; padding-bottom: 5px"
                >{{ $t('修改时间') }}：</mt-col
              >
              <mt-col :span="12"
                ><span style="font-weight: 600">{{
                  $route.params.updateTime || '--'
                }}</span></mt-col
              >
            </mt-row>
          </div>
        </div>
        <div style="display: flex">
          <mt-button
            icon-css="mt-icons mt-icon-M_PV_Save"
            css-class="e-flat"
            style="margin-left: 10x"
            @click="saveRule"
            >{{ $t('保存') }}</mt-button
          >
          <!-- <mt-button icon-css="mt-icons mt-icon-delete-05" css-class="e-flat" style="margin-left: 10x" @click="deleteRule">{{ $t("删除") }}</mt-button> -->
          <mt-button
            icon-css="mt-icons mt-icon-icon_solid_close"
            css-class="e-flat dangerClass"
            style="margin-left: 50x"
            @click="$router.go(-1)"
            >{{ $t('关闭') }}</mt-button
          >
        </div>
      </div>
    </div>
    <div style="height: 100%">
      <mt-template-page
        :hidden-tabs="true"
        ref="mainTemplatePage"
        :template-config="pageConfig"
        @handleClickToolBar="handleClickToolBar"
        @handleClickCellTool="handleClickCellTool"
      >
      </mt-template-page>
    </div>
    <!-- <mt-horizontal-list
      :data-source="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
    ></mt-horizontal-list> -->
    <mt-dialog ref="addDialog" :header="$t('新增')" :buttons="buttons" v-if="showDialog">
      <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <mt-form-item prop="name" :label="$t('字段名称')">
          <mt-input
            v-model="ruleForm.name"
            :show-clear-button="true"
            :placeholder="$t('字段名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="fieldDesc" :label="$t('描述')">
          <mt-input
            v-model="ruleForm.fieldDesc"
            :show-clear-button="true"
            :placeholder="$t('描述')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item prop="type" :label="$t('类型')">
          <mt-select
            v-model="ruleForm.type"
            :data-source="typeSelect"
            :show-clear-button="true"
            :placeholder="$t('类型')"
            :allow-filtering="true"
          ></mt-select>
        </mt-form-item>
        <mt-form-item>
          <mt-checkbox
            v-model="radioVal"
            :checked="radioVal"
            class="checkbox-item"
            :label="$t('是否是列表')"
            @change="handleChange"
          ></mt-checkbox>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>

<script>
// import Vue from 'vue'
export default {
  name: 'ObjectDetails',
  data() {
    return {
      showDialog: false,
      radioVal: false,
      typeSelect: [],
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.save,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      ruleForm: {
        type: '',
        name: '',
        fieldDesc: '',
        radioVal: false
      },
      rules: {
        type: [{ required: true, message: this.$t('请选择类型'), trigger: 'blur' }],
        name: [{ required: true, message: this.$t('插入有效的java标识符'), trigger: 'blur' }],
        fieldDesc: [{ required: true, message: this.$t('请填写字段描述'), trigger: 'blur' }]
      },
      params: { current: 1, size: 10 },
      dataSourceArr: [],
      pageConfig: [
        {
          title: this.$t('字段列表'), // tab单项
          toolbar: ['Add', 'Delete'],
          grid: {
            // 表格配置
            columnData: [
              // 列数据
              {
                width: '50',
                type: 'checkbox'
              },
              {
                field: 'fieldName',
                headerText: this.$t('字段名称'),
                width: '150',
                isCustomColumn: true // 用户自定义列
              },
              {
                field: 'fieldType',
                width: '150',
                headerText: this.$t('类型')
              },
              {
                field: 'fieldDesc',
                width: '150',
                headerText: this.$t('描述')
              },
              {
                field: 'fieldList',
                width: '150',
                headerText: this.$t('是否是列表'),
                valueConverter: {
                  type: 'map',
                  map: {
                    0: this.$t('不是列表'),
                    1: this.$t('是列表')
                  }
                }
                // template: () => {
                //   return {
                //     template: Vue.component('EmpTemplate', {
                //       data() {
                //         return {
                //           data: {}
                //         }
                //       },
                //       template: `<span>
                //             <span>
                //               <div>{{data.fieldList === 0 ? '不是列表' : '是列表'}}</div>
                //             </span>
                //           </span>`
                //     })
                //   }
                // }
              }
            ],
            asyncConfig: {
              methods: 'get',
              recordsPosition: 'data',
              url: `/ruleConfig/admin/model/field/data/${this.$route.params.id}`
            }
          }
        }
      ]
    }
  },
  mounted() {
    // this.getObjectField()
    this.getObjectFieldType()
  },
  methods: {
    handleChange(val) {
      this.radioVal = val.checked
    },
    async getObjectFieldType() {
      await this.$api.ruleEng.getObjectFieldType().then((r) => {
        this.typeSelect = [...r.data]
      })
    },
    // 刷新main列表
    refreshMain() {
      this.$refs.mainTemplatePage.refreshCurrentGridData()
    },
    hide() {
      this.$refs.addDialog.ejsRef.hide()
      this.showDialog = false
    },
    save() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.dataSourceArr.push({
            fieldDesc: this.ruleForm.fieldDesc,
            fieldList: this.radioVal ? 1 : 0,
            fieldName: this.ruleForm.name,
            fieldType: this.ruleForm.type
          })
          // const chatInfo = JSON.parse(JSON.stringify(this.pageConfig[0]))
          // this.pageConfig[0] = {}
          // this.pageConfig[0] = JSON.parse(JSON.stringify(chatInfo))
          this.hide()
          this.saveAll()
        } else {
          return false
        }
      })
    },
    saveAll() {
      const params = {
        modelId: this.$route.params.id,
        modelName: this.$route.params.modelName,
        fields: this.dataSourceArr
      }
      this.$api.ruleEng.addObjectField(params).then(() => {
        this.refreshMain()
      })
    },
    // async getObjectField() {
    //   await this.$api.ruleEng.getObjectField(this.$route.params.id).then((r) => {
    //     if (r.code === 200) {
    //       this.pageConfig[0].grid.dataSource = [...r.data]
    //     }
    //   })
    // },
    handleClickToolBar(e) {
      // 表格顶部 toolbar
      if (e.tabIndex === 0) {
        if (e.toolbar.id === 'Add') {
          this.showDialog = true
          this.$nextTick(() => {
            this.$refs.addDialog.ejsRef.show()
          })
          this.ruleForm = {
            type: '',
            name: '',
            fieldDesc: '',
            radioVal: false
          }
        } else if (e.toolbar.id === 'Delete') {
          console.log(e.grid.getSelectedRecords())
          const arr = [...e.grid.getSelectedRecords()]
          if (arr.length === 0) {
            this.$toast({
              content: this.$t('请至少选择一条数据进行操作！'),
              type: 'warning'
            })
          } else {
            this.deleteRuleModel(arr)
          }
        } else if (e.toolbar.id === 'save') {
          this.saveAll()
        }
      }
    },
    arrSubtraction(a, b) {
      if (Array.isArray(a) && Array.isArray(b)) {
        return a.filter((i) => !b.includes(i))
      }
    },
    reduceArray(arr1, arr2) {
      for (var i = arr1.length - 1; i >= 0; i--) {
        var a = arr1[i]
        for (var j = arr2.length - 1; j >= 0; j--) {
          var b = arr2[j]
          if (JSON.stringify(a) === JSON.stringify(b)) {
            arr1.splice(i, 1)
            arr2.splice(j, 1)
            break
          }
        }
      }
      return arr2
    },
    // 删除对象字段方法
    deleteRuleModel(arr) {
      this.$dialog({
        data: {
          title: this.$t('删除'),
          message: this.$t('是否确认删除？')
        },
        success: () => {
          this.dataSourceArr = this.reduceArray(this.dataSourceArr, arr)
          this.saveAll()
        }
      })
    },
    handleClickCellTool(a) {
      // 单元格 图标点击 tool
      console.log('use-handleClickCellTool', a)
      // if (a.tool.id === 'edit') {
      // } else
      if (a.tool.id === 'edeletedit') {
        this.deleteRuleModel(a.data)
      }
    }
  }
}
</script>
<style lang="scss">
.box2 {
  .dangerClass {
    color: #ff0020 !important;
  }
  display: flex;
  height: 50px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-left: 10px;
  .box21 {
    width: 200px;
    margin-right: 10px;
    overflow: auto;
    // display: flex;
    // height: 40px;
    // flex-direction: row;
    // align-items: center;
    .line {
      width: 1px;
      height: 16px;
      background: rgba(232, 232, 232, 1);
      float: right;
      margin-bottom: 5px;
    }
    .hCenter {
      display: flex;
      height: 50px;
      flex-direction: row;
      align-items: center;
    }
  }
}
</style>
