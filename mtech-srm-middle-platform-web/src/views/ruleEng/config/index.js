import { i18n } from '@/main.js'
export const pageConfig = [
  {
    title: i18n.t('规则列表'), // tab单项
    toolbar: ['Add', 'Delete'],
    grid: {
      // 表格配置
      dataSource: [], // 数据源
      columnData: [
        // 列数据
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'ruleName',
          headerText: i18n.t('规则名称'),
          width: '150',
          isCustomColumn: true, // 用户自定义列
          cellTools: [
            // 用户单元格按钮
            'edit',
            'delete'
          ]
        },
        {
          field: 'ruleVersion',
          width: '150',
          headerText: i18n.t('最新版本')
        },
        {
          field: 'enabled',
          width: '150',
          headerText: i18n.t('可用状态'),
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('可用'),
              1: i18n.t('不可用')
            }
          }
          // template: () => {
          //   return {
          //     template: Vue.component('EmpTemplate', {
          //       data() {
          //         return {
          //           data: {}
          //         }
          //       },
          //       template: `<span>
          //             <span>
          //               <div>{{data.enabled === 0 ? '可用' : '不可用'}}</div>
          //             </span>
          //           </span>`
          //     })
          //   }
          // }
        },
        {
          field: 'deployState',
          width: '150',
          headerText: i18n.t('发布状态'),
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('未发布'),
              1: i18n.t('已发布')
            }
          }
          // template: () => {
          //   return {
          //     template: Vue.component('EmpTemplate', {
          //       data() {
          //         return {
          //           data: {}
          //         }
          //       },
          //       template: `<span>
          //             <span>
          //               <div>{{data.deployState === 0 ? '未发布' : '已发布'}}</div>
          //             </span>
          //           </span>`
          //     })
          //   }
          // }
        },
        {
          field: 'ruleShared',
          width: '150',
          headerText: i18n.t('共享状态'),
          valueConverter: {
            type: 'map',
            map: {
              0: i18n.t('未共享'),
              1: i18n.t('已共享')
            }
          }
          // template: () => {
          //   return {
          //     template: Vue.component('EmpTemplate', {
          //       data() {
          //         return {
          //           data: {}
          //         }
          //       },
          //       template: `<span>
          //             <span>
          //               <div>{{data.enabled === 0 ? '已共享' : '未共享'}}</div>
          //             </span>
          //           </span>`
          //     })
          //   }
          // }
        },
        {
          field: 'createTime',
          width: '150',
          headerText: i18n.t('创建时间')
        }
      ],
      asyncConfig: {
        url: '/ruleConfig/admin/rule/page'
      }
    }
  },
  {
    title: i18n.t('规则绑定'),
    toolbar: [
      'Add',
      {
        id: 'Edit',
        title: i18n.t('编辑'),
        icon: 'edit-06'
      },
      'Delete'
    ],
    grid: {
      dataSource: [],
      columnData: [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'methodName',
          width: '150',
          headerText: i18n.t('方法名')
        },
        {
          field: 'projectName',
          width: '150',
          headerText: i18n.t('所属项目')
        },
        {
          field: 'ruleName',
          width: '150',
          headerText: i18n.t('规则'),
          isCustomColumn: true
        },
        {
          field: 'createTime',
          width: '150',
          headerText: i18n.t('创建时间')
        },
        {
          field: 'updateTime',
          width: '150',
          headerText: i18n.t('修改时间')
        }
      ],
      asyncConfig: {
        url: '/ruleConfig/admin/rule/method/page'
      }
    }
  }
]

export const objectSetPageConfig = [
  {
    title: i18n.t('对象列表'), // tab单项
    toolbar: ['Add', 'Delete'],
    grid: {
      // 表格配置
      dataSource: [], // 数据源
      columnData: [
        // 列数据
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'modelName',
          headerText: i18n.t('对象名称'),
          width: '150',
          isCustomColumn: true, // 用户自定义列
          cellTools: [
            // 用户单元格按钮
            'edit',
            'delete'
          ]
        },
        {
          field: 'projectName',
          width: '150',
          headerText: i18n.t('所属项目')
        },
        {
          field: 'createTime',
          width: '150',
          headerText: i18n.t('创建时间')
        },
        {
          field: 'updateTime',
          width: '150',
          headerText: i18n.t('修改时间')
        }
      ],
      asyncConfig: {
        url: '/ruleConfig/admin/rule/model/page'
      }
    }
  }
]

export const methodListPageConfig = [
  {
    title: i18n.t('方法列表'), // tab单项
    toolbar: ['Add', 'Delete'],
    grid: {
      // 表格配置
      dataSource: [], // 数据源
      columnData: [
        // 列数据
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'methodName',
          headerText: i18n.t('方法名称'),
          width: '150',
          isCustomColumn: true, // 用户自定义列
          cellTools: [
            // 用户单元格按钮
            'edit',
            'delete'
          ]
        },
        {
          field: 'projectName',
          width: '150',
          headerText: i18n.t('所属项目')
        },
        {
          field: 'createTime',
          width: '150',
          headerText: i18n.t('创建时间')
        },
        {
          field: 'updateTime',
          width: '150',
          headerText: i18n.t('修改时间')
        }
      ],
      asyncConfig: {
        url: '/ruleConfig/admin/method/page'
      }
    }
  }
]

export const projectListPageConfig = [
  {
    toolbar: ['Add', 'Delete'],
    grid: {
      // 表格配置
      dataSource: [], // 数据源
      columnData: [
        // 列数据
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'projectName',
          headerText: i18n.t('项目名称'),
          width: '150',
          isCustomColumn: true, // 用户自定义列
          cellTools: [
            // 用户单元格按钮
            'edit',
            'delete'
          ]
        },
        {
          field: 'createTime',
          width: '150',
          headerText: i18n.t('创建时间')
        },
        {
          field: 'updateTime',
          width: '150',
          headerText: i18n.t('修改时间')
        }
      ],
      asyncConfig: {
        url: '/ruleConfig/admin/project/page'
      }
    }
  }
]
