<!-- 词组管理 -->
<template>
  <div class="full-height vertical-flex-box">
    <collapse-search :is-grid-display="true" @reset="handleCustomReset" @search="handleSearch">
      <mt-form ref="searchFormRef" :model="searchFormModel">
        <mt-form-item prop="appName" :label="$t('应用名称')">
          <mt-input
            v-model="searchFormModel.appName"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
        <mt-form-item prop="archiveName" :label="$t('归档名称')">
          <mt-input
            v-model="searchFormModel.archiveName"
            :placeholder="$t('请输入')"
            show-clear-button
          />
        </mt-form-item>
      </mt-form>
    </collapse-search>
    <div class="flex-fit">
      <ScTable
        ref="xTable"
        :columns="columns"
        :table-data="tableData"
        height="auto"
        header-align="left"
        align="left"
        show-overflow
        :edit-config="{
          trigger: 'dblclick',
          mode: 'row',
          showStatus: true
        }"
        :row-config="{ height: 36 }"
        :scroll-x="{ gt: 0, oSize: 20 }"
        :scroll-y="{ gt: 0, oSize: 10 }"
        style="padding-top: unset"
        @edit-closed="editComplete"
        :expand-config="{ lazy: true, loadMethod: loadContentMethod }"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            size="small"
            @click="handleClickToolBar({ code: item.code, $grid: $refs.xTable.$refs.xGrid })"
            >{{ item.name }}</vxe-button
          >
        </template>
        <template #appNameEdit="{ row }">
          <vxe-select
            v-model="row.appName"
            :options="appList"
            transfer
            filterable
            :placeholder="$t('请选择')"
          />
        </template>
        <template #recordDefault="{ row }">
          <span style="cursor: pointer; color: #2783fe" @click="viewRecord(row)">
            {{ $t('查看') }}
          </span>
        </template>
        <template #detailContent="{ row, rowIndex }">
          <div class="expand-wrapper">
            <DetailTree
              class="detail-tree"
              :app-row-info="row"
              @refreshTable="handleCustomSearch"
              @nodeSelected="(e) => nodeSelected(e, row, rowIndex)"
            />
            <FormInfo
              class="form-info"
              :ref="`formInfo${rowIndex}`"
              :app-row-info="row"
              @refreshTable="handleCustomSearch"
            />
          </div>
        </template>
      </ScTable>
    </div>
    <mt-page
      class="flex-keep custom-page"
      :page-settings="pageSettings"
      :total-pages="pageSettings.totalPages"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    />
  </div>
</template>

<script>
import CollapseSearch from '@/components/collapseSearch'
import ScTable from '@/components/ScTable/src/index'
import DetailTree from './components/detailTree.vue'
import FormInfo from './components/formInfo.vue'
import { columnData, ToolBar, detailColumns } from './config'
export default {
  components: {
    CollapseSearch,
    ScTable,
    DetailTree,
    FormInfo
  },
  data() {
    return {
      searchFormModel: {},
      columns: columnData,
      detailColumns,
      tableData: [],
      toolbar: ToolBar,
      pageSettings: {
        currentPage: 1,
        pageSize: 50, // 当前每页数据量
        pageCount: 5,
        totalRecordsCount: 0, //总条数
        totalPages: 0, // 总页数
        pageSizes: [50, 100, 200, 1000]
      },
      apiWaitingQuantity: 0,
      appList: []
    }
  },
  created() {
    this.getAppList()
    this.handleSearch()
  },
  methods: {
    nodeSelected(event, row, rowIndex) {
      row.formInfo = event
      this.$refs[`formInfo${rowIndex}`].initData(event)
    },
    getAppList() {
      this.$api.taskList.getArchiveTaskAppList().then((res) => {
        const { code, data } = res
        if (code === 200) {
          this.appList = data.map((i) => ({
            label: i,
            value: i
          }))
        }
      })
    },
    loadContentMethod({ row }) {
      this.apiStartLoading()
      return this.$api.taskList
        .getArchiveTaskTableList({ app: row.appName, dbType: 0 })
        .then(async (res) => {
          row.archiveSourceTableList = res?.data || []
          const res1 = await this.$api.taskList.getArchiveTaskTableList({
            app: row.appName,
            dbType: 2
          })
          row.archiveTargetTableList = res1?.data || []
          let params = {
            // page: {
            //   current: 1,
            //   size: 20
            // },
            archiveUuid: row.archiveUuid
          }
          // 查询明细树
          const res2 = await this.$api.taskList.getArchiveTaskDetailList(params)
          // const res2 = {
          //   data: [
          //     {
          //       archiveSourceTable: 'digis_1d_timer',
          //       archiveTargetTable: 'digis_1h_timer',
          //       archiveUuid: '111111',
          //       associateField: 'business_object_type',
          //       conditionField: 'business_object_id',
          //       createUserId: 0,
          //       createUserName: '',
          //       customCondition: '',
          //       id: '1',
          //       parentId: '415662565901176921',
          //       title: 'xqqtest1',
          //       type: 0
          //     },
          //     {
          //       archiveSourceTable: 'digis_periodic_task',
          //       archiveTargetTable: 'idle_material_item',
          //       archiveUuid: '22222',
          //       associateField: 'id',
          //       conditionField: 'tenant_id',
          //       createUserId: 0,
          //       createUserName: '',
          //       customCondition: '',
          //       id: '2',
          //       parentId: '1',
          //       title: 'xqqtest2',
          //       type: 1
          //     },
          //     {
          //       archiveSourceTable: 'digis_1h_timer',
          //       archiveTargetTable: 'digis_1d_timer',
          //       archiveUuid: '33333',
          //       associateField: 'business_object_id',
          //       conditionField: 'business_object_type',
          //       createUserId: 0,
          //       createUserName: '',
          //       customCondition: '',
          //       id: '3',
          //       parentId: '1',
          //       title: 'xqqtest3',
          //       type: 1
          //     },
          //     {
          //       archiveSourceTable: 'digis_1d_timer',
          //       archiveTargetTable: 'digis_1h_timer',
          //       archiveUuid: '44444',
          //       associateField: 'business_object_type',
          //       conditionField: 'business_object_id',
          //       createUserId: 0,
          //       createUserName: '',
          //       customCondition: '',
          //       id: '4',
          //       parentId: '3',
          //       title: 'xqqtest4',
          //       type: 1
          //     },
          //     {
          //       archiveSourceTable: 'digis_periodic_task',
          //       archiveTargetTable: 'idle_material_item',
          //       archiveUuid: '55555',
          //       associateField: 'id',
          //       conditionField: 'status',
          //       createUserId: 0,
          //       createUserName: '',
          //       customCondition: '',
          //       id: '5',
          //       parentId: '2',
          //       title: 'xqqtest5',
          //       type: 1
          //     }
          //   ]
          // }
          row.childFlatData = res2.data
          row.childData = this.flatToTree(res2.data)

          this.apiEndLoading()
        })
    },
    flatToTree(data) {
      let map = new Map()
      let tree = []

      // 构建一个map，key是每个节点的id，value是对应的节点对象
      data.forEach((item) => {
        item.name = item.title
        map.set(item.id, { ...item, children: [] })
      })

      // 根据parentId将节点放入其父节点的children中
      data.forEach((item) => {
        const parent = map.get(item.parentId)
        if (parent) {
          parent.children.push(map.get(item.id))
        } else {
          tree.push(map.get(item.id))
        }
      })

      return tree
    },
    viewRecord(row) {
      this.$dialog({
        modal: () => import('./components/recordDialog.vue'),
        data: {
          title: this.$t('查看执行记录'),
          archiveUuid: row.archiveUuid
        },
        success: () => {}
      })
    },
    handleCustomReset() {
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      this.handleSearch()
    },
    handleSearch() {
      this.pageSettings.currentPage = 1
      this.handleCustomSearch()
    },
    handleCustomSearch() {
      let params = {
        page: {
          current: this.pageSettings.currentPage,
          size: this.pageSettings.pageSize
        },
        ...this.searchFormModel
      }
      this.apiStartLoading()
      this.$api.taskList
        .getArchiveTaskList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            const total = res?.data?.total || 0
            this.pageSettings.totalPages = Math.ceil(Number(total) / this.pageSettings.pageSize)
            this.pageSettings.totalRecordsCount = Number(total)
            this.tableData = res?.data?.records || []
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    editComplete(args) {
      console.log('editComplete', args)
      const { row } = args
      if (args.$event) {
        if (args.$event.target.innerText === '取消编辑') {
          this.$refs.xTable.$refs.xGrid.clearEdit()
          this.handleCustomSearch()
          return
        }
        // 2、 调保存接口
        this.handleSave(row)
      }
    },
    handleSave(row) {
      let params = { ...row }
      if (row.id && row.id.includes('row_')) {
        params.id = null
      }
      if (!params.appName) {
        this.$toast({ content: this.$t('请选择应用名称'), type: 'warning' })
        this.$refs.xTable.$refs.xGrid.setEditRow(row)
        return
      }
      if (!params.archiveName) {
        this.$toast({ content: this.$t('请输入归档名称'), type: 'warning' })
        this.$refs.xTable.$refs.xGrid.setEditRow(row)
        return
      }
      if (!params.type) {
        this.$toast({ content: this.$t('请选择任务类型'), type: 'warning' })
        this.$refs.xTable.$refs.xGrid.setEditRow(row)
        return
      }
      this.apiStartLoading()
      this.$api.taskList
        .addArchiveTaskList(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
          // 当出现错误时，指定行进入编辑状态
          this.$refs.xTable.$refs.xGrid.setEditRow(row)
        })
    },
    handleClickToolBar(args) {
      const { code, $grid } = args
      const selectedRecords = $grid.getCheckboxRecords()
      const ids = []
      selectedRecords.forEach((item) => {
        ids.push(item.id)
      })
      if (code === 'Add') {
        if (this.tableData.length === 0 || $grid.getTableData().visibleData[0]?.['archiveUuid']) {
          const item = {
            id: null
          }
          $grid.insert([item])
          this.$nextTick(() => {
            // 获取最新的表格视图数据
            const currentViewRecords = $grid.getTableData().visibleData
            // 将新增的那一条设置为编辑状态
            this.$refs.xTable.$refs.xGrid.setEditRow(currentViewRecords[0])
          })
        }
        return
      }
      if (code === 'CloseEdit') {
        this.$refs.xTable.$refs.xGrid.clearEdit()
        this.handleCustomSearch()
        return
      }
      if (code === 'Delete') {
        // 删除
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认删除选中的数据？')
          },
          success: () => {
            this.handleDelete(ids)
          }
        })
        return
      }
      const toastCode = ['Enable', 'Disable', 'Execute']
      if (selectedRecords.length !== 1 && toastCode.includes(code)) {
        this.$toast({ content: this.$t('仅可勾选一行数据进行操作'), type: 'warning' })
        return
      }
      if (code === 'Enable' || code === 'Disable') {
        let message = this.$t('确认启用选中的数据？')
        const params = {
          archiveUuid: selectedRecords[0].archiveUuid,
          enable: true
        }
        if (code === 'Disable') {
          message = this.$t('确认禁用选中的数据？')
          params.enable = false
        }
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message
          },
          success: () => {
            this.handleEnable(params)
          }
        })
      }
      if (code === 'Execute') {
        this.$dialog({
          data: {
            title: this.$t('提示'),
            message: this.$t('确认执行选中的数据？')
          },
          success: () => {
            this.handleExecute(selectedRecords)
          }
        })
      }
    },
    handleExecute(selectedRecords) {
      this.apiStartLoading()
      this.$api.taskList
        .executeArchiveTask({ id: selectedRecords[0]['id'] })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleEnable(params) {
      this.apiStartLoading()
      this.$api.taskList
        .enableArchiveTask(params)
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleDelete(ids) {
      this.apiStartLoading()
      this.$api.taskList
        .deleteArchiveTaskList({ ids })
        .then((res) => {
          this.apiEndLoading()
          if (res?.code === 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.handleCustomSearch()
          }
        })
        .catch(() => {
          this.apiEndLoading()
        })
    },
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.handleCustomSearch()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.handleCustomSearch()
    },
    // 加载完成
    apiEndLoading() {
      if (--this.apiWaitingQuantity === 0) {
        this.$store.commit('endLoading')
      }
    },
    // 加载开始
    apiStartLoading() {
      this.apiWaitingQuantity++
      this.$store.commit('startLoading')
    }
  },
  beforeRouteLeave(to, from, next) {
    document.querySelector('.vxe-table--body-wrapper').scrollTop = 0
    next()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-select-selection {
  background-color: transparent !important;
}
.full-height {
  background: #fff;
  padding: 12px 8px;
}
.flex-fit {
  overflow: unset;
}
/deep/ .vxe-cell {
  .vxe-default-select {
    background: #fff;
  }
  .mt-input-number {
    margin-bottom: -4px;
    padding-bottom: 4px;
    input {
      height: 18px;
      margin-bottom: 0;
    }
  }
  div .vxe-cell-border {
    border: solid #e6e9ed 1px;
    height: 24px;
    box-sizing: border-box;
  }
}
/deep/ .col--seq,
/deep/ .col--checkbox {
  .c--tooltip {
    padding: 0 10px;
  }
}
.expand-wrapper {
  padding: 20px;
  text-align: left;
  display: flex;
  .detail-tree {
    width: 28%;
    background: #f9f9f9;
  }
  .form-info {
    padding-left: 20px;
    flex: 1;
  }
}
</style>
