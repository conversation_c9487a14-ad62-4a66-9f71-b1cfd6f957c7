<template>
  <div class="tree-view--wrap">
    <!-- <mt-button
      v-if="!treeViewData.dataSource.length"
      iconCss="mt-icons mt-icon-icon_solid_Createorder"
      cssClass="e-flat"
      iconPosition="Right"
      @click.native="addNewRootNode"
      >{{ $t('新增根节点') }}</mt-button
    > -->

    <vxe-button
      v-if="!treeViewData.dataSource.length"
      status="info"
      style="margin-bottom: 20px"
      size="small"
      @click.native="addNewRootNode"
      >{{ $t('新增根节点') }}</vxe-button
    >
    <mt-common-tree
      ref="treeViewMenu"
      class="tree-view--template"
      :fields="treeViewData"
      :enable-persistence="true"
      @onButton="onButton"
      @nodeSelected="nodeSelected"
    ></mt-common-tree>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  name: '',
  props: {
    appRowInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      treeViewData: {
        nodeTemplate: function () {
          return {
            template: Vue.component('common', {
              template: `<div class="action-boxs">
                      <div>
                        <span>{{mtData.name}}</span>
                      </div>
                    </div>`,

              data() {
                return { data: {} }
              },
              props: {
                mtData: {
                  type: Object,
                  default: () => {}
                }
              }
            })
          }
        },
        dataSource: [],
        id: 'id',
        text: 'name',
        child: 'children'
      }
    }
  },
  mounted() {
    this.getCategoryTree()
  },
  methods: {
    async getCategoryTree() {
      this.treeViewData = Object.assign({}, this.treeViewData, {
        dataSource: this.appRowInfo.childData
      })
    },

    // 添加根节点e
    addNewRootNode() {
      this.$dialog({
        modal: () => import('./addDialog.vue'),
        data: {
          title: this.$t('新增节点'),
          appRowInfo: this.appRowInfo,
          parentData: {},
          type: 0
        },
        success: () => {
          this.refreshTable()
        }
      })
      // if (this.treeViewData.dataSource.length === 0 && !this.isStartRootNodeFormNull) {
      //   // this.isStartRootNodeFormNull = true
      //   // 只需要根据初始化的datasource维护一次
      //   this.$nextTick(() => {
      //     this.addTreeNode({ parentId: '' })
      //   })
      // } else {
      // this.addTreeNode({
      //   parentId: ''
      // })
      // }
    },
    onButton(event) {
      if (event.onBtn.text === this.$t('新增下级')) {
        this.addTreeNode(event)
      } else if (event.onBtn.text === this.$t('删除')) {
        this.deleteTreeNode(event)
      }
    },
    addTreeNode(event) {
      let parentData = {}
      this.appRowInfo.childFlatData.forEach((i) => {
        if (i.id === event.id) {
          parentData = i
        }
      })
      this.$dialog({
        modal: () => import('./addDialog.vue'),
        data: {
          title: this.$t('新增节点'),
          appRowInfo: {
            ...event,
            appName: this.appRowInfo.appName,
            archiveSourceTableList: this.appRowInfo.archiveSourceTableList,
            archiveTargetTableList: this.appRowInfo.archiveTargetTableList
          },
          parentData,
          type: 1
        },
        success: () => {
          this.refreshTable()
        }
      })
    },
    deleteTreeNode(event) {
      this.$dialog({
        data: {
          title: this.$t('警告'),
          message: this.$t('是否确定删除?')
        },
        success: () => {
          this.$api.taskList.deleteArchiveTaskDetailList({ id: event.id }).then(() => {
            this.$toast({ content: this.$t('删除成功'), type: 'success' })
            this.refreshTable()
          })
        }
      })
    },
    nodeSelected(event) {
      let obj = {}
      this.appRowInfo.childFlatData.forEach((i) => {
        if (i.id === event.nodeData.id) {
          obj = {
            ...obj,
            ...i
          }
        }
        if (i.id === event.nodeData.parentID) {
          obj.parentData = i
        }
      })
      this.$emit('nodeSelected', obj)
    },
    refreshTable() {
      this.$emit('refreshTable')
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-view--wrap {
  /deep/.mt-commom-tree-view .mt-tree-view .e-treeview .e-list-item.e-active > .e-fullrow {
    border-color: #2783fe !important;
  }
}
</style>
