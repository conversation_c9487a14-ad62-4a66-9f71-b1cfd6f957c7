<template>
  <div class="upload-wrap">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <mt-form ref="archiveRef" :model="archiveForm" :rules="archiveFormRules">
        <mt-form-item :label="$t('标题名称')" prop="title" class="fullwidth">
          <mt-input v-model="archiveForm.title" />
        </mt-form-item>
        <mt-form-item :label="$t('归档UUID')" prop="archiveUuid" class="fullwidth">
          <mt-input v-model="archiveForm.archiveUuid" disabled />
        </mt-form-item>
        <mt-form-item :label="$t('归档源表名称')" prop="archiveSourceTable" class="fullwidth">
          <mt-select
            :data-source="appRowInfo.archiveSourceTableList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择归档源表名称')"
            v-model="archiveForm.archiveSourceTable"
            @change="archiveSourceTableChange"
          ></mt-select>
        </mt-form-item>
        <mt-form-item :label="$t('归档目标表名称')" prop="archiveTargetTable" class="fullwidth">
          <mt-select
            :data-source="appRowInfo.archiveTargetTableList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择归档目标表名称')"
            v-model="archiveForm.archiveTargetTable"
          ></mt-select>
        </mt-form-item>
        <mt-form-item v-if="tableType === 0" :label="$t('自定义条件')" prop="customCondition">
          <mt-input v-model="archiveForm.customCondition" />
        </mt-form-item>
        <mt-form-item
          v-if="associateFieldList.length && tableType === 1"
          :label="`${$t('关联父级字段')}(${parentData.archiveSourceTable})`"
          prop="associateField"
          class="fullwidth"
        >
          <mt-select
            :data-source="associateFieldList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择关联父级字段')"
            v-model="archiveForm.associateField"
          ></mt-select>
        </mt-form-item>
        <mt-form-item
          v-if="conditionFieldList.length && tableType === 1"
          :label="`${$t('条件字段')}(${archiveForm.archiveSourceTable})`"
          prop="conditionField"
          class="fullwidth"
        >
          <mt-select
            :data-source="conditionFieldList"
            :show-clear-button="true"
            :allow-filtering="true"
            filter-type="Contains"
            :placeholder="$t('请选择条件字段')"
            v-model="archiveForm.conditionField"
          ></mt-select>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      archiveFormRules: {
        archiveSourceTable: [
          { required: true, message: this.$t('请选择归档源表名称'), trigger: 'blur' }
        ],
        archiveTargetTable: [
          { required: true, message: this.$t('请选择归档目标表名称'), trigger: 'blur' }
        ],
        associateField: [
          { required: true, message: this.$t('请选择关联父级字段'), trigger: 'blur' }
        ],
        conditionField: [{ required: true, message: this.$t('请选择条件字段'), trigger: 'blur' }],
        title: [{ required: true, message: this.$t('请输入标题名称'), trigger: 'blur' }],
        customCondition: [{ required: true, message: this.$t('请输入自定义条件'), trigger: 'blur' }]
      },
      archiveForm: {
        archiveUuid: ''
      },
      associateFieldList: [],
      conditionFieldList: []
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    appRowInfo() {
      return this.modalData.appRowInfo
    },
    tableType() {
      return this.modalData.type
    },
    parentData() {
      return this.modalData.parentData
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.getColumnList()
  },
  methods: {
    async getColumnList() {
      if (this.tableType === 1) {
        // const res = await this.$api.taskList.getArchiveTaskColumList({
        //   app: this.appRowInfo.appName,
        //   dbType: 2,
        //   table: this.parentData.archiveTargetTable
        // })
        // this.conditionFieldList = res.data
        const res1 = await this.$api.taskList.getArchiveTaskColumList({
          app: this.appRowInfo.appName,
          dbType: 0,
          table: this.parentData.archiveSourceTable
        })
        this.associateFieldList = res1.data
      }
    },
    // archiveTargetTableChange(e) {
    //   this.$api.taskList
    //     .getArchiveTaskColumList({ app: this.appRowInfo.appName, dbType: 2, table: e.value })
    //     .then((res) => {
    //       this.conditionFieldList = res.data
    //       this.archiveForm.conditionField = ''
    //     })
    // },
    archiveSourceTableChange(e) {
      this.$api.taskList
        .getArchiveTaskColumList({ app: this.appRowInfo.appName, dbType: 2, table: e.value })
        .then((res) => {
          this.conditionFieldList = res.data
          this.archiveForm.conditionField = ''
        })
    },
    confirm() {
      this.$refs.archiveRef.validate((valid) => {
        if (valid) {
          const params = {
            ...this.archiveForm,
            type: this.tableType,
            parentId: this.appRowInfo.id,
            archiveUuid: this.parentData.archiveUuid
              ? this.parentData.archiveUuid
              : this.appRowInfo.archiveUuid
          }
          this.$api.taskList.saveArchiveTaskDetailList(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('confirm-function')
            }
          })
        }
      })
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped></style>
