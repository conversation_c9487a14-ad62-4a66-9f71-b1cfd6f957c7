<template>
  <div class="upload-wrap">
    <!-- <mt-button
      v-if="archiveForm.archiveUuid"
      iconCss="mt-icons mt-icon-icon_solid_Createorder"
      cssClass="e-flat"
      iconPosition="Right"
      @click.native="confirm"
      >{{ $t('保存') }}</mt-button
    > -->
    <vxe-button status="info" style="margin-bottom: 20px" size="small" @click.native="confirm">{{
      $t('保存')
    }}</vxe-button>
    <mt-form ref="archiveRef" :model="archiveForm" :rules="archiveFormRules">
      <mt-form-item :label="$t('标题名称')" prop="title">
        <mt-input v-model="archiveForm.title" :disabled="!archiveForm.archiveUuid" />
      </mt-form-item>
      <mt-form-item :label="$t('归档UUID')" prop="archiveUuid">
        <mt-input v-model="archiveForm.archiveUuid" disabled />
      </mt-form-item>
      <mt-form-item :label="$t('归档源表名称')" prop="archiveSourceTable">
        <mt-select
          :data-source="appRowInfo.archiveSourceTableList"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :open-dispatch-change="true"
          :placeholder="$t('请选择归档源表名称')"
          v-model="archiveForm.archiveSourceTable"
          :disabled="!archiveForm.archiveUuid"
          @change="archiveSourceTableChange"
        ></mt-select>
      </mt-form-item>
      <mt-form-item :label="$t('归档目标表名称')" prop="archiveTargetTable">
        <mt-select
          :data-source="appRowInfo.archiveTargetTableList"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :open-dispatch-change="true"
          :placeholder="$t('请选择归档目标表名称')"
          v-model="archiveForm.archiveTargetTable"
          :disabled="!archiveForm.archiveUuid"
        ></mt-select>
      </mt-form-item>
      <mt-form-item v-if="archiveForm.type === 0" :label="$t('自定义条件')" prop="customCondition">
        <mt-input v-model="archiveForm.customCondition" :disabled="!archiveForm.archiveUuid" />
      </mt-form-item>
      <mt-form-item
        v-if="conditionFieldList.length && archiveForm.type === 1"
        :label="`${$t('条件字段')}(${archiveForm.archiveTargetTable})`"
        prop="conditionField"
      >
        <mt-select
          :data-source="conditionFieldList"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :open-dispatch-change="true"
          :placeholder="$t('请选择条件字段')"
          v-model="archiveForm.conditionField"
          :disabled="!archiveForm.archiveUuid"
        ></mt-select>
      </mt-form-item>
      <mt-form-item
        v-if="associateFieldList.length && archiveForm.type === 1"
        :label="`${$t('关联父级字段')}(${parentData.archiveSourceTable})`"
        prop="associateField"
      >
        <mt-select
          :data-source="associateFieldList"
          :show-clear-button="true"
          :allow-filtering="true"
          filter-type="Contains"
          :open-dispatch-change="true"
          :placeholder="$t('请选择关联父级字段')"
          v-model="archiveForm.associateField"
          :disabled="!archiveForm.archiveUuid"
        ></mt-select>
      </mt-form-item>
    </mt-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      archiveFormRules: {
        archiveSourceTable: [
          { required: true, message: this.$t('请选择归档源表名称'), trigger: 'blur' }
        ],
        archiveTargetTable: [
          { required: true, message: this.$t('请选择归档目标表名称'), trigger: 'blur' }
        ],
        associateField: [
          { required: true, message: this.$t('请选择关联父级字段'), trigger: 'blur' }
        ],
        conditionField: [{ required: true, message: this.$t('请选择条件字段'), trigger: 'blur' }],
        title: [{ required: true, message: this.$t('请输入标题名称'), trigger: 'blur' }],
        customCondition: [{ required: true, message: this.$t('请输入自定义条件'), trigger: 'blur' }]
      },
      archiveForm: {
        archiveUuid: ''
      },
      associateFieldList: [],
      conditionFieldList: [],
      parentData: {},
      isInit: false
    }
  },
  props: {
    appRowInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  methods: {
    async initData(event) {
      if (event) {
        this.isInit = true
        if (event.parentData) {
          this.parentData = event.parentData
        }
        if (event.type === 1 && event.archiveSourceTable && event.parentData) {
          const res = await this.$api.taskList.getArchiveTaskColumList({
            app: this.appRowInfo.appName,
            dbType: 2,
            table: event.archiveSourceTable
          })
          this.conditionFieldList = res.data
          const res1 = await this.$api.taskList.getArchiveTaskColumList({
            app: this.appRowInfo.appName,
            dbType: 0,
            table: event.parentData.archiveSourceTable
          })
          this.associateFieldList = res1.data
        }
        this.archiveForm = {
          ...event
        }
        setTimeout(() => {
          this.archiveForm.conditionField = event.conditionField
          this.isInit = false
        }, 500)
        this.$refs.archiveRef.clearValidate()
      }
    },
    archiveSourceTableChange(e) {
      if (!this.isInit) {
        this.$api.taskList
          .getArchiveTaskColumList({ app: this.appRowInfo.appName, dbType: 2, table: e.value })
          .then((res) => {
            this.conditionFieldList = res.data
            this.archiveForm.conditionField = ''
          })
      }
    },
    // getArchiveTargetTable() {
    //   this.$api.taskList
    //     .getArchiveTaskColumList({
    //       app: this.appRowInfo.appName,
    //       dbType: 2,
    //       table: this.parentData.archiveTargetTable
    //     })
    //     .then((res) => {
    //       this.conditionFieldList = res.data
    //     })
    // },
    // getArchiveSourceTable() {
    //   this.$api.taskList
    //     .getArchiveTaskColumList({
    //       app: this.appRowInfo.appName,
    //       dbType: 0,
    //       table: this.parentData.archiveSourceTable
    //     })
    //     .then((res) => {
    //       this.associateFieldList = res.data
    //     })
    // },
    confirm() {
      this.$refs.archiveRef.validate((valid) => {
        if (valid) {
          const params = {
            ...this.archiveForm
            // parentId: this.appRowInfo.id
          }
          this.$api.taskList.saveArchiveTaskDetailList(params).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('保存成功'), type: 'success' })
              this.$emit('refreshTable')
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
