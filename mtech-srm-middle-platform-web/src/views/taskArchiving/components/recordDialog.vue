<template>
  <div class="upload-wrap">
    <mt-dialog ref="dialog" css-class="create-proj-dialog" :buttons="buttons" :header="header">
      <mt-template-page ref="tempaltePageRef" :template-config="pageConfig"> </mt-template-page>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ],
      pageConfig: [
        {
          // gridId: '00fb085b-dcb1-41ee-a343-13e8d02ef4d4',
          grid: {
            // editSettings: {
            //   allowAdding: true,
            //   allowEditing: true,
            //   allowDeleting: true,
            //   mode: 'Normal', // 默认normal模式
            //   allowEditOnDblClick: true,
            //   newRowPosition: 'Top'
            // },
            height: 'auto',
            columnData: [
              {
                field: 'logInfo',
                headerText: this.$t('日志')
              },
              {
                field: 'status',
                headerText: this.$t('状态'),
                valueConverter: {
                  type: 'map',
                  fields: { text: 'label', value: 'status' },
                  map: {
                    0: this.$t('成功'),
                    1: this.$t('失败')
                  }
                }
              },
              {
                field: 'createTime',
                headerText: this.$t('创建时间')
              }
            ],
            asyncConfig: {
              url: '/statistics/tenant/archive/record/v2/task/detail/query',
              params: { archiveUuid: this.modalData.archiveUuid }
            }
          }
        }
      ]
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    archiveUuid() {
      return this.modalData.archiveUuid
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      this.$emit('confirm-function')
    },
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>
<style lang="scss" scoped></style>
