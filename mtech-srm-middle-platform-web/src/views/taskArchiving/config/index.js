import { i18n } from '@/main.js'
// import dayjs from 'dayjs'

const typeOptions = [
  { label: '', value: null },
  { label: i18n.t('归档'), value: 0 },
  { label: i18n.t('删除'), value: 1 },
  { label: i18n.t('数据抽取'), value: 2 },
  { label: i18n.t('数据复制'), value: 3 },
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true
  },
  {
    field: 'appName',
    title: i18n.t('应用名称'),
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'appNameEdit'
    }
  },
  {
    field: 'archiveName',
    title: i18n.t('归档名称'),
    showOverflow: true,
    editRender: { name: 'input', attrs: { placeholder: i18n.t('请输入') } }
    // slots: {
    //   edit: 'archiveNameEdit'
    // }
  },
  {
    field: 'archiveUuid',
    title: i18n.t('归档UUID'),
    showOverflow: true
  },
  // {
  //   field: 'nextExecutionTime',
  //   title: i18n.t('下次执行时间'),
  //   showOverflow: true,
  //   formatter: ({ cellValue }) => {
  //     let str = ''
  //     if (cellValue && Number(cellValue)) {
  //       str = dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
  //     }
  //     return str
  //   }
  // },
  {
    field: 'status',
    title: i18n.t('状态'),
    showOverflow: true,
    formatter: ({ cellValue }) => {
      const statusOptions = [
        { label: i18n.t('待执行'), value: 0 },
        { label: i18n.t('执行中'), value: 1 },
        { label: i18n.t('已执行'), value: 2 },
        { label: i18n.t('执行失败'), value: 3 }
      ]
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'isEnable',
    title: i18n.t('启用状态'),
    showOverflow: true,
    formatter: ({ cellValue }) => {
      const statusOptions = [
        { label: i18n.t('禁用'), value: 0 },
        { label: i18n.t('启用'), value: 1 }
      ]
      let item = statusOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    }
  },
  {
    field: 'type',
    title: i18n.t('任务类型'),
    showOverflow: true,
    formatter: ({ cellValue }) => {
      let item = typeOptions.find((item) => item.value === cellValue)
      return item ? item.label : ''
    },
    editRender: {
      name: 'select',
      options: typeOptions,
      props: { placeholder: i18n.t('请选择任务类型'), style: 'background: #fff' }
    }
  },
  {
    field: 'detail',
    title: i18n.t('明细'),
    showOverflow: true,
    type: 'expand',
    slots: {
      content: 'detailContent'
    }
  },
  {
    field: 'record',
    title: i18n.t('执行记录'),
    showOverflow: true,
    slots: {
      default: 'recordDefault'
    }
  },
  {
    field: 'createTime',
    title: i18n.t('创建时间'),
    showOverflow: true
  },
  {
    field: 'createUserName',
    title: i18n.t('创建人'),
    showOverflow: true
  }
]

export const detailColumns = [
  {
    type: 'checkbox',
    width: 50,
    ignore: true
  },
  {
    field: 'archiveSourceTable',
    title: i18n.t('归档源表名称'),
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'sourceTableEdit'
    }
  },
  {
    field: 'archiveTargetTable',
    title: i18n.t('归档目标表名称'),
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'targetTableEdit'
    }
  },
  {
    field: 'archiveUuid',
    title: i18n.t('归档uuid'),
    showOverflow: true
  },
  {
    field: 'associateField',
    title: i18n.t('关联字段'),
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'associateFieldEdit'
    }
  },
  {
    field: 'conditionField',
    title: i18n.t('条件字段'),
    showOverflow: true,
    editRender: {},
    slots: {
      edit: 'conditionFieldEdit'
    }
  }
  // {
  //   field: 'status',
  //   title: i18n.t('状态'),
  //   showOverflow: true,
  //   formatter: ({ cellValue }) => {
  //     const statusOptions = [
  //       { label: i18n.t('成功'), value: 0 },
  //       { label: i18n.t('失败'), value: 1 }
  //     ]
  //     let item = statusOptions.find((item) => item.value === cellValue)
  //     return item ? item.label : ''
  //   }
  // }
]

export const ToolBar = [
  {
    code: 'Add',
    name: i18n.t('新增'),
    status: 'info'
  },
  {
    code: 'CloseEdit',
    name: i18n.t('取消编辑'),
    status: 'info',
    transfer: true
  },
  {
    code: 'Delete',
    name: i18n.t('删除'),
    status: 'info'
  },
  {
    code: 'Enable',
    name: i18n.t('启用'),
    status: 'info'
  },
  {
    code: 'Disable',
    name: i18n.t('禁用'),
    status: 'info'
  },
  {
    code: 'Execute',
    name: i18n.t('手动执行'),
    status: 'info'
  }
]
