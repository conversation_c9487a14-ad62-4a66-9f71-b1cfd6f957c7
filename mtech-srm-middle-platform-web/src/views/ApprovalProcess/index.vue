<!--
 * @Author: your name
 * @Date: 2021-10-14 12:01:24
 * @LastEditTime: 2021-11-03 16:59:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalProcess\index.vue
-->
<template>
  <div class="approval-process-wrap">
    <mt-template-page
      ref="tempaltePageRef"
      :template-config="pageConfig"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTool="handleClickCellTool"
      @handleClickCellTitle="handleClickCellTitle"
    >
      <div slot="slot-filter">
        <div class="solt-wrap">
          <p class="solt-item">
            <tenant />
          </p>
          <p class="solt-item">
            <mt-select
              :width="300"
              v-model="search.applicationId"
              :data-source="applicationList"
              :show-clear-button="true"
              :placeholder="$t('请选择应用模块')"
              :fields="{ text: 'applicationName', value: 'applicationId' }"
              @change="applicationIdChange($event)"
            ></mt-select>
            <span style="margin-left: 20px">
              <mt-select
                :width="300"
                v-model="search.menuId"
                :data-source="menuList"
                :show-clear-button="true"
                :placeholder="$t('请选择归属菜单')"
                @change="menuIdChange($event)"
                :fields="{ text: 'menuName', value: 'menuId' }"
              ></mt-select>
            </span>
          </p>
        </div>
      </div>
    </mt-template-page>
  </div>
</template>
<script>
import { listToolBar1, listColumnData } from './config'
import tenant from '@/views/ApprovalProcess/components/tenant'
// import Bus from '@/utils/bus'
export default {
  components: {
    tenant
  },
  data() {
    return {
      pageConfig: [
        {
          toolbar: listToolBar1,
          grid: {
            columnData: listColumnData,
            // dataSource:listData,
            asyncConfig: {
              url: this.$api.approvalConfig.approvalConfigList
            }
          }
        }
      ],
      applicationList: [],
      menuList: [],
      search: {
        applicationId: '',
        menuId: ''
      },
      user: {}
    }
  },
  mounted() {
    //  Bus.$on('addTemplate',(res)=>{
    //    this.$router.push({
    //     path:`/middlePlatform/approval-template`
    //   })
    //  })
  },
  methods: {
    handleClickToolBar(e) {
      const { grid, toolbar } = e
      console.log(e)
      if (e.toolbar.id === 'Add') {
        this.addProcess()
      }

      const sections = grid.getSelectedRecords()
      const idList = sections.map((v) => v.configId)

      if (toolbar.id === 'version') {
        this.viewHistory(idList)
      } else if (toolbar.id === 'del') {
        if (idList.length == 0) {
          this.$toast({ content: this.$t('请选择一行数据'), type: 'warning' })
          return
        }
        this.deleteProcess(idList)
      }
    },
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      const { tool, data } = e
      if (tool.id === 'edit') {
        this.editProcess(data)
      } else if (tool.id === 'preview') {
        this.previewProcess(data)
      } else if (tool.id == 'off') {
        this.updateStatus(data)
      }
    },
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
      // if (e.field === 'column1') {
      // }
    },
    addProcess() {
      this.$dialog({
        modal: () => import('./components/addProcessDialog.vue'),
        data: {
          title: this.$t('审批流配置'),
          parentVm: this
        },
        success: () => {
          this.$refs.tempaltePageRef.refreshCurrentGridData()
        }
      })
    },
    editProcess(data) {
      this.$dialog({
        modal: () => import('./components/editProcessDialog.vue'),
        data: {
          title: this.$t('审批流详情'),
          configId: data.configId,
          parentVm: this,
          type: 2 // 2编辑  3 预览
        },
        success: (e) => {
          if (e) this.$refs.tempaltePageRef.refreshCurrentGridData()
        }
      })
    },
    previewProcess(data) {
      this.$dialog({
        modal: () => import('./components/editProcessDialog.vue'),
        data: {
          title: this.$t('审批流详情'),
          configId: data.configId,
          parentVm: this,
          type: 3 // 2编辑  3 预览
        },
        success: (e) => {
          if (e) this.$refs.tempaltePageRef.refreshCurrentGridData()
        }
      })
    },
    deleteProcess(list) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('删除后将不可恢复，是否继续？')
        },
        success: () => {
          this.$api.approvalConfig.deleteConfig({ configIds: list.join(',') }).then((res) => {
            if (res.code === 200) {
              this.$toast({ content: this.$t('操作成功'), type: 'success' })
              this.$refs.tempaltePageRef.refreshCurrentGridData()
            }
          })
        }
      })
    },
    applicationIdChange({ itemData }) {
      this.search.menuId = ''
      this.$nextTick(() => {
        if (itemData) {
          const { applicationId } = itemData
          this.getMenuList(applicationId)
          this.pageConfig[0].grid.asyncConfig.url = `${this.$api.approvalConfig.approvalConfigList}?applicationId=${applicationId}&menuId=${this.search.menuId}`
        } else {
          this.pageConfig[0].grid.asyncConfig.url = this.$api.approvalConfig.approvalConfigList
          this.menuList = []
        }
      })
    },
    getApplationList() {
      this.$api.approvalConfig.queryApplicationList({}).then((res) => {
        this.applicationList = res.data
      })
    },
    getMenuList(applicationId) {
      this.menuList = []
      this.$nextTick(() => {
        this.$api.approvalConfig.queryMenuList({ applicationId }).then((res) => {
          if (res.code === 200) {
            this.menuList = res.data
          }
        })
      })
    },
    menuIdChange({ itemData }) {
      this.$nextTick(() => {
        if (itemData) {
          const { menuId } = itemData
          this.pageConfig[0].grid.asyncConfig.url = `${this.$api.approvalConfig.approvalConfigList}?applicationId=${this.search.applicationId}&menuId=${menuId}`
        } else {
          const curl = this.$api.approvalConfig.approvalConfigList
          const applicationId = this.search.applicationId
          const url = applicationId ? `${curl}?applicationId=${applicationId}` : `${curl}`
          this.pageConfig[0].grid.asyncConfig.url = url
        }
      })
    }
  },
  created() {
    this.getApplationList()
  }
}
</script>

<style lang="scss" scoped>
.approval-process-wrap {
  width: 100%;
  height: 100%;
}
/deep/ .statusOff {
  color: #ed5633;
  padding: 2px;
  font-size: 13px;
  background: rgba(237, 86, 51, 0.3);
  position: relative;
  top: -5px;
}
/deep/ .statusOn {
  background: rgba(99, 134, 193, 0.3);
  padding: 2px;
  font-size: 13px;
  color: rgba(99, 134, 193, 1);
  position: relative;
  top: -5px;
}
.solt-wrap {
  background: #fff;
  padding: 10px;
  .solt-item {
    padding: 10px 0;
  }
}
</style>
