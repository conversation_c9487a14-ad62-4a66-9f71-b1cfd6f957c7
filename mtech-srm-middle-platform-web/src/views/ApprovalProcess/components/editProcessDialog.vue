<template>
  <div class="process-dialog-wrap">
    <mt-dialog
      ref="dialog"
      css-class="create-proj-dialog"
      :buttons="buttons"
      :header="header"
      :close="close"
    >
      <mt-form ref="createProcessRef" :model="createProcessForm" :rules="createProcessRules">
        <mt-form-item prop="applicationId" :label="$t('应用模块')">
          <mt-select
            v-model="createProcessForm.applicationId"
            float-label-type="Never"
            :allow-filtering="true"
            :readonly="readonly"
            :data-source="applicationData"
            :fields="{ text: 'applicationName', value: 'applicationId' }"
            :placeholder="$t('请选择应用模块')"
          ></mt-select>
          <!-- @change="applicationIdChange($event)">  -->
        </mt-form-item>

        <mt-form-item prop="approveStatus" :label="$t('是否启用')">
          <!-- <mt-radio
            :disabled="readonly"
            v-model="createProcessForm.approveStatus"
            :dataSource="statusData"
          ></mt-radio> -->
          <mt-switch
            class="item"
            :disabled="readonly"
            active-value="1"
            inactive-value="0"
            v-model="createProcessForm.approveStatus"
          ></mt-switch>
        </mt-form-item>
        <mt-form-item prop="otherApproveStatus" :label="$t('是否启用外部审批')">
          <mt-switch
            class="item"
            active-value="1"
            inactive-value="0"
            :disabled="readonly"
            v-model="createProcessForm.otherApproveStatus"
          ></mt-switch>
        </mt-form-item>
        <mt-form-item prop="menuId" :label="$t('归属菜单')">
          <mt-select
            v-model="createProcessForm.menuId"
            float-label-type="Never"
            :allow-filtering="true"
            :readonly="readonly"
            :data-source="menuData"
            :fields="{ text: 'menuName', value: 'menuId' }"
            :placeholder="$t('请选择归属菜单')"
          ></mt-select>
          <!-- @change="menuIdChange($event)" -->
        </mt-form-item>
        <mt-form-item prop="approvePointId" :label="$t('审批节点')">
          <mt-select
            v-model="createProcessForm.approvePointId"
            float-label-type="Never"
            :allow-filtering="true"
            :readonly="readonly"
            :data-source="pointData"
            :fields="{ text: 'approvePointName', value: 'approvePointId' }"
            :placeholder="$t('请选择审批节点')"
            @change="approvePointIdChange1($event)"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="businessTypeId" :label="$t('业务类型')" v-if="isBusinessType == 1">
          <mt-select
            v-model="createProcessForm.businessTypeId"
            float-label-type="Never"
            :allow-filtering="true"
            :disabled="readonly || businessTypeData.length <= 0"
            :data-source="businessTypeData"
            :show-clear-button="true"
            :fields="{ text: 'businessTypeName', value: 'businessTypeId' }"
            :placeholder="$t('请选择业务类型')"
          ></mt-select>
        </mt-form-item>
        <mt-form-item prop="approveTemplateId" :label="$t('审批流')">
          <mt-select
            v-model="createProcessForm.approveTemplateId"
            float-label-type="Never"
            :allow-filtering="true"
            :readonly="readonly"
            :data-source="processData"
            :fields="{
              text: 'workflowName',
              value: 'templateId'
            }"
            :placeholder="$t('请选择审批流')"
          ></mt-select>
          <mt-button css-class="e-flat" :is-primary="true" @click="addApprovalTemplate">{{
            $t('配置')
          }}</mt-button>
        </mt-form-item>

        <mt-form-item prop="name" :label="$t('中文名称')">
          <mt-input
            v-model="createProcessForm.name"
            float-label-type="Never"
            :readonly="readonly"
            :show-clear-button="!readonly"
            :placeholder="$t('请输入中文名称')"
          ></mt-input>
        </mt-form-item>
        <mt-form-item> </mt-form-item>
        <mt-form-item class="full-width" prop="approveRemark" :label="$t('备注')">
          <mt-input
            v-model="createProcessForm.approveRemark"
            :multiline="true"
            :rows="3"
            :readonly="readonly"
            float-label-type="Never"
            ax-length="200"
            :show-clear-button="!readonly"
            :placeholder="$t('请输入备注')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </mt-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      btnObj: {
        2: [
          {
            click: this.confirm,
            buttonModel: { isPrimary: 'true', content: this.$t('保存') }
          },
          {
            click: this.cancel,
            buttonModel: { content: this.$t('取消') }
          }
        ],
        3: {
          0: [
            {
              click: this.cancel,
              buttonModel: { content: this.$t('确定') }
            },
            {
              click: this.edit,
              buttonModel: { content: this.$t('编辑') }
            }
          ],
          1: [
            {
              click: this.cancel,
              buttonModel: { content: this.$t('确定') }
            },
            {
              click: this.edit,
              buttonModel: { content: this.$t('编辑') }
            },
            {
              click: this.stop,
              buttonModel: { content: this.$t('禁用') }
            }
          ]
        }
      },
      createProcessForm: {
        applicationId: '',
        approvePointId: '',
        approveRemark: '',
        approveStatus: '',
        approveTemplateId: '',
        otherApproveStatus: 0,
        businessTypeId: '',
        configId: '',
        menuId: '',
        name: '',
        version: ''
      },
      applicationData: [],
      menuData: [],
      businessTypeData: [],
      statusData: [
        {
          label: this.$t('是'),
          value: '1'
        },
        {
          label: this.$t('否'),
          value: '0'
        }
      ],
      pointData: [],
      processData: [],
      firstLoad: true,
      isExsit: false,
      isChange: false,
      isBusinessType: 0
    }
  },
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    header() {
      return this.modalData.title
    },
    configId() {
      return this.modalData.configId
    },
    readonly() {
      return this.modalData.type == 3
    },
    buttons() {
      if (this.modalData.type == 2) {
        return this.btnObj[this.modalData.type]
      } else {
        return this.btnObj[this.modalData.type][this.createProcessForm.approveStatus]
      }
    },
    createProcessRules() {
      const validateName = (rule, value, callback) => {
        const regName = /^[a-zA-Z\u4e00-\u9fa5]+$/g
        if (!regName.test(value)) {
          callback(new Error(this.$t('只能输入汉字和字母')))
        } else {
          callback()
        }
      }
      return {
        applicationId: [{ required: true, message: this.$t('请选择模块'), trigger: 'blur' }],
        menuId: [{ required: true, message: this.$t('请选择归属菜单'), trigger: 'blur' }],
        approvePointId: [{ required: true, message: this.$t('请选择审批节点'), trigger: 'blur' }],
        approveTemplateId: [{ required: true, message: this.$t('请选择审批流'), trigger: 'blur' }],
        name: [
          { required: true, message: this.$t('请输入中文名称'), trigger: 'blur' },
          { max: 20, message: this.$t('名称不能超过20个字符'), trigger: 'blur' },
          { validator: validateName, trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      // this.$emit("confirm-function");
      this.$refs.createProcessRef.validate((valid) => {
        if (valid) {
          this.save()
        }
      })
    },
    cancel() {
      this.$emit('confirm-function', this.isChange)
    },
    custom() {
      this.$emit('confirm-function', this.isChange)
      this.$toast({ content: this.$t('敬请期待...'), type: 'warning' })
      // Bus.$emit('approvaSuccess',true)
    },
    applicationIdChange() {
      this.createProcessForm.menuId = ''
      this.createProcessForm.approvePointId = ''
      this.createProcessForm.approveTemplateId = ''
      if (this.isExsit && this.createProcessForm.name) {
        this.createProcessForm.name = ''
        this.createProcessForm.approveRemark = ''
      }
      this.getMenuList()
      this.getPointList()
      this.getProcessList()
    },
    menuIdChange() {
      this.createProcessForm.approvePointId = ''
      if (this.isExsit && this.createProcessForm.name) {
        this.createProcessForm.name = ''
        this.createProcessForm.approveRemark = ''
      }
      this.getPointList()
    },
    approvePointIdChange() {
      if (this.isExsit && this.createProcessForm.name) {
        this.createProcessForm.name = ''
        this.createProcessForm.approveRemark = ''
      }
      this.checkIsOnly()
    },
    approvePointIdChange1(e) {
      const { itemData } = e
      if (itemData) {
        const { isBusinessType } = itemData
        this.isBusinessType = isBusinessType
      } else {
        this.isBusinessType = 0
        this.createProcessForm.businessTypeId = ''
      }
    },
    businessTypeIdChange() {
      if (this.isExsit && this.createProcessForm.name) {
        this.createProcessForm.name = ''
        this.createProcessForm.approveRemark = ''
      }
      this.checkIsOnly()
    },
    getApplicationList() {
      this.$api.approvalConfig.queryApplicationList({}).then((res) => {
        // this.applicationData = res.data;
        this.applicationData = []
        if (res.code === 200) this.applicationData = res.data
      })
    },
    getMenuList() {
      this.menuData = []
      this.$nextTick(() => {
        const { applicationId } = this.createProcessForm
        this.$api.approvalConfig.queryMenuList({ applicationId }).then((res) => {
          if (res.code === 200) {
            this.menuData = res.data
          }
        })
      })
    },
    getPointList() {
      this.pointData = []
      this.$nextTick(() => {
        const { applicationId, menuId } = this.createProcessForm
        console.log('menuId:', menuId)
        if (applicationId && menuId) {
          this.$api.approvalConfig.queryPointList({ applicationId, menuId }).then((res) => {
            if (res.code === 200) {
              this.pointData = res.data
            }
          })
        }
      })
    },
    getBusinessTypeList() {
      this.businessTypeData = []
      this.$api.approvalConfig.queryBusinessTypeList({}).then((res) => {
        if (res.code === 200) {
          this.businessTypeData = res.data
        }
      })
    },
    getProcessList() {
      this.processData = []
      this.$nextTick(() => {
        const { applicationId } = this.createProcessForm
        this.$api.approvalConfig.getListByApplicationId({ applicationId }).then((res) => {
          if (res.code === 200) {
            this.processData = res.data
          }
        })
      })
    },
    save() {
      const params = JSON.parse(JSON.stringify(this.createProcessForm))
      this.$api.approvalConfig.saveOrUpdate(params).then((res) => {
        if (res.code === 200) {
          this.isChange = true
          this.$emit('confirm-function', this.isChange)
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
        } else {
          this.$toast({ content: res.msg, type: 'error' })
        }
      })
    },
    close() {
      console.log(this.isChange, 'chaneg')
      this.$emit('confirm-function', this.isChange)
    },
    formInit() {
      this.$nextTick(() => {
        this.getApplicationList()
        this.getMenuList()
        this.getPointList()
        this.getProcessList()
        this.getBusinessTypeList()
      })
    },
    getConfigById() {
      return new Promise((resolve) => {
        this.$api.approvalConfig.getConfigById({ configId: this.configId }).then((res) => {
          const e = this.createProcessForm
          const {
            id,
            menuId,
            approvePointId,
            approveTemplateId,
            approveRemark,
            approveStatus,
            otherApproveStatus,
            businessTypeId,
            name,
            applicationId,
            version
          } = res.data
          e.configId = id
          e.applicationId = applicationId
          e.menuId = menuId
          e.approvePointId = approvePointId
          e.approveTemplateId = approveTemplateId
          e.approveRemark = approveRemark
          e.approveStatus = String(approveStatus)
          e.otherApproveStatus = String(otherApproveStatus)
          e.businessTypeId = businessTypeId
          e.version = version
          e.name = name
          resolve(true)
        })
      })
    },
    checkIsOnly() {
      this.$nextTick(() => {
        if (
          (this.businessTypeData.length > 0 && !this.createProcessForm.businessTypeId) ||
          !this.createProcessForm.approvePointId
        ) {
          return false
        }
        const { applicationId, menuId, approvePointId, businessTypeId } = this.createProcessForm
        this.$api.approvalConfig
          .checkConfigIsOnly({
            applicationId,
            menuId,
            approvePointId,
            businessTypeId
          })
          .then((res) => {
            if (res.data.isExsit) {
              this.isExsit = true
              const {
                applicationId,
                menuId,
                approvePointId,
                approveStatus,
                otherApproveStatus,
                approveTemplateId,
                businessTypeId,
                name,
                approveRemark,
                configId,
                version
              } = res.data.approvePointWorkflowRelResponse
              const e = this.createProcessForm
              e.applicationId = applicationId
              e.menuId = menuId
              e.approvePointId = approvePointId
              e.businessTypeId = businessTypeId
              e.approveTemplateId = approveTemplateId
              e.approveStatus = String(approveStatus)
              e.otherApproveStatus = String(otherApproveStatus)
              e.name = name
              e.approveRemark = approveRemark
              e.configId = configId
              e.version = version
            } else {
              this.isExsit = false
              this.createProcessForm.configId = this.configId
            }
          })
      })
    },
    addApprovalTemplate() {
      this.$emit('cancel-function')
      this.modalData.parentVm.$router.push({
        path: '/middlePlatform/approval-template'
      })
    },
    edit() {
      this.modalData.type = 2
    },
    stop() {
      this.$api.approvalConfig
        .chengeStatus({ configId: this.configId, approveStatus: 0 })
        .then((res) => {
          if (res.code === 200) {
            this.$toast({ content: this.$t('禁用成功'), type: 'success' })
            this.createProcessForm.approveStatus = '0'
            this.isChange = true
          }
        })
    }
  },
  created() {
    this.getConfigById().then(() => {
      this.formInit()
      this.firstLoad = false
    })
  },
  watch: {
    'createProcessForm.applicationId': {
      handler: function () {
        if (!this.firstLoad) this.applicationIdChange()
      },
      immediate: false
    },
    'createProcessForm.menuId': {
      handler: function () {
        if (!this.firstLoad) this.menuIdChange()
      },
      immediate: false
    },
    'createProcessForm.approvePointId': {
      handler: function () {
        if (!this.firstLoad) this.approvePointIdChange()
      },
      immediate: false
    },
    'createProcessForm.businessTypeId': {
      handler: function () {
        if (!this.firstLoad) this.checkIsOnly()
      },
      immediate: false
    }
  }
}
</script>
<style lang="scss" scoped>
.full-width {
  width: 100% !important;
}
</style>
