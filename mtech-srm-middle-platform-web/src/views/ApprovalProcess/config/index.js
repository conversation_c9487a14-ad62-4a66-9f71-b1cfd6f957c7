import { i18n } from '@/main.js'
/*
 * @Author: your name
 * @Date: 2021-10-14 12:23:18
 * @LastEditTime: 2021-10-27 22:02:49
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalProcess\config\index.js
 */
import columTime from '../components/columTime.vue'
export const listToolBar1 = {
  useBaseConfig: false, // 代表不使用组件中的toolbar配置，使用当前项的toolbar
  tools: [
    [
      { id: 'Add', icon: 'icon_solid_Createorder', title: i18n.t('新增') },
      { id: 'del', icon: 'icon_solid_Createorder', title: i18n.t('删除') }
    ],
    ['Refresh']
  ]
}
export const listColumnData = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'name',
    headerText: i18n.t('配置名称'),
    width: 250
  },
  {
    field: 'applicationName',
    headerText: i18n.t('应用模块'),
    cssClass: ''
  },
  {
    field: 'businessTypeName',
    headerText: i18n.t('业务类型'),
    cssClass: ''
  },
  {
    field: 'menuName',
    headerText: i18n.t('归属菜单')
  },
  {
    field: 'approvePointName',
    headerText: i18n.t('审批节点名称')
  },
  {
    field: 'workflowName',
    headerText: i18n.t('关联审批流')
  },
  {
    field: 'updateUserName',
    headerText: i18n.t('更新人')
  },
  {
    field: 'updateTime',
    headerText: i18n.t('更新时间'),
    template: function () {
      return {
        template: columTime
      }
    }
  },
  // {
  //   field:'otherApproveStatusView',
  //   headerText:'外部审批'
  // },
  {
    field: 'approveStatus',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: [
        { status: 0, label: i18n.t('已禁用'), cssClass: ['statusOff'] },
        { status: 1, label: i18n.t('已启用'), cssClass: ['statusOn'] }
      ]
    },
    cellTools: [
      {
        id: 'preview',
        icon: 'icon_Hiddenpassword',
        title: i18n.t('查看')
      }
      // {
      //   id: "off",
      //   icon: "icon_solid_edit",
      //   title: i18n.t("禁用"),
      //   visibleCondition: (data) => {
      //         //enableStatus	状态 0 草稿 1 启用 2 停用
      //         return data["approveStatus"] == 1;
      //       }
      // }
    ]
  },
  {
    field: 'approveRemark',
    headerText: i18n.t('备注')
  }
]
