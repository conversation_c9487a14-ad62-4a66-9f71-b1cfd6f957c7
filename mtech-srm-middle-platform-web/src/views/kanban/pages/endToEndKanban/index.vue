<!-- 采购看板 -->
<template>
  <div class="purchase-kanban">
    <div class="header">
      <div style="min-width: 238px">
        <button ref="btn" class="header-logo" />
      </div>
      <span class="title">{{ title }}</span>
      <span class="date">{{ dateParam }}</span>
    </div>
    <!-- 左侧bu按钮 -->
    <div class="query-box header-left">
      <span
        v-for="item in buList"
        :key="item.value"
        class="query-item"
        :class="{ 'actived-item': selectedBU === item.value }"
        @click="handlelSelectBu(item.value)"
        >{{ item.label }}</span
      >
    </div>
    <!-- 右侧时间按钮 -->
    <div class="query-box header-right">
      <span
        v-for="item in dateOptions"
        :key="item.value"
        class="query-item"
        :class="{ 'actived-item': dateType === item.value }"
        @click="handlelSelectType(item.value)"
        >{{ item.label }}</span
      >
      <datePicker :value="chooseDate" :is-visible="showDatePicker" @confirm="handleChooseDate" />
      <span class="query-item" style="margin-right: 10px; text-align: right">
        <i
          ref="scrRef"
          class="mt-icons mt-icon-a-icon_Fullscreen full-screen"
          @click="toggleScreen"
        ></i>
      </span>
    </div>
    <div class="content">
      <div class="top">
        <div class="top-left">
          <div class="top-card-area">
            <cardItem v-for="item in orderData" :data="item" :key="item.title" />
          </div>
          <div class="top-chart-area">
            <div class="pie-chart-box">
              <div class="sub-label-title">
                <span class="part-icon">{{ $t('供应商备货达成率') }}</span>
              </div>
              <div style="flex: 1; height: 100%">
                <PieChart :dataSource="pieDataSource" />
              </div>
            </div>
            <div class="bar-chart-box">
              <div class="sub-label-title">
                <span class="part-icon">{{ $t('产能与预测供需满足率') }}</span>
                <div class="sub-label-title_select-area">
                  <span
                    v-for="item in [
                      { label: '排名前10', value: 'top10' },
                      { label: '排名后10', value: 'bottom10' }
                    ]"
                    :key="item.value"
                    class="query-rank-item"
                    :class="{ 'actived-rank-item': selectedRank === item.value }"
                    @click="handleChooseRank(item.value)"
                    >{{ item.label }}</span
                  >
                  <div class="custom-select">
                    <ScSelect
                      v-model="chooseMonth"
                      :options="monthList"
                      optionWidth="62px"
                      @change="handleChooseMonth"
                    />
                  </div>
                </div>
              </div>
              <div style="flex: 1; height: 100%">
                <BarChart :barDataSource="barDataSource" />
              </div>
            </div>
          </div>
        </div>
        <div class="top-right">
          <div class="sub-label-title">
            <span class="part-icon">{{ $t('工厂车辆统计') }}</span>
          </div>
          <div style="height: 200px; overflow: hidden">
            <ScrollTable
              :columns="orderColumns"
              :tableData="orderTaleData"
              height="200px"
            ></ScrollTable>
          </div>
          <div style="height: 52px"></div>
          <div class="sub-label-title">
            <span class="part-icon">{{ $t('园区车辆实时统计') }}</span>
          </div>
          <LineChart style="flex: 1" :dataSource="lineDataSource" />
        </div>
      </div>
      <div class="bottom">
        <div class="sub-label-title">
          <span class="part-icon">{{ $t('准时交付率') }}</span>
        </div>
        <div class="bottom-content">
          <div class="bottom-left">
            <div class="chart-area">
              <TargetBarChart
                :title="$t('基地准时交货率')"
                :dataSource="deliveryRate"
                :targetValue="deliveryRateBaseValue"
              />
            </div>
          </div>
          <div class="bottom-center">
            <div class="chart-area">
              <ProgressChart
                :title="$t('整体走势分析')"
                :subTitle="$t('供应商准时交货率')"
                bu="空调"
                :rate="supplierDeliverRate"
              />
            </div>
          </div>
          <div class="bottom-right">
            <div class="chart-area">
              <searchLineChart ref="searchLineChart" @handleSearch="handleSearch" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { buList, dateOptions, orderColumns, getMonthList, getOrderList, getPieData } from './config'
import cardItem from './components/cardItem'
import LineChart from './components/LineChart'
import BarChart from './components/BarChart'
import TargetBarChart from './components/TargetBarChart'
import PieChart from './components/PieChart'
import ProgressChart from './components/ProgressChart'
import searchLineChart from './components/searchLineChart'
import ScrollTable from './components/scrollTable'
import DatePicker from './components/datePicker'
import ScSelect from './components/scSelect'
export default {
  components: {
    LineChart,
    BarChart,
    PieChart,
    ScrollTable,
    cardItem,
    TargetBarChart,
    searchLineChart,
    ProgressChart,
    DatePicker,
    ScSelect
  },
  data() {
    return {
      title: '端到端可视化综合分析看板',
      timestamp: null,
      selectedBU: 'KT',
      dateType: 'day',
      orderData: getOrderList(),
      buList,
      dateOptions,
      monthList: getMonthList(),
      chooseMonth: dayjs(new Date()).format('YYYY-MM'),
      selectedRank: 'top10',
      orderColumns,
      chooseDate: [new Date(), new Date()],
      showDatePicker: false,
      orderTaleData: [],
      lineDataSource: [],
      pieDataSource: getPieData(),
      deliveryRate: [],
      deliveryRateBaseValue: '0',
      supplierDeliverRate: 0,
      barDataSource: []
    }
  },
  computed: {
    dateParam() {
      return '周' + this.dateFormatter(this.timestamp, 'day \xa0 YYYY年MM月DD日 hh:mm:ss')
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    window.onresize = () => {
      this.setScrollHeight()
    }
    this.setScrollHeight()
    this.timestamp = +new Date()
    this.timeInterval && clearInterval(this.timeInterval)
    this.timeInterval = setInterval(() => {
      this.timestamp += 1000
    }, 1000)
    this.refreshInterval && clearInterval(this.refreshInterval)
    this.refreshInterval = setInterval(() => {
      this.queryEndtoEndKanbanData()
      this.$refs.searchLineChart.handleSearch()
    }, 180000)
  },
  beforeDestroy() {
    this.timeInterval && clearInterval(this.timeInterval)
    this.refreshInterval && clearInterval(this.refreshInterval)
  },
  methods: {
    initData() {
      this.queryEndtoEndKanbanData()
    },
    queryEndtoEndKanbanData() {
      const data = {
        businessUnitCode: this.selectedBU,
        capacityMonth: 0,
        endDate: '',
        startDate: ''
      }
      if (this.dateType === 'custom') {
        const startDate = this.chooseDate[0]
        const endDate = this.chooseDate[1]
        data.startDate = startDate
        data.endDate = endDate
      } else {
        const startDate = dayjs(new Date()).startOf(this.dateType).format('YYYY-MM-DD')
        const endDate = dayjs(new Date()).endOf(this.dateType).format('YYYY-MM-DD')
        data.startDate = startDate
        data.endDate = endDate
      }
      this.$api.kanban.queryEndtoEndKanbanData(data).then((res) => {
        if (res.code === 200) {
          // 左上角订单信息数据赋值
          this.orderData = getOrderList(res.data)
          // 工厂车辆统计
          this.orderTaleData = res.data.factoryCarInfoList || []
          // 园区车辆实时统计
          this.setLineData(res.data)
          // 供应商备货达成率
          this.pieDataSource = getPieData(res.data)
        }
      })
    },
    setLineData(data) {
      // const xAxisData = ['0-2', '2-4', '4-6', '6-8', '8-10', '10-12', '12-14', '14-16', '16-18', '18-20', '22-24']
      const totalArr = [[], [], []]
      data.carInfo?.forEach((item, index) => {
        if (typeof item === 'string') {
          item = JSON.parse(item)
        }
        item.forEach((itm, idx) => {
          totalArr[idx].push({
            name: index,
            value: itm.value
          })
        })
      })
      totalArr.push(totalArr.shift())
      this.lineDataSource = totalArr
      // this.lineDataSource = data.carInfo.map((item) => {
      //   if (typeof item === 'string') {
      //     item = JSON.parse(item)
      //   }
      //   return item.map((itm) => {
      //     return {
      //       ...itm,
      //       name: itm.code
      //     }
      //   })
      // })
    },
    handleSearch(data) {
      this.deliveryRate = Object.keys(data.deliveryRateMap).map((i) => {
        return {
          value: (data.deliveryRateMap[i] * 1000) / 10,
          name: i
        }
      })
      this.supplierDeliverRate = (data.deliveryRateTotal * 1000) / 10
      // 基地准时交货率 ---- 目标线
      this.deliveryRateBaseValue = String((data.deliveryRateBaseValue * 1000) / 10)
    },
    getDate() {
      let dateArr = []
      let currentDate = dayjs()
      for (let i = 14; i >= 0; i--) {
        let date = currentDate.subtract(i, 'day')
        dateArr.push(date.format('MM-DD'))
      }
      return dateArr
    },
    handlelSelectBu(bu) {
      console.log('目前默认是KT', bu)
      // this.selectedBU = bu
      this.selectedBU = 'KT'
      this.initData()
    },
    handlelSelectType(type) {
      this.dateType = type
      if (type === 'custom') {
        this.showDatePicker = !this.showDatePicker
      } else {
        this.queryEndtoEndKanbanData()
        this.showDatePicker = false
      }
    },
    handleChooseDate(date) {
      if (!date[1]) {
        date[1] = date[0]
      }
      this.showDatePicker = false
      this.chooseDate = date
      this.queryEndtoEndKanbanData()
    },
    handleChooseMonth(value, item) {
      console.log('结果是一个string11111-----', value, item)
    },
    handleChooseRank(value) {
      this.selectedRank = value
      console.log('结果是一个string2222-----', value)
    },
    dateFormatter(date, format = 'YYYY-MM-DD hh:mm:ss') {
      if (typeof date === 'string') {
        date = /^\d+$/.test(date) ? Number(date) : date.replace(/-/g, '/')
      }
      const dt = new Date(date)
      if (!dt.getTime()) {
        return ''
      }
      const fill = (str, num = 2) => str.toString().padStart(num, '0')
      const days = ['日', '一', '二', '三', '四', '五', '六']
      const map = {
        YYYY: dt.getFullYear(),
        MM: fill(dt.getMonth() + 1),
        DD: fill(dt.getDate()),
        hh: fill(dt.getHours()),
        mm: fill(dt.getMinutes()),
        ss: fill(dt.getSeconds()),
        ms: fill(dt.getMilliseconds(), 3),
        day: dt.getDay(),
        ts: dt.getTime()
      }
      return Object.keys(map).reduce((acc, key) => {
        const reg = new RegExp(key, 'g')
        return acc.replace(reg, () => {
          return key === 'day' ? days[map[key]] : map[key]
        })
      }, format)
    },
    toggleScreen() {
      this.setScreenful()
    },
    setScreenful() {
      this.$nextTick(() => {
        const app = document.querySelector('.purchase-kanban')
        if (app.mozRequestFullScreen) {
          app.mozRequestFullScreen()
        } else if (app.webkitRequestFullscreen) {
          app.webkitRequestFullscreen()
        } else if (app.msRequestFullscreen) {
          app.msRequestFullscreen()
        }
      })
    },
    setScrollHeight() {
      this.$nextTick(() => {
        const tableDom = document.getElementsByClassName('auto-table')[0]
        if (tableDom) {
          this.scrollHeight = tableDom.offsetHeight - 40
        }
      })
    }
  }
}
</script>

<style lang="scss">
.purchase-kanban {
  width: 100%;
  height: 100%;
  background-color: #142478 !important;
  .header {
    display: flex;
    justify-content: space-between;
    height: 80px;
    text-align: center;
    line-height: 80px;
    background: url('~@/assets/images/kanban/header.png') center center no-repeat;
    background-size: 100% 100%;
    .header-logo {
      border: none;
      outline: none;
      // margin-left: 1rem;
      background: url('~@/assets/images/kanban/top-logo.png') left center no-repeat;
      background-size: 100%;
      height: 63px;
      width: 153px;
      margin: 21px 0 0 -74px;
    }
    .title {
      flex: 1;
      text-align: center;
      line-height: 40px;
      height: 40px;
      font-size: 2.4rem;
      font-weight: bold;
      margin-top: 18px;
      background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .date {
      text-align: right;
      padding-right: 24px;
      /* stylelint-disable */
      font-family: PingFangSC-Medium;
      /* stylelint-enable */
      font-weight: 500;
      font-size: 0.9rem;
      color: #abc8ff;
      letter-spacing: 0;
      min-width: 210px;
    }
  }
  .query-box {
    position: absolute;
    color: #bfdaff;
    display: flex;
    width: 300px;
    align-items: center;
    .query-item {
      flex: 1;
      line-height: 28px;
      display: inline-block;
      cursor: pointer;
      user-select: none;
      color: #bfdaff;
      text-align: center;
    }
    .actived-item {
      color: #fff;
      position: relative;
      span {
        color: #fff;
      }
      &:after {
        content: '';
        position: absolute; /* 绝对定位，相对于最近的定位祖先元素（这里是container） */
        left: 50%; /* 水平居中 */
        bottom: 0; /* 紧贴box的底部 */
        margin-left: -2.5px; /* 由于三角形宽度为100px，所以这里需要偏移-50px来居中 */
        width: 0;
        height: 0;
        border-left: 2.5px solid transparent; /* 左边框透明 */
        border-right: 2.5px solid transparent; /* 右边框透明 */
        border-bottom: 5px solid #fff; /* 底边框设置三角形的高度和颜色 */
      }
    }
  }
  .header-left {
    right: unset;
    top: 51px;
    left: 131px;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(0, 100, 233, 0) 19%, rgba(79, 138, 255, 0.132) 99%);
    border: 0.5px solid #2570c4;
    .actived-item {
      color: #fff;
      background: linear-gradient(180deg, rgba(22, 108, 255, 0.8) 0%, rgba(89, 180, 254, 0.8) 100%);
      border-radius: 4px;
      span {
        color: #fff;
      }
      &:after {
        display: none;
      }
    }
  }
  .header-right {
    left: unset;
    right: 30px;
    top: 58px;
    width: 400px;
  }
  .content {
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    padding: 10px 24px 16px !important;
    .top {
      display: flex;
      flex: 1;
      height: 35%;
      .top-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        .top-card-area {
          display: flex;
          width: 100%;
          height: 200px;
          .card-item:first-child {
            border-left: unset;
          }
        }
        .top-chart-area {
          display: flex;
          width: 100%;
          height: calc(100% - 220px);
          margin: 10px 0;
          flex: 1;
          .pie-chart-box {
            width: 38.2%;
            background: #172d8c;
            padding: 12px 0;
            margin-right: 10px;
            height: 100%;
            display: flex;
            flex-direction: column;
            .sub-label-title {
              padding: 0 16px 0 24px;
            }
          }
          .bar-chart-box {
            flex: 1;
            background: #172d8c;
            padding: 12px 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            .sub-label-title {
              display: flex;
              justify-content: space-between;
              padding: 0 16px 0 24px;
              .part-icon::after {
                top: 9px;
              }
              .sub-label-title_select-area {
                display: flex;
                .query-rank-item {
                  color: rgba(255, 255, 255, 0.6);
                  cursor: pointer;
                  user-select: none;
                  white-space: nowrap;
                  margin-right: 8px;
                }
                .actived-rank-item {
                  color: #fff;
                  position: relative;
                  &:after {
                    content: '';
                    position: absolute; /* 绝对定位，相对于最近的定位祖先元素（这里是container） */
                    left: 50%; /* 水平居中 */
                    bottom: 0; /* 紧贴box的底部 */
                    margin-left: -2.5px; /* 由于三角形宽度为100px，所以这里需要偏移-50px来居中 */
                    width: 0;
                    height: 0;
                    border-left: 2.5px solid transparent; /* 左边框透明 */
                    border-right: 2.5px solid transparent; /* 右边框透明 */
                    border-bottom: 5px solid #fff; /* 底边框设置三角形的高度和颜色 */
                  }
                }
              }
            }
          }
        }
      }
      .top-right {
        width: 37%;
        height: calc(100% - 10px);
        background: #172d8c;
        margin: 0 0 10px 10px;
        padding: 12px 24px 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }
    }
    .bottom {
      width: 100%;
      height: calc(53% - 100px);
      display: flex;
      flex-direction: column;
      background: #172d8c;
      .sub-label-title {
        padding: 12px 16px 0 24px;
      }
      .bottom-content {
        display: flex;
        flex: 1;
        height: calc(100% - 52px);
        .bottom-left {
          flex: 1;
          height: 100%;
        }
        .bottom-center {
          flex: 1;
          height: 100%;
        }
        .bottom-right {
          width: 55%;
          height: 100%;
        }
        .chart-area {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .sub-label-title {
    // padding-left: 16px;
    height: 40px;
    line-height: 40px;
    // overflow: hidden;
    .part-icon {
      position: relative;
      /* stylelint-disable */
      font-family: PingFangSC-Medium;
      /* stylelint-enable */
      font-size: 16px;
      font-weight: 700;
      background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .part-icon::after {
      content: '';
      width: 200px;
      height: 20px;
      position: absolute;
      top: 0;
      right: -146px;
      background: url('~@/assets/images/kanban/titleicon.png');
      background-size: 100% 100%;
    }
    .part-2-icon {
      position: relative;
      /* stylelint-disable */
      font-family: PingFangSC-SNaNpxibold;
      /* stylelint-enable */
      font-weight: 600;
      font-size: 16px;
      color: #fff;
      letter-spacing: 0;
      background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .part-2-icon::before {
      content: '';
      width: 6px;
      height: 6px;
      position: absolute;
      top: 8px;
      left: -12px;
      background: #6393ab;
      border-radius: 50%;
    }
    .part-sub-label-title {
      position: relative;
      /* stylelint-disable */
      font-family: PingFangSC-SNaNpxibold;
      /* stylelint-enable */
      font-weight: 600;
      font-size: 15px;
      color: #fff;
      letter-spacing: 0;
      background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .part-sub-label-title::before {
      content: '';
      width: 6px;
      height: 6px;
      position: absolute;
      top: 8px;
      left: -12px;
      background: #6393ab;
      border-radius: 50%;
    }
  }
}
@media screen and (max-width: 1280px) {
  .purchase-kanban {
    .content {
      height: calc(100% - 50px);
    }
    .header {
      height: 50px !important;
      .title {
        margin-top: 4px;
      }
    }
  }
}
</style>
