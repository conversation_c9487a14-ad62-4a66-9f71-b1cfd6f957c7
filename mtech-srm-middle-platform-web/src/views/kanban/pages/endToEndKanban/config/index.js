import { i18n } from '@/main'
import dayjs from 'dayjs'
export const buList = [
  {
    label: i18n.t('泛智屏'),
    value: 'TV'
  },
  {
    label: i18n.t('空调'),
    value: 'KT'
  },
  {
    label: i18n.t('白电'),
    value: 'BD'
  },
  {
    label: i18n.t('手机'),
    value: 'PHONE'
  }
]
export const dateOptions = [
  {
    label: i18n.t('日'),
    value: 'day'
  },
  {
    label: i18n.t('月'),
    value: 'month'
  },
  {
    label: i18n.t('年'),
    value: 'year'
  },
  {
    label: i18n.t('自定义'),
    value: 'custom'
  }
]

export const orderColumns = [
  {
    field: 'site',
    title: '工厂',
    width: 50
  },
  {
    field: 'total',
    title: '总车位',
    width: 120
  },
  {
    field: 'available',
    title: '剩余可用车位',
    width: 80
  },
  {
    field: 'out',
    title: '已出车',
    width: 100
  },
  {
    field: 'checkIn',
    title: '已报到',
    width: 90
  },
  {
    field: 'park',
    title: '已入园',
    width: 90
  },
  {
    field: 'goaway',
    title: '已离园'
  }
]

export const getMonthList = () => {
  const curMonth = dayjs(new Date()).format('YYYY-MM')
  const monthList = [{ label: '当月', value: curMonth }]
  for (let i = 1; i < 4; i++) {
    monthList.push({
      label: `${dayjs()
        .add(i + 1, 'month')
        .format('MM')}月`,
      value: dayjs()
        .add(i + 1, 'month')
        .format('YYYY-MM')
    })
  }
  return monthList
}

export const getOrderList = (data) => {
  return [
    {
      title: '采购订单',
      orderInfo: [
        {
          label: '订单总数',
          value: data?.orderTotalCount || 0
        },
        {
          label: '未确认数',
          value: data?.orderConfirmingCount || 0
        },
        {
          label: '已完成数',
          value: data?.orderFinishedCount || 0
        }
      ]
    },
    {
      title: 'JIT计划',
      orderInfo: [
        {
          label: '订单总数',
          value: data?.jitTotalCount || 0
        },
        {
          label: '待履约数',
          value: data?.jitWaitingFeedbackCount || 0
        },
        {
          label: '已完成数',
          value: data?.jitFinishedCount || 0
        }
      ]
    },
    {
      title: '交货计划',
      orderInfo: [
        {
          label: '订单总数',
          value: data?.dpTotalCount || 0
        },
        {
          label: '待履约数',
          value: data?.dpWaitingCount || 0
        },
        {
          label: '已完成数',
          value: data?.dpFinishedCount || 0
        }
      ]
    }
  ]
}

const getRate = (number, totalCount) => {
  return (number / (totalCount ? totalCount : 1)).toFixed(3) * 1000
}

const getFinalRate = (data, totalCount) => {
  return (
    1000 -
    (getRate(data?.stockAchieveRateSatisfyCount, totalCount) +
      getRate(data?.stockAchieveRateLowCount, totalCount) +
      getRate(data?.stockAchieveRateMediumCount, totalCount))
  )
}

export const getPieData = (resData) => {
  const data = {
    stockAchieveRateSatisfyCount: resData?.stockAchieveRateSatisfyCount || 0,
    stockAchieveRateLowCount: resData?.stockAchieveRateLowCount || 0,
    stockAchieveRateMediumCount: resData?.stockAchieveRateMediumCount || 0,
    stockAchieveRateHighCount: resData?.stockAchieveRateHighCount || 0
  }
  const totalCount = Object.values(data).reduce(
    (accumulator, currentValue) => accumulator + currentValue,
    0
  )
  return [
    {
      value: getRate(data.stockAchieveRateSatisfyCount, totalCount) / 10 || 0,
      name: '无风险'
    },
    {
      value: getRate(data.stockAchieveRateLowCount, totalCount) / 10 || 0,
      name: '低风险'
    },
    {
      value: getRate(data.stockAchieveRateMediumCount, totalCount) / 10 || 0,
      name: '中风险'
    },
    {
      value: getFinalRate(data, totalCount) / 10 || 0,
      name: '高风险'
    }
  ]
}
