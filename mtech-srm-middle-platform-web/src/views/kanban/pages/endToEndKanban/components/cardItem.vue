<template>
  <div class="card-item">
    <div class="sub-title">
      <span class="title-text">{{ data.title }}</span>
      <img class="title-icon" src="~@/assets/images/kanban/carditem.png" />
    </div>
    <div class="card-content">
      <img class="card-content-logo" src="~@/assets/images/kanban/puricon.png" />
      <div class="card-content-area">
        <div class="card-content-item" v-for="(item, index) in data.orderInfo" :key="index">
          <div class="card-content-item_title">{{ item.label }}</div>
          <div class="card-content-item_number">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style lang="scss">
.card-item {
  background: #172d8c;
  width: 100%;
  border-left: 1px solid #142478;
  .sub-title {
    padding: 12px 0 0 24px;
    line-height: normal;
    .title-text {
      line-height: normal;
      text-align: right;
      letter-spacing: 0px;
      font-family: PingFangSC-Medium;
      /* stylelint-enable */
      font-size: 1rem;
      font-weight: 700;
      background-image: -webkit-linear-gradient(bottom, #6eb0f4, #b9dbff, #f4f5ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      // text-shadow: 0px 2px 4px #07216b;
    }
    .title-icon {
      width: 24px;
      margin-left: 2.6px;
    }
  }
  .card-content {
    padding: 2rem 0 2rem 2rem;
    display: flex;
    align-items: center;
    .card-content-logo {
      display: block;
      width: 4rem;
    }
    .card-content-area {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding-left: 3rem;
      height: 95px;
      .card-content-item {
        display: flex;
        align-items: flex-end;
        .card-content-item_title {
          font-family: PingFang SC;
          font-size: 15px;
          font-weight: 600;
          line-height: normal;
          letter-spacing: 0px;

          color: #9fe4ff;
        }
        .card-content-item_number {
          padding: 0 2px 0 8px;
          font-style: italic;

          font-family: Arial;
          font-size: 22px;
          font-weight: bold;
          line-height: 22px;
          letter-spacing: 0px;

          background: linear-gradient(180deg, #95e2ee 30%, #29f2f8 82%), #ffffff;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
        }
      }
    }
  }
}
</style>
