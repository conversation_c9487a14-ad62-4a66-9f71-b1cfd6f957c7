<!-- 折线图 -->
<template>
  <div ref="chartsDom" :style="{ height, width }" @resize="handleResize"></div>
</template>

<script>
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
export default {
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    dataSource: {
      type: Array,
      default: () => [
        [
          {
            value: 2,
            name: '2-4'
          },
          {
            value: 4,
            name: '4-6'
          },
          {
            value: 5,
            name: '6-8'
          },
          {
            value: 6,
            name: '8-10'
          },
          {
            value: 5,
            name: '10-12'
          },
          {
            value: 4,
            name: '12-14'
          },
          {
            value: 2,
            name: '14-16'
          },
          {
            value: 2,
            name: '16-18'
          },
          {
            value: 3,
            name: '18-20'
          },
          {
            value: 2,
            name: '22-24'
          }
        ],
        [
          {
            value: 3,
            name: '2-4'
          },
          {
            value: 5,
            name: '4-6'
          },
          {
            value: 6,
            name: '6-8'
          },
          {
            value: 7,
            name: '8-10'
          },
          {
            value: 6,
            name: '10-12'
          },
          {
            value: 3,
            name: '12-14'
          },
          {
            value: 4,
            name: '14-16'
          },
          {
            value: 3,
            name: '16-18'
          },
          {
            value: 4,
            name: '18-20'
          },
          {
            value: 3,
            name: '22-24'
          }
        ],
        [
          {
            value: 8,
            name: '2-4'
          },
          {
            value: 9,
            name: '4-6'
          },
          {
            value: 10,
            name: '6-8'
          },
          {
            value: 9,
            name: '8-10'
          },
          {
            value: 8,
            name: '10-12'
          },
          {
            value: 7,
            name: '12-14'
          },
          {
            value: 6,
            name: '14-16'
          },
          {
            value: 5,
            name: '16-18'
          },
          {
            value: 6,
            name: '18-20'
          },
          {
            value: 4,
            name: '22-24'
          }
        ]
      ]
    }
  },
  data() {
    return {
      myChart: {}
    }
  },
  computed: {
    screenWidth() {
      return document.body.clientWidth
    }
  },
  watch: {
    screenWidth: {
      handler() {
        this.handleResize()
      },
      deep: true
    },
    dataSource: {
      handler() {
        this.initCharts()
      },
      deep: true
    }
  },
  activated() {
    this.handleResize()
  },
  mounted() {
    this.initCharts()
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize()
    },
    initCharts() {
      const xAxisData = []
      const lineDataOne = []
      const lineDataTwo = []
      const lineDataThree = []
      this.dataSource.forEach((item, index) => {
        item.forEach((i) => {
          if (index === 0) {
            xAxisData.push(i.name)
            lineDataOne.push(i.value)
          } else if (index === 1) {
            lineDataTwo.push(i.value)
          } else {
            lineDataThree.push(i.value)
          }
        })
        // lineDataOne.push(
        //   lineDataOne[lineDataOne.length - 1]
        //     ? lineDataOne[lineDataOne.length - 1] - 1
        //     : lineDataOne[lineDataOne.length - 1]
        // )
        // lineDataTwo.push(
        //   lineDataTwo[lineDataTwo.length - 1]
        //     ? lineDataTwo[lineDataTwo.length - 1] - 1
        //     : lineDataTwo[lineDataTwo.length - 1]
        // )
        // lineDataThree.push(
        //   lineDataThree[lineDataThree.length - 1]
        //     ? lineDataThree[lineDataThree.length - 1] - 1
        //     : lineDataThree[lineDataThree.length - 1]
        // )
      })
      const arrLength = this.dataSource[0]?.length || 0
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(20, 36, 120, 0.9)',
          borderColor: 'rgba(20, 36, 120, 0.9)',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 12,
            color: '#fff'
          },
          formatter: function (params) {
            // 自定义格式化 tooltip 的内容
            // 这里可以根据 params 中的数据决定是否显示 tooltip
            if (params[0].dataIndex === 0 || params[0].dataIndex > arrLength) {
              return '' // 返回 false 表示不显示 tooltip
            }
            let tooltipContent = ''
            params.forEach((item) => {
              const { marker, seriesName, value } = item
              tooltipContent += marker + seriesName + ': ' + value + '<br>'
            })
            return tooltipContent
          }
          // axisPointer: {
          //   lineStyle: {
          //     color: '#F1F1F3'
          //   }
          // }
        },
        legend: {
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: ['已出车', '已报到', '已入园'],
          right: '4%',
          textStyle: {
            fontSize: 12,
            color: '#F1F1F3'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            axisLabel: {
              color: '#c0c3cd',
              fontSize: 14,
              interval: 0
            },
            type: 'category',
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: '#384267'
              }
            },
            data: ['', ...xAxisData, 'H']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#9FE4FF'
              }
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                color: '#c0c3cd',
                fontSize: 14,
                interval: 0
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 163, 233, 0.2)'
              }
            }
          }
        ],
        series: [
          {
            name: '已出车',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                width: 1
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(255, 201, 0, 0.1589)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(255, 201, 0, 0)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#FFC900'
                // borderColor: '#fff',
                // borderWidth: 1
              }
            },
            data: [
              lineDataOne[0] ? lineDataOne[0] - 1 : lineDataOne[0],
              ...lineDataOne,
              lineDataOne[lineDataOne.length - 1]
                ? lineDataOne[lineDataOne.length - 1] - 1
                : lineDataOne[lineDataOne.length - 1]
            ]
          },
          {
            name: '已报到',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                width: 1
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 169, 255, 0.3189)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(0, 169, 255, 0)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#54CCFE'
                // borderColor: '#fff',
                // borderWidth: 1
              }
            },
            data: [
              lineDataTwo[0] ? lineDataTwo[0] - 1 : lineDataTwo[0],
              ...lineDataTwo,
              lineDataTwo[lineDataTwo.length - 1]
                ? lineDataTwo[lineDataTwo.length - 1] - 1
                : lineDataTwo[lineDataTwo.length - 1]
            ]
          },
          {
            name: '已入园',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                width: 1
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 255, 160, 0.2247)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(0, 255, 160, 0)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#00FFA0'
                // borderColor: '#fff',
                // borderWidth: 1
              }
            },
            data: [
              lineDataThree[0] ? lineDataThree[0] - 1 : lineDataThree[0],
              ...lineDataThree,
              lineDataThree[lineDataThree.length - 1]
                ? lineDataThree[lineDataThree.length - 1] - 1
                : lineDataThree[lineDataThree.length - 1]
            ]
          }
        ]
      }
      const setOptionsObj = cloneDeep(option)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>
