<!-- 饼图 -->
<template>
  <div ref="chartsDom" :style="{ height, width }" @resize="handleResize"></div>
</template>

<script>
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
export default {
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    dataSource: {
      type: Array,
      default: () => [
        {
          value: 40,
          name: '无风险'
        },
        {
          value: 30,
          name: '低风险'
        },
        {
          value: 20,
          name: '中风险'
        },
        {
          value: 10,
          name: '高风险'
        }
      ] // 默认数据
    }
  },
  computed: {
    screenWidth() {
      return document.body.clientWidth
    }
  },
  watch: {
    screenWidth: {
      handler() {
        this.handleResize()
      },
      deep: true
    },
    dataSource: {
      handler() {
        this.initCharts()
      },
      deep: true
    }
  },
  data() {
    return {
      myChart: {}
    }
  },
  mounted() {
    this.initCharts()
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize()
      this.myChart &&
        this.myChart.setOption({
          width: '100%',
          height: '100%'
        })
    },
    initCharts() {
      const chartData = cloneDeep(this.dataSource)
      const chartOption = {
        tooltip: {
          trigger: 'item'
        },
        color: [
          //按顺序设置渐变颜色
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#BDF2FF'
              },
              {
                offset: 1,
                color: '#88E2FE'
              }
            ],
            global: false // 缺省为 false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#166CFF'
              },
              {
                offset: 1,
                color: '#59B4FE'
              }
            ],
            global: false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#FFD500'
              },
              {
                offset: 1,
                color: '#FFAB00'
              }
            ],
            global: false
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#FA1212'
              },
              {
                offset: 1,
                color: '#FFA46A'
              }
            ],
            global: false
          }
        ],
        legend: {
          // orient: 'vertical',
          // top: 'middle',
          // right: '4%',
          icon: 'circle',
          data: chartData,
          textStyle: {
            color: '#fff',
            fontSize: 16
          },
          itemStyle: {
            borderWidth: 0
          },
          formatter: (name) => {
            const value = chartData.filter((i) => i.name === name)[0]['value']
            return `${name.split(' ')[0]}  ${value}%`
          }
        },
        series: [
          {
            name: '备货达成率',
            type: 'pie',
            radius: ['65%', '85%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#172d8c',
              borderWidth: 10
            },
            label: {
              normal: {
                show: false,
                //formatter: '{b}\n{d}%',
                textStyle: {
                  fontSize: '16',
                  color: '#fff'
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '16',
                  color: '#fff'
                }
              }
            },
            labelLine: {
              normal: {
                show: true
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 24,
                fontWeight: 'bold',
                color: '#fff'
              }
            },
            data: chartData
          },
          // Inner circle for the glowing effect
          {
            name: '',
            type: 'pie',
            radius: ['55%', '56%'],
            center: ['50%', '50%'],
            silent: true,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#172d8c',
              borderWidth: 0
            },
            label: {
              show: false
            },
            data: [
              {
                value: 2.5,
                itemStyle: { color: 'transparent' }
              },
              {
                value: 20,
                itemStyle: { color: '#4fc3f7' }
              },
              {
                value: 5,
                itemStyle: { color: 'transparent' }
              },
              {
                value: 20,
                itemStyle: { color: '#4fc3f7' }
              },
              {
                value: 5,
                itemStyle: { color: 'transparent' }
              },
              {
                value: 20,
                itemStyle: { color: '#4fc3f7' }
              },
              {
                value: 5,
                itemStyle: { color: 'transparent' }
              },
              {
                value: 20,
                itemStyle: { color: '#4fc3f7' }
              },
              {
                value: 2.5,
                itemStyle: { color: 'transparent' }
              }
            ],
            animation: false
          }
        ],
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '备货\n\n达成率',
              textAlign: 'center',
              fill: '#B9DBFF',
              fontSize: 26,
              fontWeight: 'bold'
            }
          }
        ]
      }
      const setOptionsObj = cloneDeep(chartOption)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>
