<template>
  <div id="sc-select" ref="scSelect" class="sc-select">
    <div class="sc-select-title" :style="{ width, height }" @click="showOption">
      <span v-if="!curLabel" class="placeholder">{{ placeholder }}</span>
      <span v-else>{{ curLabel }}</span>
      <!-- <span :class="['arrow-icon', { 'arrow-down': isShow }]">&lt;</span> -->
      <img
        src="~@/assets/images/kanban/arrow.png"
        :class="['arrow-icon', { 'arrow-down': isShow }]"
      />
      <span class="clear-icon" @click="clickItem()">X</span>
    </div>
    <div class="sc-option" :style="{ width: optionWidth }" v-show="isShow">
      <div
        v-for="item in options"
        class="sc-select-content"
        @click="clickItem(item)"
        :key="item.value"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    placeholder: {
      type: String,
      default: '请选择'
    },
    value: {
      // 接收父组件传入的value，名字必须value，这样父组件才能用v-model绑定
      type: [Number, String],
      default: ''
    },
    width: {
      // 宽度
      type: String,
      default: '100%'
    },
    optionWidth: {
      // 宽度
      type: String,
      default: '200px'
    },
    height: {
      // 宽度
      type: String,
      default: '32px'
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isShow: false, // 是否下拉
      curLabel: '', // 当前选中的文字
      curValue: '' // 当前选中的value
    }
  },
  mounted() {
    // 与父组件传入的value对比，确定当前选中的curvalue
    let curOption = this.options.filter((item) => item.value === this.value)
    if (curOption.length > 0) {
      this.curLabel = curOption[0].label
      this.curValue = curOption[0].value
    }
    document.addEventListener('click', this.handleClickOutside, true)
  },
  methods: {
    handleClickOutside(event) {
      const divToHide = document.getElementById('sc-select')
      if (divToHide && !divToHide.contains(event.target)) {
        this.isShow = false
      }
    },
    clickItem(item) {
      if (!item) {
        this.curValue = ''
        this.curLabel = ''
        return
      }
      // 改变select中当前选中的元素
      this.curValue = item.value
      this.curLabel = item.label
      // 调用select中的方法，收起下拉选框
      this.showOption()
    },
    showOption() {
      // 点击下拉按钮，收起/展开options
      this.isShow = !this.isShow
    }
  },
  watch: {
    // 当前选中项变化，触发input事件，父元素既可以使用v-model监听
    curValue() {
      this.$emit('input', this.curValue)
      this.$emit('change', this.curValue, { label: this.curLabel, value: this.curValue })
    },
    value(newValue) {
      // 父组件传入的value变化，重新赋值
      if (!newValue) {
        this.clickItem()
      }
    }
  },
  destroyed() {
    if (process.browser) {
      document.removeEventListener('click', this.handleClickOutside, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.sc-select {
  width: 100%;
  color: #bfdaff;
  display: inline-block;
  cursor: pointer;
  position: relative;
}
.arrow-icon {
  width: 10px;
  transform: rotate(90deg);
  cursor: pointer;
  user-select: none;
}
.arrow-down {
  transform: rotate(0deg);
}
.clear-icon {
  display: none;
  user-select: none;
  color: #3256bd;
}
.sc-select-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 6px;
  border: 1px solid #3256bd;
  color: #bfdaff;
  margin-bottom: 4px;
  border-radius: 4px;
  &:hover {
    .arrow-icon {
      display: none;
    }
    .clear-icon {
      display: inline-block;
    }
  }
}
.sc-select-title span {
  /* 一行显示，超长省略号 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.placeholder {
  color: rgba(255, 255, 255, 0.6);
  user-select: none;
}
.sc-option {
  background: rgba(14, 28, 102, 0.95);
  padding: 2px 0;
  border-radius: 2px;
  z-index: 99999;
  position: absolute;
}
.sc-select-content {
  color: #bfdaff;
  // border-right: 1px solid rgba(14, 28, 102, 0.95);
  // border-left: 1px solid rgba(14, 28, 102, 0.95);
  // border-bottom: 1px solid rgba(14, 28, 102, 0.95);
  padding: 5px 0 5px 8px;
  text-align: left;
  cursor: pointer;
  user-select: none;
  /* 一行显示，超长省略号 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  &:hover {
    background: rgba(45, 73, 169, 0.5);
  }
}
</style>
