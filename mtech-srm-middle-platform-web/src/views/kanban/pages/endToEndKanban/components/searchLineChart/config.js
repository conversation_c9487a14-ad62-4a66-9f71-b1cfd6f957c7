import { i18n } from '@/main'
export const formSchema = [
  {
    field: 'businessUnitCode',
    label: '基地',
    fields: { text: 'label', value: 'value' },
    dataSource: 'buData',
    // filtering: 'remoteSupplierList',
    placeholder: i18n.t('请选择基地')
  },
  {
    field: 'purchaseGroupCode',
    fieldName: 'purchaseGroup',
    label: '采购组',
    fields: { text: 'label', value: 'value' },
    dataSource: 'purchaseGroupData',
    filtering: 'remotePurchaseGroupList',
    placeholder: i18n.t('请选择采购组')
  },
  {
    field: 'supplierCode',
    fieldName: 'supplier',
    label: '供应商',
    fields: { text: 'label', value: 'value' },
    dataSource: 'supplierData',
    filtering: 'remoteSupplierList',
    placeholder: i18n.t('请选择供应商')
  },
  {
    field: 'materialGroupCode',
    fieldName: 'category',
    label: '物料组名称',
    fields: { text: 'label', value: 'value' },
    dataSource: 'materialGroupData',
    filtering: 'remoteMaterialGroupList',
    placeholder: i18n.t('请选择物料组名称')
  }
]

export const buData = [
  {
    label: i18n.t('中山基地'),
    value: '中山基地'
  },
  {
    label: i18n.t('万颗子基地'),
    value: '万颗子基地'
  },
  {
    label: i18n.t('武汉基地'),
    value: '武汉基地'
  },
  {
    label: i18n.t('九江基地'),
    value: '九江基地'
  }
]
