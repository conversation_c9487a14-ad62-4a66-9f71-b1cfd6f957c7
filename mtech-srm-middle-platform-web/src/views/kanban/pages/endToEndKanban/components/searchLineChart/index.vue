<!-- 折线图 -->
<template>
  <div class="search-line-area" :style="{ height, width }">
    <div class="search-area">
      <div v-for="item in formSchema" class="search-item" :key="item.field">
        <span class="search-item-label">{{ item.label }}</span>
        <!-- <mt-select
          v-if="item.filtering"
          v-model="seachModel[item.field]"
          :data-source="getFiltering(item.dataSource)"
          :show-clear-button="true"
          :allow-filtering="true"
          :filtering="getFiltering(item.filtering)"
          filter-type="Contains"
          :fields="item.fields"
          :enable-group-check-box="true"
          popup-width="500px"
          mode="CheckBox"
          :placeholder="item.placeholder"
        ></mt-select> -->
        <RemoteAutocomplete
          v-if="item.filtering"
          v-model="seachModel[item.field]"
          url="/contract/tenant/app/integratedBoard/querySelectValue"
          :placeholder="$t('请选择')"
          :params="{ fieldName: item.fieldName, pageCount: 1, pageSize: 20 }"
          paramsKey="fuzzyValue"
          popup-width="300px"
        ></RemoteAutocomplete>
        <mt-select
          v-else
          v-model="seachModel[item.field]"
          :data-source="getFiltering(item.dataSource)"
          :show-clear-button="true"
          :allow-filtering="true"
          :fields="item.fields"
          :enable-group-check-box="true"
          filter-type="Contains"
          popup-width="300px"
          mode="CheckBox"
          :placeholder="item.placeholder"
        ></mt-select>
        <!-- <ScSelect v-model="seachModel[item.field]" :options="item.options" /> -->
      </div>
      <div class="button-group">
        <span class="search-btn" @click="handleSearch">查询</span>
        <span style="margin-left: 8px" @click="handleReset">重置</span>
      </div>
    </div>
    <div ref="chartsDom" :style="{ height, width, flex: 1 }" @resize="handleResize"></div>
  </div>
</template>

<script>
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
// import { debounce } from '@/utils/utils'
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
import { formSchema, buData } from './config'
export default {
  components: {
    RemoteAutocomplete
  },
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      myChart: {},
      seachModel: {
        businessUnitCode: '中山基地',
        purchaseGroupCode: '陈志豪1'
      },
      dataSource: [],
      purchaseGroupData: [
        {
          label: '陈志豪1',
          value: '陈志豪1'
        }
      ],
      supplierData: [],
      materialGroupData: [],
      buData,
      formSchema
    }
  },
  computed: {
    screenWidth() {
      return document.body.clientWidth
    }
  },
  watch: {
    screenWidth: {
      handler() {
        this.handleResize()
      },
      deep: true
    }
  },
  activated() {
    this.handleResize()
  },
  created() {
    // this.remoteSupplierList = debounce(
    //   (e) => this.getRemoteList(e, 'supplier', 'supplierData'),
    //   300
    // )
    // this.getRemoteList('', 'supplier', 'supplierData')
    // this.remotePurchaseGroupList = debounce(
    //   (e) => this.getRemoteList(e, 'purchaseGroup', 'purchaseGroupData'),
    //   300
    // )
    // this.getRemoteList('', 'purchaseGroup', 'purchaseGroupData')
    // this.remoteMaterialGroupList = debounce(
    //   (e) => this.getRemoteList(e, 'category', 'materialGroupData'),
    //   300
    // )
    // this.getRemoteList('', 'category', 'materialGroupData')
  },
  mounted() {
    this.handleSearch()
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    getFiltering(data) {
      return this[data]
    },
    // 获取供应商列表数据
    getRemoteList(e, fieldName, dataSource) {
      this.$api.kanban
        .querySelectValue({ fuzzyValue: e.text, fieldName, pageCount: 3, pageSize: 20 })
        .then((res) => {
          if (res.code === 200) {
            this[dataSource] = res.data?.records?.map((item) => {
              return {
                value: item,
                label: item
              }
            })
          }
        })
    },
    handleSearch() {
      if (!this.seachModel.businessUnitCode) {
        this.$toast({
          content: this.$t('基地必填'),
          type: 'warning'
        })
        return false
      }
      if (!this.seachModel.purchaseGroupCode) {
        this.$toast({
          content: this.$t('采购组必填'),
          type: 'warning'
        })
        return false
      }
      this.$api.kanban.queryKanbanDeliveryRateData(this.seachModel).then((res) => {
        const { code, data } = res
        if (code === 200) {
          const deliveryRollingRateMap = data.deliveryRollingRateMap
          this.dataSource = Object.keys(deliveryRollingRateMap)
            .sort()
            .map((i) => {
              return {
                name: i,
                key: isNaN(deliveryRollingRateMap[i]['key'])
                  ? '0'
                  : String((deliveryRollingRateMap[i]['key'] * 1000) / 10),
                value: isNaN(deliveryRollingRateMap[i]['value'])
                  ? '0'
                  : String((deliveryRollingRateMap[i]['value'] * 1000) / 10),
                markLine: data.wholeTrendBaseValue
                  ? String((data.wholeTrendBaseValue * 1000) / 10)
                  : '0'
              }
            })
          this.$emit('handleSearch', data)
          this.initCharts()
        }
      })
    },
    handleReset() {
      Object.keys(this.seachModel).forEach((key) => {
        this.seachModel[key] = null
      })
      this.seachModel['businessUnitCode'] = '中山基地'
      this.seachModel['purchaseGroupCode'] = '陈志豪1'
    },
    handleResize() {
      this.myChart && this.myChart.resize()
    },
    initCharts() {
      const xAxisData = []
      const lineDataOne = []
      const lineDataTwo = []
      const mrekLineData = []
      this.dataSource.forEach((item) => {
        xAxisData.push(item.name)
        lineDataOne.push(item.key)
        lineDataTwo.push(item.value)
        mrekLineData.push(item.markLine)
      })
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(20, 36, 120, 0.9)',
          borderColor: 'rgba(20, 36, 120, 0.9)',
          textStyle: {
            fontWeight: 'normal',
            fontSize: 12,
            color: '#fff'
          },
          formatter: function (params) {
            // 自定义格式化 tooltip 的内容
            // 这里可以根据 params 中的数据决定是否显示 tooltip
            if (params[0].dataIndex === 0 || params[0].dataIndex > xAxisData.length) {
              return '' // 返回 false 表示不显示 tooltip
            }
            let tooltipContent = ''
            params.forEach((item) => {
              const { marker, seriesName, value } = item
              tooltipContent += marker + seriesName + ': ' + value + '<br>'
            })
            return tooltipContent
          }
        },
        legend: {
          // icon: 'circle',
          itemWidth: 14,
          itemHeight: 6,
          itemGap: 13,
          data: [
            { name: '今年', icon: 'circle' },
            { name: '去年', icon: 'circle' },
            { name: '目标线', icon: 'roundRect' }
          ],
          right: '4%',
          textStyle: {
            fontSize: 12,
            color: '#F1F1F3'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            axisLabel: {
              color: '#c0c3cd',
              fontSize: 14,
              interval: 0,
              rotate: 20,
              margin: 15
            },
            type: 'category',
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: '#384267'
              }
            },
            data: ['', ...xAxisData, '']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '%        ',
            nameTextStyle: {
              // 调整单位文字样式
              color: '#fff'
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#9FE4FF'
              }
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                color: '#c0c3cd',
                fontSize: 14,
                interval: 0
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 163, 233, 0.2)'
              }
            }
          }
        ],
        series: [
          {
            name: '今年',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: true,
            lineStyle: {
              normal: {
                width: 1,
                // shadowColor: hexToRgba('#000', 0.5),
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 1,
                shadowOffsetY: 7
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(255, 201, 0, 0.1589)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(255, 201, 0, 0)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#FFC900',
                borderColor: '#fff', // 拐点边框颜色
                borderWidth: 1 // 拐点边框大小
              }
            },
            data: [
              lineDataOne[0] && lineDataOne[0] > 1 ? lineDataOne[0] - 1 : lineDataOne[0],
              ...lineDataOne,
              lineDataOne[lineDataOne.length - 1] && lineDataOne[lineDataOne.length - 1] > 1
                ? lineDataOne[lineDataOne.length - 1] - 1
                : lineDataOne[lineDataOne.length - 1]
            ]
          },
          {
            name: '去年',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: true,
            lineStyle: {
              normal: {
                width: 1,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 1,
                shadowOffsetY: 7
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 169, 255, 0.3189)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(0, 169, 255, 0)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#54CCFE',
                borderColor: '#fff', // 拐点边框颜色
                borderWidth: 1 // 拐点边框大小
                // borderColor: '#fff',
                // borderWidth: 1
              }
            },
            data: [
              lineDataTwo[0] && lineDataTwo[0] > 1 ? lineDataTwo[0] - 1 : lineDataTwo[0],
              ...lineDataTwo,
              lineDataTwo[lineDataTwo.length - 1] && lineDataTwo[lineDataTwo.length - 1] > 1
                ? lineDataTwo[lineDataTwo.length - 1] - 1
                : lineDataTwo[lineDataTwo.length - 1]
            ]
          },
          {
            name: '目标线',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                width: 1,
                type: 'dashed'
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 255, 160, 0.2247)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(13, 13, 22, 0.0139)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#00FFA0 '
                // borderColor: '#fff', // 拐点边框颜色
                // borderWidth: 1 // 拐点边框大小
                // borderColor: '#fff',
                // borderWidth: 1
              }
            },
            data: [mrekLineData[0], ...mrekLineData, mrekLineData[0]]
          }
        ]
      }
      const setOptionsObj = cloneDeep(option)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-line-area {
  display: flex;
  flex-direction: column;
  .search-area {
    display: flex;
    justify-content: space-between;
    padding-bottom: 24px;
    .search-item {
      display: flex;
      align-items: center;
      width: 100%;
      padding-right: 30px;
      ::v-deep.mt-select {
        .e-input-group.e-control-wrapper:not(.e-success):not(.e-warning):not(.e-error):not(.e-float-icon-left) {
          border: 1px solid #3256bd;
          border-radius: 4px;
          color: #bfdaff;
          padding-left: 3px;
        }
        .e-control,
        .e-dropdownlist,
        .e-lib,
        .e-input::placeholder {
          color: rgba(255, 255, 255, 0.6);
          padding-left: 3px;
        }
        .e-control,
        .e-dropdownlist,
        .e-lib,
        .e-input {
          color: #bfdaff;
          padding-left: 3px;
        }
        .e-ddl.e-input-group.e-control-wrapper .e-ddl-icon::before {
          color: #3256bd;
        }
      }
      .search-item-label {
        color: #fff;
        font-size: 14px;
        white-space: nowrap;
        margin-right: 8px;
      }
    }
    .button-group {
      display: flex;
      span {
        padding: 9px 16px;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #3256bd;
        user-select: none;
        cursor: pointer;
        font-size: 14px;
        color: #9ce4ff;
        font-family: PingFang SC;
        font-weight: normal;
        white-space: nowrap;
      }
      .search-btn {
        background: linear-gradient(
          180deg,
          rgba(85, 132, 255, 0.4414) 0%,
          rgba(104, 147, 255, 0) 99%
        );
      }
    }
  }
}
</style>
