<template>
  <div>
    <ScTable
      ref="xTable"
      :columns="columns"
      :table-data="tableData"
      :is-show-toolbar="false"
      :height="height"
      header-align="left"
      align="left"
      :row-config="{ height: 36 }"
      :column-config="{ resizable: true }"
      :scroll-y="{ enabled: false, gt: 0, oSize: 1000 }"
    >
    </ScTable>
  </div>
</template>
<script>
import ScTable from '@/components/ScTable/src/index'
export default {
  components: {
    ScTable
  },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '150px'
    }
  },
  // watch: {
  //   tableData: {
  //     handler(val) {
  //       if (val) {
  //         this.setScroll()
  //       }
  //     }
  //   }
  // },
  data() {
    return {
      // animation: null,
      // delay: 1.5 * 60 * 1000
    }
  },
  computed: {},
  mounted() {},
  methods: {
    // setScroll() {
    //   this.$nextTick(() => {
    //     const ele = this.$refs.xTable
    //     const tableBody = ele.$el.querySelector('.vxe-table--body-wrapper .vxe-table--body')
    //     if (!this.tableData?.length) return
    //     const duration = 1500 * this.tableData?.length // 动画时长 一行1.ss
    //     this.animation = tableBody.animate(
    //       [{ transform: 'translateY(0px)' }, { transform: 'translateY(-100%)' }],
    //       {
    //         duration: duration,
    //         iterations: Infinity
    //       }
    //     )
    //   })
    // }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .vex-table-contianer {
    background: #172d8c !important;
    .vxe-table--render-default .vxe-table--header .vxe-header--row {
      background: rgb(16, 39, 136);
      color: rgb(159, 228, 255);
      margin-bottom: 4px;
      .vxe-header--column {
        border: none;
        .vxe-resizable.is--line:before {
          background: none;
        }
      }
    }
    .vxe-table--render-default .vxe-table--body-wrapper {
      background: #172d8c;
      border-color: #172d8c;
    }
    .vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line {
      border-color: #172d8c;
    }
    .vxe-table--render-default .vxe-table--border-line {
      border-color: #172d8c;
    }
    .vxe-table--empty-content {
      color: rgb(159, 228, 255);
    }
    .vxe-body--row {
      margin-bottom: 4px !important;
    }
    .vxe-body--column {
      background: rgb(30, 52, 154) !important;
      color: rgb(159, 228, 255) !important;
      border-left: none !important;
      border-bottom: 10px solid #172d8c !important;
    }
    .vxe-cell {
      height: 42px;
      max-height: 42px;
      line-height: 42px;
    }
    .vxe-header--gutter {
      background: rgb(16, 39, 136) !important;
      border-left: none;
    }
    ::-webkit-scrollbar {
      width: 8px !important;
    }
    ::-webkit-scrollbar-thumb {
      background: rgb(16, 39, 136) !important;
    }
    ::-webkit-scrollbar-track {
      background: #172d8c !important;
    }
  }
}
</style>
