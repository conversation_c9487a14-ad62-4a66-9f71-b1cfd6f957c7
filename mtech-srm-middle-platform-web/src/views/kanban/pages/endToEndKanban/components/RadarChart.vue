<!-- 雷达图 -->
<template>
  <div ref="chartsDom" :style="{ height: '100%', width: '100%' }" @resize="handleResize"></div>
</template>

<script>
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
export default {
  props: {
    indicator: {
      type: Array,
      default: () => {}
    },
    radarData: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      myChart: {},
      option: {
        grid: {
          containLabel: true,
          left: 40,
          right: 40,
          bottom: 10,
          top: 40
        },
        textStyle: {
          color: 'rgba(159, 228, 255, 1)'
        },
        radar: {
          // shape: 'circle',
          indicator: [
            { name: '交货', max: 6500 },
            { name: '入库', max: 16000 },
            { name: '质检', max: 30000 },
            { name: '采购订单', max: 38000 },
            { name: '送货', max: 52000 },
            { name: '叫料', max: 25000 }
          ],
          splitLine: {
            lineStyle: {
              color: 'rgb(159 228 255 / 20%)'
            }
          },
          splitArea: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: 'rgb(159 228 255 / 20%)'
            }
          }
        },
        series: [
          {
            type: 'radar',
            tooltip: {
              trigger: 'item'
            },
            symbol: 'none',
            color: '#21E7FF',
            lineStyle: {
              color: '#21E7FF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(33, 231, 255, 0.5)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(33, 231, 255, 0)'
                  }
                ],
                global: false
              }
            },
            data: [
              {
                value: [5500, 13000, 28000, 35000, 50000, 22000]
              }
            ]
          }
        ]
      }
    }
  },
  mounted() {
    this.initCharts()
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize()
    },
    initCharts() {
      // this.option.radar.indicator = this.indicator
      // this.option.series[0].data = this.radarData
      const setOptionsObj = cloneDeep(this.option)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>
