<!-- 柱状图 -->
<template>
  <div ref="chartsDom" :style="{ height, width }" @resize="handleResize"></div>
</template>

<script>
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
export default {
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    dataSource: {
      type: Array,
      default: () => [
        {
          value: 100,
          name: '类别1'
        },
        {
          value: 30.56,
          name: '类别2'
        },
        {
          value: 87.87,
          name: '类别3'
        },
        {
          value: 56.45,
          name: '类别4'
        },
        {
          value: 45.65,
          name: '类别5'
        },
        {
          value: 20.76,
          name: '类别6'
        },
        {
          value: 26.45,
          name: '类别7'
        },
        {
          value: 90.09,
          name: '类别8'
        },
        {
          value: 45.76,
          name: '类别9'
        },
        {
          value: 56.76,
          name: '类别10'
        }
      ] // 默认数据
    }
  },
  computed: {
    screenWidth() {
      return document.body.clientWidth
    }
  },
  data() {
    return {
      myChart: {}
    }
  },
  watch: {
    screenWidth: {
      handler() {
        this.handleResize()
      },
      deep: true
    },
    seriesData: {
      handler() {
        this.initCharts()
      },
      deep: true
    }
  },
  mounted() {
    this.initCharts()
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize()
    },
    initCharts() {
      const xAxisData = []
      const seriesData = []
      const seriesBottomData = []
      this.dataSource.forEach((i) => {
        xAxisData.push(i.name)
        seriesData.push(i.value)
        seriesBottomData.push(1)
      })
      const option = {
        color: ['#63caff', '#205DFF'],
        grid: {
          containLabel: true,
          left: 20,
          right: 20,
          bottom: 10,
          top: 40
        },
        xAxis: {
          axisLabel: {
            color: '#c0c3cd',
            fontSize: 14,
            interval: 0,
            rotate: 20,
            margin: 15
          },
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#384267',
              width: 1,
              type: 'dashed'
            },
            show: true
          },
          data: xAxisData,
          type: 'category'
        },
        yAxis: {
          axisLabel: {
            color: '#c0c3cd',
            fontSize: 14,
            margin: 10
          },

          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 163, 233, 0.2)',
              type: 'solid'
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 163, 233, 0.2)',
              width: 1,
              type: 'solid'
            },
            show: false
          },
          name: '%        ',
          nameTextStyle: {
            // 调整单位文字样式
            color: '#fff'
          }
        },
        series: [
          {
            data: seriesData,
            type: 'bar',
            barWidth: 30,
            itemStyle: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [
                  {
                    offset: 0,
                    color: '#166CFF'
                  },
                  {
                    offset: 1,
                    color: '#59F0FE'
                  }
                ]
              }
            },
            label: {
              show: true,
              position: 'top',
              distance: 10,
              color: '#fff'
            }
          },
          {
            data: seriesBottomData,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbol: 'diamond',
            symbolOffset: [0, '50%'],
            symbolSize: [30, 15]
          },
          {
            data: seriesData,
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [30, 12],
            zlevel: 2
          }
        ],
        tooltip: {
          trigger: 'item',
          show: true
        }
      }
      const setOptionsObj = cloneDeep(option)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>
