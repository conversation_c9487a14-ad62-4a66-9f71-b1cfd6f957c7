<!-- 柱状图:后续整体封装 -->
<template>
  <div class="wrapper">
    <p class="title">{{ title }}</p>
    <div ref="chartsDom" :style="{ height, width, flex: 1 }" @resize="handleResize"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    onTimeTarget: {
      type: String,
      default: '65'
    },
    targetValue: {
      type: String,
      default: '70'
    },
    dataSource: {
      type: Array,
      default: () => []
    }
  },
  // computed: {
  //   targetLine() {
  //     return this.dataSource.targetDeliveryRate
  //   },
  //   totalLine() {
  //     return this.dataSource.supplierDeliverRate
  //   },
  //   list() {
  //     return this.dataSource.baseDeliveryRate || []
  //   }
  // },
  data() {
    return {
      myChart: {}
    }
  },
  watch: {
    dataSource() {
      this.initCharts()
    }
  },
  mounted() {
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize()
      this.myChart &&
        this.myChart.setOption({
          width: '100%',
          height: 'calc(100% - 36px)'
        })
    },
    initCharts() {
      const xAxisData = []
      const seriesData = []
      const seriesBottomData = []
      const markLineOne = []
      // const markLineTwo = []
      this.dataSource.forEach((item) => {
        xAxisData.push(item.name)
        seriesData.push(item.value)
        seriesBottomData.push(1)
        markLineOne.push(this.targetValue)
        // markLineTwo.push(this.onTimeTarget)
      })
      const option = {
        color: ['#63caff', '#205DFF'],
        grid: {
          containLabel: true,
          left: 20,
          right: 20,
          bottom: 10,
          top: 40
        },
        legend: {
          icon: 'circle',
          itemWidth: 14,
          itemHeight: 6,
          itemGap: 13,
          data: [
            { name: '基地准时交货率' },
            { name: '空调准时交货率', icon: 'roundRect' },
            { name: '目标线', icon: 'roundRect' }
          ],
          right: '4%',
          textStyle: {
            fontSize: 12,
            color: '#F1F1F3'
          }
        },
        xAxis: {
          axisLabel: {
            color: '#c0c3cd',
            fontSize: 14,
            interval: 0,
            rotate: 20,
            margin: 15
          },
          splitLine: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#384267',
              width: 1,
              type: 'dashed'
            },
            show: true
          },
          boundaryGap: false,
          data: ['', ...xAxisData, ''],
          type: 'category'
        },
        yAxis: {
          axisLabel: {
            color: '#c0c3cd',
            fontSize: 14,
            margin: 10
          },

          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 163, 233, 0.2)',
              type: 'solid'
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 163, 233, 0.2)',
              width: 1,
              type: 'solid'
            },
            show: false
          },
          name: '%        ',
          nameTextStyle: {
            // 调整单位文字样式
            color: '#fff'
          }
        },
        series: [
          {
            name: '基地准时交货率',
            data: ['', ...seriesData, ''],
            type: 'bar',
            barWidth: 30,
            itemStyle: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [
                  {
                    offset: 0,
                    color: '#166CFF'
                  },
                  {
                    offset: 1,
                    color: '#59F0FE'
                  }
                ]
              }
            },
            label: {
              show: true,
              position: 'top',
              distance: 10,
              color: '#fff'
            }
            // markLine: {
            //   symbol: ['none', 'none'],
            //   itemStyle: {
            //     normal: {
            //       lineStyle: {
            //         type: 'dashed',
            //         color: '#00FFA0'
            //       },
            //       label: {
            //         show: true,
            //         position: 'end',
            //         distance: 100,
            //         textStyle: {
            //           color: '#00FFA0',
            //           fontSize: 30
            //         }
            //       }
            //     }
            //   },
            //   label: {
            //     normal: {
            //       show: false
            //     }
            //   },
            //   data: [
            //     {
            //       yAxis: '80' // 目标线
            //     },
            //     {
            //       yAxis: '60', // 准时交货率
            //       lineStyle: {
            //         type: 'dashed',
            //         color: '#FFBA00'
            //       }
            //     }
            //   ]
            // }
          },
          {
            data: ['', ...seriesBottomData, ''],
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbol: 'diamond',
            symbolOffset: [0, '50%'],
            symbolSize: [30, 15],
            zlevel: 2
          },
          {
            data: ['', ...seriesData, ''],
            type: 'pictorialBar',
            barMaxWidth: '20',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [30, 12],
            zlevel: 2
          },
          {
            name: '目标线',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            boundaryGap: false,
            zlevel: 3,
            lineStyle: {
              normal: {
                width: 1,
                type: 'dashed'
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 255, 160, 0.2247)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(13, 13, 22, 0.0139)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: '#00FFA0'
                // borderColor: '#fff', // 拐点边框颜色
                // borderWidth: 1 // 拐点边框大小
                // borderColor: '#fff',
                // borderWidth: 1
              }
            },
            data: [this.targetValue, ...markLineOne, this.targetValue]
          }
          // {
          //   name: '空调准时交货率',
          //   type: 'line',
          //   smooth: true,
          //   symbol: 'circle',
          //   symbolSize: 5,
          //   showSymbol: false,
          //   boundaryGap: false,
          //   zlevel: 3,
          //   lineStyle: {
          //     normal: {
          //       width: 1,
          //       type: 'dashed'
          //     }
          //   },
          //   areaStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(
          //         0,
          //         0,
          //         0,
          //         1,
          //         [
          //           {
          //             offset: 0,
          //             color: 'rgba(255, 186, 0, 0.2247)'
          //           },
          //           {
          //             offset: 0.8,
          //             color: 'rgba(13, 13, 22, 0.0139)'
          //           }
          //         ],
          //         false
          //       ),
          //       shadowColor: 'rgba(0, 0, 0, 0.1)',
          //       shadowBlur: 10
          //     }
          //   },
          //   itemStyle: {
          //     normal: {
          //       color: '#FFBA00'
          //       // borderColor: '#fff', // 拐点边框颜色
          //       // borderWidth: 1 // 拐点边框大小
          //       // borderColor: '#fff',
          //       // borderWidth: 1
          //     }
          //   },
          //   data: [this.onTimeTarget, ...markLineTwo, this.onTimeTarget]
          // }
        ],
        tooltip: {
          trigger: 'item',
          show: true
        }
      }
      const setOptionsObj = cloneDeep(option)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 22px;
  .title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 600;
    color: #9fe4ff;
    margin-left: 22px;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #6393ab;
      border-radius: 6px;
      margin-right: 8px;
    }
  }
}
</style>
