<!-- 饼图 -->
<template>
  <div class="wrapper">
    <p class="title">{{ title }}</p>
    <p class="subTitle">{{ subTitle }}</p>
    <div ref="chartsDom" :style="{ height, width, flex: 1 }" @resize="handleResize"></div>
    <p class="bu">{{ bu }}</p>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { cloneDeep } from 'lodash'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    subTitle: {
      type: String,
      default: ''
    },
    bu: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    rate: {
      type: [Number, String],
      default: 0
    }
  },
  computed: {
    screenWidth() {
      return document.body.clientWidth
    }
  },
  watch: {
    screenWidth: {
      handler() {
        this.handleResize()
      },
      deep: true
    },
    rate() {
      this.initCharts()
    }
  },
  data() {
    return {
      myChart: {}
    }
  },
  mounted() {
    this.initCharts()
    window.removeEventListener('resize', this.handleResize, true)
    window.addEventListener('resize', this.handleResize, true)
  },
  destroyed() {
    echarts.dispose(this.myChart)
    this.myChart = null
    window.removeEventListener('resize', this.handleResize, true)
  },
  methods: {
    handleResize() {
      this.myChart && this.myChart.resize()
    },
    initCharts() {
      const dataArr = [
        {
          value: this.rate,
          name: this.bu
        }
      ]
      const color = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 0,
          color: '#5CF9FE' // 0% 处的颜色
        },
        {
          offset: 0.17,
          color: '#468EFD' // 100% 处的颜色
        },
        {
          offset: 0.9,
          color: '#468EFD' // 100% 处的颜色
        },
        {
          offset: 1,
          color: '#5CF9FE' // 100% 处的颜色
        }
      ])
      const colorSet = [
        [0.91, color],
        [1, '#15337C']
      ]
      const rich = {
        white: {
          fontSize: 34,
          color: '#fff',
          fontWeight: '500'
        },
        bule: {
          fontSize: 34,
          fontFamily: 'DINBold',
          color: '#fff',
          fontWeight: '500'
        },
        radius: {
          width: 120,
          height: 34,
          borderWidth: 1,
          borderColor: '#019BE2',
          fontSize: 20,
          color: 'rgba(255, 255, 255, 0.8)',
          backgroundColor: '#142880',
          borderRadius: 20,
          textAlign: 'center'
        },
        size: {
          height: 400,
          padding: [100, 0, 0, 0]
        }
      }
      const chartOption = {
        backgroundColor: '#172D8C',
        tooltip: {
          formatter: '{a} <br/>{b} : {c}%'
        },

        series: [
          {
            //内圆
            type: 'pie',
            radius: '85%',
            center: ['50%', '50%'],
            z: 0,
            itemStyle: {
              normal: {
                color: new echarts.graphic.RadialGradient(
                  0.5,
                  0.5,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(17,24,43,0)'
                    },
                    {
                      offset: 0.5,
                      // color: '#172D8C'
                      color: '#172D8C'
                    },
                    {
                      offset: 1,
                      color: '#172D8C'
                      // color:'rgba(17,24,43,0)'
                    }
                  ],
                  false
                ),
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            label: {
              show: false
            },
            tooltip: {
              show: false
            },
            data: [100]
          },
          {
            type: 'gauge',
            name: '外层辅助',
            radius: '74%',
            startAngle: '245',
            endAngle: '-65',
            splitNumber: '100',
            pointer: {
              show: false
            },
            detail: {
              show: false
            },
            data: [
              {
                value: 1
              }
            ],
            // data: [{value: 1, name: 90}],
            title: {
              show: true,
              offsetCenter: [0, 30],
              textStyle: {
                color: '#fff',
                fontStyle: 'normal',
                fontWeight: 'normal',
                fontFamily: '微软雅黑',
                fontSize: 20
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: [[1, '#019BE2']],
                width: 2,
                opacity: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              length: 20,
              lineStyle: {
                color: '#172D8C',
                width: 0,
                type: 'solid'
              }
            },
            axisLabel: {
              show: false
            }
          },
          {
            type: 'gauge',
            radius: '70%',
            startAngle: '245',
            endAngle: '-65',
            pointer: {
              show: false
            },
            detail: {
              formatter: (value) => {
                return '{bule|' + value + '}{white|%}' + '{size|' + '}'
              },
              rich: rich,
              offsetCenter: ['0%', '0%']
            },
            data: dataArr,
            title: {
              show: false
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: colorSet,
                width: 25,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                opacity: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false,
              length: 25,
              lineStyle: {
                color: '#172D8C',
                width: 2,
                type: 'solid'
              }
            },
            axisLabel: {
              show: false
            }
          },
          {
            name: '灰色内圈', //刻度背景
            type: 'gauge',
            z: 2,
            radius: '56%',
            startAngle: '360',
            endAngle: '0',
            //center: ["50%", "75%"], //整体的位置设置
            axisLine: {
              // 坐标轴线
              lineStyle: {
                // 属性lineStyle控制线条样式
                color: [[1, '#172D8C']],
                width: 10,
                opacity: 1 //刻度背景宽度
              }
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            },
            axisTick: {
              show: false
            },
            detail: {
              show: 0
            }
          },
          {
            //内圆
            type: 'pie',
            radius: '56%',
            center: ['50%', '50%'],
            z: 1,
            itemStyle: {
              normal: {
                color: new echarts.graphic.RadialGradient(
                  0.5,
                  0.5,
                  0.8,
                  [
                    {
                      offset: 0,
                      color: '#4978EC'
                    },
                    {
                      offset: 0.5,
                      color: '#1E2B57'
                    },
                    {
                      offset: 1,
                      color: '#142478'
                    }
                  ],
                  false
                ),
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            label: {
              show: false
            },
            tooltip: {
              show: false
            },
            data: [100]
          }
        ]
      }
      const setOptionsObj = cloneDeep(chartOption)
      const myChart = echarts.init(this.$refs.chartsDom)
      this.myChart = myChart
      if (setOptionsObj && typeof setOptionsObj === 'object') {
        this.myChart.setOption(setOptionsObj, true)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.wrapper {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 22px;
  .title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 600;
    color: #9fe4ff;
    margin-left: 22px;
    &::before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      background: #6393ab;
      border-radius: 6px;
      margin-right: 8px;
    }
  }
  .subTitle {
    font-family: PingFang SC;
    font-size: 18px;
    font-weight: 500;
    margin-top: 22px;
    text-align: center;
    line-height: normal;
    letter-spacing: 0px;
    background: linear-gradient(0deg, #0263c6 -55%, #b9dbff 39%, #ffffff 136%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .bu {
    display: inline-block;
    position: absolute;
    background: #142880;
    box-sizing: border-box;
    border: 1px solid #019be2;
    color: #ffffff;
    width: 120px;
    height: 34px;
    border-radius: 34px;
    line-height: 34px;
    font-size: 16px;
    font-weight: bold;
    bottom: 10%;
    left: calc(50% - 60px);
    letter-spacing: 20px;
    text-align: center;
    text-indent: 20px;
  }
}
</style>
