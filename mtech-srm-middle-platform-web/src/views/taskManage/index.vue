<template>
  <div class="task-container">
    <div style="background: #fff; padding: 24px; border-radius: 8px">
      <!-- 搜索区域 -->
      <div class="search-input">
        <div class="toggle-tag" @click="isToggle = !isToggle">
          <span>{{ isToggle ? $t('收起') : $t('展开') }}</span>
          <i
            class="mt-icons mt-icon-icon_Sort_up"
            :style="isToggle ? '' : 'transform: rotate(180deg) scale(0.4)'"
          />
          <i
            class="mt-icons mt-icon-icon_Sort_up"
            :style="isToggle ? '' : 'transform: rotate(180deg) scale(0.4)'"
          />
        </div>
        <div v-show="isToggle" class="search-area">
          <div class="input-area">
            <span class="input-field">{{ $t('关键字') }}</span>
            <mt-input v-model="keyword" type="text" :placeholder="$t('请输入关键字查询')" />
          </div>
          <div class="button-group">
            <span @click="reset()">{{ $t('重置') }}</span>
            <span @click="search()">{{ $t('查询') }}</span>
          </div>
        </div>
      </div>
      <!-- 表格 -->
      <mt-data-grid
        ref="dataGrid"
        :toolbar="toolbarOptions"
        :column-data="columnData"
        :selection-settings="{ checkboxOnly: true }"
        :data-source="dataSource"
        :allow-sorting="true"
        :allow-scrolling="true"
        :allow-paging="true"
        :page-settings="pageSettings"
        @toolbarClick="toolbarClick"
        @currentChange="handleCurrentChange"
        @sizeChange="handleSizeChange"
      >
      </mt-data-grid>
    </div>
  </div>
</template>

<script>
import MtInput from '@mtech-ui/input'
import Vue from 'vue'
export default {
  components: {
    MtInput
  },
  data() {
    return {
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        totalRecordsCount: 0,
        pageSizes: [10, 20, 50]
      },
      dataSource: [], // 表格数据
      keyword: '', // 查询关键字
      toolbarOptions: [
        {
          text: this.$t('删除'),
          id: 'delete',
          prefixIcon: 'e-delete'
        }
      ], // 表格上方工具栏
      isToggle: true,
      isPurchase: false // 判断是是否为采方页面
    }
  },
  watch: {
    $route: {
      handler: 'setIsPurchase',
      immediate: true
    }
  },
  computed: {
    // 表格配置根据计算属性动态获取
    columnData() {
      const _this = this
      return [
        {
          width: '50',
          type: 'checkbox'
        },
        {
          field: 'no',
          headerText: this.$t('序号'),
          width: '80',
          allowSorting: false,
          template: function () {
            return {
              template: Vue.component('listIndex', {
                template: `<span>{{Number(data.index) + 1}}</span>`
              })
            }
          }
        },
        {
          field: 'todoType',
          headerText: this.$t('类型'),
          width: '100',
          template: function () {
            return {
              template: Vue.component('fileNameOption', {
                template: `<div style="display: flex; align-items: center;"><span style="display: block; width: 6px; height: 6px; margin-right: 8px; border-radius: 50%;" :style="data.todoType == 0 ? 'background: #52C41A;' : 'background: #FFB700;'" />data.todoType == 0 ? '任务' : ${this.$t(
                  '流程'
                )}</div>`,
                data() {
                  return { data: { data: {} } }
                }
              })
            }
          }
        },
        {
          field: 'title',
          width: '180',
          headerText: this.$t('标题'),
          template: function () {
            return {
              template: Vue.component('fileNameOption', {
                template: `<span style="color: #6386C1; cursor: pointer;" @click="linkTo(data.urlRef)">{{data.title}}</span>`,
                data() {
                  return { data: { data: {} } }
                },
                methods: {
                  linkTo(ref) {
                    if (ref) {
                      // window.open(ref, '_blank')
                      const linkArr = ref.split('/#')
                      if (linkArr[1]) {
                        this.$router.push({
                          path: linkArr[1]
                          // query: {
                          //   id: args.id
                          // }
                        })
                      }
                    }
                  }
                }
              })
            }
          }
        },
        {
          field: 'applyUserName',
          width: '100',
          headerText: this.$t('发起人')
        },
        {
          field: 'handlerNameList',
          width: '150',
          headerText: this.$t('当前处理人'),
          template: function () {
            return {
              template: Vue.component('handlerNameList', {
                template: `<div style="display: flex; flex-direction: column;"><span v-for="(itm, idx) in data.handlerNameList" :key="idx">{{itm}}</span></div>`
              })
            }
          }
        },
        {
          field: 'createTime',
          width: '100',
          headerText: this.$t('创建时间')
        },
        {
          field: 'stayTime',
          width: '100',
          headerText: this.$t('停留时间')
        },
        {
          field: 'operator',
          width: '100',
          headerText: this.$t('操作'),
          cellTools: ['delete'],
          template: function () {
            return {
              template: Vue.component('operatorOption', {
                template: `<div><span v-if="data.isDelete == 1" style="color: #6386C1; cursor: pointer;" @click="deleteData(data)">删除</span></div>`,
                data() {
                  return { data: { data: {} } }
                },
                methods: {
                  deleteData(e) {
                    console.log('删除', e)
                    // 调删除接口之后刷新列表
                    _this.startBatchDelete(e.id)
                  }
                }
              })
            }
          }
        }
      ]
    }
  },
  methods: {
    setIsPurchase() {
      // 根据当前路由判断是否更改采方页面判断开关
      this.isPurchase = this.$route.path === '/middlePlatform/pur/taskManage'
      this.search()
    },
    reset() {
      this.keyword = ''
      this.search()
    },
    getTodoQuery(params) {
      if (this.isPurchase) {
        // 判断是否为采方页面
        return this.$api.taskList.adminTodoQuery(params)
      }
      return this.$api.taskList.adminSupplierTodoQuery(params)
    },
    search() {
      console.log('查询', this.keyword)
      // 请求表格数据
      const params = {
        todoGroup: 0,
        todoType: 0,
        todoTab: 0,
        title: this.keyword,
        current: this.pageSettings.currentPage,
        size: this.pageSettings.pageSize
      }
      this.getTodoQuery(params).then((res) => {
        if (res.code === 200) {
          this.dataSource = res.data.records
          this.pageSettings = {
            ...this.pageSettings,
            currentPage: parseInt(res.data.current),
            pageSize: parseInt(res.data.size),
            totalRecordsCount: parseInt(res.data.total)
          }
        }
      })
    },
    // 分页的两个方法
    handleCurrentChange(currentPage) {
      this.pageSettings.currentPage = currentPage
      this.search()
    },
    handleSizeChange(pageSize) {
      this.pageSettings.pageSize = pageSize
      this.search()
    },

    toolbarClick(args) {
      if (args.item.id === 'delete') {
        console.log('点击删除', args)
        this.startBatchDelete()
      }
    },
    // 批量删除
    startBatchDelete(id) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除所选数据吗？')
        },
        success: () => {
          let ids = []
          if (id) {
            // 表格内单个删除按钮
            ids = [id]
          } else {
            // 批量删除
            const ref = this.$refs.dataGrid.ejsRef
            const deleteList = ref.getSelectedRecords()
            // 勾选删除的数据，map出id发送请求，
            // ids = deleteList.map(({ id }) => id)
            deleteList.forEach((item) => {
              if (item.isDelete == 1) {
                ids.push(item.id)
              }
            })
          }
          console.log('勾选删除的数据', ids)
          if (ids.length) {
            this.deleteTodoList(ids).then((res) => {
              const { code, message } = res
              if (code === 200) {
                // 调删除接口成功回调之后刷新列表
                this.$toast({
                  content: message ? message : this.$t('操作成功'),
                  type: 'success'
                })
                this.search()
              }
            })
          } else {
            this.search()
          }
        }
      })
    },
    deleteTodoList(params) {
      if (this.isPurchase) {
        return this.$api.taskList.todoDelete(params)
      }
      return this.$api.taskList.supplierTodoDelete(params)
    }
  }
}
</script>

<style lang="scss" scoped>
.task-container {
  padding-top: 24px;
}
.task-tabs {
  display: inline-flex;
  border-radius: 4px;
  border: 1px solid #4e5a70;
  span {
    padding: 11px 16px;
    font-size: 14px;
    color: #4e5a70;
    border-right: 1px solid #4e5a70;
    box-sizing: border-box;
    cursor: pointer;
    &:last-child {
      border-right: 0px;
    }
    &.active {
      color: #fff;
      background: #4e5a70;
    }
  }
}
.list-item-tabs {
  padding-top: 10px;
  border-bottom: 1px dashed #ececec;
  display: flex;
  &__tab {
    margin: 0 10px;
    padding: 18px 0;
    box-sizing: border-box;
    cursor: pointer;
    &:first-child {
      margin-left: 0;
    }
    display: flex;
    align-items: center;
    p {
      margin-bottom: unset;
      color: #999;
      font-size: 16px;
    }
    span {
      font-size: 12px;
      transform: scale(0.83);
      color: #fff;
      display: block;
      width: 19.2px;
      height: 19.2px;
      background: #f55448;
      border: 1px solid #ffffff;
      line-height: 19.2px;
      text-align: center;
      border-radius: 50%;
      margin: -8px 0 0 2px;
    }
    &.active {
      // padding: 10px 0 8px;
      // border-bottom: 2px solid red;
      p {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  position: relative;
  .tabs-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 33px;
    height: 2px;
    background: #f55448;
    border-radius: 1px;
  }
}
.search-input {
  padding: 10px 0 16px;
  .toggle-tag {
    padding-right: 15px;
    color: #2783fe;
    display: inline-block;
    font-size: 12px;
    position: relative;
    cursor: pointer;
    user-select: none;
    .mt-icons {
      font-size: 12px;
      position: absolute;
      transform: scale(0.4);
      top: -2px;
      left: 26px;
      &:nth-child(2) {
        top: 2px;
      }
    }
  }
  .search-area {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 16px 24px 12px;
    margin-top: 12px;
    .input-area {
      display: flex;
      align-items: center;
      .input-field {
        font-size: 14px;
        color: #333333;
        margin-right: 16px;
        font-family: PingFangSC-Regular;
      }
    }
    .button-group {
      padding-top: 10px;
      span {
        margin-right: 16px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #2783fe;
        cursor: pointer;
        user-select: none;
      }
    }
  }
}
</style>
