<!--
 * @Author: your name
 * @Date: 2021-12-01 15:08:31
 * @LastEditTime: 2021-12-02 14:37:53
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-middle-platform-web\src\views\logs\Index.vue
-->
<template>
  <div class="mt-pt-md">
    <search-form ref="searchForm" :fields="columns"></search-form>
    <div class="flex justify-end mt-mb-sm">
      <mt-button css-class="e-info" @click="search">{{ $t('查 询') }}</mt-button>
      <mt-button css-class="e-flat" :is-primary="true" @click="reset">{{ $t('重 置') }}</mt-button>
    </div>

    <mt-data-grid
      id="dataGrid"
      ref="dataGrid"
      :allow-paging="true"
      :data-source="data"
      :column-data="columns"
      :page-settings="pageSettings"
      @currentChange="handleCurrentChange"
      @sizeChange="handleSizeChange"
    ></mt-data-grid>

    <detail-view ref="detailView" :row-data="rowData"></detail-view>
  </div>
</template>

<script>
import Vue from 'vue'
import DetailView from './components/DetailView.vue'
import SearchForm from './components/SearchForm.vue'

const typeOption = [
  { text: this.$t('添加'), value: 'INSERT' },
  { text: this.$t('修改'), value: 'UPDATE' },
  { text: this.$t('删除'), value: 'DELETE' },
  { text: this.$t('查询'), value: 'QUERY' }
]
export default {
  components: {
    DetailView,
    SearchForm
  },
  data() {
    return {
      data: [],
      columns: (function (that) {
        return [
          {
            field: 'applicationCode',
            headerText: this.$t('应用编码'),
            template: function () {
              return {
                template: Vue.component('applicationCode', {
                  template: `
                    <div class="action-col">
                      <div class="action-col--content">{{ data.applicationCode }}</div>
                      <div class="action-col--action">
                        <div class="action-col--action__btn" @click="preview">
                          <i class="mt-icons mt-icon-icon_Hiddenpassword"></i>
                          <span class="icon-title">{{ $t("预览") }}</span>
                        </div>
                      </div>
                    </div>
                  `,
                  data() {
                    return {
                      data: {}
                    }
                  },
                  methods: {
                    preview() {
                      that.preview(this.data)
                    }
                  }
                })
              }
            }
          },
          {
            field: 'businessCode',
            headerText: this.$t('业务编码')
          },
          {
            field: 'objectId',
            headerText: this.$t('业务对象ID')
          },
          {
            field: 'objectName',
            headerText: this.$t('业务对象名称')
          },
          {
            field: 'updateUserName',
            headerText: this.$t('操作人姓名')
          },
          {
            field: 'updateTime',
            headerText: this.$t('操作时间'),
            searchParams: {
              type: 'dateRange'
            }
          },
          {
            field: 'operationName',
            headerText: this.$t('操作类型'),
            searchParams: {
              type: 'select',
              options: typeOption
            },
            template: function () {
              return {
                template: Vue.component('operationName', {
                  template: `
                    <div>
                      {{ label }}
                    </div>
                  `,
                  data() {
                    return {
                      data: {}
                    }
                  },
                  computed: {
                    label() {
                      const opt = typeOption.filter((o) => o.value === this.data.operationName)
                      return opt[0].text
                    }
                  }
                })
              }
            }
          },
          {
            field: 'comment',
            headerText: this.$t('操作备注'),
            searchParams: {
              searchable: false
            }
          }
        ]
      })(this),
      pageSettings: {
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 20, 50, 100, 200],
        totalRecordsCount: 0
      },
      rowData: {},
      searchForm: {}
    }
  },
  mounted() {
    this.dataGet()
  },
  methods: {
    dataGet() {
      const { currentPage: pageNo, pageSize } = this.pageSettings
      const query = {
        pageNo,
        pageSize,
        ...this.searchForm
      }

      this.$api.logs
        .logQuery(query)
        .then((res) => {
          const { operationList, totalCount } = res.data

          this.pageSettings.totalRecordsCount = totalCount
          this.data = operationList
        })
        .catch(() => {})
    },
    preview(rowData) {
      this.rowData = rowData
      this.$refs.detailView.open()
    },
    search() {
      this.searchForm = this.$refs.searchForm.getFormValue()
      this.pageSettings.currentPage = 1
      this.dataGet()
    },
    reset() {
      this.$refs.searchForm.reset()
      this.search()
    },
    handleCurrentChange(page) {
      this.pageSettings.currentPage = page
      this.dataGet()
    },
    handleSizeChange(size) {
      this.pageSettings.currentPage = 1
      this.pageSettings.pageSize = size
      this.dataGet()
    }
  }
}
</script>

<style lang="scss" scoped>
$sm: 10px;
$md: 20px;

.flex {
  display: flex;
}
.justify-end {
  justify-content: end;
}
.mt-pa-sm {
  padding: $sm;
}
.mt-pt-md {
  padding-top: $md;
}
.mt-mb-sm {
  margin-bottom: $sm;
}

::v-deep .action-col {
  &--content {
    color: #00469c;
    font-size: 14px;
    cursor: pointer;
    text-align: left;
    &:hover {
      font-weight: 500;
    }
  }
  &--action {
    display: flex;
    justify-content: start;
    &__btn {
      font-size: 12px;
      color: #6386c1;
      cursor: pointer;
    }
  }
}
</style>
