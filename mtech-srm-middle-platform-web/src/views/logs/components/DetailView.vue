<!--
 * @Author: your name
 * @Date: 2021-12-01 17:23:48
 * @LastEditTime: 2021-12-02 14:10:56
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-middle-platform-web\src\views\logs\components\DetailView.vue
-->
<template>
  <mt-dialog ref="dialog" :header="$t('详情')">
    <div class="mt-pa-md">
      <mt-data-grid
        id="dataGridView"
        ref="dataGridView"
        :data-source="data"
        :column-data="columns"
      ></mt-data-grid>
    </div>
  </mt-dialog>
</template>

<script>
export default {
  props: {
    rowData: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columns: [
        {
          field: 'attributeName',
          headerText: this.$t('属性Key')
        },
        {
          field: 'attributeAlias',
          headerText: this.$t('属性名')
        },
        {
          field: 'attributeType',
          headerText: this.$t('类型')
        },
        {
          field: 'oldValue',
          headerText: this.$t('旧值')
        },
        {
          field: 'newValue',
          headerText: this.$t('新值')
        }
      ]
    }
  },
  computed: {
    data() {
      return this.rowData?.attributeModelList
    }
  },
  methods: {
    open() {
      this.$refs.dialog.ejsRef.show()
    }
  }
}
</script>

<style lang="scss" scoped>
$md: 20px;

.mt-pa-md {
  padding: $md;
}
</style>
