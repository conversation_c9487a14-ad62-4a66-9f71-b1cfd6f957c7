<!--
 * @Author: your name
 * @Date: 2021-12-02 09:51:23
 * @LastEditTime: 2021-12-02 14:04:27
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \mtech-srm-middle-platform-web\src\views\logs\components\SearchForm.vue
-->
<template>
  <mt-form ref="ruleForm" :model="ruleForm" :rules="rules">
    <mt-row :gutter="20">
      <mt-col v-for="f in fields" :key="f.field" :sm="8" :md="6" :lg="4" :xl="4">
        <mt-form-item :prop="f.field" :label="f.headerText">
          <mt-date-range-picker
            v-if="f.searchParams && f.searchParams.type === 'dateRange'"
            v-model="ruleForm[f.field]"
            :placeholder="$t('选择开始时间和结束时间')"
          ></mt-date-range-picker>
          <mt-select
            v-else-if="f.searchParams && f.searchParams.type === 'select'"
            v-model="ruleForm[f.field]"
            :data-source="f.searchParams.options"
            :show-clear-button="true"
            :allow-filtering="true"
            :placeholder="$t('请选择类型')"
          ></mt-select>
          <mt-input
            v-else
            v-model="ruleForm[f.field]"
            :show-clear-button="true"
            type="text"
            :placeholder="$t('请输入名称')"
          ></mt-input>
        </mt-form-item>
      </mt-col>
    </mt-row>
  </mt-form>
</template>

<script>
export default {
  props: {
    fields: {
      required: false,
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      ruleForm: {},
      rules: {}
    }
  },
  methods: {
    getFormValue() {
      const { updateTime, ...otherFields } = this.ruleForm
      const startTime = updateTime ? new Date(updateTime[0]).getTime() : null
      const endTime = updateTime ? new Date(updateTime[1]).getTime() : null

      return {
        startTime,
        endTime,
        ...otherFields
      }
    },
    reset() {
      this.$refs.ruleForm.resetFields()
    }
  }
}
</script>

<style></style>
