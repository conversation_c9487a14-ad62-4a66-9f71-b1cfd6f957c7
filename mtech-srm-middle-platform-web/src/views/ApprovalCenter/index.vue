<template>
  <div style="padding-top: 20px; height: 100%">
    <approvalInfo :task="task"></approvalInfo>
    <div class="approval-list-wrap">
      <div class="table-left" :class="{ wid50: fold }">
        <mt-treeView
          class="approval-tree-box"
          v-if="treeShow"
          :fields="approvalFileds"
          :node-template="Template"
          @nodeSelecting="clickTreeHandle"
        ></mt-treeView>
        <div class="solt" @click="toggle">
          <MtIcon name="a-icon_Shrinkexpand" />
        </div>
      </div>

      <div class="table-right" :class="{ wid100: fold }">
        <mt-template-page
          :template-config="pageConfigOuter"
          @handleSelectTab="handleSelectTabOuter"
        >
          <mt-template-page
            ref="tempaltePageRef"
            slot="slot-0"
            :template-config="pageConfig"
            @handleClickToolBar="handleClickToolBar"
            @handleClickCellTool="handleClickCellTool"
            @handleClickCellTitle="handleClickCellTitle"
            @handleSelectTab="handleSelectTab"
          >
          </mt-template-page>
          <mt-template-page
            ref="tempaltePageRef1"
            slot="slot-1"
            :template-config="pageConfigOther"
            @handleClickToolBar="handleClickToolBarOther"
            @handleClickCellTool="handleClickCellToolOther"
            @handleClickCellTitle="handleClickCellTitleOther"
            @handleSelectTab="handleSelectTabOther"
          >
          </mt-template-page>
        </mt-template-page>
        <treeTemplateVue v-if="false" />
      </div>
    </div>
  </div>
</template>
<script>
import { approvalListColumnData, historyListColumnData, pageConfigOther } from './config'
import approvalInfo from '@/views/ApprovalCenter/components/approvalInfo'
import treeTemplateVue from '@/views/ApprovalCenter/components/treeTemplateVue'
export default {
  components: {
    approvalInfo,
    treeTemplateVue
  },
  data() {
    return {
      currentPublishTemplate: '',
      currentCategory: '',
      approvalFileds: {
        dataSource: [],
        id: 'id',
        text: 'approvePointName',
        child: 'childrens'
      },
      Template: function () {
        return {
          template: treeTemplateVue
        }
      },
      pageConfig: [
        {
          title: this.$t('待审批'),
          toolbar: { useBaseConfig: false, tools: [[], ['Refresh']] },
          grid: {
            columnData: approvalListColumnData,
            asyncConfig: {
              url: this.$api.approvalCenter.approvalCenterWatingApi,
              params: {}
            }
          }
        },
        {
          title: this.$t('已审批'),
          toolbar: { useBaseConfig: false, tools: [[], ['Refresh']] },
          grid: {
            // frozenColumns: 1,
            columnData: historyListColumnData,
            asyncConfig: {
              url: this.$api.approvalCenter.approvalCenterWatiedApi,
              params: {}
            }
          }
        }
      ],
      pageConfigOuter: [
        {
          title: this.$t('内部审批')
        },
        {
          title: this.$t('OA审批')
        }
      ],
      pageConfigOther: pageConfigOther(
        this.$api.approvalCenter.otherWatingApi,
        this.$api.approvalCenter.otherWatiedApi
      ),
      currentType: 0,
      task: {},
      treeShow: false,
      fold: true,
      other: false
    }
  },
  mounted() {
    this.getData()
    this.getTree()
  },
  methods: {
    handleClickToolBar(e) {
      const { grid, toolbar } = e
      if (e.toolbar.id === 'Add') {
        this.addTemplate()
      }

      const sections = grid.getSelectedRecords()
      if (sections.length === 0) {
        return
      }
      const idList = sections.map((v) => v.id)

      if (toolbar.id === 'version') {
        this.viewHistory(idList)
      } else if (toolbar.id === 'Delete') {
        this.deleteTemplate(idList)
      }
    },
    handleClickCellTool(e) {
      console.log('use-handleClickCellTool', e)
      const { tool, data } = e
      if (tool.id === 'audit') {
        this.approvalShow(data)
      } else if (tool.id === 'preview') {
        this.approvalShow(data)
      }
    },
    handleClickCellTitle(e) {
      console.log('use-handleClickCellTitle', e)
    },
    handleClickToolBarOther() {},
    handleClickCellToolOther() {},
    handleClickCellTitleOther() {},
    toApproval() {
      console.log('审批')
    },
    arrovalSuccessFn(data) {
      console.log('审批已经完成', data)
    },
    clickTreeHandle({ nodeData }) {
      if (nodeData.parentID) {
        const data = this.getTreeData(nodeData.parentID, nodeData.id)
        const { applicationId, menuId, approvePointId } = data
        if (!this.other) {
          this.pageConfig[0].grid.asyncConfig.url = `${this.$api.approvalCenter.approvalCenterWatingApi}?applicationId=${applicationId}&menuId=${menuId}&approvePointId=${approvePointId}`
          this.pageConfig[1].grid.asyncConfig.url = `${this.$api.approvalCenter.approvalCenterWatiedApi}?applicationId=${applicationId}&menuId=${menuId}&approvePointId=${approvePointId}`
        } else {
          this.pageConfigOther[0].grid.asyncConfig.url = `${this.$api.approvalCenter.otherWatingApi}?applicationId=${applicationId}&menuId=${menuId}&approvePointId=${approvePointId}`
          this.pageConfigOther[1].grid.asyncConfig.url = `${this.$api.approvalCenter.otherWatiedApi}?applicationId=${applicationId}&menuId=${menuId}&approvePointId=${approvePointId}`
        }
      }
    },
    // 审核和查看弹窗
    approvalShow(data) {
      this.$router.push({
        path: '/middlePlatform/approvalDialog',
        query: {
          taskId: data.taskId,
          taskDefId: data.taskDefId,
          processInstanceId: data.processInstanceId,
          tab: this.currentType
        }
      })
      // this.$dialog({
      //   modal: () => import("./approvalDialog.vue"),
      //   data: {
      //     title: this.$t("审批信息"),
      //     data: data,
      //     tab: this.currentType,
      //   },
      //   success: () => {
      //     this.$refs.tempaltePageRef.refreshCurrentGridData();
      //     this.getData();
      //   },
      // });
    },
    handleSelectTab(e) {
      this.currentType = e
    },
    handleSelectTabOuter(e) {
      this.other = e
      e ? this.getOtherData() : this.getData()
    },
    handleSelectTabOther() {},
    getData() {
      this.$api.approvalCenter.getInfoData({}).then((res) => {
        if (res.data) {
          this.task = { ...res.data }
        }
      })
    },
    getOtherData() {
      this.$api.approvalCenter.getOtherUserInfo({}).then((res) => {
        if (res.data) {
          this.task = { ...res.data }
        }
      })
    },
    getTree() {
      this.$api.approvalConfig.getTree({}).then((res) => {
        if (res.code == 200) {
          if (res.data.length > 0) {
            const data = this.formateData(res.data)
            this.approvalFileds.dataSource = data
            this.approvalFileds.dataSource[0].expanded = true
            this.treeShow = true
          }
        }
      })
    },
    formateData(data) {
      data.forEach((element) => {
        // element.approvePointId = element.applicationId;
        element.approvePointName = element.applicationName
        element.id = element.applicationId
        const pid = element.applicationId
        if (element.childrens && element.childrens.length > 0) {
          element.childrens.forEach((d) => {
            d.id = `${pid}-${d.approvePointId}`
          })
        }
      })
      return data
    },
    getTreeData(pid, id) {
      const [parentData] = [...this.approvalFileds.dataSource.filter((v) => v.applicationId == pid)]
      const [elementData] = [...parentData.childrens.filter((v) => v.id == id)]
      return elementData
    },
    toggle() {
      this.fold = !this.fold
    }
  }
}
</script>

<style lang="scss" scoped>
.approval-list-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  /deep/ .action {
    color: blue;
    cursor: pointer;
  }
}

.approval-tree-box {
  width: 100%;
  background: #fff;
  border-right: 1px solid #eee;
  padding-top: 20px;
  .tree-view--template {
    width: 100%;
  }
  ::v-deep .e-list-text {
    width: 100% !important;
  }

  /deep/ .e-list-text {
    width: 100% !important;
  }
  /deep/ .e-ul {
    overflow: visible;
  }
}

.data-grid--wrap {
  flex: 1;
  overflow: hidden;
}
.table-left {
  width: 300px;
  height: 100%;
  overflow: auto;
  padding-left: 10px;
  position: relative;
  transition: all 0.3s linear;
  .solt {
    position: absolute;
    top: 28px;
    right: 13px;
    color: #98aac3;
    cursor: pointer;
  }
}
.wid50 {
  width: 35px;
}
.table-right {
  width: calc(100% - 300px);
}
.table-right.wid100 {
  width: calc(100% - 35px);
}

/deep/ .waitingfor {
  display: inline-block;
  // width: 40px
  text-align: center;
  position: relative;
  text-align: left;
  top: -5px;
  font-size: 13px;
  span {
    background: rgba(99, 134, 193, 0.3);
    color: #6386c1;
    padding: 2px 4px;
  }
}
/deep/ .top0 {
  top: 0;
}
/deep/ .btns {
  position: relative;
  top: -4px;
}
/deep/ .success {
  color: rgba(77, 132, 29, 1);
  background: rgba(77, 132, 29, 0.3);
  font-size: 13px;
  padding: 2px;
}
/deep/ .primiy {
  color: rgba(3, 120, 213, 1);
  background: rgba(3, 120, 213, 0.3);
  font-size: 13px;
  padding: 2px;
}
/deep/ .error {
  color: #ed5633;
  padding: 2px;
  font-size: 13px;
  background: rgba(237, 86, 51, 0.3);
  position: relative;
  top: -5px;
}
/deep/ .cancel {
  font-size: 13px;
  padding: 2px;
}
/deep/ .warning {
  color: rgba(0, 70, 156, 1);
  background: rgba(0, 70, 156, 0.3);
  font-size: 13px;
  padding: 2px;
}
</style>
