<!--
 * @Author: your name
 * @Date: 2021-10-13 12:28:38
 * @LastEditTime: 2021-11-08 14:15:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalCenter\components\approvalDialog.vue
-->
<template>
  <div class="approvalDialog">
    <div class="dialog-content">
      <div class="approv-type">
        <label>{{ $t('审批流程图') }}：</label>
        <div class="actionBtns">
          <mt-button
            class="btn--revert"
            icon-css="mt-icons mt-icon-icon_solid_finish"
            css-class="e-flat"
            icon-position="Right"
            v-if="!readonly"
            @click.native="success"
            >{{ $t('通过') }}</mt-button
          >
          <mt-button
            class="btn--revert"
            icon-css="mt-icons mt-icon-icon_Share_2"
            css-class="e-flat"
            v-if="!readonly"
            icon-position="Right"
            @click.native="fail"
            >{{ $t('驳回') }}</mt-button
          >
          <mt-button
            class="btn--revert"
            icon-css="mt-icons mt-icon-icon_solid_close"
            css-class="e-flat"
            icon-position="Right"
            @click.native="cancel"
            >{{ $t('返回') }}</mt-button
          >
        </div>
      </div>
      <Step :data="tabProcess" v-if="readonly"></Step>
      <watingStep :data="tabProcess" v-if="!readonly"></watingStep>
      <div class="approv-advice" v-if="!readonly">
        <label>{{ $t('审批意见') }}</label>

        <mt-input
          class="advice"
          :readonly="readonly"
          v-model="msg"
          :multiline="true"
          :rows="5"
          type="text"
          maxlength="200"
          :placeholder="$t('请输入审批意见')"
        ></mt-input>
      </div>
      <div class="aprroveHtml" v-if="!url && tabSource.length != 0">
        <template v-if="tabSource.length <= 1">
          <ul class="render-ul" v-for="props in tabSource" :key="props.tab">
            <li v-for="item in props.bizData" :key="item.name">
              <template v-if="!isNetWork">
                <div class="item-key" v-for="(value, key) in item" :key="key">
                  <label>{{ key }}：</label>
                  <span>{{ value }}</span>
                </div>
              </template>
              <template else>
                <div class="item-key" v-for="(value, key) in item.key" :key="key">
                  <label>{{ value.label }}</label>
                  <span>{{ value.value }}</span>
                </div>
              </template>
            </li>
          </ul>
        </template>
        <template v-if="tabSource.length > 1">
          <mt-tabs
            :data-source="tabSource"
            :selected-item="0"
            tab-key="tab"
            :width="'100%'"
            :height="'auto'"
            css-class="tabRender"
            v-if="tabSource.length > 0"
          >
            <template #header="{ props }">
              <div>{{ props.tab }}</div>
            </template>
            <template #content="{ props }">
              <ul class="render-ul">
                <li v-for="item in props.bizData" :key="item.name">
                  <template v-if="!isNetWork">
                    <div class="item-key" v-for="(value, key) in item" :key="key">
                      <label>{{ key }}：</label>
                      <span>{{ value }}</span>
                    </div>
                  </template>
                  <template else>
                    <div class="item-key" v-for="(value, key) in item.key" :key="key">
                      <label>{{ value.label }}</label>
                      <span>{{ value.value }}</span>
                    </div>
                  </template>
                </li>
              </ul>
            </template>
          </mt-tabs>
        </template>
      </div>

      <div
        class="aprroveHtml"
        id="d18bde12-236a-4ee1-8e68-496f677a6f04"
        width="100%"
        :height="iframeHeight"
      ></div>
    </div>
  </div>
</template>
<script>
import Step from './components/step.vue'
import watingStep from './components/watingStep.vue'
import { loadMicroApp } from 'qiankun'

export default {
  components: {
    Step,
    watingStep
  },
  data() {
    return {
      msg: '',
      tabSource: [],
      tabProcess: [],
      url: '',
      microApp: undefined,
      iframeHeight: 150,
      modalData: {},
      isNetWork: false
    }
  },
  computed: {
    readonly() {
      return this.modalData.data.tab != 0
    },
    processInstanceId() {
      return this.modalData.data.processInstanceId
    }
  },
  mounted() {},
  beforeDestroy() {
    this.microApp.unmount()
  },
  methods: {
    success() {
      this.$api.approvalCenter
        .reject({
          msg: this.msg,
          taskId: this.modalData.data.taskId,
          approvalType: 'pass'
        })
        .then(() => {
          this.$toast({ content: this.$t('操作成功'), type: 'success' })
          this.cancel()
        })
    },
    cancel() {
      this.$router.push({
        path: '/middlePlatform/approval-center'
      })
    },
    fail() {
      if (!this.msg) {
        this.$toast({ content: this.$t('请输入审批意见'), type: 'warning' })
        return
      }
      this.$api.approvalCenter
        .reject({
          msg: this.msg,
          taskId: this.modalData.data.taskId,
          approvalType: 'reject'
        })
        .then((res) => {
          if (res.code == 200) {
            this.$toast({ content: this.$t('操作成功'), type: 'success' })
            this.cancel()
          }
        })

      // Bus.$emit('approvaSuccess',true)
    },
    getHtmlData() {
      this.$api.approvalCenter
        .getHtmlData({ processInstanceId: this.processInstanceId })
        .then((res) => {
          if (res.data && res.data.approveBillInfo) {
            const data = JSON.parse(res.data.approveBillInfo)
            this.tabSource = data
            if (res.data.applicationId == 2 || res.data.applicationId == 4) {
              this.isNetWork = true
            }
          }
          if (res.data.billUrl) {
            this.url = res.data.billUrl
            const { hostname, pathname, protocol } = new URL(this.url)
            const defaultPath = this.url.split('#')[1]
            this.microApp = loadMicroApp({
              name: 'center',
              entry: protocol + '//' + hostname + pathname,
              container: '#d18bde12-236a-4ee1-8e68-496f677a6f04',
              props: { routerMode: 'abstract', defaultPath }
            })
          }
        })
    },
    queryWaitProcessUserInfo() {
      const { processInstanceId, taskDefId, taskId } = this.modalData.data
      this.$api.approvalCenter
        .queryWaitProcessUserInfo({ processInstanceId, taskDefId, taskId })
        .then((res) => {
          if (res.data.length > 0) {
            this.tabProcess = res.data[0].users
          }
        })
    },
    queryAlreadyProcessUserInfo() {
      const { processInstanceId, taskDefId, taskId } = this.modalData.data
      this.$api.approvalCenter
        .queryAlreadyProcessUserInfo({ processInstanceId, taskDefId, taskId })
        .then((res) => {
          this.tabProcess = res.data
        })
    },
    load() {
      this.iframeHeight = window.document.documentElement.clientHeight
      const iframe = document.querySelector('#iframe_id').contentWindow
      setTimeout(() => {
        const ele = iframe.document.querySelectorAll('.invite-btn').length
        for (let i = 0; i < ele; i++) {
          iframe.document.querySelectorAll('.invite-btn')[i].style.display = 'none'
        }
      }, 1000)
    }
  },
  created() {
    this.modalData.data = this.$route.query
    this.getHtmlData()
    if (this.readonly) {
      this.queryAlreadyProcessUserInfo()
    } else {
      this.queryWaitProcessUserInfo()
    }
  }
}
</script>
<style lang="scss" scoped>
.approvalDialog {
  height: 100%;
  margin: 20px 20px 20px 0;
  padding: 20px 15px 0 15px;
  background: #fff;
}
.dialog-content {
  font-size: 14px;
  /deep/ span.e-input-group {
    padding: 0;
  }
  /deep/ .process-desc {
    width: 820px !important;
  }
  .approv-type {
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    label {
      color: rgba(41, 41, 41, 1);
      padding-left: 9px;
      font-weight: bold;
      position: relative;
      &::before {
        content: '';
        width: 3px;
        height: 14px;
        background: rgba(99, 134, 193, 1);
        position: absolute;
        top: 3px;
        left: 0;
      }
    }
    span {
      font-weight: bold;
      .approv-way {
        padding: 3px;
        color: rgba(237, 161, 51, 1);
        background: rgba(237, 161, 51, 0.1);
        font-weight: normal;
        border-radius: 2px;
        font-size: 12px;
      }
    }
  }
  .approv-advice {
    margin-top: 20px;
    position: relative;
    label {
      display: block;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .actionBtns {
      position: absolute;
      top: -7px;
      right: 20px;
    }
  }
  .aprroveHtml {
    margin-top: 15px;
    position: relative;
    // height: 516px;
    border: 1px solid rgba(232, 232, 232, 1);
    overflow: auto;
    border-radius: 2px;
    .applayHref {
      padding: 15px;
      font-size: 14px;
      display: block;
    }
    // &::before{
    //   content:'';
    //   width:270px;
    //   height: 100%;
    //   background: #fff;
    //   position: absolute;
    //   top: 0;
    //   left: 0;

    // }
  }
}
/deep/ .mt-input {
  border: 1px solid rgba(232, 232, 232, 1);
  height: 106px;
  border-radius: 2px;
  /deep/ .e-input {
    text-indent: 2em;
  }
}
/deep/ .advice .e-multi-line-input {
  padding-left: 10px !important;
}
/deep/ .mt-input .e-input {
  text-indent: 1em;
}
.render-ul {
  display: flex;
  flex-wrap: wrap;
  padding: 20px;
  font-size: 12px;
  li {
    margin-right: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    .item-key {
      margin-right: 15px;
      min-width: 350px;
      margin-bottom: 15px;

      label {
        font-weight: bold;
        font-size: 14px;
      }
      span {
        line-height: 1.5;
      }
    }
  }
}
</style>
