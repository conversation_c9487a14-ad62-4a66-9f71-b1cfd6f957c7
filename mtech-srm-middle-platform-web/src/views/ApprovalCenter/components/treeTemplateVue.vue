<template>
  <div class="treeItem">
    <span class="tag" v-if="data.childrens">{{ i18n.t('菜单') }}</span>
    <span class="tag blue" v-else>{{ i18n.t('节点') }}</span>
    {{ data.approvePointName }}
  </div>
</template>
<script>
import { i18n } from '@/main.js'
export default {
  data() {
    return {
      i18n
    }
  }
}
</script>
<style scoped lang="scss">
.treeItem {
  display: flex;
  align-items: center;
}
.tag {
  height: 16px;
  line-height: 16px;
  border-radius: 2px;
  color: #fff;
  padding: 0 5px;
  font-size: 10px;
  font-family: PingFangSC;
  font-weight: 400;
  background: rgb(237, 201, 81);
  margin-right: 10px;
}
.blue {
  background: rgb(99, 134, 193);
}
</style>
