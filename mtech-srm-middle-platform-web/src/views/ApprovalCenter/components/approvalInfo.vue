<!--
 * @Author: your name
 * @Date: 2021-10-13 13:50:14
 * @LastEditTime: 2021-10-26 15:19:28
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalCenter\components\approvalInfo.vue
-->
<template>
  <div class="my-approval-info">
    <div class="user-intraduce">
      <!-- <img  v-if="user.enterpriseLogo" :src="user.enterpriseLogo" alt=""> -->
      <div class="user-company">
        <div class="user-company-slot">
          <span class="userName">{{ user.username }}</span>
          <!-- <span class="userSf"></span> -->
        </div>
        <p class="lev" v-if="user.enterpriseName">
          {{ user.enterpriseName }}-{{ user.departmentName }}-{{ user.tenantName }}
        </p>
      </div>
    </div>
    <div class="approval-info">
      <div class="approval-info-item">
        <label>{{ $t('待审批') }}</label
        ><span class="waiting">{{ task.approvalTaskNum }}</span
        >{{ $t('条') }}
      </div>
      <div class="approval-info-item ahistory">
        <label>{{ $t('历史审批') }}</label
        ><span class="waiting">{{ task.finishProcessNum }}</span
        >{{ $t('条') }}
      </div>
    </div>
    <div class="warn-tip">
      <p>
        {{ $t('尊敬的') }} <span>{{ user.username }}</span
        >{{ $t('先生/女士') }}:
      </p>
      <p class="pd28">{{ $t('您的职责非常重要，请谨慎处理') }}</p>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    task: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      user: {}
    }
  },
  methods: {
    getUserInfo() {
      this.$api.approvalCenter.getUserInfo({}).then((res) => {
        if (res.data) this.user = res.data
      })
    }
  },
  created() {
    this.getUserInfo()
  }
}
</script>
<style lang="scss" scoped>
.my-approval-info {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 0 30px;
  height: 120px;
  align-items: center;
  background: rgba(99, 134, 193, 0.06);
  box-shadow: inset 0 -1px 0 0 rgba(232, 232, 232, 1);
  border-bottom: 2px solid #eaeaea;
  .user-intraduce {
    display: flex;
    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
    }
    .user-company {
      margin-left: 15px;
      .user-company-slot {
        margin-top: 8px;
        span.userSf {
          font-size: 12px;
          color: rgba(99, 134, 193, 1);
          background: rgba(99, 134, 193, 0.1);
          border-radius: 2px;
          padding: 4px;
        }
        .userName {
          font-size: 16px;
          font-weight: bold;
          margin-right: 10px;
          color: rgba(41, 41, 41, 1);
        }
      }
      .lev {
        margin-top: 14px;
        font-weight: normal;
        color: rgba(157, 170, 191, 1);
      }
    }
  }
  .approval-info {
    display: flex;
    .approval-info-item {
      font-weight: bold;
      height: 60px;
      background: rgba(255, 255, 255, 1);
      color: rgba(0, 70, 156, 1);
      border-radius: 4px;
      display: flex;
      align-items: center;
      overflow: hidden;
      position: relative;
      padding: 0 20px;
      &::after {
        content: '';
        width: 4px;
        height: 60px;
        background: rgba(99, 134, 193, 1);
        position: absolute;
        top: 0;
        left: 0;
      }
      .waiting {
        color: rgba(0, 70, 156, 1);
        font-size: 36px;
        margin-right: 10px;
      }

      label {
        margin: 0 20px 0 16px;
        color: #000;
      }
      &:last-child {
        margin-left: 20px;
      }
    }
    .ahistory {
      color: rgba(237, 86, 51, 1);
      &::after {
        background: rgba(237, 86, 51, 1);
      }
      .waiting {
        color: rgba(237, 86, 51, 1);
      }
    }
  }
  .warn-tip {
    line-height: 22px;
    span {
      color: rgba(0, 70, 156, 1);
    }
    .pd28 {
      padding-left: 28px;
    }
  }
}
</style>
