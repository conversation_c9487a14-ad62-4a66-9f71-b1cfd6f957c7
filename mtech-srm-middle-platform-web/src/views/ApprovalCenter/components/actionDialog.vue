<!--
 * @Author: your name
 * @Date: 2021-10-13 11:16:17
 * @LastEditTime: 2021-11-01 21:09:17
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalCenter\components\actionDialog.vue
-->
<template>
  <div>
    <span class="action" @click="approvalShow">{{
      data.approveStatus == 1 ? $t('审批') : $t('查看')
    }}</span>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: {}
    }
  },
  methods: {
    // 审核和查看弹窗
    approvalShow() {
      // console.log('弹框', this.data)
      this.$dialog({
        modal: () => import('../approvalDialog.vue'),
        data: {
          title: this.$t('审批信息'),
          data: this.data
        },
        success: () => {
          // this.$refs.templateRef.refreshCurrentGridData();
        }
      })
    }
  }
}
</script>
