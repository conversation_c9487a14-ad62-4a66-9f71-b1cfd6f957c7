<!--
 * @Author: your name
 * @Date: 2021-10-14 21:25:18
 * @LastEditTime: 2021-11-08 10:39:34
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalTemplate\components\templateStep.vue
-->

<template>
  <div class="showProcess">
    <ul>
      <li class="list" v-for="(item, index) in data" :key="index" @click="Alert(item.msg)">
        <div class="ProcessBox" :class="{ active: item.result }">
          <div class="process-tips">
            <div class="process-index">{{ index + 1 }}</div>
            <div
              class="process-result"
              :class="{ pass: item.result == 'pass', fail: item.result == 'reject' }"
            >
              {{
                item.result == 'pass'
                  ? $t('通过')
                  : item.result == 'reject'
                  ? $t('驳回')
                  : $t('待审批')
              }}
            </div>
          </div>
          <p class="process-name">
            <label>{{ $t('审批人') }}：</label><span>{{ item.name }}</span>
          </p>
          <p class="process-time" v-if="item.endTime">
            <label>${{ $t('审批时间') }}：</label><span>{{ item.endTime }}</span>
          </p>
          <div class="_line" v-if="index != data.length - 1">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: {
    step: {
      type: Number,
      default: 1
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {
    Alert(msg) {
      if (!msg) return
      this.$dialog({
        data: {
          title: this.$t('审批意见'),
          message: msg
        },
        success: () => {}
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.showProcess {
  box-sizing: border-box;
  padding: 20px 0;
  width: 100%;
  background-color: #ffffff;
  overflow: auto;
  ul {
    display: flex;
    align-items: center;
    justify-content: center;
    width: max-content;
    .list {
      margin-right: 40px;
      height: 70px;
      .ProcessBox {
        width: 180px;
        display: flex;
        // align-items: center;
        justify-content: center;
        flex-direction: column;
        font-weight: 400;
        // text-align: center;
        position: relative;
        .Process {
          width: 64px;
          height: 64px;
          background: #cccccc;
          border-radius: 50%;
          font-size: 14px;
          font-family: Impact;
          font-weight: 400;
          color: #ffffff;
          line-height: 64px;
          margin: 0 90px;
          img {
            width: 64px;
            height: 64px;
          }
        }
        .ProcessName {
          max-width: 165px;
          height: 16px;
          font-size: 16px;
          //   font-family: Source Han Sans CN;
          font-weight: 400;
          color: #818181;
          margin-top: 25px;
        }
        .active {
          background: #0852c4;
        }
        .actives {
          color: #0852c4;
        }
        ._line {
          display: flex;
          width: 40px;
          height: 4px;
          justify-content: space-between;
          align-items: center;
          position: absolute;
          right: 0;
          top: 10px;
          span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }
      .active.ProcessBox {
        .process-index {
          background: rgba(99, 134, 193, 1);
          color: #fff;
        }
        ._line {
          span {
            background: rgba(99, 134, 193, 1);
          }
        }
      }
    }
  }
}
/* 以下是关键部分 */
.Process {
  position: relative;
  /* 利用伪元素来添加左右横线 */
  &:before,
  &:after {
    content: '';
    width: 95px;
    height: 1px;
    border-bottom: 4px solid #ccc;
    display: inline-block;
    position: absolute;
    top: 30px;
  }
  &:before {
    left: 60px;
  }
  &:after {
    right: 60px;
  }
}
.active {
  &:before,
  &:after {
    border-bottom: 4px solid #0852c4;
  }
}
/* 利用伪类来控制样式去掉首尾的横线 */
.list:first-child {
  .Process {
    &::after {
      display: none;
    }
  }
}
.list:last-child {
  .Process {
    &::before {
      display: none;
    }
  }
}
.process-tips {
  display: flex;
  align-items: center;
  .process-index {
    width: 24px;
    height: 24px;
    color: rgba(99, 134, 193, 1);
    border: 1px solid rgba(99, 134, 193, 1);
    border-radius: 50%;
    line-height: 24px;
    text-align: center;
    margin-right: 15px;
  }
}
.ProcessBox {
  p {
    font-size: 12px;
    color: rgba(154, 154, 154, 1);
    margin-top: 10px;
    label {
      width: 60px;
      display: inline-block;
      text-align: right;
    }
  }
}
.pass {
  color: #4d841d;
  cursor: pointer;
}
.fail {
  color: #ed5633;
  cursor: pointer;
}
</style>
