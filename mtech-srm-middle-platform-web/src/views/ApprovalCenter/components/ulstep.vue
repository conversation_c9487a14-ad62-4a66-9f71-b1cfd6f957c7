<!--
 * @Author: your name
 * @Date: 2021-10-28 10:38:26
 * @LastEditTime: 2021-11-02 16:41:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalCenter\components\ulstep.vue
-->
<template>
  <ul class="clearfix">
    <li v-for="item in data" :key="item.id">
      <template v-if="item.users">
        <div class="outside">
          <div class="userWrap" v-for="(c, cx) in item.users" :key="cx">
            <p>
              {{ c.name
              }}<span
                v-show="item.status != 'none'"
                style="margin-left: 5px; cursor: pointer"
                @click="checkDeatil(c)"
                >{{ $t('查看') }}</span
              ><span
                v-show="item.status != 'none'"
                class="result"
                :class="{ pass: c.result == 'pass', reject: c.result == 'reject' }"
                >{{
                  c.result == 'pass' ? $t('通过') : c.result == 'reject' ? $t('驳回') : $t('待审批')
                }}</span
              >
            </p>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="outside inbok">
          <div class="userWrap">
            {{
              item.type == 'start'
                ? $t('开始')
                : item.type == 'end'
                ? $t('结束')
                : item.type == 'parallelGateway'
                ? $t('并行')
                : $t('进行中')
            }}
          </div>
        </div>
      </template>
      <ulstep :data="item.childrens" v-if="item.childrens.length > 0"></ulstep>
    </li>
  </ul>
</template>
<script>
export default {
  name: 'Ulstep',
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  methods: {
    checkDeatil(e) {
      this.$dialog({
        modal: () => import('../components/detail.vue'),
        data: {
          title: this.$t('审批详情'),
          data: e
        },
        success: () => {}
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.outside {
  border-radius: 5px;
  padding: 15px;
  border: 2px solid #00469c;
  display: inline-block;
  .userWrap {
    color: #00469c;
    //    background: #94a0b4;

    // padding: 15px;
    font-size: 12px;
    ._link {
      cursor: pointer;
      color: #00469c;
    }
    &:last-child {
      margin-right: 0;
    }
    p {
      margin: 3px 0;
    }
  }
}
.inbok {
  display: inline-block;
  padding: 15px;
}
.result {
  padding: 0 3px;
  color: #d3dce6;
  font-size: 18px;
  font-weight: bold;
}
.pass {
  color: #138347;
}
.reject {
  color: #ed5633;
}
</style>
