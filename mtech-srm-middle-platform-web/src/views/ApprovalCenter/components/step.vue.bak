<!--
 * @Author: your name
 * @Date: 2021-10-14 21:25:18
 * @LastEditTime: 2021-10-27 18:33:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalTemplate\components\templateStep.vue
-->

<template>
  <div class="showProcess">
    <ul v-for="(item, index) in data" :key="index">
      <template v-if="item.childrens && item.childrens[0].type != 'end' && index == 0">
        <li class="list" v-for="(c, cx) in item.childrens" :key="cx">
          <template v-for="(d, dx) in c.users">
            <div class="ProcessBox" :class="{ active: d.result }" :key="dx">
              <div class="process-tips">
                <div class="process-index">{{ dx + 1 }}</div>
                <div class="process-result">
                  {{
                    d.result == "pass"
                      ? "通过"
                      : d.result == "reject"
                      ? "驳回"
                      : "待审批"
                  }}
                </div>
              </div>
              <p class="process-name">
                <label>审批人：</label><span>{{ d.name }}</span>
              </p>
              <p class="process-time" v-if="d.endTime">
                <label>审批时间：</label><span>{{ d.endTime }}</span>
              </p>
              <div
                class="_line"
                v-if="
                  item.childrens && item.childrens[0].childrens[0].type != 'end'
                "
              >
                <span></span>
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </template>
          <step
            :data="item.childrens"
            v-if="
              item.childrens && item.childrens[0].childrens[0].type != 'end'
            "
          ></step>
        </li>
      </template>
    </ul>
  </div>
</template>
<script>
export default {
  name: "step",
  props: {
    step: {
      type: Number,
      default: 1,
    },
    data: {
      type: Array,
      default: () => {
        return [
          {
            id: "Event_0zy6xtn",
            type: "start",
            approval: null,
            name: null,
            createTime: null,
            status: "finish",
            users: null,
            next: ["Activity_0in13k9"],
            childrens: [
              {
                id: "Activity_0in13k9",
                type: "task",
                approval: "Sequential",
                name: "任务1",
                createTime: null,
                status: "running",
                users: [
                  {
                    id: "1399260348896878594",
                    name: this.$t("李长江"),
                    msg: null,
                    result: null,
                    endTime: null,
                  },
                ],
                next: ["Activity_0jx7aqt"],
                childrens: [
                  {
                    id: "Activity_0jx7aqt",
                    type: "task",
                    approval: "Sequential",
                    name: "任务2",
                    createTime: null,
                    status: "none",
                    users: [
                      {
                        id: "1438419253803479041",
                        name: this.$t("顾涛"),
                        msg: null,
                        result: null,
                        endTime: null,
                      },
                    ],
                    next: ["Event_0vsxt5p"],
                    childrens: [
                      {
                        id: "Event_0vsxt5p",
                        type: "end",
                        approval: null,
                        name: null,
                        createTime: null,
                        status: "none",
                        users: null,
                        next: [],
                        childrens: [],
                      },
                    ],
                  },
                ],
              },
              {
                id: "Activity_0in13k9",
                type: "task",
                approval: "Sequential",
                name: "任务1",
                createTime: null,
                status: "running",
                users: [
                  {
                    id: "1399260348896878594",
                    name: this.$t("李长江"),
                    msg: null,
                    result: null,
                    endTime: null,
                  },
                ],
                next: ["Activity_0jx7aqt"],
                childrens: [
                  {
                    id: "Activity_0jx7aqt",
                    type: "task",
                    approval: "Sequential",
                    name: "任务2",
                    createTime: null,
                    status: "none",
                    users: [
                      {
                        id: "1438419253803479041",
                        name: this.$t("顾涛"),
                        msg: null,
                        result: null,
                        endTime: null,
                      },
                    ],
                    next: ["Event_0vsxt5p"],
                    childrens: [
                      {
                        id: "Event_0vsxt5p",
                        type: "end",
                        approval: null,
                        name: null,
                        createTime: null,
                        status: "none",
                        users: null,
                        next: [],
                        childrens: [],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ];
      },
    },
  },
  data() {
    return {
      last: false,
    };
  },
  created() {
    console.log(this.data);
  },
};
</script>
<style lang="scss" scoped>
.showProcess {
  box-sizing: border-box;
  // padding: 20px 0;
  // width: 100%;
  background-color: #ffffff;
  ul {
    display: flex;
    align-items: center;
    justify-content: center;
    width: max-content;
    .list {
      // margin-right: 40px;
      display: flex;
      .ProcessBox {
        width: 178px;
        display: flex;
        // align-items: center;
        justify-content: center;
        flex-direction: column;
        font-weight: 400;
        // text-align: center;
        position: relative;
        .Process {
          width: 64px;
          height: 64px;
          background: #cccccc;
          border-radius: 50%;
          font-size: 14px;
          font-family: Impact;
          font-weight: 400;
          color: #ffffff;
          line-height: 64px;
          margin: 0 90px;
          img {
            width: 64px;
            height: 64px;
          }
        }
        .ProcessName {
          max-width: 165px;
          height: 16px;
          font-size: 16px;
          //   font-family: Source Han Sans CN;
          font-weight: 400;
          color: #818181;
          margin-top: 25px;
        }
        .active {
          background: #0852c4;
        }
        .actives {
          color: #0852c4;
        }
        ._line {
          display: flex;
          width: 40px;
          height: 4px;
          justify-content: space-between;
          align-items: center;
          position: absolute;
          right: 20px;
          top: 8px;
          span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: rgba(99, 134, 193, 0.1);
          }
        }
      }
      .active.ProcessBox {
        .process-index {
          background: rgba(99, 134, 193, 1);
          color: #fff;
        }
        ._line {
          span {
            background: rgba(99, 134, 193, 1);
          }
        }
      }
    }
  }
}
/* 以下是关键部分 */
.Process {
  position: relative;
  /* 利用伪元素来添加左右横线 */
  &:before,
  &:after {
    content: "";
    width: 95px;
    height: 1px;
    border-bottom: 4px solid #ccc;
    display: inline-block;
    position: absolute;
    top: 30px;
  }
  &:before {
    left: 60px;
  }
  &:after {
    right: 60px;
  }
}
.active {
  &:before,
  &:after {
    border-bottom: 4px solid #0852c4;
  }
}
/* 利用伪类来控制样式去掉首尾的横线 */
.list:first-child {
  .Process {
    &::after {
      display: none;
    }
  }
}
.list:last-child {
  .Process {
    &::before {
      display: none;
    }
  }
}
.process-tips {
  display: flex;
  align-items: center;
  .process-index {
    width: 24px;
    height: 24px;
    color: rgba(99, 134, 193, 1);
    border: 1px solid rgba(99, 134, 193, 1);
    border-radius: 50%;
    line-height: 24px;
    text-align: center;
    margin-right: 15px;
  }
}
.ProcessBox {
  p {
    font-size: 12px;
    color: rgba(154, 154, 154, 1);
    margin-top: 10px;
    label {
      width: 60px;
      display: inline-block;
      text-align: right;
    }
  }
}
</style>


