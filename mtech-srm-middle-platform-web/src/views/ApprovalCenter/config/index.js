import { i18n } from '@/main.js'
import columTime from '../components/columTime.vue'
import pointColumTime from '@/views/ApprovalCenter/components/pointColumTime'
/*
 * @Author: your name
 * @Date: 2021-10-12 15:43:41
 * @LastEditTime: 2021-11-01 16:27:37
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\views\ApprovalCenter\config\index.js
 */
// 待审批展示 字段
export const approvalListColumnData = [
  {
    field: 'approveTitle',
    headerText: i18n.t('审批标题'),
    width: 200
  },
  {
    field: 'businessKey',
    headerText: i18n.t('单据ID'),
    width: 200
  },
  {
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyEnterpriseName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },

  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    template: function () {
      return {
        template: columTime
      }
    }
  },
  {
    field: 'statusView',
    headerText: i18n.t('状态'),
    cssClass: ['waitingfor'],
    cellTools: [
      {
        id: 'audit',
        icon: 'icon_solid_Configuration',
        title: i18n.t('审批')
      }
    ]
  }
]
// 已审批展示字段
export const historyListColumnData = [
  {
    field: 'approveTitle',
    headerText: i18n.t('审批标题'),
    width: 200
  },
  {
    field: 'businessKey',
    headerText: i18n.t('单据ID'),
    width: 200
  },
  {
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyEnterpriseName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },

  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    template: function () {
      return {
        template: columTime
      }
    }
  },
  {
    field: 'nowPointView',
    headerText: i18n.t('当前节点')
  },
  {
    field: 'nowPointTime',
    headerText: i18n.t('时间'),
    template: function () {
      return {
        template: pointColumTime
      }
    }
  },
  {
    field: 'status',
    headerText: i18n.t('状态'),
    valueConverter: {
      type: 'map',
      fields: { text: 'label', value: 'status' },
      map: [
        { status: 0, label: i18n.t('申请中'), cssClass: ['btns warning'] },
        { status: 20, label: i18n.t('已通过'), cssClass: ['btns success'] },
        { status: 10, label: i18n.t('审批中'), cssClass: ['btns primiy'] },
        { status: 30, label: i18n.t('已驳回'), cssClass: ['btns error'] },
        { status: 40, label: i18n.t('已取消'), cssClass: ['btns cancel'] }
      ]
    },
    cellTools: [
      {
        id: 'preview',
        icon: 'icon_Hiddenpassword',
        title: i18n.t('查看')
      }
    ]
  }
]
const otherColumnData = [
  {
    field: 'approveTitle',
    headerText: i18n.t('审批标题'),
    width: 200
  },
  {
    field: 'businessKey',
    headerText: i18n.t('单据ID'),
    width: 200
  },
  {
    field: 'applyUserName',
    headerText: i18n.t('申请人')
  },
  {
    field: 'applyEnterpriseName',
    headerText: i18n.t('公司名称')
  },
  {
    field: 'applyDepName',
    headerText: i18n.t('申请部门')
  },
  {
    field: 'approveTypeView',
    headerText: i18n.t('审批类型')
  },
  {
    field: 'applyTime',
    headerText: i18n.t('申请时间'),
    template: function () {
      return {
        template: columTime
      }
    }
  },
  {
    field: 'statusView',
    headerText: i18n.t('状态'),
    cssClass: ['waitingfor top0']
  }
]
export const pageConfigOther = (api1, api2) => [
  {
    title: i18n.t('待审批'),
    toolbar: { useBaseConfig: false, tools: [[], ['Refresh']] },
    grid: {
      columnData: otherColumnData,
      asyncConfig: {
        url: api1,
        params: {}
      }
    }
  },
  {
    title: i18n.t('已审批'),
    toolbar: { useBaseConfig: false, tools: [[], ['Refresh']] },
    grid: {
      columnData: otherColumnData,
      asyncConfig: {
        url: api2,
        params: {}
      }
    }
  }
]
