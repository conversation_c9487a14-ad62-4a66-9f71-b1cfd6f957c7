import Vue from 'vue'
import { sso } from '@mtech-sso/single-sign-on'
import '@digis/component-props-state'
import App from './App.vue'
import Router from 'vue-router'
import routes from './router'
import { baseConfig } from '@mtech-common/http'
import moment from 'moment'
import API from './apis'
import store from './store'
import '@mtech-micro-frontend/vue-cli-plugin-micro/public-path.js'
import './mtechUi'
import '@/components/SvgIcon/index.js'
import '@/components/Progress/index.js'
import { setLocal } from '@mtech-ui/base'
import indexDB from '@digis/internationalization'
import { Theme } from '@mtech-common/utils'
import Deploy from '@mtech-form-design/deploy'
import waves from '@/directive/waves'

const i18n = indexDB.digisI18n(Vue, 'middle-platform')

export { i18n }
Vue.use(Router)
Vue.use(waves)
Vue.use(Deploy)
Vue.config.productionTip = false
Vue.prototype.$api = API
Vue.prototype.$store = store
Vue.prototype.$moment = moment

const internationlization = localStorage.getItem('internationlization')
if (internationlization === 'zh' || internationlization === 'zh-CH') {
  setLocal('zh-CN')
}

let router = null
let instance = null

function render(props = {}) {
  const { container, routerMode = 'hash', defaultPath } = props
  router = new Router({
    routes,
    mode: routerMode
  })

  router.beforeEach((to, from, next) => {
    next()
  })
  indexDB
    .layoutCreatLanguage()
    .then(() => {
      instance = new Vue({
        router,
        store,
        i18n,
        render: (h) => h(App)
      }).$mount(container ? container.querySelector('#middlePlatform') : '#middlePlatform')
    })
    .catch(() => {
      instance = new Vue({
        router,
        store,
        i18n,
        render: (h) => h(App)
      }).$mount(container ? container.querySelector('#middlePlatform') : '#middlePlatform')
    })

  if (defaultPath) {
    router.push(defaultPath)
  }
}

if (!window.__POWERED_BY_QIANKUN__) {
  sso()
  render()
  theme({
    entry: location.origin,
    container: document.head
  })
}

function storeTest(props) {
  const user = props.data.user ? props?.data?.user?.userInfo : props?.data?.app?.userInfo
  store.commit('setUser', user)
  props.onGlobalStateChange &&
    props.onGlobalStateChange(
      (value, prev) => console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev),
      true
    )
  props.setGlobalState &&
    props.setGlobalState({
      ignore: props.name,
      user: {
        name: props.name
      }
    })
}
function theme(props) {
  const { entry, container } = props

  const changeTheme = new Theme()

  const themeName = localStorage.getItem('mt-layout-theme') || 'default'

  changeTheme.add(themeName, container, entry)
}
// single-spa的生命周期函数
export async function bootstrap({ fns = [] } = {}) {
  Array.isArray(fns) &&
    fns.map((i) => {
      Vue.prototype[i.name] = i
    })
  console.log('%c ', 'color: green;', 'app bootstraped')
}

export async function mount(props) {
  // 在这里挂载vue
  storeTest(props)
  render(props)
  theme(props)
  console.log('%c ', 'color: green;', 'app mount', props)
}

export async function unmount() {
  // 在这里unmount实例的vue
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
  router = null
  console.log('%c ', 'color: green;', 'app unmount')
}

baseConfig.setDefault({ baseURL: '/api' })

baseConfig.addNotify({
  success: function (msg) {
    // 可以使用$toast组件，msg当前版本下默认为接口response.msg
    Vue.prototype.$toast({
      content: msg,
      type: 'success'
    })
  },
  error: function (msg) {
    Vue.prototype.$toast({
      content: msg,
      type: 'error'
    })
    Vue.prototype.$store.commit('endLoading')
  }
})
