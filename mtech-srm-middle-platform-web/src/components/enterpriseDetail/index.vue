<template>
  <div class="approve diolog">
    <BusinessContent :formData="formData" class="mb--20" />
    <ServiceInfo :formData="formData" class="mb--20" />
    <EnterpriseInfo :formData="formData" class="mb--20" />
    <InvoiceInfo :formData="formData" class="mb--20" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'
import BusinessContent from './BusinessContent.vue'
import ServiceInfo from './ServiceInfo.vue'
import EnterpriseInfo from './EnterpriseInfo.vue'
import InvoiceInfo from './InvoiceInfo.vue'

@Component({
  components: {
    BusinessContent,
    ServiceInfo,
    EnterpriseInfo,
    InvoiceInfo
  }
})
export default class EnterpriseDetail extends Vue {
  @Prop()
  enterpriseId!: string

  formData: any = {}

  created() {
    this.getEnterpriseDetail()
  }

  private getEnterpriseDetail() {
    this.$api.enterprise
      .enterpriseDetail({
        id: this.enterpriseId
      })
      .then((res: { code: number; data: any }) => {
        if (res.code === 200 && res.data) {
          const {
            businessStartDate = '',
            businessEndDate = '',
            enterpriseLogoFileId = '',
            // enterpriseInfoIdentityDTOList = [],
            enterpriseInfoInvoiceDTOList = [],
            longTermFlag,
            registerAddressCountryName = '',
            registerAddressProvinceName = '',
            registerAddressCityName = '',
            registerAddressDetail = '',
            operationAddressCountryName = '',
            operationAddressProvinceName = '',
            operationAddressCityName = '',
            operationAddressDetail = ''
          } = res.data

          this.formData = res.data
          // this.formData.identityCode = enterpriseInfoIdentityDTOList[0]?.identityCode
          // this.formData.identityTypeCode = enterpriseInfoIdentityDTOList[0]?.identityTypeCode
          this.formData.businessDateRange =
            businessStartDate + ' 至 ' + (longTermFlag === 1 ? '永久' : businessEndDate)
          this.formData.bankName =
            enterpriseInfoInvoiceDTOList && enterpriseInfoInvoiceDTOList[0]?.bankName
          this.formData.bankAccount =
            enterpriseInfoInvoiceDTOList && enterpriseInfoInvoiceDTOList[0]?.bankAccount
          this.formData.registerAddress =
            registerAddressCountryName +
            registerAddressProvinceName +
            registerAddressCityName +
            registerAddressDetail
          this.formData.operationAddress =
            operationAddressCountryName +
            operationAddressProvinceName +
            operationAddressCityName +
            operationAddressDetail

          this.setLogoFileId(enterpriseLogoFileId)
          this.setProofFileList()

          this.$emit('success', this.formData)
        }
      })
  }

  private setProofFileList() {
    ;(this.formData.enterpriseInfoProofFileDTOList || []).forEach(async (item: any) => {
      const url = await this.getImageUrl(item.fileId)
      this.$set(item, 'fileUrl', url)
    })
  }

  private async setLogoFileId(id: string) {
    const url = await this.getImageUrl(id)
    this.formData.enterpriseLogoFileId = url
  }

  private async getImageUrl(id: string) {
    const url = await this.$api.file.imageUrl({ id: id })
    return url?.data || ''
  }
}
</script>

<style lang="scss" scoped>
.mb--20 {
  margin-bottom: 20px;
}
</style>
