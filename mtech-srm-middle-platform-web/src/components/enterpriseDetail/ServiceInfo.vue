<template>
  <div class="business-content">
    <!-- 工商信息 -->
    <mt-accordion :dataSource="serviceDataSource"></mt-accordion>

    <mt-form id="PLATFORM-ENTERPRISE-DETAIL-SERVICE-INFO" style="display: none">
      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('企业类型')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="formData.enterpriseRegisterTypeName"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('所属行业')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="serviceInfo.industryList"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('服务区域')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="serviceInfo.areaList"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item label="主要产品/服务">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="serviceInfo.productList"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('企业标签')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="serviceInfo.labelList"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12"> </mt-col>
      </mt-row>
    </mt-form>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class ServiceInfo extends Vue {
  @Prop()
  formData!: any

  serviceDataSource: any[] = [
    {
      header: '服务信息',
      expanded: false,
      content: '#PLATFORM-ENTERPRISE-DETAIL-SERVICE-INFO'
    }
  ]

  get serviceInfo() {
    const { enterpriseInfoServiceConfDTOList = [] } = this.formData
    const info: any = {
      industryList: [],
      areaList: [],
      productList: [],
      labelList: []
    }
    enterpriseInfoServiceConfDTOList.forEach((item: any) => {
      if (item.serviceType === 1) {
        // 行业信息
        info.industryList.push(item.dictName)
      } else if (item.serviceType === 2) {
        // 产品/服务
        info.productList.push(item.dictName)
      } else if (item.serviceType === 3) {
        // 标签信息
        info.labelList.push(item.dictName)
      } else if (item.serviceType === 4) {
        // 服务区域
        info.areaList.push(item.dictName)
      }
    })

    return {
      industryList: info.industryList.join(),
      areaList: info.areaList.join(),
      productList: info.productList.join(),
      labelList: info.labelList.join()
    }
  }

  mounted() {
    // this.serviceDataSource[0].content = this.$refs.service
  }
}
</script>
