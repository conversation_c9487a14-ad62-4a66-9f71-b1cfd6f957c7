<template>
  <div class="enterprise-info--wrap">
    <!--  发票信息 -->
    <mt-accordion :dataSource="enterpriseDataSource"></mt-accordion>

    <mt-data-grid
      id="PLATFORM-ENTERPRISE-DETAIL-Invoice-INFO"
      style="display: none"
      class="data-grid--content"
      :dataSource="dataSource"
      :columnData="columnData"
      :allowPaging="false"
    ></mt-data-grid>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from '@mtech/vue-property-decorator'
import { invoiceInfoColumnData } from './config/index'

@Component({})
export default class InvoiceInfo extends Vue {
  @Prop()
  formData!: any

  enterpriseDataSource: any[] = [
    {
      header: ' 发票信息',
      expanded: false,
      content: '#PLATFORM-ENTERPRISE-DETAIL-Invoice-INFO'
    }
  ]

  get dataSource() {
    const { enterpriseInfoInvoiceDTOList } = this.formData

    return (enterpriseInfoInvoiceDTOList || []).map((item: any) => {
      const {
        registerAddressCountryName = '',
        registerAddressProvinceName = '',
        registerAddressCityName = '',
        registerAddressDetail = ''
      } = item

      return {
        ...item,
        registerAddress:
          registerAddressCountryName +
          registerAddressProvinceName +
          registerAddressCityName +
          registerAddressDetail
      }
    })
  }

  columnData = invoiceInfoColumnData
}
</script>

<style scoped lang="scss"></style>
