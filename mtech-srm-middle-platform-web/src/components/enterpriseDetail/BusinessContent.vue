<template>
  <div class="business-content">
    <!-- 工商信息 -->
    <mt-accordion :dataSource="businessDataSource"></mt-accordion>

    <mt-form id="PLATFORM-ENTERPRISE-DETAIL-BUSINESS-CONTENT" style="display: none">
      <mt-row :gutter="20">
        <mt-col :span="6">
          <div class="logo-image-wrap">
            <img :src="formData.enterpriseLogoFileId" />
          </div>
        </mt-col>

        <mt-col :span="18">
          <mt-row :gutter="20">
            <mt-col :span="12">
              <mt-form-item :label="$t('企业名称')">
                <mt-input
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                  v-model="formData.enterpriseName"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item :label="$t('企业中文简称')">
                <mt-input
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                  v-model="formData.enterpriseFormerName"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row :gutter="20">
            <mt-col :span="12">
              <mt-form-item :label="$t('企业英文全称')">
                <mt-input
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                  v-model="formData.enterpriseFullEnglishName"
                ></mt-input>
              </mt-form-item>
            </mt-col>
            <mt-col :span="12">
              <mt-form-item :label="$t('曾用名')">
                <mt-input
                  :readonly="true"
                  :show-clear-button="false"
                  type="text"
                  v-model="formData.enterpriseFormerName"
                ></mt-input>
              </mt-form-item>
            </mt-col>
          </mt-row>

          <mt-row :gutter="20">
            <mt-col :span="12"> </mt-col>
          </mt-row>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('企业身份代码类型')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="formData.identityTypeName"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('企业身份代码')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="formData.identityCode"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('企业性质')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="formData.enterpriseRegisterTypeName"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('法人代表')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="formData.corporation"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('注册资金')">
            <mt-input
              class="registerCapital"
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :value="`${formData.registerCapital} ${formData.capitalCurrency}`"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('成立时间')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              v-model="formData.enterpriseRegisteredDate"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="12">
          <mt-form-item :label="$t('营业期限')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :value="formData.businessDateRange"
            ></mt-input>
          </mt-form-item>
        </mt-col>
        <mt-col :span="12">
          <mt-form-item :label="$t('核准日期')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :value="formData.issuranceDate"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="24">
          <mt-form-item :label="$t('注册地址')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :value="formData.registerAddress"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="24">
          <mt-form-item :label="$t('经营地址')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :value="formData.operationAddress"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>

      <mt-row :gutter="20">
        <mt-col :span="24">
          <mt-form-item :label="$t('经营范围')">
            <mt-input
              :readonly="true"
              :show-clear-button="false"
              type="text"
              :value="formData.businessScope"
            ></mt-input>
          </mt-form-item>
        </mt-col>
      </mt-row>
    </mt-form>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from '@mtech/vue-property-decorator'

@Component({
  components: {}
})
export default class BusinessContent extends Vue {
  @Prop()
  formData!: any

  businessDataSource: any[] = [
    {
      header: '工商信息',
      expanded: false,
      content: '#PLATFORM-ENTERPRISE-DETAIL-BUSINESS-CONTENT'
    }
  ]

  mounted() {
    // this.businessDataSource[0].content = this.$refs.business
  }
}
</script>

<style lang="scss" scoped>
.logo-image-wrap {
  width: 180px;
  height: 120px;
  text-align: center;
  background: #fbfcfd;
  border: 1px dashed #e8e8e8;

  img {
    max-height: 100%;
    max-width: 100%;
  }
}
</style>
