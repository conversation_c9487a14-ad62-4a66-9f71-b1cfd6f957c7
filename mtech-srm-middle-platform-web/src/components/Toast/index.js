import Vue from 'vue'
import MtToast from './base-ejs.vue'

const MtToastConstructor = Vue.extend(MtToast)

const NAME = '$toast'
const instances = []
let seed = 1
const removeInstance = (instance) => {
  if (!instance) return
  const len = instances.length
  const index = instances.findIndex((inst) => instance.id === inst.id)

  if (index > -1) {
    instances.splice(index, 1)
    if (len <= 1) return
    const removeHeight = instance.vm.$el.offsetHeight
    for (let i = index; i < len - 1; i++) {
      instances[i].verticalOffset = parseInt(instances[i].verticalOffset) - removeHeight
    }
  }
}
const COMPONENT = (options) => {
  const { ...rest } = options
  const instance = new MtToastConstructor({
    propsData: {
      ...rest
    }
  })
  instance.id = `mt-toast-${seed++}`
  instance.vm = instance.$mount()
  document.body.appendChild(instance.vm.$el)

  instance.vm.visible = true
  let verticalOffset = 0
  instances.forEach((item) => {
    verticalOffset += item.$el.offsetHeight
  })
  instance.verticalOffset = verticalOffset
  instances.push(instance)
  instance.vm.$on('closed', () => {
    console.warn('mt-toast-closed')
    instance.vm.$destroy()
  })

  instance.vm.$on('close', () => {
    console.warn('mt-toast-close')
    instance.vm.visible = false
    removeInstance(instance)
    document.body.removeChild(instance.vm.$el)
  })

  return instance.vm
}
export { NAME, COMPONENT }
