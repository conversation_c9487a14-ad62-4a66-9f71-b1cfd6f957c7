<template>
  <div class="icon-button" :class="{ disabled: disabled }" @click="handleClick">
    <mt-icon v-if="icon" :name="icon" />
    <span class="icon-button-text">{{ text }}</span>
  </div>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.$emit('click')
    }
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  cursor: not-allowed !important;
  color: #ccc !important;
}
.icon-button {
  display: flex;
  align-items: center;
  margin-right: 16px;
  background: #19a2d5;
  padding: 5px 8px;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
  &-text {
    margin-left: 4px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    word-break: keep-all;
  }
}
</style>
