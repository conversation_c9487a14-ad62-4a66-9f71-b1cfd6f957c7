<template>
  <div class="cell-upload" :id="'cell-upload-'">
    <div @click="showFileBaseInfo" class="cell-operable-title">
      <!-- {{ uploadFileList | listNumFormat }} -->
      {{ uploadInfo }}
    </div>

    <!-- 需求附件弹窗 -->
    <uploader-dialog
      @change="fileChange"
      @confirm="setFile"
      v-bind="$attrs"
      v-on="$listeners"
      ref="uploaderDialog"
    ></uploader-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import UploaderDialog from './uploaderDialog'

// 草稿或审批拒绝状态才能再次上传
export default {
  name: 'CellUpload',
  components: {
    UploaderDialog
  },
  // props: ['fileList'],
  props: {
    fileList: {
      // 查看状态下传入文件数据
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      headerStatus: null, // 0-草稿，1. 待审批，2. 审批通过，3. 审批拒绝，4. 关闭
      uploadFileList: [], // 上传的附件(初始值赋值之前上传过的)
      uploadInfo: ''
    }
  },
  watch: {
    fileList(val) {
      console.log('fileList', val)
      this.uploadFileList = val
    },
    uploadFileList: {
      handler(val) {
        if (val && val.length > 0) {
          this.uploadInfo = `${this.$t('文件数')} ${val.length}，${this.$t('点此操作')}`
        } else {
          this.uploadInfo = this.$t('点击上传')
        }
      },
      deep: true,
      immediate: true
    }
  },
  filters: {
    byteToKB: (value) => Math.floor((value / 1024) * 100) / 100
    // listNumFormat(value) {
    //   console.log('value', value)
    //   if (value && value.length > 0) {
    //     return `文件数 ${value.length}，点此操作`;
    //   } else {
    //     return '点击上传'
    //   }
    // }
  },
  mounted() {
    this.$nextTick(() => {
      console.log('我是mounted----', this.fileList)
      if (this.fileList && this.fileList.length) {
        this.uploadFileList = this.fileList
      }
    })
  },
  methods: {
    showFileBaseInfo() {
      console.log('弹窗可预览的图片', this.uploadFileList)
      const dialogParams = {
        fileData: cloneDeep(this.uploadFileList),
        isView: false, // 是否可上传
        required: false, // 是否必须
        title: this.$t('附件')
      }
      this.$refs.uploaderDialog.dialogInit(dialogParams)
    },

    // 行附件弹窗内文件变动
    fileChange(data) {
      console.log('fileChange', data)
      this.uploadFileList = data
      this.$emit('setFiles', data)
    },
    // 点击行附件上传的确认按钮
    setFile() {
      console.log('点击了确认', this.uploadFileList)
      // this.fileList = this.uploadFileList
      // this.$emit('setFiles',this.uploadFileList)
    }
  }
}
</script>

<style scoped>
.cell-operable-title {
  display: inline-block;
  color: var(--accent);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
}
</style>
