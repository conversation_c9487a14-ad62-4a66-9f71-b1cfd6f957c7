import { i18n } from '@/main.js'
import Vue from 'vue'
import Component from './base'
const extendConstructor = Vue.extend(Component)
const NAME = '$dialog'

const COMPONENT = (options) => {
  const { ...rest } = options
  const instance = new extendConstructor({
    i18n,
    propsData: {
      ...rest
    }
  })
  instance.id = 'his-compose'
  instance.vm = instance.$mount()
  document.body.appendChild(instance.vm.$el)
  // document.getElementById("mt-main-container").appendChild(instance.vm.$el);

  instance.vm.visible = true

  instance.vm.$on('closed', () => {
    instance.vm.$destroy()
  })

  instance.vm.$on('close', (e) => {
    console.warn('compose-closed', e)
    instance.vm.visible = false
    instance.vm.$el.remove()
  })

  instance.vm.$on('confirm', (e, callback) => {
    if (e) {
      console.warn('compose-confirm', e)
    }
    instance.vm.visible = false
    instance.vm.$el.remove()
    callback(e)
  })

  return instance.vm
}
export { NAME, COMPONENT }
