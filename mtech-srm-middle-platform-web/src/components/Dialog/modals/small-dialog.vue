<template>
  <mt-dialog
    ref="dialog"
    :css-class="propsData && propsData.cssClass"
    :buttons="propsData && propsData.buttons"
    :header="header"
    :open="onOpen"
  >
    <div class="dialog-content">
      {{ propsData && propsData.message }}
    </div>
  </mt-dialog>
</template>

<script>
export default {
  data() {
    return {
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.confirm,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  /**
   * 小弹窗：白色头部，且头部加个icon，宽度500px，加cssClass为small-dialog
   * 大弹窗：默认弹窗，蓝色的头部，宽900
   * props需要传递：
   *    title 标题
   *    message（如果只有一行文字的话）
   *    cssClass
   *    buttons 按钮，可不传，有默认值
   */
  props: {
    modalData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    propsData() {
      return {
        ...this.modalData,
        width: '500px',
        buttons: this.modalData.buttons || this.buttons,
        cssClass: ('small-dialog ' + this.modalData.cssClass).trim()
      }
    },
    header() {
      const _commonHeader = this.modalData.title
      // let _smallHeader =
      //   '<i class="mt-icons mt-icon-icon_solid_Warning"></i><div class="header-text" id="_title">' +
      //   _commonHeader +
      //   "</div>";
      return _commonHeader
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
  },
  methods: {
    confirm() {
      // 单行操作 如果有确认按钮，可以传执行方法，这样可复用
      if (Object.prototype.hasOwnProperty.call(this.modalData, 'confirm')) {
        this.modalData.confirm().then((res) => {
          this.$toast({
            content: res.message ? res.message : '操作成功',
            type: 'success'
          })
          this.$emit('confirm-function', res)
        })
      } else {
        this.$emit('confirm-function')
      }
    },
    cancel() {
      this.$emit('cancel-function')
    },
    onOpen: function (args) {
      args.preventFocus = true
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-top: 18px;
  font-size: 16px;
}
</style>
