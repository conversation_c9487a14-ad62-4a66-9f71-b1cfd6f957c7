<template>
  <div class="strategy-content mt-flex">
    <div class="left-nav-tabs">
      <ul class="nav-container mt-flex-direction-column">
        <li
          :class="['nav-item', { active: index == activeTab }]"
          v-for="(tab, index) in leftNavList"
          :key="index"
          @click="activeTab = index"
        >
          <div class="title">{{ tab }}</div>
          <mt-icon name="a-icon_Packup" />
        </li>
      </ul>
    </div>
    <div class="content-container">
      <div class="common-config" v-show="activeTab == 0">
        <div class="config-title">{{ $t('基础配置') }}</div>
        <div class="config-content">
          <div class="config-item">
            <div class="config-option strategy-flow">
              <div class="flow-name-label">
                <div class="circle"></div>
              </div>
              <div class="flow-name-description">
                <div class="flow-name">{{ $t('工作流名称') }}</div>
                <div class="flow-description">{{ $t('工作流描述') }}</div>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">询标处理流程:</div>
              <div class="strategy-common-container">
                <mt-select
                  css-class="strategy-element"
                  value="工作流"
                  :data-source="flowList"
                ></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">供应商数量至少:</div>
              <div class="strategy-common-container">
                <mt-input-number
                  css-class="strategy-element"
                  value="0"
                  :show-clear-button="false"
                ></mt-input-number>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">供应商数量至多:</div>
              <div class="strategy-common-container">
                <mt-input-number
                  css-class="strategy-element"
                  value="0"
                  :show-clear-button="false"
                ></mt-input-number>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="common-config" v-show="activeTab == 1">
        <div class="config-title">{{ $t('跨轮次询报价配置') }}</div>
        <div class="config-content">
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">允许报价轮数:</div>
              <div class="strategy-common-container">
                <mt-select
                  css-class="strategy-element"
                  v-model="rfxPrice"
                  :data-source="rfxPriceList"
                ></mt-select>
              </div>
            </div>
            <div class="config-option" v-if="rfxPrice == '指定轮次'">
              <div class="strategy-common-label middle">轮次:</div>
              <div class="strategy-common-container">
                <mt-input-number css-class="strategy-element"></mt-input-number>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">报价触发下一轮条件流程:</div>
              <div class="strategy-common-container">
                <mt-select
                  css-class="strategy-element"
                  v-model="nextPrice"
                  :data-source="nextRfxList"
                ></mt-select>
              </div>
            </div>
            <div class="config-option strategy-rule" v-if="nextPrice == '自动'">
              <div class="rule-label">条件详情:</div>
              <div class="rule-container">
                <simple-rule></simple-rule>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">增加供应商:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">淘汰供应商:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">禁用/启用供应商:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="common-config" v-show="activeTab == 1">
        <div class="config-title">{{ $t('轮次内询报价控制') }}</div>
        <div class="config-content">
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">按寻源行/成本模型报价:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">每轮报价次数:</div>
              <div class="strategy-common-container">
                <mt-select
                  css-class="strategy-element"
                  v-model="eachRfx"
                  :data-source="eachRfxList"
                ></mt-select>
              </div>
            </div>
            <div class="config-option" v-if="eachRfx == '指定'">
              <div class="strategy-common-label middle">指定:</div>
              <div class="strategy-common-container">
                <mt-input-number css-class="strategy-element"></mt-input-number>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">报价范围:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">报价方式:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">报价时公开:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">允许还价:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option strategy-rule">
              <div class="rule-label large">报价结束:</div>
              <div class="rule-container">
                <simple-rule></simple-rule>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="common-config" v-show="activeTab == 2">
        <div class="config-title">{{ $t('开标配置') }}</div>
        <div class="config-content">
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">是否密封价格:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">开标时间:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
            <div class="config-option">
              <div class="strategy-common-label middle">开标时间:</div>
              <div class="strategy-common-container">
                <mt-date-picker css-class="strategy-element"></mt-date-picker>
              </div>
            </div>
          </div>
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">开标密码:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="common-config" v-show="activeTab == 3">
        <div class="config-title">{{ $t('评标配置') }}</div>
        <div class="config-content">
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">评标方式:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
            <div class="mt-flex-direction-column">
              <div class="config-option">
                <div class="strategy-common-label middle">评标顺序:</div>
                <div class="strategy-common-container">
                  <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
                </div>
              </div>
              <div class="config-option strategy-score">
                <div class="strategy-score-desc">
                  <div class="box mt-flex-direction-column">
                    <div class="box-item">
                      <div class="circle orange"></div>
                      <span>{{ $t('技术') }}</span>
                      <span>{{ getSliderValue }}%</span>
                    </div>
                    <div class="box-item">
                      <div class="circle"></div>
                      <span>{{ $t('商务') }}</span>
                      <span>{{ 100 - getSliderValue }}%</span>
                    </div>
                  </div>
                </div>
                <div class="strategy-score-slider">
                  <span class="slider-value left-value">{{ $t('技术') }}</span>
                  <mt-slider
                    :step="5"
                    :value="getSliderValue"
                    @change="changedSlider"
                    @changed="changedSlider"
                    css-class="strategy-slider"
                  ></mt-slider>
                  <span class="slider-value right-value">{{ $t('商务') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="common-config" v-show="activeTab == 4">
        <div class="config-title">{{ $t('决标策略') }}</div>
        <div class="config-content">
          <div class="config-item">
            <div class="config-option">
              <div class="strategy-common-label large">决标策略:</div>
              <div class="strategy-common-container">
                <mt-select css-class="strategy-element" :data-source="[]"></mt-select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SimpleRule from './SimpleRule.vue'
export default {
  name: 'StrategyDetail',
  components: { SimpleRule },
  props: {
    activeTab: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      leftNavList: ['基本配置', '招投标配置', '开标配置', '评标配置', '决标配置'],
      sliderValue: 30,
      flowList: ['工作流', '工作流2'],
      rfxPrice: '',
      rfxPriceList: ['不限制', '指定轮次'],
      nextPrice: '',
      nextRfxList: ['手动', '自动'],
      eachPrice: '',
      eachRfxList: ['不限制', '指定'],
      groupLinkList: [
        { text: '或', value: 'or' },
        { text: '且', value: 'and' }
      ],
      queryFields: { text: 'headerText', value: 'field' },
      ruleData: [
        {
          groupLeft: '(',
          dataSource: 'OrderID_1',
          dataValue: '22',
          symbol: '相同',
          groupRight: '',
          groupLink: 'OR'
        },
        {
          groupLeft: '',
          dataSource: 'OrderID_2',
          dataValue: '选项B',
          symbol: '=',
          groupRight: '',
          groupLink: 'OR'
        }
      ],
      queryDataSource: [
        {
          field: 'OrderID_1',
          headerText: '输入类型-1',
          type: 'text'
        },
        {
          field: 'OrderID_2',
          headerText: '枚举类型-2',
          type: 'select',
          source: ['选项A', '选项B', '选项C']
        },
        {
          field: 'OrderID_3',
          headerText: 'Map类型-3',
          type: 'select',
          source: [
            { key: 'k1', value: '数据1' },
            { key: 'k2', value: '数据2' },
            { key: 'k3', value: '数据3' }
          ],
          fields: { text: 'value', value: 'key' }
        }
      ]
    }
  },
  computed: {
    getSliderValue() {
      return this.sliderValue
    },
    getDataSourceObject() {
      return (_rule) => {
        const _dataSource = _rule.dataSource
        const _find = this.queryDataSource.filter((e) => {
          return e[this.queryFields.value] === _dataSource
        })
        return _find.length ? _find[0] : {}
      }
    },
    showElementByType() {
      return (rule, type) => {
        const _dataSourceObject = this.getDataSourceObject(rule)
        if (_dataSourceObject && Object.prototype.hasOwnProperty.call(_dataSourceObject, 'type')) {
          return _dataSourceObject.type === type
        } else {
          return type === 'text'
        }
      }
    }
  },
  mounted() {},
  methods: {
    changedSlider(e) {
      this.sliderValue = e.value
    }
  }
}
</script>
<style lang="scss" scoped>
.strategy-common-label {
  width: 70px;
  font-size: 14px;
  font-family: PingFangSC;
  font-weight: 500;
  color: #465b73;
  text-align: right;
  display: inline-block;
  margin-right: 20px;
  position: relative;
  // display: flex;
  // align-items: center;
  // justify-content: flex-end;
  &.middle {
    width: 100px;
  }
  &.large {
    width: 170px;
    margin-right: 30px;
  }
}
.strategy-common-container {
  width: 270px;
  display: inline-block;
  height: 40px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 0 20px;
}
/deep/.strategy-element {
  border: none;
  border-color: transparent !important;
  position: relative;
  top: 4px;
  &:before,
  &:after {
    display: none;
  }
  .e-float-line {
    display: none;
  }
  .e-control {
    color: #292929;
    border: none;
    border-color: transparent !important;
  }
  .e-spin-down {
    position: absolute;
    right: -5px;
    top: 9px;
  }
  .e-spin-up {
    position: absolute;
    right: -1px;
    top: -4px;
  }
  .e-ddl-icon,
  .e-date-icon {
    position: absolute;
    right: 0;
    top: 3px;
  }
}
.strategy-content {
  background: #e8e8e8;
  flex: 1;
  min-height: 0;
  .left-nav-tabs {
    flex-shrink: 0;
    background: #fff;
    width: 172px;
    margin-right: 10px;
    .nav-container {
      .nav-item {
        height: 54px;
        padding: 0 20px;
        position: relative;
        background: #ffffff;
        color: #232b39;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .mt-icons {
          display: none;
        }
        &.active,
        &:hover {
          border: 1px solid #e8e8e8;
          color: #6386c1;
          background: #f5f6f9;
          .mt-icons {
            display: block;
          }
        }
      }
    }
  }
  .content-container {
    flex: 1;
    background: #fff;
    padding: 20px;
    overflow-y: auto;
    .common-config {
      .config-title {
        font-size: 16px;
        color: #292929;
        display: inline-block;
        padding-left: 13px;
        position: relative;
        margin-bottom: 20px;

        &:before {
          content: '';
          position: absolute;
          width: 3px;
          height: 14px;
          background: #6386c1;
          border-radius: 0 2px 2px 0;
          left: 0;
          top: 2px;
        }
      }
    }
    .config-content {
      .config-item {
        margin-bottom: 20px;
        display: flex;
        .config-option {
          &.strategy-flow {
            display: flex;
            .flow-name-label {
              width: 170px;
              position: relative;
              margin-right: 30px;
              display: flex;
              align-items: center;
              .circle {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                border: 2px solid #6386c1;
                position: absolute;
                right: 0;
              }
            }
            .flow-name-description {
              height: 34px;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .flow-name {
                font-size: 14px;
                font-family: PingFangSC;
                font-weight: 500;
                color: rgba(35, 43, 57, 1);
              }
              .flow-description {
                font-size: 12px;
                font-family: PingFangSC;
                font-weight: 500;
                color: rgba(154, 154, 154, 1);
              }
            }
          }
          &.strategy-rule {
            display: flex;
            .rule-label {
              width: 100px;
              position: relative;
              margin-right: 20px;
              text-align: right;
              line-height: 40px;
              &.large {
                width: 170px;
                margin-right: 30px;
              }
            }
            .rule-container {
            }
          }
          &.strategy-score {
            margin-top: 10px;
            display: flex;
            .strategy-score-desc {
              width: 100px;
              margin-right: 20px;
              position: relative;
              .box {
                width: 100px;
                height: 60px;
                transform: scale(0.83);
                border: 1px solid #e8e8e8;
                position: absolute;
                right: -10px;
                padding: 12px;
                font-size: 12px;
                font-weight: 500;
                justify-content: space-between;
                .box-item {
                  display: flex;
                  justify-content: space-between;
                  .circle {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    border: 2px solid #6386c1;
                    display: inline-block;
                    &.orange {
                      border-color: #eda133;
                    }
                  }
                }
              }
            }
            .strategy-score-slider {
              width: 270px;
              position: relative;
              .slider-value {
                font-size: 12px;
                color: #9daabf;
                position: absolute;
                top: 5px;
                &.right-value {
                  right: 0;
                }
              }
              /deep/ .e-slider-track {
                height: 6px;
                background: rgba(250, 250, 250, 1);
                border-radius: 4px;
                box-shadow: inset 0 1px 2px 0 rgba(232, 232, 232, 1);
              }
              /deep/.e-handle {
                width: 24px;
                height: 24px;
                background: radial-gradient(
                  circle at 41.61% 37.86%,
                  rgb(243, 186, 103) 0%,
                  rgb(237, 161, 51) 100%
                );
                border: 2px solid rgba(0, 0, 0, 0.1);
                top: calc(50% - 12px);
                &.e-large-thumb-size {
                  transform: scale(1);
                }
              }
            }
          }
        }
      }
    }
  }
}
.common-rule-config {
  background: #ffffff;

  .rule-item-container {
    background: #ffffff;
    height: 40px;
    width: 350px;
    border-radius: 4px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
    margin-top: 10px;

    &:first-of-type {
      margin-top: 0;
    }

    .rule-item {
      display: flex;
      position: relative;
      justify-content: center;
      align-items: center;

      &.split:after {
        content: '';
        height: 20px;
        border-left: 1px solid #e8e8e8;
        position: relative;
        margin: 0 10px;
      }

      .rule-element {
        border: none;
        border-color: transparent !important;
        &:before,
        &:after {
          display: none;
        }
        .e-float-line {
          display: none;
        }
        .e-control {
          color: #292929;
          border: none;
          border-color: transparent !important;
        }
        &.e-date-wrapper,
        &.e-time-wrapper {
          .e-icons {
            display: none;
          }
        }
        .e-ddt-icon,
        .e-ddl-icon {
          &:before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-top: 4px solid #292929;
            border-bottom: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            left: 50%;
            top: 50%;
          }
        }
      }
    }

    .rule-item-handler {
      background: #dedede;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px 0 0 4px;
      cursor: move;
      &:before {
        content: '';
        height: 16px;
        border-left: 1px solid #ffffff;
        margin-left: 3px;
      }
      &:after {
        content: '';
        height: 16px;
        border-left: 1px solid #ffffff;
        margin: 0 3px;
      }

      &:hover {
        background: #6386c1;
      }
    }

    .rule-link {
      .select-container {
        width: 44px;
        .rule-element {
          .e-control {
            text-align: right;
            color: #9a9a9a;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #9a9a9a;
            }
          }
        }
      }
      &.last-link {
        margin-right: 10px;
        .rule-element {
          .e-control {
            color: #eda133;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #eda133;
            }
          }
        }
      }
      &.last-item {
        visibility: hidden;
      }
    }

    .rule-symbol {
      .select-container {
        width: 65px;
        .rule-element {
          .e-control {
            text-align: center;
            color: #00469c;
          }
          .e-ddl-icon {
            &:before {
              border-top-color: #00469c;
            }
          }
        }
      }
      &:after {
        visibility: hidden;
      }
    }

    .rule-data-source {
      .select-container {
        width: 150px;
      }
    }

    .rule-data-value {
      min-width: 20px;
      flex: 1;
      &.simple-data-value {
        visibility: hidden;
        &:after {
          visibility: hidden;
        }
      }
    }

    .rule-icons {
      position: absolute;
      top: 10px;
      width: 50px;
      right: -70px;
      justify-content: space-between;
      img {
        height: 20px;
        width: 20px;
        cursor: pointer;
      }
    }
  }

  .simple-container {
    .rule-data-source {
      .select-container {
        width: 240px;
        .e-control {
          padding-left: 20px;
        }
      }
    }
    .rule-data-value {
      &::after {
        visibility: hidden;
      }
    }
  }
}
</style>
