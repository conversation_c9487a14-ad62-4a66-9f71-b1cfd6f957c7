import Vue from 'vue'
import MtTooltip from './base.vue'

const MtTooltipConstructor = Vue.extend(MtTooltip)

const NAME = '$tooltip'
const COMPONENT = (options) => {
  const { ...rest } = options
  const instance = new MtTooltipConstructor({
    propsData: {
      ...rest
    }
  })
  instance.vm = instance.$mount()
  options.target.after(instance.vm.$el)
  // document.body.appendChild(instance.vm.$el);

  instance.vm.visible = true
  instance.vm.$on('closed', () => {
    console.warn('mt-tooltip-closed')
    instance.vm.$destroy()
  })

  instance.vm.$on('close', () => {
    console.warn('mt-tooltip-close')
    instance.vm.visible = false
    instance.vm.$el.remove()
  })

  return instance.vm
}
export { NAME, COMPONENT }
