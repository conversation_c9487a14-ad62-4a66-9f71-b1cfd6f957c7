<template>
  <div class="column-time-template">
    <p>{{ time }}</p>
    <p>{{ date }}</p>
  </div>
</template>
<script>
export default {
  name: 'ColumnTimeTemplate',
  data() {
    return {
      data: {}
    }
  },

  computed: {
    field() {
      return this.data.column?.field
    },
    dateTime() {
      return this.data[this.field] || ''
    },
    date() {
      return this.dateTime.split(' ')[0] || ''
    },
    time() {
      return this.dateTime.split(' ')[1] || ''
    }
  }
}
</script>
<style lang="scss" scoped></style>
