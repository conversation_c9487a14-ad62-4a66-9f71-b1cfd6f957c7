<!-- 进度条 -->
<template>
  <div class="progress-container">
    <div class="progress-bar">
      <div
        class="progress"
        :style="{
          width: progressBarWidth,
          'background-color': percent === 100 ? '#33b593' : color
        }"
      ></div>
    </div>
    <div class="progress-text">{{ progressBarWidth }}</div>
  </div>
</template>

<script>
export default {
  props: {
    percent: {
      type: Number,
      required: true,
      default: 100
    },
    color: {
      type: String,
      default: '#0ca8f2'
    }
  },
  computed: {
    progressBarWidth() {
      const per = isNaN(this.percent) ? 0 : Number(this.percent || 0)
      return `${(per / 100) * 100}%`
    }
  }
}
</script>

<style scoped>
.progress-container {
  display: flex;
  align-items: center;
}
.progress-bar {
  background-color: #f5f5f5;
  height: 10px;
  width: 100%;
  border-radius: 100px;
}
.progress {
  height: 100%;
  border-radius: 100px;
  transition: width 0.5s ease-in-out;
}
.progress-text {
  margin-left: 8px;
}
</style>
