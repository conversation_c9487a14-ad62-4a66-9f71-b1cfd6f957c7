!(function (e) {
  var t,
    n,
    o,
    i,
    c,
    d,
    s =
      '<svg><symbol id="icon-remove-rule" viewBox="0 0 1024 1024"><path d="M512 0a512 512 0 1 0 512 512A512 512 0 0 0 512 0z m0 921.6A409.6 409.6 0 1 1 921.6 512 409.6 409.6 0 0 1 512 921.6z m256-460.8H256a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4z"  ></path></symbol><symbol id="icon-add-rule" viewBox="0 0 1024 1024"><path d="M874.24 150.016C777.6 53.376 648.96 0.256 512 0.256s-265.6 53.12-362.24 149.76c-199.68 199.68-199.68 524.16 0 723.84 96.64 96.64 225.28 149.76 362.24 149.76s265.6-53.12 362.24-149.76c96.64-96.64 149.76-224.64 149.76-361.6s-53.12-265.6-149.76-362.24z m-72.32 652.16c-77.44 76.8-180.48 119.68-289.92 119.68-109.44 0-212.48-42.88-289.92-119.68-160-160-160-419.84 0-579.2 77.44-77.44 180.48-119.68 289.92-119.68 109.44 0 212.48 42.88 289.92 119.68 77.44 77.44 119.68 180.48 119.68 289.92 0 109.44-42.88 211.84-119.68 289.28z m-85.12-341.12H563.2v-153.6c0-28.16-23.04-51.2-51.2-51.2s-51.2 23.04-51.2 51.2v153.6H307.2c-28.16 0-51.2 23.04-51.2 51.2s23.04 51.2 51.2 51.2h153.6v153.6c0 28.16 23.04 51.2 51.2 51.2s51.2-23.04 51.2-51.2v-153.6h153.6c28.16 0 51.2-23.04 51.2-51.2s-23.04-51.2-51.2-51.2z"  ></path></symbol></svg>',
    a = (a = document.getElementsByTagName('script'))[a.length - 1].getAttribute('data-injectcss')
  if (a && !e.__iconfont__svg__cssinject__) {
    e.__iconfont__svg__cssinject__ = !0
    try {
      document.write(
        '<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>'
      )
    } catch (e) {}
  }
  function l() {
    c || ((c = !0), o())
  }
  ;(t = function () {
    var e, t, n
    ;((n = document.createElement('div')).innerHTML = s),
      (s = null),
      (t = n.getElementsByTagName('svg')[0]) &&
        (t.setAttribute('aria-hidden', 'true'),
        (t.style.position = 'absolute'),
        (t.style.width = 0),
        (t.style.height = 0),
        (t.style.overflow = 'hidden'),
        (e = t),
        (n = document.body).firstChild
          ? (t = n.firstChild).parentNode.insertBefore(e, t)
          : n.appendChild(e))
  }),
    document.addEventListener
      ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState)
        ? setTimeout(t, 0)
        : ((n = function () {
            document.removeEventListener('DOMContentLoaded', n, !1), t()
          }),
          document.addEventListener('DOMContentLoaded', n, !1))
      : document.attachEvent &&
        ((o = t),
        (i = e.document),
        (c = !1),
        (d = function () {
          try {
            i.documentElement.doScroll('left')
          } catch (e) {
            return void setTimeout(d, 50)
          }
          l()
        })(),
        (i.onreadystatechange = function () {
          'complete' == i.readyState && ((i.onreadystatechange = null), l())
        }))
})(window)
