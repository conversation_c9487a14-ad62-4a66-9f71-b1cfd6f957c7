<template>
  <div class="grid-edit-column mt-flex-direction-column">
    <div :class="data.cssClass"
         @click="handleClickCellTitle()">
      <template v-if="valueConverter && valueConverter.cssClass">
        <span :class="valueConverter.cssClass">{{valueConverter['text']}}</span>
      </template>
      <template v-else>
        {{valueConverter}}
      </template>
    </div>
    <div class="column-tool mt-flex">
      <div :class="['template-svg', {'icon-disabled':checkOptionIsDiasbled(item)}]"
           v-for="(item, toolbarIndex) in geTemplateToolBars"
           :key="toolbarIndex"
           :name="item.icon"
           v-if="checkVisibleCondition(item)"
           @click="handleClickCellTool(item)">
        <i :class="['mt-icons', 'mt-icon-'+item.icon]"
           v-if="item.icon"></i>
        <span class="icon-title">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import MtSvgIcon from "./MtSvgIcon";
import MtIcon from '@mtech-ui/icon'
export default {
  name: "ColumnTemplate",
  components: { MtSvgIcon, MtIcon },
  data() {
    return {
      data: {},
    };
  },
  inject: ['parentVm'],
  mounted() {
  },
  computed: {
    checkVisibleCondition() {
      return (item) => {
        if (Object.prototype.hasOwnProperty.call(item, "visibleCondition") && (typeof item["visibleCondition"]) === 'function') {
          return item["visibleCondition"](this.data);
        } else {
          return true;
        }
      }
    },
    //校验当前按钮是否需要添加‘disable’标记
    checkOptionIsDiasbled() {
      return (item) => {
        let _iconIsVisible = this.checkVisibleCondition(item);//按照‘可显示的逻辑’，不可显示的置灰
        let _ignoreToolList = ["more-option-btn", "template_preview", "template_delete", "template_edit"];
        let _iconIsIgnore = _ignoreToolList.indexOf(item.id) > -1;//按照‘ignoreList’，设置置灰
        return !_iconIsVisible || _iconIsIgnore
      }
    },
    geTemplateToolBars() {
      const toolbarMaps = {
        edit: { id: 'edit', icon: 'icon_Editor', title: this.$t('编辑') },
        preview: { id: 'preview', icon: 'icon_Hiddenpassword', title: this.$t('预览') },
        delete: { id: 'delete', icon: 'icon_Delete', title: this.$t('删除') },
        publish: { id: 'publish', icon: 'icon_Share_2', title: this.$t('发布') },
        activation: { id: 'activation', icon: 'icon_Activation', title: this.$t('激活') },
        close: { id: 'close', icon: 'icon_Close_2', title: this.$t('关闭') }
      }
      const getToolBars = (_tools) => {
        if (!_tools) return [];
        if (Array.isArray(_tools) && _tools.length > 0) {
          for (let i in _tools) {
            if (typeof _tools[i] === "string" && _tools[i]) {
              let _t = {};
              if (_tools[i]) {
                let _id = _tools[i].toLowerCase();
                if (Object.prototype.hasOwnProperty.call(toolbarMaps, _id)) {
                  _t = toolbarMaps[_id]
                }
              }
              if (Object.prototype.hasOwnProperty.call(_t, "icon")) {
                _tools[i] = _t;
              }
            }
          }
          return _tools;
        } else {
          return [];
        }
      };
      let _tools = getToolBars(this.data.cellTools);
      if (this.parentVm.useToolTemplate && this.data.columnIndex == 1) {
        _tools = this.serializeTools(_tools)
      }

      return _tools;
    },
    //单元格数据序列化 Number、Map、Placeholder、Date、Function
    valueConverter() {
      let _field = this.data.templateField;
      let _value = this.data[_field];
      if (_field.indexOf(".") > -1) {
        let _fieldMap = _field.split('.');
        let _res = null;
        for (let i = 0; i < _fieldMap.length; i++) {
          if (i < 1) {
            _res = this.data[_fieldMap[i]]
          } else {
            _res = _res[_fieldMap[i]]
          }
        }
        _value = _res;
      }
      if (this.data && this.data.hasOwnProperty('valueConverter')) {
        const _convertor = this.data.valueConverter;
        let res = "";
        switch (_convertor.type) {
          case 'number':
            if (Object.prototype.hasOwnProperty.call(_convertor, 'digit')) {
              res = this.dataFormat(_value, parseInt(_convertor['digit']))
            } else {
              res = this.dataFormat(_value)
            }
            break
          case 'date':
            if (Object.prototype.hasOwnProperty.call(_convertor, 'format')) {
              res = this.dateFormat(_value, _convertor['format'])
            } else {
              res = this.dateFormat(_value)
            }
            break
          case 'placeholder':
            if (Object.prototype.hasOwnProperty.call(_convertor, 'placeholder')) {
              res = this.existFormat(_value, _convertor['placeholder'])
            } else {
              res = this.existFormat(_value)
            }
            break
          case 'map':
            const _map = Object.prototype.hasOwnProperty.call(_convertor, 'map')
              ? _convertor['map']
              : {}
            if (Object.prototype.hasOwnProperty.call(_convertor, 'fields')) {
              res = this.mapFormat(_value, _map, _convertor['fields'])
            } else {
              res = this.mapFormat(_value, _map)
            }
            break
          case 'function':
            if (
              Object.prototype.hasOwnProperty.call(_convertor, 'filter') &&
              typeof _convertor['filter'] === 'function'
            ) {
              res = this.functionFormat(_value, _convertor['filter'])
            } else {
              res = this.functionFormat(_value)
            }
            break
          default:
            res = _value;
        }
        return res;
      } else {
        return _value
      }
    }
  },
  methods: {
    handleClickCellTool(item) {
      let _ignoreToolList = ["more-option-btn", "template_preview", "template_delete", "template_edit"];
      if (_ignoreToolList.indexOf(item.id) > -1) {
        //对于_ignore列表中的cellIconId,点击不抛回事件
        return;
      }
      let _data = {...this.data};
      delete _data.cellTools;
      delete _data.column;
      delete _data.columnIndex;
      delete _data.cssClass;
      delete _data.foreignKeyData;
      delete _data.index;
      delete _data.templateField;
      delete _data.valueConverter;
      delete _data.gridTemplate;

      delete _data.checkboxState;
      delete _data.uniqueID;
      delete _data.childRecords;
      delete _data.expanded;
      delete _data.hasChildRecords;
      delete _data.level;
      delete _data.taskData;
      delete _data.parentUniqueID;
      delete _data.parentItem;

      this.parentVm.$emit('handleClickCellTool', {
        tool: item,
        data: _data,
        componentData: this.data,
        tabIndex: this.data.tabIndex,
        grid: this.data.gridTemplate,
      })
    },
    handleClickCellTitle() {
      let _data = {...this.data};
      delete _data.cellTools;
      delete _data.column;
      delete _data.columnIndex;
      delete _data.cssClass;
      delete _data.foreignKeyData;
      delete _data.index;
      delete _data.templateField;
      delete _data.valueConverter;
      delete _data.gridTemplate;

      delete _data.checkboxState;
      delete _data.uniqueID;
      delete _data.childRecords;
      delete _data.expanded;
      delete _data.hasChildRecords;
      delete _data.level;
      delete _data.taskData;
      delete _data.parentUniqueID;
      delete _data.parentItem;
      this.parentVm.$emit('handleClickCellTitle', {
        field: this.data.templateField,
        data: this.data,
         data: _data,
        componentData: this.data,
        tabIndex: this.data.tabIndex,
        grid: this.data.gridTemplate,
      })
    },
    //序列化数字类型，默认为2位小数点
    dataFormat(value, format = 2) {
      var minus = false
      if (value == null) return '0.00'
      if (value.toString().indexOf('-') > -1) {
        value = value.toString().substring(1)
        minus = true
      }
      let s = parseFloat((value + '').replace(/[^\d\.-]/g, '')).toFixed(format) + ''
      let l = s.split('.')[0].split('').reverse(),
        r = s.split('.')[1]
      let f = ''
      for (let i = 0; i < l.length; i++) {
        f += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? ',' : '')
      }
      if (minus) return '-' + f.split('').reverse().join('') + '.' + r
      return f.split('').reverse().join('') + '.' + r
    },
    //序列化PlaceHolder，默认为'未填'
    existFormat(value, format = '未填') {
      if (!value || value === null || value === '') return format
      else return value
    },
    //序列化枚举类型，可以自定义fields
    mapFormat(value, map = {}, fields = { text: 'text', value: 'value' }) {
      if (Array.isArray(map)) {
        let _find = map.filter(e => {
          return e[fields.value] === value
        })
        if (_find.length > 0) {
          // return _find[0][fields.text]
          return { ..._find[0], text: _find[0][fields.text] }
        }
      } else if (typeof map === 'object') {
        if (Object.prototype.hasOwnProperty.call(map, value)) {
          return map[value]
        } else {
          return ''
        }
      } else {
        return ''
      }
    },
    //序列化用户自定义的数据
    functionFormat(
      value,
      func = e => {
        return e
      }
    ) {
      if (typeof func === 'function') {
        return func(value)
      } else {
        return value
      }
    },
    //序列化日期数据，默认为YYYY-MM-DD
    dateFormat(rawDate, format) {
      if (!rawDate) return ''
      var _date = new Date(rawDate)
      var __Y = _date.getFullYear(),
        __M = _date.getMonth() + 1,
        __D = _date.getDate()
      var __H = _date.getHours(),
        __m = _date.getMinutes(),
        __s = _date.getSeconds()
      /* add possible pre 0 */
      var preM = __M > 9 ? __M.toString() : '0' + __M.toString()
      var preD = __D > 9 ? __D.toString() : '0' + __D.toString()
      var preH = __H > 9 ? __H.toString() : '0' + __H.toString()
      var prem = __m > 9 ? __m.toString() : '0' + __m.toString()
      var pres = __s > 9 ? __s.toString() : '0' + __s.toString()
      var formatted
      switch (format) {
        case 'YYYY-MM-DD HH:mm:ss':
          formatted = __Y + '-' + preM + '-' + preD + ' ' + preH + ':' + prem + ':' + pres
          break
        case 'YYYY-MM-DD':
          formatted = __Y + '-' + preM + '-' + preD
          break
        case 'YYYY/MM/DD HH:mm:ss':
          formatted = __Y + '/' + preM + '/' + preD + ' ' + preH + ':' + prem + ':' + pres
          break
        case 'YYYY/MM/DD':
          formatted = __Y + '/' + preM + '/' + preD
          break
        default:
          formatted = __Y + '-' + preM + '-' + preD
          break
      }
      return formatted
    },
    serializeTools(_tools) {
      let _unuseTemplate = [];
      let _toolTemplate = [
        { id: "template_edit", icon: "icon_Editor", title: this.$t("编辑") },
        // { id: "template_preview", icon: "icon_Hiddenpassword", title: "查看" },
        { id: "template_delete", icon: "icon_Delete", title: this.$t("删除") }];
      _tools.forEach(tool => {
        let _find = _toolTemplate.filter(temp => {
          return temp.title === tool.title;
        });
        if (_find.length) {
          if (Object.prototype.hasOwnProperty.call(tool, "visibleCondition") && (typeof tool["visibleCondition"]) === 'function') {
           if(tool["visibleCondition"](this.data)){//按钮根据配置规则，如果需要显示，则使用用户配置的按钮
            _toolTemplate.forEach(_t => {
              if (_t.title === tool['title']) {
                _t.id = tool.id;
                _t.icon = tool.icon;
              }
            })
           }
          }else{
             _toolTemplate.forEach(_t => {
              if (_t.title === tool['title']) {
                _t.id = tool.id;
                _t.icon = tool.icon;
              }
            })
          }
        } else {
          _unuseTemplate.push(tool);
        }
      })
      return _toolTemplate.concat(_unuseTemplate);
    },
  },
};
</script>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}

.field-content {
  color: #00469c;
  font-size: 14px;
  cursor: pointer;
  &:hover {
    font-weight: 500;
  }
}
.grid-edit-column {
  height: 60px;
  justify-content: space-around;
  padding: 12px 0;
  box-sizing: border-box;
  .column-tool {
    .template-svg {
      margin-left: 10px;
      line-height: 1;
      &:first-of-type {
        margin-left: 0;
      }
      .mt-icons,
      .icon-title {
        cursor: pointer;
        font-size: 12px;
        color: #6386c1;
      }
      .icon-title {
        font-family: 'Roboto', 'Segoe UI', 'GeezaPro', 'DejaVu Serif', 'sans-serif', '-apple-system',
          'BlinkMacSystemFont';
      }
      &.icon-disabled {
        .mt-icons,
        .icon-title {
          cursor: not-allowed;
          color: #9daabf;
        }
      }
    }
  }
}
</style>
