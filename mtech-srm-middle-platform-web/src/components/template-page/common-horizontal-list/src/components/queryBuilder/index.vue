<template>
  <div class="query-builder">
    <div class="query-btns">
      <mt-button @click="handleQuerySearch">
        <i class="mt-icons mt-icon-BT_Excelfilter"></i>
        {{ $t('过滤') }}
      </mt-button>
      <mt-button @click="handleQueryReset">
        <i class="mt-icons mt-icon-BT_Excelclear"></i> {{ $t('重置') }}
      </mt-button>
      <!-- <mt-button> <i class="mt-icons mt-icon-save-02"></i> 保存</mt-button>
        <mt-button>
          <i class="mt-icons mt-icon-FilterFields"></i> 模板</mt-button
        > -->
    </div>

    <mt-query-builder
      ref="queryBuilder"
      :show-buttons="showButtons"
      width="100%"
      :column-data="queryColumnData"
    >
    </mt-query-builder>
  </div>
</template>

<script>
import lodash from 'lodash'
import MtQueryBuilder from '@mtech-ui/query-builder'

import getSelectTemp from './selectTemp'

export default {
  components: { MtQueryBuilder },
  props: {
    columnData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      queryColumnData: [],

      filterOptions: {
        type: 'Menu'
      },

      showButtons: {
        ruleDelete: true
      },
      // 条件验证
      validateRule: {
        isRequired: true
      }
    }
  },
  mounted() {
    let _queryColumnData = lodash.cloneDeep(this.columnData)
    if (_queryColumnData && _queryColumnData.length > 0) {
      if (_queryColumnData[0].type == 'checkbox') _queryColumnData.shift()
      _queryColumnData.forEach((item) => {
        item.label = item.headerText
        item.type = item.queryType || 'string'
        item.values = item.queryValues || null
        item.operators = this.getOpt(item.type, item.queryTemplate)
        item.template = item.queryTemplate ? this.getTemp(item.queryTemplate) : null
      })
    }

    this.queryColumnData = _queryColumnData
  },
  methods: {
    handleQuerySearch() {
      this.$emit('handleQuerySearch', this.$refs.queryBuilder.$refs.ejsRef.getRules())
    },

    // 获取当前列使用哪种选择器
    getOpt(type, temp) {
      var operators = {
        stringOperator: [
          { key: this.$t('包含'), value: 'contains' },
          { key: this.$t('等于'), value: 'equal' },
          { key: this.$t('不等于'), value: 'notequal' }
        ],
        numberOperator: [
          { key: this.$t('等于'), value: 'equal' },
          { key: this.$t('不等于'), value: 'notequal' },
          { key: this.$t('大于'), value: 'greaterthan' },
          { key: this.$t('大于等于'), value: 'greaterthanorequal' },
          { key: this.$t('小于'), value: 'lessthan' },
          { key: this.$t('小于等于'), value: 'lessthanorequal' }
        ],
        dateOperator: [
          { key: this.$t('包含'), value: 'contains' },
          { key: this.$t('不等于'), value: 'notequal' },
          { key: this.$t('大于'), value: 'greaterthan' },
          { key: this.$t('大于等于'), value: 'greaterthanorequal' },
          { key: this.$t('小于'), value: 'lessthan' },
          { key: this.$t('小于等于'), value: 'lessthanorequal' }
        ],
        booleanOperator: [{ key: this.$t('等于'), value: 'equal' }],
        selectOperator: [
          { key: this.$t('等于'), value: 'equal' },
          { key: this.$t('不等于'), value: 'notequal' }
        ]
      }
      if (temp) {
        let tempType = temp.type
        if ('select' == tempType) {
          return operators.selectOperator
        } else {
          return []
        }
      } else {
        if ('string' == type) {
          return operators.stringOperator
        } else if ('number' == type) {
          return operators.numberOperator
        } else if ('boolean' == type) {
          return operators.booleanOperator
        } else if ('date' == type) {
          return operators.dateOperator
        } else {
          return []
        }
      }
    },

    getTemp(args) {
      if (!args) return
      let type = args.type
      let options = args.options
      if ('select' == type) {
        return getSelectTemp({
          $mainInstance: this,
          fields: options.fields,
          ds: options.ds
        })
      } else if ('mulitiSelect' == type) {
        return
      } else {
        return
      }
    },

    handleQueryReset() {
      this.$refs.queryBuilder.$refs.ejsRef.reset()
      // this.handleQuerySearch();
      this.$emit('handleQueryReset')
    }
  }
}
</script>

<style lang="scss" scoped>
.query-builder {
  padding-top: 20px;
}
</style>
