<template>
  <div class="cell-checkbox-container">
    <!-- <mt-checkbox v-model="data[data.templateField]"
                 :id="serializeCellId"
                 @change="handleChangeCellCheckBox">
    </mt-checkbox> -->
    <div class="checkbox">
      <input type="checkbox"
             :id="serializeCellId"
             v-model="data[data.templateField]"
             @change="handleChangeCellCheckBox($event)" />
      <label :for="serializeCellId"></label>
    </div>
  </div>
</template>
<script>
import MtCheckbox from '@mtech-ui/checkbox'
export default {
  name: "ColumnCheckboxTemplate",
  components: {
    MtCheckbox,
  },
  inject: ['parentVm'],
  data() {
    return {
      data: {},
    };
  },
  mounted() {
  },
  computed: {
    serializeCellId() {
      return `cell-${this.data.tabIndex}-${this.data.templateField}-${this.data.index}`
    },
    serializeCellValue() {
      let _value = this.data[this.data.templateField];
      if ((typeof _value) === 'object' && Object.prototype.hasOwnProperty.call(_value, "checked")) {
        return _value.checked;
      } else {
        return _value;
      }
    }
  },
  methods: {
    handleChangeCellCheckBox(e) {
      // delete this.data.templateField;
      // delete this.data.gridRef;
      // delete this.data.tabIndex;
      // delete this.data.gridTemplate;
      // this.$set(this.parentVm.gridData, +this.data.index, this.data);
      // this.$set(this.data.parentVm.gridData[+this.data.index], this.data.templateField, e.target.checked);
      this.parentVm.$emit('handleChangeCellCheckBox', {
        field: this.data.templateField,
        data: this.data,
        checked: e.target.checked,
        gridRef: this.data.gridRef,
        tabIndex: this.data.tabIndex,
        grid: this.data.gridTemplate,
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.cell-checkbox-container {
  display: flex;
  justify-content: center;
  align-items: center;
  .checkbox {
    display: inline-block;
    position: relative;
    height: 100%;
    label {
      width: 16px;
      height: 16px;
      background: #fff;
      border: 1px solid #e8e8e8;
      cursor: pointer;
      border-radius: 2px;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      top: 50%;
      bottom: auto;
      transform: translateY(-50%);
    }
    input[type='checkbox']:checked + label {
      background: #00469c;
      border: 1px solid #02469c;
      &:after {
        opacity: 1;
        content: '';
        width: 10px;
        height: 6px;
        border: 2px solid #ffffff;
        transform: rotate(-45deg);
        background: transparent;
        border-top: none;
        border-right: none;
        position: absolute;
        top: 3px;
        left: 2px;
      }
    }
  }
}
</style>
