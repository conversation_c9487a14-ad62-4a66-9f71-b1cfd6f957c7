<template>
  <div
    :class="[
      'common-template-page',
      'mt-flex-direction-column',
      { 'template-hidden-tabs': hiddenTabs && paddingTop }
    ]"
  >
    <div :class="['tabs-container', { 'detail-tabs': isDetailPage }]" v-if="!hiddenTabs">
      <div class="tab-hori">
        <div class="tabs-arrow" @click="scrolllLeft"></div>
        <div class="tab-wrap" ref="tab-wrap">
          <ul :class="['tab-container', { 'line-tab-container': tabStyle == 'line' }]">
            <li
              :class="[
                'tab-item',
                item.titleCanChange && 'tab-item-change',
                { active: _index == activeTab }
              ]"
              v-for="(item, _index) in getTabDataSource"
              :key="_index"
            >
              <span @click="changeActiveTab(_index)">{{ item.title }}</span>

              <div class="editBox" v-if="item.titleCanChange">
                <mt-icon name="icon_Editor" @click.native="handleEditTab(item, _index)" />
                <mt-icon name="icon_Close_1" @click.native="handleDeleteTab(item, _index)" />
              </div>
            </li>
          </ul>
        </div>
        <div class="tabs-arrow arrow-right" @click="scrolllRight"></div>
      </div>
      <div class="add-tabs" v-if="showAddTagBtn" @click="handleAddTab">
        <mt-icon name="icon_solid_add" /><span>{{ $t('添加') }}</span>
      </div>
    </div>
    <div
      :class="[
        'page-grid-container',
        'mt-flex-direction-column',
        { 'grid-no-toolbar': !showTemplate(item, 'toolbar') }
      ]"
      v-for="(item, configIndex) in componentConfig"
      :key="configIndex"
      v-show="configIndex === activeTab"
    >
      <!-- 过滤器 -->
      <query-builder
        v-if="showTemplate(item, 'grid')"
        :column-data="item.grid.columnData"
        v-show="item.showQueryBuilder"
        @handleQuerySearch="handleQuerySearch"
        @handleQueryReset="handleQueryReset"
      ></query-builder>

      <div class="grid-options-bar mt-flex" v-if="showTemplate(item, 'toolbar')">
        <div
          class="mt-flex"
          v-for="(tool, toolbarIndex) in getGridToolBars(item.toolbar)"
          :key="configIndex + '-' + toolbarIndex"
        >
          <div
            :class="['svg-option-item', { 'icon-disabled': checkOptionIsDiasbled(temp) }]"
            v-for="(temp, toolIndex) in tool.base"
            :key="configIndex + '-' + toolbarIndex + '-' + toolIndex"
            :id="'tool-' + configIndex + '-' + toolbarIndex + '-' + toolIndex"
            @click="handleClickToolBar(item, temp, configIndex)"
            :tabIndex="toolIndex"
            @blur="blurToolBarItem"
          >
            <mt-icon v-if="temp.icon" :name="temp.icon" />
            <span v-if="temp.id == 'Filter'"
              >{{ item.showQueryBuilder ? $t('关闭') : $t('打开') }}{{ temp.title }}</span
            >
            <span v-else>{{ temp.title }}</span>
            <span
              v-if="temp.id == 'more-option-btn'"
              :class="['e-ddl-icon', { 'up-arrow': showToolBarOption }]"
            ></span>
            <div class="card-option" v-if="temp.id == 'more-option-btn' && showToolBarOption">
              <div
                class="svg-option-item"
                v-for="(ext, extIndex) in tool.ext"
                :key="configIndex + '-' + toolbarIndex + '-' + toolIndex + '-' + extIndex"
                :id="'tool-' + configIndex + '-' + toolbarIndex + '-' + toolIndex + '-' + extIndex"
                @click.stop="handleClickToolBar(item, ext, configIndex)"
              >
                <mt-icon v-if="ext.icon" :name="ext.icon" />
                <span>{{ ext.title }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <grid-template
        v-if="showTemplate(item, 'grid')"
        :ref="'gridTemplate-' + configIndex"
        :id="item.showQueryBuilder && 'grid-query-builder'"
        :data-source="item.grid.dataSource"
        :column-data="item.grid.columnData"
        :config-index="configIndex"
        :use-tool-template="useToolTemplate"
        :grid-height="getGridConfig(item.grid, 'gridHeight')"
        :grid-width="getGridConfig(item.grid, 'gridWidth')"
        :grid-lines="getGridConfig(item.grid, 'gridLines')"
        :allow-reordering="getGridConfig(item.grid, 'allowReordering')"
        :allow-resizing="getGridConfig(item.grid, 'allowResizing')"
        :allow-editing="getGridConfig(item.grid, 'allowEditing')"
        :edit-settings="getGridConfig(item.grid, 'editSettings')"
        :allow-paging="getGridConfig(item.grid, 'allowPaging')"
        :page-settings="getGridConfig(item.grid, 'pageSettings')"
        :allow-sorting="getGridConfig(item.grid, 'allowSorting')"
        :sort-settings="getGridConfig(item.grid, 'sortSettings')"
        :allow-filtering="getGridConfig(item.grid, 'allowFiltering')"
        :filter-settings="getGridConfig(item.grid, 'filterSettings')"
        :allow-selection="getGridConfig(item.grid, 'allowSelection')"
        :selection-settings="getGridConfig(item.grid, 'selectionSettings')"
        :frozen-columns="getGridConfig(item.grid, 'frozenColumns')"
        :auto-fit-columns="getGridConfig(item.grid, 'autoFitColumns')"
        :row-template="getGridConfig(item.grid, 'rowTemplate')"
        :row-data-bound="getGridConfig(item.grid, 'rowDataBound')"
        :query-cell-info="getGridConfig(item.grid, 'queryCellInfo')"
        @handleClickCellTool="handleClickCellTool"
        @handleClickCellTitle="handleClickCellTitle"
        @handleChangeCellCheckBox="handleChangeCellCheckBox"
        @handleGridCurrentChange="handleGridCurrentChange"
        @handleGridSizeChange="handleGridSizeChange"
        @handleQuerySearch="handleQuerySearch"
      ></grid-template>
      <text-editor
        v-if="showTemplate(item, 'textEditor')"
        :ref="'gridEditor-' + configIndex"
        :config-index="configIndex"
        :editor-config="item.textEditor"
      ></text-editor>
      <slot v-show="configIndex === activeTab" :name="'content-' + configIndex"></slot>
    </div>
  </div>
</template>

<script>
import MtButton from '@mtech-ui/button'
import GridTemplate from './components/GridTemplate'
import MtSvgIcon from './components/MtSvgIcon'
import MtIcon from '@mtech-ui/icon'
import textEditor from './components/TextEditorTemplate'
import queryBuilder from './components/queryBuilder'
export default {
  name: 'CommonLisHorizontal',
  props: {
    useToolTemplate: {
      type: Boolean,
      default: true
    },
    tabStyle: {
      type: String,
      default: 'line'
    },
    hiddenTabs: {
      type: Boolean,
      default: false
    },
    paddingTop: {
      type: Boolean,
      default: false
    },
    showAddTagBtn: {
      type: Boolean,
      default: false
    },
    dataSource: {
      type: Array,
      default: () => {
        return []
      }
    },
    isDetailPage: {
      type: Boolean,
      default: false
    }
  },
  components: {
    MtButton,
    MtSvgIcon,
    GridTemplate,
    textEditor,
    queryBuilder,
    MtIcon
  },
  data() {
    return {
      activeTab: 0,
      showToolBarOption: false
    }
  },
  mounted() {
    // window.onresize = () => {
    //   this.setTabScroll();
    // }
  },
  // watch: {
  //   componentConfig:{
  //    handler(val, oldVal){
  //   },
  //   deep:true
  //   }
  // },
  computed: {
    componentConfig: {
      get() {
        return this.dataSource
      },
      set() {}
    },
    checkVisibleCondition() {
      return (item) => {
        if (
          Object.prototype.hasOwnProperty.call(item, 'visibleCondition') &&
          typeof item.visibleCondition === 'function'
        ) {
          return item.visibleCondition(this.data)
        } else {
          return true
        }
      }
    },
    // 校验当前按钮是否需要添加‘disable’标记
    checkOptionIsDiasbled() {
      return (item) => {
        const _iconIsVisible = this.checkVisibleCondition(item) // 按照‘可显示的逻辑’，不可显示的置灰
        const _ignoreToolList = ['template_add', 'template_delete', 'template_edit']
        const _iconIsIgnore = _ignoreToolList.indexOf(item.id) > -1 // 按照‘ignoreList’，设置置灰
        return !_iconIsVisible || _iconIsIgnore
      }
    },
    // 处理顶部操作按钮的参数
    getGridToolBars() {
      const toolbarMaps = {
        add: { id: 'Add', icon: 'icon_solid_Createorder', title: this.$t('新增') },
        delete: { id: 'Delete', icon: 'icon_solid_Delete', title: this.$t('删除') },
        options: { id: 'Options', icon: 'icon_solid_Operation', title: this.$t('操作') },
        filter: { id: 'Filter', icon: 'icon_solid_Filter', title: this.$t('筛选') },
        export: { id: 'Export', icon: 'icon_solid_Download', title: this.$t('导出') },
        refresh: { id: 'Refresh', icon: 'icon_solid_Refresh', title: this.$t('刷新') },
        setting: { id: 'Setting', icon: 'icon_solid_Settingup', title: this.$t('设置') }
      }
      const getToolBars = (_tools) => {
        for (const i in _tools) {
          const _tool = _tools[i]
          if (Array.isArray(_tool)) {
            if (_tool.length > 0) {
              for (const j in _tool) {
                if (typeof _tool[j] === 'string' && _tool[j]) {
                  let _t = {}
                  if (_tool[j]) {
                    const _id = _tool[j].toLowerCase()
                    if (Object.prototype.hasOwnProperty.call(toolbarMaps, _id)) {
                      // 判断是否是预置的icons
                      _t = toolbarMaps[_id]
                    }
                  }
                  if (Object.prototype.hasOwnProperty.call(_t, 'id')) {
                    _tool[j] = _t
                  }
                }
              }
            }
          } else {
            _tools = getToolBars([_tools])
            break
          }
        }
        return _tools
      }
      return (t) => {
        let _res = t ? getToolBars(t) : []
        if (this.useToolTemplate) {
          // 使用模板中固定的‘新增、删除、编辑’操作
          _res = this.serializeTemplateTools(_res)
        }
        const _serializeList = []
        _res.forEach((e) => {
          if (e.length > 5) {
            const _base = []
            const _ext = []
            for (let i = 0; i < e.length; i++) {
              if (i < 4) {
                _base.push(e[i])
              } else {
                _ext.push(e[i])
              }
            }
            _base.push({
              id: 'more-option-btn',
              icon: 'icon_solid_Configuration',
              title: this.$t('更多操作')
            })
            _serializeList.push({
              data: e,
              base: _base,
              ext: _ext
            })
          } else {
            _serializeList.push({
              data: e,
              base: e,
              ext: []
            })
          }
        })
        // return t ? getToolBars(t) : [];
        return _serializeList
      }
    },
    // 获取@mtech/data-grid的入参
    getGridConfig() {
      return (item, key) => {
        if (item && Object.prototype.hasOwnProperty.call(item, key)) {
          return item[key]
        } else {
          let res = null
          switch (key) {
            case 'gridHeight':
              res = '100%'
              break
            case 'gridWidth':
              res = 'auto'
              break
            case 'gridLines':
              res = 'Horizontal'
              break
            case 'allowResizing':
              res = true
              break
            case 'allowReordering':
              res = true
              break
            case 'allowPaging':
              res = true
              break
            case 'allowEditing':
              res = false
              break
            case 'editSettings':
              res = item.allowEditing
                ? item.editSettings
                  ? item.editSettings
                  : {
                      allowEditing: true,
                      allowAdding: true,
                      allowDeleting: true,
                      mode: 'Normal',
                      allowEditOnDblClick: false,
                      newRowPosition: 'Top'
                    }
                : {}
              break
            case 'pageSettings':
              res = { pageSizes: [10, 50, 100, 200], totalRecordsCount: item.dataSource.length }
              break
            case 'allowSorting':
              res = true
              break
            case 'sortSettings':
              res = {}
              break
            case 'allowFiltering':
              res = true
              break
            case 'filterSettings':
              res = { type: 'Menu' }
              break
            case 'allowSelection':
              res = true
              break
            case 'selectionSettings':
              res = {
                type: 'Multiple',
                checkboxOnly: true
              }
              break
            case 'frozenColumns':
              res = 0
              break
            case 'autoFitColumns':
              res = []
              break
            default:
              res = null
          }
          return res
        }
      }
    },
    // 从组件参数中，获取Tab数组
    getTabDataSource() {
      const _tabs = []
      for (const i in this.componentConfig) {
        // _tabs.push({
        //   header: { text: this.componentConfig[i]["title"] },
        // });
        _tabs.push({
          title: this.componentConfig[i].title,
          titleCanChange: this.componentConfig[i].titleCanChange || false
        })
      }
      // this.setTabScroll();
      return _tabs
    },
    // 判断当前元素是否显示 组件入参时，toolbar、grid、textEditor均为可选参数
    showTemplate() {
      return (item, key) => {
        return item && Object.prototype.hasOwnProperty.call(item, key)
      }
    }
  },
  methods: {
    // 使用toolTempalte时，序列化‘新增、编辑、删除’。只处理左侧tools
    serializeTemplateTools(_tools) {
      const _res = []
      for (let i = 0; i < _tools.length; i++) {
        if (i < 1) {
          _res.push(this.serializeTools(_tools[i]))
        } else {
          _res.push(_tools[i])
        }
      }
      // _tools.forEach(tool => {
      //   _res.push(this.serializeTools(tool));
      // });
      return _res
    },
    serializeTools(_tools) {
      const _unuseTemplate = []
      const _toolTemplate = [
        { id: 'template_add', icon: 'icon_solid_Createorder', title: this.$t('新增') },
        { id: 'template_delete', icon: 'icon_solid_Delete', title: this.$t('删除') },
        { id: 'template_edit', icon: 'icon_solid_edit', title: this.$t('编辑') }
      ]
      _tools.forEach((tool) => {
        const _find = _toolTemplate.filter((temp) => {
          return temp.title === tool.title
        })
        if (_find.length) {
          _toolTemplate.forEach((_t) => {
            if (_t.title === tool.title) {
              _t.id = tool.id
              _t.icon = tool.icon
            }
          })
        } else {
          _unuseTemplate.push(tool)
        }
      })
      return _toolTemplate.concat(_unuseTemplate)
    },
    selectTab(e) {
      this.activeTab = e.selectedIndex
      this.$emit('handleSelectTab', e.selectedIndex)
    },
    changeActiveTab(i) {
      if (+this.activeTab !== +i) {
        this.activeTab = i
        this.$emit('handleSelectTab', i)
      }
    },
    getCurrentActiveTab() {
      return this.activeTab
    },
    blurToolBarItem() {
      this.showToolBarOption = false
    },
    // 点击顶部按钮操作
    handleClickToolBar(item, temp, index) {
      this.showToolBarOption = temp.id == 'more-option-btn'

      const _ignoreToolList = [
        'more-option-btn',
        'template_add',
        'template_delete',
        'template_edit'
      ]
      if (_ignoreToolList.indexOf(temp.id) > -1) {
        // 对于_ignore列表中的toolId,点击不抛回事件
        return
      }

      const _res = {
        toolbar: temp,
        tabIndex: index
      }
      if (this.showTemplate(item, 'grid')) {
        _res.gridRef = `pageGrid-${index}`
        _res.grid = this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef
      } else if (this.showTemplate(item, 'textEditor')) {
        _res.editorRef = `editorRef-${index}`
        _res.editor = this.$refs[`gridEditor-${index}`][0].$refs[`editorRef-${index}`].ejsRef
      }
      if (_res.toolbar && _res.toolbar.id == 'Setting') {
        this.getCurrentTabRef().grid.openColumnChooser()
      }
      if (_res.toolbar && _res.toolbar.id == 'Filter') {
        this.$set(
          this.componentConfig[index],
          'showQueryBuilder',
          !this.componentConfig[index].showQueryBuilder
        )
        if (
          this.getGridConfig(item.grid, 'allowFiltering') &&
          this.componentConfig[index].showQueryBuilder
        ) {
          // 如果允许过滤，并且打开了query-builder的话，清除列过滤
          this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef.clearFiltering()
        }
      }
      if (_res.toolbar && _res.toolbar.id == 'Refresh') {
        if (this.getGridConfig(item.grid, 'allowFiltering')) {
          // 如果允许过滤的话，清除列过滤
          this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef.clearFiltering()
        }
      }
      this.$emit('handleClickToolBar', _res)
    },
    // 过滤器搜索
    handleQuerySearch(rules) {
      this.$emit('handleQuerySearch', rules)
    },
    handleQueryReset() {
      this.$emit('handleQueryReset')
    },
    // 获取当前TAB下的主体对象
    getCurrentTabRef() {
      const index = this.activeTab
      if (this.showTemplate(this.componentConfig[index], 'grid')) {
        const grid = this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef
        return { tabIndex: index, type: 'grid', grid: grid }
      } else if (this.showTemplate(this.componentConfig[index], 'textEditor')) {
        const editor = this.$refs[`gridEditor-${index}`][0].$refs[`editorRef-${index}`].ejsRef
        return { tabIndex: index, type: 'editor', editor: editor }
      } else {
        return { tabIndex: index, type: null }
      }
    },
    // 获取所有TAB下的主体对象
    getAllTabRef() {
      const res = []
      for (const index in this.componentConfig) {
        if (this.showTemplate(this.componentConfig[index], 'grid')) {
          const grid = this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef
          res.push({ tabIndex: +index, type: 'grid', grid: grid })
        } else if (this.showTemplate(this.componentConfig[index], 'textEditor')) {
          const editor = this.$refs[`gridEditor-${index}`][0].$refs[`editorRef-${index}`].ejsRef
          res.push({ tabIndex: +index, type: 'editor', editor: editor })
        } else {
          res.push({ tabIndex: +index, type: null })
        }
      }
      return res
    },
    // 获取当前Tab中的数据
    getCurrentTabGridData() {
      const index = this.activeTab
      if (this.showTemplate(this.componentConfig[index], 'grid')) {
        const grid = this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef
        return { tabIndex: index, type: 'grid', grid: grid, data: grid.getCurrentViewRecords() }
      } else if (this.showTemplate(this.componentConfig[index], 'textEditor')) {
        const editor = this.$refs[`gridEditor-${index}`][0].$refs[`editorRef-${index}`].ejsRef
        return {
          tabIndex: index,
          type: 'editor',
          editor: editor,
          data: {
            text: editor.getText(),
            html: editor.getHtml()
          }
        }
      } else {
        return { tabIndex: index, type: null, data: null }
      }
    },
    // 获取所有Tab中的数据
    getAllTabGridData() {
      const res = []
      for (const index in this.componentConfig) {
        if (this.showTemplate(this.componentConfig[index], 'grid')) {
          const grid = this.$refs[`gridTemplate-${index}`][0].$refs[`pageGrid-${index}`].ejsRef
          res.push({
            tabIndex: +index,
            type: 'grid',
            grid: grid,
            data: grid.getCurrentViewRecords()
          })
        } else if (this.showTemplate(this.componentConfig[index], 'textEditor')) {
          const editor = this.$refs[`gridEditor-${index}`][0].$refs[`editorRef-${index}`].ejsRef
          res.push({
            tabIndex: +index,
            type: 'editor',
            editor: editor,
            data: {
              text: editor.getText(),
              html: editor.getHtml()
            }
          })
        } else {
          res.push({ tabIndex: +index, type: null, data: null })
        }
      }
      return res
    },
    // 当单元格为chekbox时，勾选触发的事件，重写grid表格数据
    handleChangeCellCheckBox(data) {
      const _tabIndex = data.tabIndex
      const _data = data.data
      const _dataIndex = _data.index
      const _field = _data.templateField
      const _value = data.checked
      delete _data.templateField
      delete _data.gridRef
      delete _data.tabIndex
      delete _data.gridTemplate
      this.$set(this.componentConfig[_tabIndex].grid.dataSource, _dataIndex, _data)
    },
    // 单元格内容点击时的事件
    handleClickCellTitle(data) {
      this.$emit('handleClickCellTitle', data)
    },
    // 单元格Icon按钮点击时的事件
    handleClickCellTool(data) {
      this.$emit('handleClickCellTool', data)
    },
    handleGridCurrentChange(data) {
      this.$emit('handleGridCurrentChange', data)
    },
    handleGridSizeChange(data) {
      this.$emit('handleGridSizeChange', data)
    },

    // 新增tab
    handleAddTab() {
      this.$emit('handleAddTab')
    },
    // 编辑tab
    handleEditTab(item, index) {
      this.$emit('handleEditTab', item.title, index)
    },
    handleDeleteTab(item, index) {
      this.$emit('handleDeleteTab', item.title, index)
    },
    // 判断水平tab是否溢出
    setTabScroll() {
      this.$nextTick(() => {
        const _ifEllipsis = this.isEllipsis(this.$refs['tab-wrap'])
        if (_ifEllipsis) {
          this.$refs['tab-wrap'].previousElementSibling.style.display = 'block'
          this.$refs['tab-wrap'].nextElementSibling.style.display = 'block'
        } else {
          this.$refs['tab-wrap'].previousElementSibling.style.display = 'none'
          this.$refs['tab-wrap'].nextElementSibling.style.display = 'none'
        }
      })
    },
    // 判断dom是否溢出
    isEllipsis(dom) {
      let flag = false
      if (dom && dom.scrollWidth) {
        flag = dom.scrollWidth > dom.offsetWidth
      }
      return flag
    },

    scrolllLeft() {},

    scrolllRight() {}
  }
}
</script>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}
.common-template-page {
  width: 100%;
  height: 100%;
  background: #fafafa;
  padding: 0;
  box-sizing: border-box;

  &.template-hidden-tabs {
    padding-top: 20px;
  }
  .tabs-container {
    box-sizing: border-box;
    height: 50px;
    border: none;
    border-radius: 4px 4px 0 0;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    flex-shrink: 0;

    &.detail-tabs {
      border: 1px solid #e8e8e8;
      li.tab-item {
        &:first-of-type {
          margin-left: 20px !important;
        }
      }
    }

    .tab-hori {
      display: flex;
      flex: 1;
      overflow: hidden;

      .tab-wrap {
        height: 48px;
        overflow-x: auto;
        overflow-y: hidden;

        &::-webkit-scrollbar {
          height: 4px;
          display: none;
        }
        &:hover {
          &::-webkit-scrollbar {
            display: block;
          }
        }
      }

      .tab-item {
        display: flex;
        align-items: center;
        white-space: nowrap;
        .editBox {
          display: flex;
          float: right;
          visibility: hidden;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          height: 100%;
          margin-left: 6px;
          font-size: 12px;
          transform: scale(0.8);

          .mt-icons:first-child {
            margin-bottom: 6px;
          }
        }
        &-change:hover,
        &-change.active {
          .editBox {
            visibility: visible;
          }
        }
      }
    }
    .add-tabs {
      cursor: pointer;
      padding: 0 10px;
      margin-right: 10px;
      margin-left: 10px;
      .mt-icons {
        font-size: 14px !important;
        position: relative;
        top: -1px;
        margin-right: 6px;
      }
    }

    .tabs-arrow {
      display: none;
      width: 20px;
      height: 48px;
      line-height: 46px;
      position: relative;
      cursor: pointer;
      border-radius: 4px 0 0 0;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
      &:after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-left: 0;
        border-right: 4px solid #6e7a8d;
        left: 8px;
        top: 19px;
      }
      &.arrow-right {
        border-radius: 0 4px 0 0;
        &:after {
          border-top: 4px solid transparent;
          border-bottom: 4px solid transparent;
          border-left: 4px solid #6e7a8d;
          border-right: 0;
        }
      }
    }
    ul.tab-container {
      height: 48px;
      display: flex;
      align-items: center;
      list-style: none;
      margin: 0;
      padding: 0;
      border: 0;
      flex-shrink: 0;
      li.tab-item {
        font-size: 14px;
        font-weight: 400;
        border: none;
        // margin: 0 20px;
        padding: 4px 20px;
        border-radius: 4px;
        color: #9a9a9a;
        cursor: pointer;
        box-sizing: border-box;
        border: 1px solid #fafafa;

        &.active,
        &:hover {
          color: #00469c;
          background: rgba(0, 70, 156, 0.06);
          border: 1px solid rgba(0, 70, 156, 0.1);
          font-weight: 500;
        }

        &:first-of-type {
          margin-left: 0;
        }
      }
    }
    .line-tab-container {
      display: flex;
      li.tab-item {
        flex-shrink: 0;
        color: #292929;
        font-size: 14px;
        font-weight: 400;
        height: 46px;
        line-height: 46px;
        min-width: 60px;
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        .item-content {
          cursor: pointer;
          padding: 0;
          display: flex;
          position: relative;
          min-width: 60px;
          color: #4f5b6d;
          align-items: center;
          justify-content: center;
          .mt-icons {
            position: relative;
            top: -1px;
            margin-right: 6px;
          }
          .config-checkbox {
            &.mt-icon-a-icon_MultipleChoice_on {
              color: #6386c1;
            }
            &.mt-icon-a-icon_MultipleChoice_off {
              color: #9daabf;
            }
          }
          &.fixed {
            .config-checkbox {
              &.mt-icon-a-icon_MultipleChoice_on {
                color: #9a9a9a;
              }
            }
          }
        }

        &.active,
        &:hover {
          border-color: transparent !important;
          background: transparent !important;
        }

        &.active {
          color: #00469c;
          font-weight: 600;

          &:after {
            content: '';
            border: 1px solid #00469c;
            width: 60%;
            animation: active-tab 0.3s ease;
            position: absolute;
            bottom: 6px;
            left: 20%;
          }
          @keyframes active-tab {
            0% {
              width: 0;
              left: 50%;
            }
            100% {
              width: 60%;
              left: 20%;
            }
          }
        }
      }
    }
  }

  .page-grid-container {
    flex: 1;
    // border-radius: 8px 8px 0 0;
    border-radius: 2px 2px 0 0;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    overflow-x: auto;
    overflow-y: hidden;
    background: #ffffff;
    padding: 0 20px;
    &.grid-no-toolbar {
      padding-top: 10px;
    }
    .grid-options-bar {
      justify-content: space-between;
      height: 50px;
      box-sizing: border-box;
      align-items: center;
      background: #ffffff;
      padding: 0 20px;
      flex-shrink: 0;

      .svg-option-item {
        min-width: auto;
        line-height: 1;
        cursor: pointer;
        padding: 0;
        margin-left: 20px;
        display: flex;
        align-items: center;
        position: relative;
        .mt-icons {
          font-size: 16px;
        }
        .mt-icons {
          font-size: 14px;
          color: #4f5b6d;
          margin-right: 5px;
        }
        span {
          word-break: keep-all;
          font-size: 14px;
          color: #4f5b6d;
          font-weight: normal;
        }
        &:first-of-type {
          margin-left: 0;
        }
        &:hover {
          .mt-icons {
            color: #707b8b;
          }
          span {
            color: #707b8b;
          }
        }
        .e-ddl-icon {
          position: relative;
          &:before {
            content: '\e969';
            font-family: e-icons;
            position: relative;
            margin-left: 9px;
            font-size: 12px;
            top: 2px;
          }
          &.up-arrow {
            display: inline-flex;
            &::before {
              transform: rotate(180deg);
            }
          }
        }
        &.icon-disabled {
          .mt-icons {
            color: #9baac1;
          }
          span {
            color: #9baac1;
          }
          &:hover {
            cursor: not-allowed;
            .mt-icons {
              color: #9baac1;
            }
            span {
              color: #9baac1;
            }
          }
        }
      }
      .card-option {
        position: absolute;
        min-width: 100px;
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 2px;
        box-shadow: 0 4px 10px 0 rgb(0 0 0 / 20%);
        padding: 16px 20px;
        left: -25px;
        top: 20px;
        z-index: 3;
        text-align: left;

        .svg-option-item {
          margin-left: 0;
          margin-top: 20px;
          &:first-of-type {
            margin-top: 0;
          }
          &:hover {
            span {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}
</style>
