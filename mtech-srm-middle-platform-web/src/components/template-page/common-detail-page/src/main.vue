<template>
  <div class="detail-container mt-flex-direction-column">
    <div class="detail-description">
      <div>
        <span class="detail-code">{{ detailConfig.detailCode }}</span>
        <span class="detail-status" v-if="detailConfig.detailStatus">{{detailConfig.detailStatus}}</span>
      </div>
      <div class="detail-title">{{ detailConfig.detailName }}</div>
      <div class="detail-options mt-flex invite-btn">
        <div v-for="(tool, toolbarIndex) in getGridToolBars(detailConfig.toolbar)"
             :key="toolbarIndex">
          <div class="svg-option-item"
               v-for="(temp, toolIndex) in tool"
               :key="toolbarIndex + '-' + toolIndex"
               :id="'tool-' + toolbarIndex + '-' + toolIndex"
               @click="handleClickOptionsToolBar(temp)">
            <mt-svg-icon v-if="temp.icon"
                         :icon="temp.icon"></mt-svg-icon>
            <span>{{ temp.title }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="detail-content mt-flex">
      <div class="detail-fields-panel"
           v-if="!hiddenDetailColumns">
        <div class="detail-field-panel mt-flex"
             v-for="(detail, index) in detailConfig.detailFileds"
             :key="index"
             :class="{ 'detail-field-close': detail.close }">
          <div class="field-logo">
            <mt-svg-icon icon="M_Surface4"></mt-svg-icon>
          </div>
          <div class="detail-fields">
            <div class="detail-name"
                 v-if="detail.name">{{ detail.name }}</div>
            <ul>
              <li class="detail-field"
                  v-for="(item, _index) in detailConfig.columnData"
                  :key="index + '-' + _index">
                <div class="field-key"
                     :class="{ 'top-left-arrow-tag': item.hasTag }">
                  {{ item.headerText }}
                </div>
                <div class="field-value">
                  {{ detail.details[item.field] }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="page-content-panel">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { utils } from '@mtech-common/utils'
import MtSvgIcon from './components/MtSvgIcon'

export default {
  name: 'DetailPage',
  components: { MtSvgIcon },
  props: {
    detailConfig: {
      type: Object,
      default: () => {
        return {
          toolbar: [],
          detailName: '',
          detailCode: '',
          columnData: [],
          detailFileds: []
        }
      }
    },
    hiddenDetailColumns: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() { },
  computed: {
    getGridToolBars() {
      const toolbarMaps = {
        add: { id: 'Add', icon: 'add', title: this.$t('新增') },
        delete: { id: 'Delete', icon: 'delete', title: this.$t('删除') },
        options: { id: 'Options', icon: 'table-setting', title: this.$t('操作') },
        filter: { id: 'Filter', icon: 'filter', title: this.$t('筛选') },
        export: { id: 'Export', icon: 'export', title: this.$t('导出') },
        refresh: { id: 'Refresh', icon: 'refresh', title: this.$t('刷新') },
        setting: { id: 'Setting', icon: 'M_Properties', title: this.$t('设置') }
      }
      const getToolBars = (_t) => {
        let _tools = utils.cloneDeep(_t)
        for (const i in _tools) {
          const _tool = _tools[i]
          if (Array.isArray(_tool)) {
            if (_tool.length > 0) {
              for (const j in _tool) {
                if (typeof _tool[j] === 'string' && _tool[j]) {
                  const _t = _tool[j] ? (Object.prototype.hasOwnProperty.call(toolbarMaps, _tool[j].toLowerCase()) ? toolbarMaps[_tool[j].toLowerCase()] : {}) : {}
                  if (Object.prototype.hasOwnProperty.call(_t, 'id')) {
                    _tool[j] = _t
                  }
                }
              }
            }
          } else {
            _tools = getToolBars([_tools])
            break
          }
        }
        return _tools
      }
      return (t) => {
        return t ? getToolBars(t) : []
      }
    }
  },
  methods: {
    handleClickOptionsToolBar(e) {
      this.$emit('handleClickOptionsToolBar', e)
    }
  }
}
</script>
<style>
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  /*font-size: 100%;*/
  font: inherit;
  vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
main {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
html * {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*:after,
*:before {
  box-sizing: border-box;
}
</style>
<style lang="scss" scoped>
.mt-flex {
  display: flex;
  position: relative;
}

.mt-flex-direction-column {
  display: flex;
  flex-direction: column;
  position: relative;
}
.detail-container {
  width: 100%;
  height: 100%;
  // padding: 10px 10px 0 10px;
  padding: 20px 0 0 0;
  background: #fafafa;
  box-sizing: border-box;
  .svg-option-item {
    margin-top: 20px;
    cursor: pointer;
    padding: 0;
    margin-left: 20px;
    display: inline-block;
    text-align: center;
    position: relative;
    min-width: 60px;
    .mt-tooltip-container {
      font-size: 18px;
      color: #4d5b6f;
      margin-right: 5px;
    }
    span {
      color: #4f5b6d;
    }
    &:first-of-type {
      margin-left: 0;
    }
  }
  .detail-description {
    width: 100%;
    height: 120px;
    padding: 20px;
    background: #fff;
    border-radius: 8px 8px 0 0;
    border: 1px solid #e8e8e8;
    .detail-code {
      color: #292929;
      font-size: 20px;
      font-weight: 500;
      display: inline-block;
    }
    .detail-status {
      height: 20px;
      color: #eda133;
      border-radius: 2px;
      font-size: 12px;
      border: 1px solid #eda133;
      display: inline-block;
      vertical-align: bottom;
      padding: auto;
      padding: 0 4px;
      line-height: 20px;
      margin-left: 8px;
    }
    .detail-title {
      color: #292929;
      font-size: 14px;
      font-weight: 400;
      margin-top: 10px;
    }
    .detail-options {
      justify-content: space-between;
      .svg-option-item {
        margin-right: 20px;

        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }
  .detail-content {
    flex: 1;
    min-height: 0;
    .detail-fields-panel {
      width: 300px;
      height: 100%;
      border: 1px solid #e8e8e8;
      border-top: none;
      border-bottom: none;
      overflow-y: auto;
      background: #fff;
      flex-shrink: 0;

      .detail-field-panel {
        background: #fff;
        padding: 16px 20px;
        position: relative;
        margin-top: 10px;

        &.detail-field-close {
          &:after {
            content: '+';
            position: absolute;
            color: #98aac3;
            font-size: 24px;
            font-weight: 400;
            top: 20px;
            right: 20px;
            transform: rotate(45deg);
          }
        }

        &:first-of-type {
          margin-top: 0;
        }

        .field-logo {
          width: 30px;
          height: 30px;
          margin-right: 20px;
          border: 1px solid #e8e8e8;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          .mt-icons {
            font-size: 22px;
          }
        }
        .detail-fields {
          flex: 1;
          .detail-name {
            color: #232b39;
            font-size: 24px;
            font-weight: 500;
            height: 30px;
            line-height: 30px;
            margin-bottom: 20px;
          }
          li.detail-field {
            margin-top: 20px;
            .field-key {
              font-size: 12px;
              color: #9a9a9a;
            }
            .field-value {
              // border-bottom: 1px solid #e8e8e8;
              // padding: 8px;
              // font-size: 14px;
              // color: #232b39;
              // font-weight: 500;
              height: 34px;
              line-height: 34px;
              border-bottom: 1px solid #e8e8e8;
              font-size: 14px;
              color: #232b39;
              font-weight: 500;
              padding: 0;
            }

            &:first-of-type {
              margin-top: 0;
            }
            .top-left-arrow-tag {
              padding-left: 5px;
              position: relative;
              &:before {
                content: '';
                height: 0;
                width: 0;
                border-right: 6px solid transparent;
                border-top: 6px solid #eda133;
                position: absolute;
                left: 0;
                top: 0;
              }
            }
          }
        }
      }
    }
    .page-content-panel {
      flex: 1;
      min-height: 0;
      background: #ffffff;
      margin-top: 10px;
      overflow: auto;
    }
  }
}
</style>
