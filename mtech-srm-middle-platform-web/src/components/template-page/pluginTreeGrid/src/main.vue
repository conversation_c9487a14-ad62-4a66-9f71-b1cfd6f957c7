<template>
  <div class="grid-container">
    <mt-tree-grid
      v-bind="$attrs"
      v-on="$listeners"
      :class="[{ hasFrozen: hasFrozenColumn }]"
      :id="gridId"
      ref="gridRef"
      :height="gridHeightCfg.height"
      :data-source="gridData"
      :columns="getGridColumnData"
      :child-mapping="childMapping"
      :grid-lines="gridLines"
      :allow-selection="allowSelection"
      :selection-settings="selectionSettings"
      :allow-reordering="allowReordering"
      :column-drop="columnDrop"
      :allow-resizing="allowResizing"
      :allow-paging="allowPaging"
      :page-settings="pageSetting"
      :allow-sorting="allowSorting"
      :allow-filtering="allowFiltering"
      :filter-settings="serializeFilterSetting"
      :show-column-chooser="true"
      :frozen-columns="frozenColumns"
      :allow-excel-export="allowExcelExport"
      :use-row-select="useRowSelect"
      @currentChange="currentChange"
      @sizeChange="sizeChange"
      @rowSelected="rowSelected"
      @rowDeselected="rowDeselected"
      @resizeStop="handleResizeStop"
      @actionBegin="actionBegin"
    />
    <mt-dialog
      ref="toast"
      :header="$t('已选项')"
      :buttons="buttons"
      :show-close-icon="false"
      :open="onOpen"
    >
      <div style="height: 100%; paddingtop: 20px">
        <mt-data-grid
          v-bind="$attrs"
          v-on="$listeners"
          class="chosen-list-height"
          :id="gridId"
          :data-source="gridDataOnNum"
          :column-data="getGridColumnData2"
        />
      </div>
    </mt-dialog>
  </div>
</template>

<script>
import MtTreeGrid from '@mtech-ui/tree-grid'
import MtDataGrid from '@mtech-ui/data-grid'
import MtDialog from '@mtech-ui/dialog'

// import PluginGridMixin from '@mtech/plugin-grid-mixin'
import PluginGridMixin from '../../pluginGridMixin/src/index.js'

export default {
  inheritAttrs: false,
  name: 'PluginTreeGrid',
  components: {
    MtTreeGrid,
    MtDataGrid,
    MtDialog,
  },
  mixins: [PluginGridMixin],
  data() {
    return {
      buttons: [
        {
          click: this.hide,
          buttonModel: { content: this.$t('关闭') },
        },
        {
          click: () => {
            this.$dialog({
              data: {
                title: this.$t('警告'),
                message: this.$t('是否清空所有选中项?'),
              },
              success: () => {
                this.$refs.gridRef.clearAll()
                this.gridDataOnNum = []
                this.hide()
              },
            })
          },
          buttonModel: { isPrimary: 'true', content: this.$t('清空') },
        },
      ],
      gridDataOnNum: [],
    }
  },
  methods: {
    actionBegin(param) {
      if (
        param.requestType === 'sorting' &&
        param?.direction &&
        this.columnData.find((e) => e.field === param.columnName)?.allowGlobalSorting
      ) {
        const _columnqueryParams = {
          column: param.columnName.replace(/[A-Z]/g, '_$&').toLowerCase(),
          asc: param.direction === 'Ascending',
        }
        if (this.pageSetting) {
          this.pageSetting.orders = []
          this.pageSetting.orders.push(_columnqueryParams)
        }
        this.$emit('handleQuerySearch', this.queryBuilderRules)
      } else if (
        param.requestType === 'sorting' &&
        !Object.hasOwnProperty.call(param, 'direction') &&
        this.columnData.find((e) => e.field === param.columnName)?.allowGlobalSorting
      ) {
        this.pageSetting.orders = []
        this.$emit('handleQuerySearch', this.queryBuilderRules)
      }
      if (param.requestType === 'add' || param.requestType === 'beginEdit') {
        const _parent = document.getElementById(this.gridId)
        const _elements = _parent.getElementsByClassName('hasFrozenColumn')
        if (_elements.length > 0) {
          _parent.removeChild(_elements[0])
        }
      }
      if (param.action === 'filter') {
        // 列过滤数据
        const _columnqueryParams = []
        if (param.columns && param.columns.length > 0) {
          param.columns.forEach((item) => {
            if (item.value) {
              _columnqueryParams.push({
                field: item.field,
                operator: item.operator,
                value: item.value,
              })
            }
          })
          this.queryBuilderRules = { rules: _columnqueryParams }
          // this.$refs.gridRef.ejsRef.clearFiltering()
          this.$emit('handleQuerySearch', { rules: _columnqueryParams })
        }
      } else if (param.action === 'clearFilter') {
        // 列过滤数据-重置
        const _columnqueryParams = this.queryBuilderRules?.rules ?? []
        _columnqueryParams.forEach((item, index) => {
          if (item.field === param.currentFilterColumn.field) {
            _columnqueryParams.splice(index, 1)
          }
        })
        this.queryBuilderRules = { rules: _columnqueryParams }
        this.$emit('handleQuerySearch', { rules: _columnqueryParams })
      } else if (param.requestType === 'refresh') {
        if (this.$refs?.gridRef?.ejsRef?.closeEdit) {
          this.$refs.gridRef.ejsRef.closeEdit()
        }
      }
    },
    columnDrop() {
      this.$nextTick(() => {
        let _showColumns = this.$refs.gridRef.ejsRef.getVisibleColumns()
        if (Array.isArray(_showColumns) && _showColumns.length) {
          const _visible = []
          _showColumns.forEach((c) => {
            if (c.field && c.headerText) {
              _visible.push({
                field: c.field,
                headerText: c.headerText,
                ignore: !!c.ignore,
                sticky: !!c.sticky,
              })
            }
          })
          this.$parent.$emit('handleSaveMemory', { visibleCols: _visible })
        }
      })
    },
    filterChildRows(record) {
      if (Array.isArray(record)) return []
      let indexList = []
      if (typeof record?.hasChildRecords === 'boolean' && record?.hasChildRecords) {
        indexList.push(record.index)
        const _data = record.childRecords
        _data.forEach((e) => {
          indexList = indexList.concat(this.filterChildRows(e))
        })
      } else {
        indexList.push(record.index)
      }
      return indexList
    },
    rowSelected(e) {
      if (this.useRowSelect) {
        const indexList = this.filterChildRows(e?.data)
        if (indexList.length) {
          const _selectRecords = [...this.$refs.gridRef.ejsRef.getSelectedRecords()]
          const _selectIndex = []
          _selectRecords.forEach((k) => {
            _selectIndex.push(k.index)
          })
          const _mergeList = new Set(_selectIndex.concat(indexList))
          this.$refs.gridRef.ejsRef.selectRows([..._mergeList])
        }
      }
    },
    rowDeselected(e) {
      if (this.useRowSelect) {
        const indexList = this.filterChildRows(e?.data)
        if (indexList.length) {
          const _mergeList = []
          const _selectRecords = [...this.$refs.gridRef.ejsRef.getSelectedRecords()]
          const _selectIndex = []
          _selectRecords.forEach((k) => {
            _selectIndex.push(k.index)
          })
          _selectIndex.forEach((f) => {
            if (indexList.indexOf(f) < 0) {
              _mergeList.push(f)
            }
          })
          this.$refs.gridRef.ejsRef.selectRows([..._mergeList])
        }
      }
    },
    onOpen(args) {
      args.preventFocus = true
    },
    onNum(e) {
      this.gridDataOnNum = []
      this.gridDataOnNum.push(...e)
      this.show()
      // this.gridDataOnNum = e
    },
    show() {
      this.$refs.toast.ejsRef.show()
    },
    hide() {
      this.$refs.toast.ejsRef.hide()
    },
  },
  computed: {
    getGridColumnData2() {
      let arr = [...this.getGridColumnData]
      arr.shift()
      return arr
    },
  },
  deactivated() {
    this.hide()
  },
}
</script>
<style lang="scss" scoped>
@import '../themes/dark.scss';
.grid-container {
  width: 100%;
  height: 100%;
  background: var(--plugin-tg-bg-ff);
  ::v-deep .mt-data-grid {
    .e-grid {
      border: none;
      .e-gridcontent {
        border-top: 0;
        border-bottom: 0;
        .e-content {
          overflow-y: auto !important; //mt-data-grid中，overflow-y:scroll
        }
        .e-scrollbar {
          .e-frozenscrollbar {
            border-top: none;
          }
          .e-movablescrollbar {
            //mt-data-grid中，overflow-x:scroll
            overflow-x: auto;
            min-height: unset !important;
            max-height: unset !important;
            height: 10px !important;
            .e-movablechild {
              min-height: unset !important;
              max-height: unset !important;
              height: 10px !important;
            }
          }
        }
      }
      th.e-headercell {
        vertical-align: middle;
      }
      // td.e-rowcell{
      //   border-left: 0 !important;
      //   border-right: 0 !important;
      // }
    }

    //底部mt-page相关样式
    .mt-pagertemplate {
      margin: 0;
      padding: 10px 0;
      box-sizing: border-box;
      border-top: 1px solid var(--plugin-tg-border-color);
    }
    //存在冻结列，相关样式
    &.hasFrozen {
      .e-frozenheader > .e-table,
      .e-frozencontent > .e-table {
        border-right: 0;
        box-shadow: 1px 0px 5px 0 var(--plugin-tg-shadow-color);
        position: relative;
      }
      .e-emptyrow {
        display: none;
      }
      .e-movablecontent {
        td.e-active:first-of-type:before {
          display: none;
        }
      }
    }

    //修改谷歌内核浏览器滚动条样式
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }
    ::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: var(--scroll-bar-track-color);
    }

    ::-webkit-scrollbar-thumb {
      background-color: var(--scroll-bar-thumb-color);
      border-radius: 2px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--scroll-bar-thumb-hover-color);
    }

    ::-webkit-scrollbar-thumb:active {
      background-color: var(--scroll-bar-thumb-active-color);
    }
  }
}
::v-deep .chosen-list-height {
  height: 100%;
  > .e-grid {
    height: 100%;
    .e-gridcontent {
      height: calc(100% - 40px);
      .e-content {
        height: 100% !important;
      }
    }
  }
}
</style>
