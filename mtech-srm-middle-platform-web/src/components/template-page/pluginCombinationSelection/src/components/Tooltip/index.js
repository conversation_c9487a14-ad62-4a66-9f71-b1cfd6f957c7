import Vue from 'vue'
import MtTooltip from './base.vue'

const MtTooltipConstructor = Vue.extend(MtTooltip)

const NAME = '$combinationTooltip'
const COMPONENT = (options) => {
  const { ...rest } = options
  const instance = new MtTooltipConstructor({
    propsData: {
      ...rest
    }
  })
  instance.vm = instance.$mount()
  options.target.after(instance.vm.$el)
  instance.vm.visible = true
  instance.vm.$on('closed', () => {
    instance.vm.$destroy()
  })

  instance.vm.$on('close', () => {
    instance.vm.visible = false
    instance.vm.$el.remove()
  })

  return instance.vm
}
export { NAME, COMPONENT }
