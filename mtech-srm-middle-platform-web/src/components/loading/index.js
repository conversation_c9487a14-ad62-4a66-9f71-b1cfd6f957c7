import loadingVue from './loading'

const loading = {
  install: function (Vue) {
    let Layer = Vue.extend(loadingVue)
    let layer = new Layer()
    document.body.appendChild(layer.$mount().$el)

    Vue.prototype.$loading = (option) => {
      layer.params = {
        type: 'loading',
        show: true,
        txt: 'loading',
        isOpen: false,
        ...option
      }
    }

    Vue.prototype.$hloading = () => {
      layer.params.show = false
    }
  }
}

export default loading
