<template>
  <div class="upload">
    <div class="tinymce_editor" :cssClass="cssClass">
      <Editor
        ref="tinymceEditor"
        :id="tinymceId"
        v-model="content"
        :init="init"
        v-if="isShow"
      ></Editor>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/themes/mobile'
import 'tinymce/icons/default/icons'
import 'tinymce/plugins/advlist' // 高级列表
import 'tinymce/plugins/anchor' // 锚点插件
import 'tinymce/plugins/autolink' // 自动链接
import 'tinymce/plugins/autoresize' // 编辑器自适应
import 'tinymce/plugins/autosave' // 自动存稿
import 'tinymce/plugins/bbcode' //
import 'tinymce/plugins/charmap' // 特殊字符
import 'tinymce/plugins/code' // 编辑源码
import 'tinymce/plugins/codesample' // 代码示例
import 'tinymce/plugins/directionality' // 文字方向
import 'tinymce/plugins/emoticons' // 表情插件
import 'tinymce/plugins/fullpage' // 文档属性
import 'tinymce/plugins/fullscreen' // 全屏
import 'tinymce/plugins/help' // 帮助
import 'tinymce/plugins/hr' // 水平分割线
import 'tinymce/plugins/image' // 插入编辑图片
import 'tinymce/plugins/imagetools' // 图片编辑工具
import 'tinymce/plugins/importcss' // 引入css
import 'tinymce/plugins/insertdatetime' // 插入日期时间
import 'tinymce/plugins/legacyoutput' // 输出HTML4
import 'tinymce/plugins/link' // 超链接
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/media' // 插入编辑媒体
import 'tinymce/plugins/nonbreaking' // 插入不间断空格
import 'tinymce/plugins/noneditable' // 不可编辑元素
import 'tinymce/plugins/pagebreak' // 插入分页符
import 'tinymce/plugins/paste' // 粘贴插件
import 'tinymce/plugins/preview' // 预览
import 'tinymce/plugins/print' // 打印
import 'tinymce/plugins/quickbars' // 快速工具栏
import 'tinymce/plugins/save' // 保存
import 'tinymce/plugins/searchreplace' // 查找替换
import 'tinymce/plugins/spellchecker' // 拼写检查
import 'tinymce/plugins/tabfocus' // tab切入切出
import 'tinymce/plugins/table' // 表格插件
import 'tinymce/plugins/template' // 内容模板
import 'tinymce/plugins/textpattern' // 快速排版
import 'tinymce/plugins/toc' // 目录生成器
import 'tinymce/plugins/visualblocks' // 显示元素范围
import 'tinymce/plugins/visualchars' // 显示不可见字符
import 'tinymce/plugins/wordcount' // 字数统计
import './tinymce/ax_wordlimit/plugin.min.js' //字数限制

/* 增加自定义上传图片按钮 */
tinymce.PluginManager.add('axupimgs', (editor, url) => {
  let pluginName = '图片上传'
  editor.ui.registry.getAll().icons.axupimgs ||
    editor.ui.registry.addIcon(
      'axupimgs',
      '<svg viewBox="0 0 1280 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M1126.2,779.8V87.6c0-24-22.6-86.9-83.5-86.9H83.5C14.7,0.7,0,63.7,0,87.7v692c0,36.2,29.2,89.7,83.5,89.7l959.3-1.3c51.7,0,83.5-42.5,83.5-88.3zm-1044,4V86.3h961.6V783.7H82.2v0.1z" fill="#53565A"/><path d="M603,461.6L521.1,366.3,313,629.8,227.2,546.8,102.4,716.8H972.8v-170L768.2,235.2,603.1,461.6zM284.6,358.4a105.4,105.4,0,0,0,73.5-30c19.5-19.1,30.3-45,30.2-71.8,0-56.8-45.9-103-102.4-103-56.6,0-102.4,46.1-102.4,103C183.4,313.5,228,358.4,284.6,358.4z" fill="#9598A0"/><path d="M1197.7,153.6l-0.3,669.3s13.5,113.9-67.4,113.9H153.6c0,24.1,23.9,87.2,83.5,87.2h959.3c58.3,0,83.6-49.5,83.6-89.9V240.8c-0.1-41.8-44.9-87.2-82.3-87.2z" fill="#53565A"/></svg>'
    )
  editor.ui.registry.addButton('axupimgs', {
    icon: 'axupimgs',
    tooltip: pluginName,
    onAction: function () {
      document.querySelector('.custom_multi_map_upload input').click()
    }
  })
  return {
    getMetadata: function () {
      return {
        name: pluginName,
        url
      }
    }
  }
})

import './tinymce/skins/ui/oxide/skin.css' //皮肤编辑器需要一个skin才能正常工作，所以要设置一个skin_url指向之前复制出来的skin文件
import './tinymce/langs/zh_CN.js' //简体中文
import './tinymce/langs/zh_TW.js'

export default {
  name: 'RichTextEditor',
  components: {
    Editor
  },
  model: {
    prop: 'value',
    event: 'content'
  },
  props: {
    // 富文本id，当前页面需要多个富文本共存的话需要设置此项
    richTextId: {
      type: String,
      default: 'customTinymce'
    },
    // 	绑定值
    value: {
      type: String
    },
    // 配置选中元素可用的背景色调色板，配合工具栏中的backgroundColor使用
    backgroundColor: {
      type: Object
    },
    // 添加到根元素的css样式
    cssClass: {
      type: String
    },
    // 是否支持跨域脚本
    enableHtmlSanitizer: {
      type: Boolean,
      default: true
    },
    // 是否支持改变尺寸
    enableResize: {
      type: Boolean,
      default: false
    },
    // 是否支持从右向左进行渲染
    enableRtl: {
      type: Boolean,
      default: false
    },
    // 配置可选用的字体
    fontSize: {
      type: String,
      default: '8px 10px 12px 14px 16px 18px 24px 36px'
    },
    // 富文本宽度
    width: {
      type: [Number, String],
      default: '100%'
    },
    // 富文本高度
    height: {
      type: [Number, String],
      default: 400
    },
    // 最大宽度
    maxWidth: {
      type: [Number, String]
    },
    // 最大高度
    maxHeight: {
      type: [Number, String]
    },
    // 默认显示项
    pluginsContent: {
      type: String,
      default:
        'lists, advlist autolink autosave charmap directionality fullscreen hr image link nonbreaking pagebreak preview quickbars searchreplace tabfocus table image code wordcount anchor print'
    },
    // 默认展示项配置
    toolbar: {
      type: [Number, String],
      // default: `bold italic underline | formatselect alignleft aligncenter alignright alignjustify bullist numlist | link image backcolor | code undo redo` //默认展示
      default: `code undo redo restoredraft | cut copy paste | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent lineheight | formatselect fontselect fontsizeselect | bullist numlist | subscript superscript removeformat ltr rtl | table image media charmap emoticons hr pagebreak print nonbreaking searchreplace preview | fullscreen` //全部功能
    },
    //是否隐藏编辑器底部的状态栏
    statusbar: {
      type: Boolean,
      default: true
    },
    // 只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean
    },
    // 隐藏底栏的元素路径
    elementpath: {
      type: Boolean,
      default: true
    },
    // 行高列表
    lineheightFormats: {
      type: String,
      default: '1 1.1 1.2 1.3 1.4 1.5 2'
    },
    // 内容预展示文本
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    // 自动给其加上链接
    typeaheadUrls: {
      type: Boolean,
      default: true
    },
    // 默认协议
    linkDefaultProtocol: {
      type: String,
      default: 'https'
    },
    // 协议前缀补齐提示
    linkAssumeExternalTargets: {
      type: Boolean,
      default: true
    },
    // 上面菜单栏显示隐藏
    menubar: {
      type: Boolean,
      default: false
    },
    // 配置图片的可操作选项
    fileManagerSettings: {
      type: Object
    },
    // 	设置组件会保存的回撤操作的最大步数
    undoRedoSteps: {
      type: Number,
      default: 10
    },
    // 字体列表
    fontFormats: {
      type: String
    },
    // 图片设置
    insertImageSettings: {
      type: Object
    }
  },
  data() {
    return {
      isShow: false, //控制富文本加载以后显示
      tinymceId: this.richTextId, //ID
      content: '', //富文本内容
      allowedTypes: [], //图片格式
      size: 0, //图片大小
      saveUrl: '/api/file/user/file/uploadPublic?useType=1', //请求地址
      init: {
        selector: `textarea#${this.richTextId}`, //tinymce的id
        suffix: '.min', //后缀
        // language_url: `${process.env.NODE_ENV == 'development' ? '/static/tinymce/langs/zh_CN.js':'/tinymce/langs/zh_CN.js'}`,//汉化包
        language: 'zh_CN', //语言
        // skin_url: `${process.env.NODE_ENV == 'development' ? '/static/tinymce/skins/ui/oxide':'/tinymce/skins/ui/oxide'}`, //编辑器需要一个skin才能正常工作，所以要设置一个skin_url指向之前复制出来的skin文件
        content_style: 'img {max-width:100%;vertical-align:top;} p {margin:0;padding:0;}',
        plugins: this.pluginsContent, //指定需加载的插件
        toolbar: this.toolbar, //自定义工具栏
        inline: false, //内联模式
        height: this.height, //编辑器高度
        width: this.width, //宽
        max_width: this.maxWidth, //最大宽度
        max_height: this.maxHeight, //最大高度
        content_security_policy: this.enableHtmlSanitizer ? 'connect-src' : "default-src 'self'", //仅允许当前域名，不包括子域名 内容安全策略
        readonly: this.readonly, //只读模式
        auto_focus: true, //自动获得焦点
        branding: false, //隐藏右下角技术支持
        draggable_modal: true, //模态窗口允许拖动
        elementpath: this.elementpath, //隐藏底栏的元素路径
        font_formats: this.fontFormats, //字体列表
        fontsize_formats: this.fontSize, //文字大小列表
        lineheight_formats: this.lineheightFormats, //行高列表
        placeholder: this.placeholder, //内容预展示文本
        resize: this.enableResize, //调整编辑器大小工具
        statusbar: this.statusbar, //隐藏编辑器底部的状态栏
        toolbar_sticky: false, //粘性工具栏
        color_cols: 5, //颜色选择列表的列数
        custom_colors: true, //调色盘开关
        color_map: undefined, //自定义颜色选择列表的颜色
        visual: true, //网格线开关
        directionality: this.enableRtl ? 'rtl' : 'ltr', //文字方向
        allow_script_urls: false, //允许链接和图像url使用js
        convert_urls: true, //自动转换URL
        br_in_pre: true, //pre内回车行为
        custom_undo_redo_levels: this.undoRedoSteps, //撤销次数
        object_resizing: true, //调整大小控件开关
        typeahead_urls: this.typeaheadUrls, //当在编辑区内输入网址并回车或按下空格时，TinyMCE会分析出你键入的可能是一个网址，并在自动给其加上链接
        menubar: this.menubar, //上面菜单栏显示隐藏
        paste_data_images: false, //开启图片拖拽上传
        image_caption: false, //图片标题
        autosave_ask_before_unload: false, //当关闭或跳转URL时，弹出提示框提醒用户仍未保存变更内容。默认开启提示
        autosave_retention: '20m', //设置自动草稿的有效期。当草稿超过有效期则忽略。值是字符串，单位是分。语法是：数字加字母m，例如20分钟写作'20m'
        imagetools_toolbar: 'rotateleft rotateright | flipv fliph | editimage', //当单击内容区图片时，希望快速工具栏出现的图标，可以为以下值：rotateleft 逆时针旋转rotateright 顺时针旋转flipv 垂直翻转fliph 水平翻转editimage 编辑图片imageoptions 图片选项
        link_default_protocol: this.linkDefaultProtocol, //默认协议
        link_title: false, //标题开关
        link_assume_external_targets: this.linkAssumeExternalTargets, //协议前缀补齐提示
        link_context_toolbar: true, //链接的右键增强菜单
        pagebreak_split_block: true, //插入时拆分块元素
        quickbars_selection_toolbar: 'bold italic forecolor | link h2 h3',
        contextmenu: 'link table spellchecker', //右键关联菜单
        quickbars_insert_toolbar: false, //设置快速插入触发提供的工具栏(简称回车触发工具栏)
        // 自定义段落样式格式
        style_formats: [
          { title: 'Bold text', inline: 'b' },
          { title: 'Red text', inline: 'span', styles: { color: '#ff0000' } },
          { title: 'Red header', block: 'h1', styles: { color: '#ff0000' } },
          { title: 'Example 1', inline: 'span', classes: 'example1' },
          { title: 'Example 2', inline: 'span', classes: 'example2' },
          { title: '首行缩进', block: 'p', styles: { 'text-indent': '2em' } },
          { title: 'Table styles' },
          { title: 'Table row 1', selector: 'tr', classes: 'tablerow1' }
        ],
        style_formats_merge: true, //替换还是附加到自定义段落样式列表
        style_formats_autohide: false, //隐藏当前不可用的样式列表
        toolbar_persist: false, //内联模式始终显示工具栏
        toolbar_mode: 'wrap', //工具栏模式
        // automatic_uploads: false,//指定接收图片上传的远程地址
        // images_upload_url:'https://ej2.syncfusion.com/services/api/uploadbox/Save',//上传地址
        // images_upload_base_path: '/demo',
        image_advtab: false, //高级参数高级参数显示隐藏
        image_title: false, // 是否开启图片标题设置的选择，这里设置否
        setup: (editor) => {
          // console.log("ID为: " + editor.id + " 的编辑器即将初始化.");
          this.$emit('beforeCreate', editor)
        },
        /* 初始化结束后执行 */
        init_instance_callback: (editor) => {
          // console.log("ID为: " + editor.id + " 的编辑器已初始化完成.");
          this.$emit('created', editor)
        },
        /* 自定义图片上传 */
        images_upload_handler: (blobInfo, success, failFun) => {
          if (this.size > 0) {
            if (blobInfo.blob().size / 1024 / 1024 > this.size) {
              failFun('上传失败，图片大小请控制在' + this.size + this.$Ji18n.t('以内'))
            }
          }
          if (this.allowedTypes.length) {
            if (
              !this.allowedTypes.some((item) => {
                return item == blobInfo.blob().type
              })
            ) {
              failFun(`上传失败，请上传指定格式的图片`)
            }
          }
          let formData = new FormData()
          formData.append('UploadFiles', blobInfo.blob(), blobInfo.blob().name) //此处与源文档不一样
          axios
            .post(this.saveUrl, formData)
            .then((res) => {
              const { data = {} } = res.data
              success(
                `${window.location.origin}/api/file/common/file/viewPublicImageFile?id=${data.id}`
              )
            })
            .catch((error) => {
              failFun('图片上传失败：' + error.responseText)
            })
        }
      }
    }
  },
  created() {
    if (localStorage.getItem('internationlization')) {
      switch (localStorage.getItem('internationlization')) {
        case 'zh-CH' || 'zh':
          this.init.language = 'zh_CN'
          break
        case 'en':
          this.init.language = 'en_US'
          break
      }
    }
  },
  watch: {
    /* 监听传进来得内容 */
    value: {
      handler(newVal, oldVal) {
        this.content = newVal
      },
      immediate: true,
      depp: true
    },
    /* 监听变化内容 传递出去 */
    content: {
      handler(newVal, oldVal) {
        this.$emit('content', newVal)
        this.$emit('change', newVal)
      },
      immediate: true,
      deep: true
    },
    /* 配置颜色 */
    backgroundColor: {
      handler(newVal, oldVal) {
        const { columns, modeSwitcher, colorCode } = newVal || {}
        this.init.color_cols = columns //颜色一行几列
        this.init.custom_colors = modeSwitcher //调色盘
        const { Custom } = colorCode || {}
        this.init.color_map = Custom //颜色色值
      },
      immediate: true,
      deep: true
    },
    /* 监听图片设置 */
    insertImageSettings: {
      handler(newVal, oldVal) {
        if (newVal) {
          const { saveUrl, allowedTypes, display, width, height, path, size } = newVal || {}
          if (saveUrl) {
            this.saveUrl = saveUrl
          }
          if (allowedTypes) {
            this.allowedTypes = allowedTypes
          }
          if (size) {
            this.size = size
          }
          if (path) {
            let editor = this.$refs.tinymceEditor.editor
            editor.insertContent(`<img src="${path}" alt="">`)
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.isShow = true
      setTimeout(() => {
        this.readonly || this.disabled
          ? tinymce.editors[this.tinymceId].setMode('readonly')
          : tinymce.editors[this.tinymceId].setMode('design') // 开启只读模式 : 开启编辑模式
      }, 500)
    })
  },

  methods: {
    /* 创建富文本 */
    initTiny() {
      tinymce.init(this.init)
    },
    /* 组件销毁 */
    destroy() {
      tinyMCE.editors[this.tinymceId].destroy()
    },
    /* 设置内容 */
    setContent(val) {
      tinyMCE.editors[this.tinymceId].setContent(val)
    },
    /* 插入内容 */
    insertContent(val) {
      tinyMCE.editors[this.tinymceId].insertContent(val)
    },
    /* 光标放在最后 */
    goEnd() {
      let editor = tinyMCE.editors[this.tinymceId]
      editor.execCommand('selectAll')
      editor.selection.getRng().collapse(false)
      editor.focus()
    },
    /* 隐藏编辑器 */
    hideTiny() {
      tinyMCE.editors[this.tinymceId].hide()
    },
    /* 显示编辑器 */
    showTiny() {
      tinyMCE.editors[this.tinymceId].show()
    },
    /* 获取html */
    getHtml() {
      let cnt = tinyMCE.editors[this.tinymceId].getContent()
      return cnt
    },
    /* 获取纯文本 */
    getText() {
      let cnt = tinyMCE.editors[this.tinymceId].getContent({ format: 'text' })
      return cnt
    }
  }
}
</script>

<style lang="scss" scoped>
.upload {
  .tinymce_editor {
  }
}
</style>
