import { RouteConfig } from 'vue-router'

const routes2: Array<RouteConfig> = [
  {
    path: 'workflowtemplate',
    name: 'workflowtemplate',
    component: () => import('@/views/workflow/index.vue'), // 工作流
    meta: { name: '工作流列表' }
  },
  {
    path: 'workflowadd/:id',
    name: 'workflowadd',
    component: () => import('@/views/workflow/add.vue'), // 新增工作流
    meta: { name: '新增工作流' }
  },
  {
    path: 'workfloweditor/:id',
    name: 'workfloweditor',
    component: () => import('@/views/workflow/editor.vue'), // 工作流编辑器
    meta: { name: '工作流编辑器' }
  },
  {
    path: 'workflowhistory/:id',
    name: 'workflowhistory',
    component: () => import('@/views/workflow/history.vue'), // 历史版本
    meta: { name: '历史版本' }
  },
  {
    path: 'workflowprocess',
    name: 'workflowprocess',
    component: () => import('@/views/workflow/flowinstance.vue'), // 流程管理
    meta: { name: '流程管理' }
  },
  {
    path: 'workflowserve',
    name: 'workflowserve',
    component: () => import('@/views/workflow/serve.vue'), // 服务管理
    meta: { name: '服务管理' }
  },
  {
    path: 'workflowinterface',
    name: 'workflowinterface',
    component: () => import('@/views/workflow/interface.vue'), // 接口管理
    meta: { name: '接口管理' }
  }
]

export default routes2
