import { RouteConfig } from 'vue-router'

const routes3: Array<RouteConfig> = [
  {
    path: 'formEdit',
    name: 'formEdit',
    meta: { name: '动态表单新增' },
    component: () => import('@/views/trendForm/components/FormEdit.vue') // 动态表单新增
  },
  {
    path: 'formTemplateEdit',
    name: 'formTemplateEdit',
    meta: { name: '模板新增' },
    component: () => import('@/views/trendForm/components/FormTemplateEdit.vue') // 模板新增
  },
  {
    path: 'templateSet',
    name: 'templateSet',
    meta: { name: '模板配置' },
    component: () => import('@/views/trendForm/templateSet/index.vue') // 模板配置
  },
  {
    path: 'trendFormSet',
    name: 'trendFormSet',
    meta: { name: '动态表单配置' },
    component: () => import('@/views/trendForm/trendFormSet/index.vue') // 动态表单配置
  }
]
export default routes3
