const Router = [
  {
    path: 'system-notice-management', // 采方公告列表
    name: 'system-notice',
    component: () => import('@/views/messageCenter/systemNotice/index.vue'),
    meta: {
      name: 'management-system-notice'
    }
  },
  // 消息中心--系统公告
  {
    path: 'pur/system-notice', // 采方公告列表
    name: 'system-notice',
    component: () => import('@/views/messageCenter/systemNotice/index.vue'),
    meta: {
      name: 'pur-system-notice'
    }
  },
  {
    path: 'sup/system-notice', // 供方公告列表
    name: 'system-notice',
    component: () => import('@/views/messageCenter/systemNotice/index.vue'),
    meta: {
      name: 'sup-system-notice'
    }
  },
  {
    path: 'system-notice-management/detail', //管理详情
    name: 'detail',
    component: () => import('@/views/messageCenter/systemNotice/detail.vue'),
    meta: { title: '公告信息详情' }
  },
  {
    path: 'pur/system-notice/detail', //采方详情
    name: 'detail',
    component: () => import('@/views/messageCenter/systemNotice/detail.vue'),
    meta: { title: '公告信息详情' }
  },
  {
    path: 'sup/system-notice/detail', //供方详情
    name: 'detail',
    component: () => import('@/views/messageCenter/systemNotice/detail.vue'),
    meta: { title: '公告信息详情' }
  },
  {
    path: 'system-notice-management/add', //采方添加页面
    name: 'add',
    component: () => import('@/views/messageCenter/systemNotice/release.vue'),
    meta: { title: '公告信息添加' }
  },
  {
    path: 'system-notice-management/edit', //编辑页面
    name: 'edit',
    component: () => import('@/views/messageCenter/systemNotice/release.vue'),
    meta: { title: '公告信息编辑' }
  }
]

export default Router
