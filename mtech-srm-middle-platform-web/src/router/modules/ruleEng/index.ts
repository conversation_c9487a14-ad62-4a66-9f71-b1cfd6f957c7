import { RouteConfig } from 'vue-router'
const routes4: Array<RouteConfig> = [
  {
    // 规则设置
    path: 'ruleSet',
    name: 'ruleSet',
    component: () => import('@/views/ruleEng/ruleSet.vue'),
    meta: { name: '规则设置' }
  },
  {
    path: 'editRule/:id',
    name: 'editRule',
    component: () => import('@/views/ruleEng/editRule.vue'),
    meta: {
      name: '编辑规则',
      parent: {
        path: '/editRule',
        meta: { name: '规则设置' }
      }
    }
  },
  {
    // 对象设置
    path: 'ruleEng/objectSet',
    name: 'objectSet',
    component: () => import('@/views/ruleEng/objectSet.vue'),
    meta: { name: '对象设置' }
  },
  {
    // 对象详情
    path: 'ruleEng/objectDetails/:id/:modelName/:createTime/:updateTime',
    name: 'objectDetails',
    component: () => import('@/views/ruleEng/objectDetails.vue'),
    meta: { name: '对象字段' }
  },
  {
    // 方法列表
    path: 'ruleEng/methodList',
    name: 'methodList',
    component: () => import('@/views/ruleEng/methodList.vue'),
    meta: { name: '方法列表' }
  },
  {
    // 项目列表
    path: 'ruleEng/projectList',
    name: 'projectList',
    component: () => import('@/views/ruleEng/projectList.vue'),
    meta: { name: '项目列表' }
  },
  {
    path: 'fieldSet',
    name: 'fieldSet',
    meta: { name: '字段配置' },
    component: () => import('@/views/trendForm/fieldSet/index.vue') // 字段配置
  }
]

export default routes4
