import { RouteConfig } from 'vue-router'

const routes5: Array<RouteConfig> = [
  {
    path: 'business-data',
    name: 'business-data',
    meta: { name: '业务数据查看', keepAlive: true },
    component: () => import('@/views/dataManage/businessData/index.vue') // 业务数据查看
  },
  {
    path: 'field-configuration',
    name: 'field-configuration',
    meta: { name: '业务字段配置', keepAlive: true },
    component: () => import('@/views/dataManage/fieldConfiguration/index.vue') // 业务字段配置
  },
  {
    path: 'task-configuration',
    name: 'task-configuration',
    meta: { name: '任务配置', keepAlive: true },
    component: () => import('@/views/dataManage/taskConfiguration/index.vue') // 任务配置
  }
]
export default routes5
