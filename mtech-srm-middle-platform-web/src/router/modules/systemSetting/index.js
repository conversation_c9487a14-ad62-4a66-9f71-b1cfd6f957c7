/**
 * 系统设置
 */
const Router = [
  // 打印模板管理
  {
    path: '/middlePlatform/printTemplate',
    name: 'print-template',
    component: () => import('@/views/systemSetting/printTemplate/index.vue'),
    meta: {
      name: '打印模板管理'
    }
  },
  // 灰度发布管理
  {
    path: '/middlePlatform/grayscaleManage',
    name: 'grayscale-manage',
    component: () => import('@/views/systemSetting/grayscaleManage/index.vue'),
    meta: {
      name: '灰度发布管理'
    }
  }
]

export default Router
