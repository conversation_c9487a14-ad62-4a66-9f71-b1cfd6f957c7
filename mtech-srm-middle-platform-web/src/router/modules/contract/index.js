const Router = [
  // 后台配置
  {
    path: 'contractBuyerList', // 采方合同列表
    name: 'contractBuyerList',
    component: () => import('@/views/contract/contractBuyerList.vue')
  },
  // 后台配置
  {
    path: 'contractSupplierList', // 供方合同列表
    name: 'contractSupplierList',
    component: () => import('@/views/contract/contractSupplierList.vue')
  },
  {
    path: 'createContract', // 创建合同
    name: 'createContract',
    component: () => import('@/views/contract/createContract.vue')
  },
  {
    path: 'feedbackContract', // 合同反馈
    name: 'feedbackContract',
    component: () => import('@/views/contract/feedbackContract.vue')
  },
  {
    path: 'contractDetail', // 合同编辑
    name: 'contractDetail',
    component: () => import('@/views/contract/contractDetail.vue')
  },
  {
    path: 'contractPrivew', // 合同查看
    name: 'contractPrivew',
    component: () => import('@/views/contract/contractPrivew.vue')
  }
]

export default Router
