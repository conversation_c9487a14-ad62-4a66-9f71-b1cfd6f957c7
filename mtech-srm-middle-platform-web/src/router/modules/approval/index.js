/*
 * @Author: your name
 * @Date: 2021-10-12 13:58:49
 * @LastEditTime: 2021-11-03 14:57:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\router\modules\approval\index.js
 */

const Router = [
  // 后台配置
  {
    path: 'approval-center', // 审批中心
    name: 'approval-center',
    component: () => import('@/views/ApprovalCenter/index.vue'),
    meta: {
      name: '审批任务'
    }
  },
  // 后台配置
  {
    path: 'approval-config', // 审批流配置
    name: 'approval-config',
    component: () => import('@/views/ApprovalProcess/index.vue'),
    meta: {
      name: '审批流配置'
    }
  },
  {
    path: 'approval-template', // 审批模板
    name: 'approval-template',
    component: () => import('@/views/ApprovalTemplate/index.vue'),
    meta: {
      name: '审批流模板配置'
    }
  },
  {
    path: 'template-add', // 添加审批流模板
    name: 'template-add',
    component: () => import('@/views/ApprovalTemplate/add.vue'),
    meta: {
      name: '添加审批流模板'
    }
  },
  {
    path: 'template-edit/:id/:aid/:tid', // 编辑审批流模板
    name: 'template-edit',
    component: () => import('@/views/ApprovalTemplate/edit.vue'),
    meta: {
      name: '编辑审批流模板'
    }
  },
  {
    path: 'approvalDialog', // 编辑审批流模板
    name: 'approvalDialog',
    component: () => import('@/views/ApprovalCenter/approvalDialog.vue'),
    meta: {
      name: '审批详情'
    }
  },
  {
    path: 'pur/taskList', // 任务列表-采方
    name: 'taskList',
    component: () => import('@/views/taskList/index.vue'),
    meta: {
      name: '任务列表'
    }
  },
  {
    path: 'sup/taskList', // 任务列表-供方
    name: 'missionList',
    component: () => import('@/views/taskList/index.vue'),
    meta: {
      name: '任务列表'
    }
  },
  {
    path: 'pur/taskManage', // 任务管理-采方
    name: 'taskManage',
    component: () => import('@/views/taskManage/index.vue'),
    meta: {
      name: '任务管理'
    }
  },
  {
    path: 'sup/taskManage', // 任务管理-供方
    name: 'supTaskManage',
    component: () => import('@/views/taskManage/index.vue'),
    meta: {
      name: '任务管理'
    }
  }
]

export default Router
