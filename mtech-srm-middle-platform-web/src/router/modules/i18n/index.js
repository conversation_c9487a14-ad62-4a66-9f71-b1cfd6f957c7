const Router = [
  {
    path: 'i18n-dictGroup', // 词组管理
    name: 'i18n-dictGroup',
    component: () => import('@/views/i18n/dictGroup/index.vue')
  },
  {
    path: 'i18n-lang', // 语言管理
    name: 'i18n-lang',
    component: () => import('@/views/i18n/lang/index.vue')
  },
  {
    path: 'i18n-entry', // 词条管理
    name: 'i18n-entry',
    component: () => import('@/views/i18n/entry/index.vue')
  }
]

export default Router
