/*
 * @Author: your name
 * @Date: 2021-09-29 17:47:40
 * @LastEditTime: 2021-10-12 14:40:17
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\router\index.js
 */
var modules = [
  {
    path: 'personal',
    name: 'personal',
    component: () => import('@/views/personal/index.vue'), // 个人中心
    meta: { name: '个人中心' }
  }
]
const routerJSFile = require.context('./modules', true, /\.j|ts$/)
// console.log("apiJSFile", routerJSFile);

routerJSFile.keys().forEach((key) => {
  const mod = routerJSFile(key)
  const _mode = mod.default ? mod.default : mod
  modules = modules.concat(_mode)
})

console.log('modules', modules)

const routes = [
  {
    path: '/middlePlatform',
    component: () => import('@/views/public.vue'),
    children: modules
  }
]

// console.log("routes", routes);

export default routes
