import Vue from 'vue'

// 引入样式
import './styles/index.scss'
import '@mtech-form-design/deploy/build/esm/bundle.css'

import loading from './components/loading'
Vue.use(loading)

// 引入权限
import commonPermission from '@mtech/common-permission'
Vue.use(commonPermission)

// mtech-ui组件
import MtDialog from '@mtech-ui/dialog'
import MtButton from '@mtech-ui/button'
import MtForm from '@mtech-ui/form'
import MtRow from '@mtech-ui/row'
import MtCol from '@mtech-ui/col'
import MtFormItem from '@mtech-ui/form-item'
import MtInput from '@mtech-ui/input'
import MtDataGrid from '@mtech-ui/data-grid'
import MtSelect from '@mtech-ui/select'
import MtTabs from '@mtech-ui/tabs'
import MtTooltip from '@mtech-ui/tooltip'
import MtRadio from '@mtech-ui/radio'
import MtToast from '@mtech-ui/toast'
import MtSwitch from '@mtech-ui/switch'
import MtIcon from '@mtech-ui/icon'
import MtCheckbox from '@mtech-ui/checkbox'
import MtDateTangePicker from '@mtech-ui/date-range-picker'
import MtDateTimePicker from '@mtech-ui/date-time-picker'
import MtTimePicker from '@mtech-ui/time-picker'
import MtTreeView from '@mtech-ui/tree-view'
import MtDatePicker from '@mtech-ui/date-picker'
import MtInputNumber from '@mtech-ui/input-number'
import MtDropDownTree from '@mtech-ui/drop-down-tree'
import MtMultiSelect from '@mtech-ui/multi-select'
import MtTag from '@mtech-ui/tag'
import MtUploader from '@mtech-ui/uploader'
import MtProgress from '@mtech-ui/progress'
import MtPage from '@mtech-ui/page'
import MtCommonTree from '@mtech/common-tree-view'

import * as Dialog from '@/components/Dialog'
import * as Toast from '@/components/Toast'
import * as ToolTip from '@/components/Tooltip'

import MtMicroLoading from '@/components/micro-loading'
import '@mtech/common-loading/build/esm/bundle.css'
import '@digis/digis-bpmn-editor/lib/css/style.css'
import '@mtech/common-tree-view/build/esm/bundle.css'

Vue.use(MtDialog)
Vue.use(MtButton)
Vue.use(MtForm)
Vue.use(MtRow)
Vue.use(MtCol)
Vue.use(MtFormItem)
Vue.use(MtInput)
Vue.use(MtDataGrid)
Vue.use(MtSelect)
Vue.use(MtTabs)
Vue.use(MtTooltip)
Vue.use(MtRadio)
Vue.use(MtToast)
Vue.use(MtSwitch)
Vue.use(MtIcon)
Vue.use(MtCheckbox)
Vue.use(MtDateTangePicker)
Vue.use(MtDateTimePicker)
Vue.use(MtTimePicker)
Vue.use(MtTreeView)
Vue.use(MtDatePicker)
Vue.use(MtInputNumber)
Vue.use(MtDropDownTree)
Vue.use(MtMultiSelect)
Vue.use(MtTag)
Vue.use(MtUploader)
Vue.use(MtProgress)
Vue.use(MtPage)

Vue.prototype[Dialog.NAME] = Dialog.COMPONENT
Vue.prototype[Toast.NAME] = Toast.COMPONENT
Vue.prototype[ToolTip.NAME] = ToolTip.COMPONENT

Vue.component('mt-loading', MtMicroLoading)
Vue.component('mt-progress', MtProgress)
Vue.component('mt-page', MtPage)
Vue.component('mt-common-tree', MtCommonTree)

// mt-template-page 组件
import MtTemplatePage from '@/components/template-page'
Vue.component('mt-template-page', MtTemplatePage)
