import { API } from '@mtech-common/http'

const getEnterprise = ($axios: any) => ({
  // 我创建的企业-已创建
  queryCreate: () => {
    return $axios.get('/platform/user/enterprise/queryCreate')
  },
  // 我创建的企业-审核中
  queryPending: () => {
    return $axios.get('/platform/user/enterprise/queryPending')
  },
  // 我创建的企业-已创建
  queryReject: () => {
    return $axios.get('/platform/user/enterprise/queryReject')
  },
  // 我创建的企业-驳回查看信息
  enterpriseDetail: (params: any) => {
    return $axios.get('/platform/common/enterprise/detail', params)
  },

  // 我的邀请-待处理
  companyInviteWait: () => {
    return $axios.get('/usercenter/user/company-invite-user/unapprovaled-company-invite-list')
  },
  // 我的邀请-已处理
  companyInviteOver: () => {
    return $axios.get('/usercenter/user/company-invite-user/approvaled-company-invite-list')
  },
  // 我的邀请-接受拒绝
  companyOperation: (params: any) => {
    return $axios.put('/usercenter/user/company-invite-user/update-approval-status', params, {
      useNotify: true
    })
  },
  // 我的企业-我加入的企业列表
  queryAddDetail: (params: any) => {
    return $axios.post('/usercenter/user/user-join-tenant/query', params)
  },
  // 获取版本内容
  dropDownList: () => {
    return $axios.get('/platform/common/version/dropDownList')
  },
  // 获取企业默认租户版本
  queryDefaultVersion: (params: any) => {
    return $axios.post('/platform/user/enterprise-apply-version/queryDefaultVersion', params)
  },

  // 我的企业-申请开通
  addEnterprise: (params: any) => {
    return $axios.post('/platform/user/enterprise-apply-version/add', params, {
      useNotify: true
    })
  },
  // 申请开通-查看版本详情
  getVersionDetail: (params: any) => {
    return $axios.post('/platform/common/version/permissionList/' + params)
  },
  // 企业名称、企业编码相互带出
  getEnterpriseByCodeOrName: (params: any) => {
    return $axios.post('/platform/user/tenant/list/all', params)
  },
  // 申请新企业-提交
  addNewBusiness: (params: any) => {
    return $axios.post('/usercenter/user/user-join-tenant/add', params, {
      useNotify: true
    })
  },
  // 设置默认租户/企业
  setDefaultTenant: (params: any) => {
    return $axios.post('/usercenter/user/defaultTenantConfig/add', params, {
      useNotify: true
    })
  },
  // 查找可转化为租户的企业列表
  enterpriseAvailableList: (params: any) => {
    return $axios.post('/platform/admin/enterprise/availableList', params)
  },

  // 平台账号注册
  register: (params: any) => {
    return $axios.post('/iam/common/account/register', params)
  },
  // 获取注册时的手机验证码
  getVerifyCode: (params: any) => {
    return $axios.post('/iam/common/account/sms_register_code', params)
  },
  // 获取注册时的邮箱验证码
  getMailSmsCode: (params: any) => {
    return $axios.post('/iam/common/account/mail_register_code', params)
  },
  // 验证企业名称是否已经注册
  validateCompanyName: (params: any) => {
    return $axios.get('/platform/common/enterprise/validateCompanyName', params)
  },
  // 保存企业头部信息
  enterpriseSaveHeader: (params: any) => {
    return $axios.post('/platform/common/enterprise/saveHeader', params)
  },
  // 保存企业详情信息
  enterpriseSaveMain: (params: any) => {
    return $axios.post('/platform/common/enterprise/saveMain', params)
  },
  // 在平台站中保存企业详情信息
  saveMainAfterLogin: (params: any) => {
    return $axios.post('/platform/user/enterprise/saveMainAfterLogin', params)
  },
  // 绑定当前登录用户账号并提交审核
  bindUserIdToEnterprise: (params: any) => {
    return $axios.get('/platform/admin/enterprise/bindUserId', params)
  },
  // 企业注册审核通过
  enterpriseFlowApprove(params: any) {
    // return $axios.post('/platform/enterprise/register/flow/approve', params)
    return $axios.post('/platform/admin/enterprise/approve', params)
  },
  // 企业注册审核驳回
  enterpriseFlowBack(params: any) {
    // return $axios.post('/platform/enterprise/register/flow/back', params)
    return $axios.post('/platform/admin/enterprise/back', params)
  },

  // 账号信息-获取当前租户信息
  getAccount: () => {
    return $axios.get('/masterDataManagement/tenant/employee/userEmployeeInfos')
  },
  // 获取账号信息
  getAccountDetail: () => {
    return $axios.post('/iam/tenant/account/info')
  },
  // 修改账号信息
  changeAccount: (params: any) => {
    return $axios.post('/iam/tenant/account/info/change', params, {
      useNotify: true
    })
  },
  // 修改手机，获取旧手机验证码
  getVerificationCode: () => {
    return $axios.post('/iam/tenant/account/sms_info_change_code', '', {
      useNotify: true
    })
  },
  // 修改手机，获取新手机验证码
  getVerificationCodeNew: (params: any) => {
    return $axios.post('/iam/common/account/sms_mobile_check_code', params, {
      useNotify: true
    })
  },
  // 获取当前用户组织架构信息
  getTenantOrg: () => {
    return $axios.get('/masterDataManagement/tenant/user/findUserOrg')
  },
  // 获取用户详情
  getUserDetail: (params: any) => {
    return $axios.get('/masterDataManagement/findUserEmployeeInfoById', params)
  },
  // 企业注册审核提交
  submitEnterpriseRegister(params: any) {
    return $axios.post('/platform/enterprise/register/flow/submit', params)
  },
  // 筛选产品服务
  getEshopTree(params: any = {}) {
    return $axios.post('/masterDataManagement/common/category/platform-product-tree', params)
  },
  // 根据父级获取产品服务
  findPlatformProduct(params: any = {}) {
    return $axios.post(
      '/masterDataManagement/common/category/find-platform-product-child-categorys',
      params
    )
  },

  // 查询征信企业基本信息接口
  queryEnterpriseInfoByName(params: any = {}) {
    return $axios.get('/platform/common/enterprise/queryEnterpriseInfoByName', params)
  }
})

export default getEnterprise(API)
