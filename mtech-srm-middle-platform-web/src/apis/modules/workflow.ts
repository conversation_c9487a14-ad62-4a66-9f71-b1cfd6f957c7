import { API } from '@mtech-common/http'

const getWorkflow = ($axios: any) => ({
  /**
   * 获取模板分类树
   */
  categoryTree: (params: any) => {
    return $axios.get('/flow/common/category/tree', params)
  },

  /**
   * 获取分类树
   */
  categoryProcessTree: (params: any) => {
    return $axios.get('/flow/tenant/category/tree', params)
  },

  /**
   * 添加分类树节点
   * @param params
   * @returns
   */
  addCategoryNode(params: any) {
    return $axios.post('/flow/tenant/category/add', params)
  },

  /**
   * 更新分类树节点
   * @param params
   * @returns
   */
  updateCategoryNode(params: any) {
    return $axios.post('/flow/tenant/category/update', params)
  },

  /**
   * 删除分类树节点
   * @param params
   * @returns
   */
  deleteCategoryTreeNode(params: any) {
    return $axios.delete('/flow/tenant/category/delete', params)
  },

  /**
   * 分页查询模板,
   * @param params
   * @returns
   */
  getTemplatePage(params: any) {
    return $axios.get('/flow/tenant/template/page', params)
  },

  /**
   * 查询模板详情
   */
  getTemplateData(params: any) {
    return $axios.get('/flow/tenant/template/getData', params)
  },

  /**
   * 新增工作流和模板
   */
  addFlowTemplate(params: any) {
    return $axios.post('/flow/tenant/template/add', params)
  },

  /**
   * 新增工作流和模板的校验规则
   */
  addFlowTemplateValid(params: any) {
    return $axios.get('/flow/tenant/template/add-valid', params)
  },

  /**
   * 保存已经新增但尚未发布的工作流或模板
   * @param params
   * @returns
   */

  saveFlowTemplate(params: any) {
    return $axios.post('/flow/tenant/template/save', params)
  },

  // 发布模板
  publishTemplate(params: any) {
    return $axios.post('/flow/tenant/template/publish', params)
  },

  // 发布模板通过id
  publishTemplateById(params: any) {
    return $axios.post('/flow/tenant/template/publishById', params)
  },

  // 删除模板
  deleteTemplate(params: any) {
    return $axios.delete('/flow/tenant/template/delete', params)
  },

  /**
   * 获取历史版本列表
   * @param params
   * @returns
   */
  tempateHistoryPage(params: any) {
    return $axios.get('/flow/tenant/template/historyPage', params)
  },

  /**
   * 获取版本的svg图片
   * @param params
   * @returns
   */
  templateHistoryImg(params: any) {
    return $axios.get('/flow/tenant/template/historyImg', params)
  },

  /**
   * 回滚历史版本
   * @param params
   * @returns
   */
  rollbackHistory(params: any) {
    return $axios.post('/flow/tenant/template/rollbackHistory', params)
  },

  // 分页查询工作流服务
  flowServicePage(params: any) {
    return $axios.get('/flow/tenant/flowService/page', params)
  },

  // 获取服务详情
  flowServiceInfo(params: any) {
    return $axios.get('/flow/tenant/flowService/info', params)
  },

  // 更新服务详情
  flowServiceUpdate(params: any) {
    return $axios.post('/flow/tenant/flowService/update', params)
  },

  // 添加服务
  flowServiceAdd(params: any) {
    return $axios.post('/flow/tenant/flowService/add', params)
  },

  // 删除服务
  flowServiceDelete(params: any) {
    return $axios.delete('/flow/tenant/flowService/delete', params)
  },

  // 分页查询工作流接口
  flowInterfacePage(params: any) {
    return $axios.get('/flow/tenant/flowInterface/page', params)
  },

  // 添加工作流接口
  flowInterfaceAdd(params: any) {
    return $axios.post('/flow/tenant/flowInterface/add', params)
  },

  // 删除工作流接口
  flowInterfaceDelete(params: any) {
    return $axios.delete('/flow/tenant/flowInterface/delete', params)
  },

  // 获取接口详情
  flowInterfacInfo(params: any) {
    return $axios.get('/flow/tenant/flowInterface/info', params)
  },

  // 更新接口内容
  flowInterfacUpdate(params: any) {
    return $axios.post('/flow/tenant/flowInterface/update', params)
  },

  /**
   * 查询部门树
   */
  deptTree: (params: any) => {
    return $axios.get('/flow/tenant/org/deptTree', params)
  },
  /**
   * 查询用户信息
   */
  userPage: (params: any) => {
    return $axios.get('/flow/tenant/org/userPage', params)
  },

  // 获取列表
  getPageModel: (params: any) => {
    return $axios.get('/flow/tenant/model/page', params)
  }
})

export default getWorkflow(API)
