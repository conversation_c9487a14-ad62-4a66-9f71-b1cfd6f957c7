import { API } from '@mtech-common/http'

const getFile = ($axios: any) => ({
  // 文件服务提供的预览地址
  imageUrl: (params: any) => {
    return $axios.get('/file/user/file/publicImageFileUrl', params)
  },
  // 通过文件ID获取文件
  downloadPublicFile: (params: any) => {
    return $axios.get('/file/user/file/viewPublicImageFile?id=' + params)
  },
  // Header: api-token
  uploadLogo(params: any) {
    return $axios.post('/file/user/file/uploadLogo', params)
  },

  // 上传公共文件
  uploadPublicFile(params: any) {
    return $axios.post('/file/user/file/uploadPublic', params)
  },

  // 删除公共文件
  deletePublicFile(params: any) {
    return $axios.get('/file/user/file/deletePublicFile', params)
  }
})

export default getFile(API)
