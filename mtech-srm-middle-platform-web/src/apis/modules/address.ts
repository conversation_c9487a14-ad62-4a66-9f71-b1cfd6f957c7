import { API } from '@mtech-common/http'

const getAddress = ($axios: any) => ({
  // 分页列表
  addressList: (params: any) => {
    return $axios.post('/usercenter/user/myAddress/listForPage', params)
  },
  // 新增
  addressAdd: (params: any) => {
    return $axios.post('/usercenter/user/myAddress/add', params)
  },
  // 删除
  addressDelete: (params: any) => {
    return $axios.delete('/usercenter/user/myAddress/delete?id=' + params.id)
  },
  // 详情
  addressDetail: (params: any) => {
    return $axios.post('/usercenter/user/myAddress/detail', params)
  },
  // 设置别名
  addressAlias: (params: any) => {
    return $axios.patch('/usercenter/user/myAddress/setAlias', params)
  },
  // 默认地址
  addressDefault: (params: any) => {
    return $axios.patch('/usercenter/user/myAddress/setDefaultAddress', params)
  },
  // 更新
  addressUpdate: (params: any) => {
    return $axios.put('/usercenter/user/myAddress/update', params)
  },
  // 检查用户下是否存在采购商
  addressCheckExistBuyer: (params: any) => {
    return $axios.get('usercenter/user/myAddress/checkExistBuyer', params)
  }
})

export default getAddress(API)
