/*
 * @Author: your name
 * @Date: 2021-10-18 10:03:56
 * @LastEditTime: 2021-10-25 11:51:33
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\apis\modules\approvalConfig.js
 */
import { API } from '@mtech-common/http'
import qs from 'qs'

const PROXY_BASE = 'approveCenter/approve' // 请求接口的前缀
const CONFIG_ACTION = '/pointWorkflowRel'
const BASE_DATA = '/point'
const TEMPLATE = '/template'

export const NAME = 'approvalConfig'

export const approvalConfigList = `${PROXY_BASE}${CONFIG_ACTION}/configList`
export const approvalConfigDelete = `${PROXY_BASE}${CONFIG_ACTION}/delete`
export const approvalUpdateStatus = `${PROXY_BASE}${CONFIG_ACTION}/enable`
export const saveOrUpdate = (params) => {
  return API.post(`${PROXY_BASE}${CONFIG_ACTION}/saveOrUpdate`, params)
}

export const queryApplicationList = (params) => {
  return API.get(`${PROXY_BASE}${BASE_DATA}/queryApplicationList`, params)
}
export const queryMenuList = (params) => {
  return API.get(`${PROXY_BASE}${BASE_DATA}/queryMenuList`, params)
}
export const queryPointList = (params) => {
  return API.get(`${PROXY_BASE}${BASE_DATA}/queryPointList`, params)
}
export const queryBusinessTypeList = (params) => {
  return API.get(`${PROXY_BASE}${BASE_DATA}/queryBusinessTypeList`, params)
}
export const getConfigById = (params) => {
  return API.post(`${PROXY_BASE}${CONFIG_ACTION}/getConfigById`, qs.stringify(params), {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

export const chengeStatus = (params) => {
  return API.post(`${PROXY_BASE}${CONFIG_ACTION}/enable`, params)
}
export const deleteConfig = (params) => {
  return API.post(`${PROXY_BASE}${CONFIG_ACTION}/delete`, qs.stringify(params), {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
export const getListByApplicationId = (params) => {
  return API.get(`${PROXY_BASE}${TEMPLATE}/getListByApplicationId`, params)
}
export const checkConfigIsOnly = (params) => {
  return API.post(`${PROXY_BASE}${CONFIG_ACTION}/checkConfigIsExsit`, params)
}

export const getTree = (params) => {
  return API.get(`${PROXY_BASE}${CONFIG_ACTION}/getTree`, params)
}
