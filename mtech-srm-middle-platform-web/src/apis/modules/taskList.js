import { API } from '@mtech-common/http'
const MESSAGE = '/message' // 请求接口的前缀
// 任务列表 消息条数-采方
export const todoCount = (params) => {
  return API.post(`${MESSAGE}/user/todoHeader/count`, params)
}
// 任务列表 消息条数-供方
export const supplierTodoCount = (params) => {
  return API.post(`${MESSAGE}/user/supplierTodoHeader/count`, params)
}
// 任务列表 待办列表查询-采方
export const todoQuery = (params) => {
  return API.post(`${MESSAGE}/user/todoHeader/query`, params)
}
// 任务列表 待办列表查询-供方
export const supplierTodoQuery = (params) => {
  return API.post(`${MESSAGE}/user/supplierTodoHeader/query`, params)
}
// 任务列表 待办列表管理员查询-采方
export const adminTodoQuery = (params) => {
  return API.post(`${MESSAGE}/admin/todoHeader/queryBuilder`, params)
}
// 任务列表 待办列表管理员查询-供方
export const adminSupplierTodoQuery = (params) => {
  return API.post(`${MESSAGE}/admin/supplierTodoHeader/queryBuilder`, params)
}
// 任务列表 管理员删除待办列表-采方
export const todoDelete = (params) => {
  return API.post(`${MESSAGE}/admin/todoHeader/batch-delete`, params)
}
// 任务列表 管理员删除待办列表-供方
export const supplierTodoDelete = (params) => {
  return API.post(`${MESSAGE}/admin/supplierTodoHeader/batch-delete`, params)
}
// 归档任务 - 查询
export const getArchiveTaskList = (params) =>
  API.post('/statistics/tenant/archive/v2/task/query', params)
// 归档任务 - 新增
export const addArchiveTaskList = (params) =>
  API.post('/statistics/tenant/archive/v2/task/save', params)
// 归档任务 - 删除
export const deleteArchiveTaskList = (params) =>
  API.post('/statistics/tenant/archive/v2/task/delete', params)
// 归档任务 - 获取应用名称的下拉列表
export const getArchiveTaskAppList = (params) =>
  API.get('/statistics/tenant/archive/metadata/v2/app/list', params)
// 归档任务 - 获取列字段的下拉列表
export const getArchiveTaskColumList = (params) =>
  API.get('/statistics/tenant/archive/metadata/v2/colum/list', params)
// 归档任务 - 获取表名称的下拉列表
export const getArchiveTaskTableList = (params) =>
  API.get('/statistics/tenant/archive/metadata/v2/table/list', params)
// 归档任务 - 查询归档任务明细
export const getArchiveTaskDetailList = (params) =>
  API.post('/statistics/tenant/archive/detail/v2/task/detail/query', params)
// 归档任务 - 新增归档任务明细
export const saveArchiveTaskDetailList = (params) =>
  API.post('/statistics/tenant/archive/detail/v2/task/detail/save', params)
// 归档任务 - 删除归档任务明细
export const deleteArchiveTaskDetailList = (params) =>
  API.post('/statistics/tenant/archive/detail/v2/task/detail/delete', params)
// 归档任务 - 启用-禁用归档任务
export const enableArchiveTask = (params) =>
  API.get('/statistics/tenant/archive/v2/task/enable', params)
// 归档任务 - 手动执行归档任务
export const executeArchiveTask = (params) =>
  API.get('/statistics/tenant/archive/v2/task/execute', params)
