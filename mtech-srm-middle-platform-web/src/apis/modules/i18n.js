import { API } from '@mtech-common/http'

export const NAME = 'i18n'

// 词组管理 - 分页查询
export const pageDictGroupApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/dictGroup/pageQuery`, data)

// 词组管理 - 保存
export const saveDictGroupApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/dictGroup/save`, data)

// 词组管理 - 删除
export const deleteDictGroupApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/dictGroup/batchDel`, data)

// 语言管理 - 分页查询
export const pageLanguageApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/lang/pageQuery`, data)

// 语言管理 - 保存
export const saveLanguageApi = (data = {}) => API.post(`/i18n/tenant/admin/i18n/lang/save`, data)

// 语言管理 - 删除
export const deleteLanguageApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/lang/batchDel`, data)

// 语言管理 - 获取语言列表
export const getLanguageApi = (data = {}) => API.post(`/i18n/tenant/admin/i18n/lang/list`, data)

// 词条管理 - 分页查询
export const pageEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/pageQuery`, data)

// 词条管理 - 保存
export const saveEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/save`, data)

// 词条管理 - 删除
export const deleteEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/batchDel`, data)

// 词条管理 - 获取字典类型列表
export const getDictTypeListEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/dictTypeList`, data)

// 词条管理 - 启用
export const enableEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/batchEnable`, data)

// 词条管理 - 禁用
export const disableEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/batchDisable`, data)

// 词条管理 - 翻译
export const translateEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/batchTranslate`, data)

// 词条管理 - 刷新
export const refreshEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/batchReload`, data)

// 词条管理 - 导入
export const importEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/importDict`, data, {
    responseType: 'blob'
  })

// 词条管理 - 导入模板下载
export const tempDownloadEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/downloadTemplate`, data, {
    responseType: 'blob'
  })

// 词条管理 - 导出
export const exportEntryApi = (data = {}) =>
  API.post(`/i18n/tenant/admin/i18n/translationDict/export`, data, {
    responseType: 'blob'
  })
