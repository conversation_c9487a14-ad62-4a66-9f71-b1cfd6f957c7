// import moment from 'moment'
import { API } from '@mtech-common/http'
const MESSAGE = '/message' // 请求接口的前缀
export const NAME = 'messageAccount'
// 查询模板管理列表
export const queryBuilder = (params) => {
  return API.post(`${MESSAGE}/tenant/account/queryBuilder`, params)
}
// 添加账户
export const accountAdd = (params) => {
  return API.post(`${MESSAGE}/tenant/account/add`, params)
}
// 获取配置信息
export const accountConfig = (params) => {
  return API.get(`${MESSAGE}/tenant/account/config`, params)
}
// 获取短信配置信息
export const accountSmsConfig = (params) => {
  return API.get(`${MESSAGE}/tenant/account/smsConfig`, params)
}
// 编辑保存接口
export const accountUpdate = (params) => {
  return API.post(`${MESSAGE}/tenant/account/update`, params)
}
// 删除短信配置信息
export const accountDel = (id = '') => {
  return API.delete(`${MESSAGE}/tenant/account/del?id=${id}`)
}
// 查询详情
export const accountInfo = (id = '') => {
  return API.get(`${MESSAGE}/tenant/account/info?id=${id}`)
}

// 下载(导出运营商模板)
export const smsTemplateRefDownload = (params) => {
  return API.get(`${MESSAGE}/tenant/smsTemplateRef/download`, params, { responseType: 'blob' })
}

// 上传(upload)
export const smsTemplateRefUpload = (params) => {
  return API.post(`${MESSAGE}/tenant/smsTemplateRef/upload`, params)
}
