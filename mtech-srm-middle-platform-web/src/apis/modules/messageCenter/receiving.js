// import moment from 'moment'
import { API } from '@mtech-common/http'
const MESSAGE = '/message' // 请求接口的前缀
export const NAME = 'messageReceiving'
// 查询接收人列表
export const queryBuilder = (params) => {
  return API.post(`${MESSAGE}/tenant/tenantReceiver/queryBuilder`, params)
}
// 从主数据查员工
export const employeefuzzyQuery = (params) => {
  return API.get('/masterDataManagement/tenant/employee/fuzzy-query', params)
}
// 从主数据查岗位
export const stationPagedQuery = (params) => {
  return API.post('/masterDataManagement/tenant/station/paged-query', params)
}
// 从用户据查岗位
export const userPagedQuery = (params) => {
  return API.post('/masterDataManagement/tenant/user/paged-query', params)
}
// 从主数据查角色
export const rolePagedQuery = (params) => {
  return API.post('/iam/tenant/role/paged-query', params)
}
// 删除短信配置信息
export const tenantReceiverDel = (id = '') => {
  return API.delete(`${MESSAGE}/tenant/tenantReceiver/del?id=${id}`)
}
// 添加接收人
export const tenantReceiverAdd = (params) => {
  return API.post(`${MESSAGE}/tenant/tenantReceiver/add`, params)
}
// 编辑info接口
export const tenantReceiverInfo = (id = '') => {
  return API.get(`${MESSAGE}/tenant/tenantReceiver/info?id=${id}`)
}
// 编辑保存接口
export const tenantReceiverUpdate = (params) => {
  return API.post(`${MESSAGE}/tenant/tenantReceiver/update`, params)
}
