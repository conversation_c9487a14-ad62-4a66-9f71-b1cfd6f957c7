// import moment from 'moment'
import { API } from '@mtech-common/http'
const MESSAGE = '/message' // 请求接口的前缀
export const NAME = 'messageCenter'
// 查询所有分组
export const templateGroupList = (params) => {
  return API.get(`${MESSAGE}/tenant/templateGroup/list`, params)
}
// 查询模板管理列表
export const queryBuilder = (params) => {
  return API.post(`${MESSAGE}/tenant/template/queryBuilder`, params)
}
// 添加新消息模板
export const templateAdd = (params) => {
  return API.post(`${MESSAGE}/admin/template/add`, params)
}
// 编辑查询回填
export const templateInfo = (id = '') => {
  return API.get(`${MESSAGE}/tenant/template/info?id=${id}`)
}
// 关闭通道
export const templateCloseChannel = (params) => {
  return API.post(`${MESSAGE}/tenant/template/closeChannel`, params)
}
// 启用通道
export const templateOpenChannel = (params) => {
  return API.post(`${MESSAGE}/tenant/template/openChannel`, params)
}
// 编辑保存接口
export const templateUpdate = (params) => {
  return API.post(`${MESSAGE}/admin/template/update`, params)
}
// 删除新消息模板
export const templatedel = (id = '') => {
  return API.delete(`${MESSAGE}/admin/template/del?id=${id}`)
}
// 获取配置信息
export const templateConfig = (params) => {
  return API.get(`${MESSAGE}/admin/template/config`, params)
}
// 公告保存
export const noticeSave = (params) => {
  return API.post(`${MESSAGE}/tenant/notice/for/save`, params)
}
// 公告发布
export const noticeRelease = (params) => {
  return API.post(`${MESSAGE}/tenant/notice/for/release`, params)
}
// 公告删除
export const noticeDel = (params) => {
  return API.delete(`${MESSAGE}/tenant/notice/for/del`, params)
}
// 公告撤回
export const noticeWithdraw = (params) => {
  return API.post(`${MESSAGE}/tenant/notice/for/withdraw`, params)
}
// 获取公告详情数据 - 采方
export const getPurNotice = (id) => {
  return API.get(`${MESSAGE}/tenant/notice/for/${id}`)
}

// 获取公告详情页数据 - 供方
export const getSupNotice = (id) => {
  return API.get(`${MESSAGE}/tenant/notice/supplier/${id}`)
}

// 获取公告编辑页数据
export const getEditNotice = (id) => {
  return API.get(`${MESSAGE}/tenant/notice/for/view/${id}`)
}
// 供应商导入
export const supplierImport = (data = {}) => {
  return API.post(`${MESSAGE}/admin/notice/import`, data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    responseType: 'blob'
  })
}

// 供应商导入模板下载
export const supplierTempDownload = (data = {}) => {
  return API.get(`${MESSAGE}/admin/notice/templateDownload`, data, {
    responseType: 'blob'
  })
}

// 附件下载
export const downloadFile = (params) => {
  return API.get(`/file/user/file/downloadPublicFile`, params, { responseType: 'blob' })
}
// 附件预览
export const getFilePreview = (params) => {
  return API.get(`/file/user/file/mtPreview`, params)
}

// 添加浏览记录
export const browseRecordsAdd = (data) => {
  return API.post(`${MESSAGE}/tenant/noticeViewLog/addLog`, data)
}

// 浏览记录导出
export const browseRecordsExport = (data) => {
  return API.post(`${MESSAGE}/tenant/noticeViewLog/export`, data, { responseType: 'blob' })
}

// 获取有权限的公司列表
export const getPermissionCompany = (data) => {
  return API.get(`/sourcing/tenant/permission/company`, data)
}
