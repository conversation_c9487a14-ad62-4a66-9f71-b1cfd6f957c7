import { API } from '@mtech-common/http'
const constract = ($axios) => ({
  queryBuyerListServe: {
    // 采方合同管理页面
    waitingListApi: '/contract/purchase/apply/page',
    contractListApi: '/contract/contract/purchase/page',
    contractItemApi: '/contract/contract-item/detail/page',
    contractSearch: (param) => {
      return $axios.get('/contract/contract/purchase/query', param)
    },
    createView: (param) => {
      return $axios.post('/contract/contract/purchase/create-view', param)
    },
    contractAction: (type, contractId) => {
      return $axios.put(`/contract/contract/purchase/operation-${type}/${contractId}`)
    },
    sign: (contractId, param) => {
      return $axios.put(`/contract/contract/purchase/operation-sign/${contractId}`, param)
    },
    archive: (contractId, param) => {
      return $axios.put(`/contract/contract/purchase/operation-archive/${contractId}`, param, {
        'Content-Type': 'application/form-data'
      })
    },
    // 合同创建  保存草稿
    createContractSave: (param) => {
      return $axios.post('/contract/contract/purchase/save', param)
    },
    // 提交
    createContractCommit: (param) => {
      return $axios.post('/contract/contract/purchase/save-commit', param)
    },
    // 发布
    createContractPublish: (param) => {
      return $axios.post('/contract/contract/purchase/save-publish', param)
    },
    createOrder: (param) => {
      return $axios.post('/contract/contract-item/order', param)
    },
    getSignOaLink: (param) => {
      return $axios.post('/contract/contract/purchase/getSignOaLink', param)
    },
    checkItemData: (param) => {
      return $axios.post('/contract/purchase/apply/checkItemData', param)
    },
    createOrderCheck: (param) => {
      return $axios.post('/contract/contract/purchase/createOrderCheck', param)
    },
    getLogisticsCodeById: (data) =>
      API.post(`/contract/tenant/logistics/request/header/getOneDetail`, data)
  },
  contractDetailBuyer: {
    // 采方详情
    // 获取详情
    getDetail: (param) => {
      return $axios.put(`/contract/contract/purchase/operation-edit/${param}`)
    },
    getPrview: (param) => {
      return $axios.get(`/contract/contract/purchase/operation-view/${param}`)
    },
    // 保存草稿
    save: (param) => {
      return $axios.post('/contract/contract/purchase/save', param)
    },
    // 物流保存草稿
    saveOrUpdate: (param) => {
      return $axios.post('/contract/contract/purchase/saveOrUpdate', param)
    },
    // 提交
    submit: (param) => {
      return $axios.post('/contract/contract/purchase/save-commit', param)
    },
    // 发布
    publish: (param) => {
      return $axios.post('/contract/contract/purchase/save-publish', param)
    },
    // 采购申请创建合同草稿
    createDraftContractDirectlyApi: (param) => {
      return $axios.post('/contract/contract/purchase/createDraftContractDirectly', param)
    },
    // 采购申请直接发布合同
    publishContractDirectlyApi: (param) => {
      return $axios.post('/contract/contract/purchase/publishContractDirectly', param)
    }
  },
  uploadFile: {
    upload: (param) => {
      return $axios.post('/contract/attachment/upload', param, {
        'Content-Type': 'application/form-data'
      })
    },
    delete: (param) => {
      return $axios.delete(`/contract/attachment/${param}`)
    },
    download: (param) => {
      return $axios.get(`/contract/attachment/download/${param}`)
    },
    // 查询
    fileList: (param) => {
      return $axios.get('/contract/attachment', param)
    }
  },
  queryCreateServe: {
    // 采方创建合同
  },
  // 供方
  querySupplierListServe: {
    supplierList: '/contract/contract/supplier/page',
    supplierDetails: (param) => {
      return $axios.get(`/contract/contract/supplier/detail/${param}`)
    },
    // 操作接受与拒绝
    contractSupplierAction: (type, param) => {
      return $axios.put(`/contract/contract/supplier/operation-${type}`, param)
    },
    // 详情左侧列表
    sidebarDetailsList: (sidebar, param) => {
      return $axios.post(`/contract/contract${sidebar}`, param)
    }
  },
  // 获取select接口
  querySelectData: {
    // 公司
    OrgFindSpecifiedChildrenLevelOrgs: (param) => {
      return $axios.post(
        '/masterDataManagement/tenant/organization/findSpecifiedChildrenLevelOrgs',
        param
      )
    },
    // 采购组织
    getOrganizateByOrgId: (param) => {
      return $axios.get(
        `/masterDataManagement/tenant/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem('currentBu')}`,
        param
      )
    },
    // 采购组
    getBuyerGroupSelect: (param) => {
      return $axios.post(
        `/masterDataManagement/tenant/business-group/criteria-query?BU_CODE=${localStorage.getItem(
          'currentBu'
        )}`,
        param
      )
    },
    // 获取业务负责人下拉列表
    getSupplierContact: (param) => {
      return $axios.post(
        '/masterDataManagement/tenant/business_partner_contact/findBusinessPartnerContact',
        param
      )
    },
    // 根据supplierID获取业务伙伴联系人
    findBusinessPartnerContactBySupplierIdApi: (param) => {
      return $axios.get(
        `/masterDataManagement/tenant/business_partner_contact/findBusinessPartnerContactBySupplierId`,
        param
      )
    },
    // 采购员
    getBuyer: (param) => {
      return $axios.get('/masterDataManagement/tenant/employee/currentTenantEmployees', param)
    },
    // 货币
    getMoneySelect: (param) => {
      return $axios.post('/contract/tenant/currency/criteriaQuery', param)
    },
    // 币种
    getCurrency: (param) => {
      return $axios.post('/masterDataManagement/tenant/currency/queryAll', param)
    },
    // 付款条件与合同类型
    conditionType: (param) => {
      return $axios.post('/masterDataManagement/tenant/dict-item/item-tree', param)
    },
    // 合同模板
    contractTemplate: () => {
      return $axios.get('/contract/form/selectable/contractTemplate')
    },
    // 是否立项
    isEstablishment: () => {
      return $axios.get('/contract/form/selectable/established')
    },
    // // 合同类别   与付款条件与合同类型重复了
    // contractTypeTree: (param)=>{
    //     return $axios.post(`/masterDataManagement/tenant/dict-item/item-tree`,param)
    // }
    // 获取采购组织拉框数据
    queryPurchase: (param) => {
      return $axios.get(
        `/masterDataManagement/tenant/business-organization/getBusinessOrganizationByOrgId?BU_CODE=${localStorage.getItem('currentBu')}`,
        param
      )
    },
    // 获取公司下拉数据
    queryCompany: (param) => {
      return $axios.post('/contract/tenant/organization/getFuzzyCompanyTree', param)
    },
    getNumDictListAllByCode: (param = {}) =>
      API.post(`/masterDataManagement/tenant/dict-item/getByDictCodes`, param)
  },
  // 获取当前用户信息
  queryUserData: {
    getUser: (param) => {
      return $axios.get('/contract/contract/user-info', param)
    }
  }
})
export default constract(API)
