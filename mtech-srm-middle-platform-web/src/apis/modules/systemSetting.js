import { API } from '@mtech-common/http'
const PATH = '/srm-purchase-execute' // 请求接口的前缀
export const NAME = 'systemSetting'
import qs from 'qs'

// 打印模板 - 上传
export const templateUpload = (params) => {
  return API.post(`${PATH}/admin/printTemplate/config/upload`, params)
}
// 打印模板 - 下载
export const templateDownload = (params) => {
  return API.post(`${PATH}/admin/printTemplate/config/download`, qs.stringify(params), {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob'
  })
}

// 打印模板 - 预览
export const templatePreview = (params) => {
  return API.post(`${PATH}/admin/printTemplate/config/preview`, qs.stringify(params), {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob'
  })
}

// 打印模板 - 渲染
export const templateRender = (params) => {
  return API.post(`${PATH}/admin/printTemplate/config/render`, params, {
    responseType: 'blob'
  })
}

// 用户下拉列表
export const getBuyerList = (data = {}) => {
  return API.get(`/masterDataManagement/tenant/employee/currentTenantEmployees`, data)
}

// 所有人员
export const usersPage = (data = {}) => {
  return API.post(`/iam/tenant/granted-subject/auth/users/page`, data)
}

// 灰度发布管理 待办查询
export const grayQuery = (params) => {
  return API.post(`/iam/tenant/_canary_config/queryBuilder`, params)
}

// 灰度发布管理 新增-编辑
export const grayBatchSave = (params) => {
  return API.post(`/iam/tenant/_canary_config/batchSave`, params)
}

// 灰度发布管理 批量删除
export const grayBatchDelete = (params) => {
  return API.post(`/iam/tenant/_canary_config/batchDelete`, params)
}

// 灰度发布管理 查询灰度用户列表
export const getGrayUserList = (params) => {
  return API.post(`/iam/tenant/_canary_config/detail/queryList`, params)
}
