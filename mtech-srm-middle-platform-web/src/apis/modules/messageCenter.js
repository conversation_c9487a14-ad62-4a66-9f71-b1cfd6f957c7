// // import moment from 'moment'
// import { API } from '@mtech-common/http'
// const MESSAGE = '/message' // 请求接口的前缀
// export const NAME = 'messageCenter'
// // 查询所有分组
// export const templateGroupList = (params) => {
//   return API.get(`${MESSAGE}/tenant/templateGroup/list`, params)
// }
// // 查询模板管理列表
// export const queryBuilder = (params) => {
//   return API.post(`${MESSAGE}/tenant/template/queryBuilder`, params)
// }
// // 添加新消息模板
// export const templateAdd = (params) => {
//   return API.post(`${MESSAGE}/admin/template/add`, params)
// }
// // 账户管理列表
// export const accountQueryBuilder = (params) => {
//   return API.post(`${MESSAGE}/tenant/account/queryBuilder`, params)
// }
