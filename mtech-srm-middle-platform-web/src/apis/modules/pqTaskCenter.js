import { API } from '@mtech-common/http'
export const NAME = 'pqTasckCenter'
// const PROXY_BASE = '/analysis'
// import qs from 'qs'

// 任务中心获取 弹框配置
export const getRawMaterialInfo = (data = {}) => {
  return API.post(`/analysis/tenant/buyer/assess/rawMaterialInfo/pageQuery`, data)
}
// 任务中心  保存列表
export const saveTask = (data = {}) => {
  return API.post(`/platform/tenant/task/save`, data)
}
// 任务中心  获取供应商列表数据
export const getSupplierList = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/supplier/achievements/fuzzy-query`, data)
}
// 任务中心  获取品类列表数据
export const getCategoryData = (data = {}) => {
  return API.post(`/supplier/tenant/buyer/categoryDivision/getByOrgCodeList`, data)
}
// 任务中心  获取物料列表数据
export const getItemData = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/category-item/getByOrgCodeList`, data)
}
// 绩效组织架构设置 - 获取公司层级下的架构tree平铺list数据
export const getStructureListOrgForSpecial = () => {
  return API.post(`/analysis/tenant/buyer/assess/categoryTemplateRelation/getOrgTree`)
}
// 根据字典类型编码 获取字典详情
export const getDictCode = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/dict-item/dict-code`, data)
}
// 获取用户权限工厂
export const queryFactory = (data = {}) => {
  return API.post('/iam/feign/permission-data/queryUserPermission', data)
}
// 获取用户权限工厂 -- 不允许直调feign接口因此替换为该接口
export const getOrgListByCode = (params) =>
  API.get(`/analysis/tenant/buyer/assess/comprehensiveResult/getOrgListByCode`, params)
// 提交配额任务
export const saveQuotaTask = (data = {}) => {
  return API.post(`/platform/tenant/task/genericSave`, data)
}
// 获取当前登录者角色权限
export const queryLogerRole = (data = {}) => {
  return API.post(`/price/tenant/quotaAdjustmentFiling/checkQuotaSpecialRole`, data)
}
// 品类列表
export const queryPermissionCategories = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/permission/queryCategories`, data)
}
// 品类列表
export const getCategoryListAll = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/category/paged-query`, data)
}
