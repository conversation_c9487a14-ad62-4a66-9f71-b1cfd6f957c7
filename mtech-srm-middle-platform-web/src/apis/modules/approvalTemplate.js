/*
 * @Author: your name
 * @Date: 2021-09-29 17:47:40
 * @LastEditTime: 2021-10-19 19:47:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\apis\modules\approvalTemplate.js
 */
import { API } from '@mtech-common/http'

/**
 * 获取分类树
 */
const PROXY_BASE = 'approveCenter/approve'
export const NAME = 'approvalTemplate'

const TEMPLATE = '/template'

export const getTempListApi = `${PROXY_BASE}${TEMPLATE}/pageList`

export const changeTempStatus = (params) => {
  return API.post(`${PROXY_BASE}${TEMPLATE}/enable`, params)
}
export const getTempDetailById = (params) => {
  return API.get(`${PROXY_BASE}${TEMPLATE}/getByTemplateId`, params)
}

export const publishTemp = (params) => {
  return API.post(`${PROXY_BASE}${TEMPLATE}/publish`, params)
}

export const updateTemp = (params) => {
  return API.post(`${PROXY_BASE}${TEMPLATE}/update`, params)
}

export const saveTemp = (params) => {
  return API.post(`${PROXY_BASE}${TEMPLATE}/add`, params)
}

export const getTempImg = (params) => {
  return API.get(`${PROXY_BASE}${TEMPLATE}/getImg`, params)
}

export const getTempDetailByWorkId = (params) => {
  return API.get(`${PROXY_BASE}${TEMPLATE}/getData`, params)
}
