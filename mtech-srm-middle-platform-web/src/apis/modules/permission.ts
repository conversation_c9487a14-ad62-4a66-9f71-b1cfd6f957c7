import { API } from '@mtech-common/http'

const permission = ($axios: any) => ({
  // 获取自己的角色列表
  querySelfRoleList(params: any) {
    return $axios.post('/iam/tenant/user/permission/summary/querySelfRoleList', params)
  },
  // 查询数据权限
  querySelfPermissionList(params: any) {
    return $axios.post('/iam/tenant/user/permission/summary/queryUserSelfPermission', params)
  },

  // 查询数据权限
  permissionDimensionEnum(params: any) {
    return $axios.get('/iam/common/selectable/permissionDimensionEnum', params)
  },
  // 查询权限树列表
  menuTreeGet(params: any) {
    return $axios.post('/iam/tenant/role-permission-rel/list-tree', params)
  }
})

export default permission(API)
