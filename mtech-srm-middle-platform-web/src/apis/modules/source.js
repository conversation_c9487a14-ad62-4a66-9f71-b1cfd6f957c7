import qs from 'qs'
const MESSAGE = '/message' // 请求接口的前缀
export const NAME = 'source'
import { API } from '@mtech-common/http'

export const queryUserInfo = (params) => {
  return API.get('/iam/common/account/userinfo', params)
}
export const queryUserMenu = (params) => {
  return API.get('/iam/tenant/user-permission-query/user-menu', params)
}
export const queryUserJoinTenant = (params) => {
  return API.post(`/usercenter/admin/user-join-tenant/detail/${params}`)
}
export const userApproval = (params) => {
  return API.post('/usercenter/admin/user-join-tenant/approval', params)
}
export const getChildrenCompanyOrganization = (params) => {
  return API.post(
    '/masterDataManagement/tenant/organization/getChildrenCompanyOrganization',
    params
  )
}
export const getChildrenDepartmentOrganization = (params) => {
  return API.post(
    '/masterDataManagement/tenant/organization/getChildrenDepartmentOrganization',
    params
  )
}
export const getChildrenStationOrganization = (params) => {
  return API.post(
    '/masterDataManagement/tenant/organization/getChildrenStationOrganization',
    params
  )
}
//未读消息
export const inmailUnReadCount = (params) => {
  return API.get(`${MESSAGE}/user/inmail/unReadCount`, params)
}
//我的站内消息
export const inmailMy = (params) => {
  return API.get(`${MESSAGE}/user/inmail/my`, params)
}
//设置单条消息已读
export const inmailSetRead = (params) => {
  return API.post(`${MESSAGE}/user/inmail/setRead`, params)
}
//获取首页系统公告-采方
export const getPurAnnouncement = (params) => {
  return API.post(`${MESSAGE}/tenant/notice/for/queryPur`, params)
}
//获取首页系统公告-供方
export const getSupAnnouncement = (params) => {
  return API.post(`${MESSAGE}/tenant/notice/supplier/querySup`, params)
}
//获取首页待办事项 -采方
export const getBuyTodoList = (params) => {
  return API.post(`/srm-purchase-execute/tenant/buyerWorkCenterTask/query`, params)
}
//获取首页待办列表 -采方
export const getPurTodoList = (params) => {
  return API.post(`${MESSAGE}/user/todoHeader/queryMainList`, params)
}
// 配额管理 - 获取首页待办列表 - 采方
export const getQuotaWaitList = (params) => {
  return API.post('/price/tenant/price/change/record/queryCount', params)
}

//获取首页待办列表-供方
export const getSupTodoList = (params) => {
  return API.post(`${MESSAGE}/user/supplierTodoHeader/queryMainList`, params)
}
//获取首页待办列表-供方 new
export const getNewSupTodoList = (params) => {
  return API.post(`/srm-purchase-execute/tenant/supplierWorkCenterTask/query`, params)
}
//获取首页待办事项 -采方 -送货协同-待收货的送货单
export const getDeliveryItem = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerDeliveryItem`,
    qs.stringify({
      taskBusinessType: 'ORDER_DELIVERY_PRO'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -送货协同-同步失败送货单
export const getDeliverySynFail = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerDeliverySynFail`,
    qs.stringify({
      taskBusinessType: 'ORDER_DELIVERY_NO_SYN'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -送货协同-待入库送货单
export const getDeliveryShipping = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerDeliveryShipping`,
    qs.stringify({
      taskBusinessType: 'ORDER_DELIVERY_SHIPPING'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -订单协同-待确认订单查询
export const getOrderComfirmed = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerOrderComfirmed`,
    qs.stringify({
      taskBusinessType: 'ORDER_COMFIRMED'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -订单协同-待发布的交货计划
export const getDemandPlanReleased = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerDemandPlanReleased`,
    qs.stringify({
      taskBusinessType: 'DEMAND_PLAN_RELEASED'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -订单协同-待反馈的交货计划
export const getDemandPlanFeedback = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerDemandPlanFeedback`,
    qs.stringify({
      taskBusinessType: 'DEMAND_PLAN_FEEDBACK'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -订单协同-已确认未打单的交货计划
export const getDemandPlanNoPrint = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerDemandPlanNoPrint`,
    qs.stringify({
      taskBusinessType: 'DEMAND_PLAN_NO_PRINT'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}

//获取首页待办事项 -采方 -委外-待确认领料单
export const getKtPickUp = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtPickUp`,
    qs.stringify({
      taskBusinessType: 'KT_PICK_UP'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-待确认直退退货单
export const getKtOutCancelDirect = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtOutCancelDirect`,
    qs.stringify({
      taskBusinessType: 'KT_OUT_CANCEL_DIRECT'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-待确认退本厂退货单
export const getKtOutCancel = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtOutCancel`,
    qs.stringify({
      taskBusinessType: 'KT_OUT_CANCEL'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-待确认已确认调拨单
export const getKtExchange = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtExchange`,
    qs.stringify({
      taskBusinessType: 'KT_EXCHANGE'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-同步失败领料单
export const getKtPickUpFail = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtPickUpFail`,
    qs.stringify({
      taskBusinessType: 'KT_PICK_UP_FAIL'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-已确认未完成的领料单
export const getKtPickUpComfirmed = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtPickUpConfirmed`,
    qs.stringify({
      taskBusinessType: 'KT_PICK_UP_COMFIRMED'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-委外	已确认未完成的退货单
export const getKtOutCancelComfirmed = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtOutCancelConfirmed`,
    qs.stringify({
      taskBusinessType: 'KT_OUT_CANCEL_COMFIRMED'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}
//获取首页待办事项 -采方 -委外-委外	已确认未完成的调拨单
export const getKtExchangeComfirmed = () => {
  return API.post(
    `/statistics/tenant/buyerWorkCenterTask/handlerKtExchangeConfirmed`,
    qs.stringify({
      taskBusinessType: 'KT_EXCHANGE_COMFIRMED'
    }),
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
  )
}

// 供方-工作台-获取白电供方闭环
export const querySupplierLoopApi = (params) => {
  return API.get(`/contract/tenant/supplier/close/loop/list`, params)
}

// 供方-工作台-获取模具待办
export const queryMouldListApi = (params) => {
  return API.get(`/message/tenant/_supplier_todo_header/queryMouldList`, params)
}
