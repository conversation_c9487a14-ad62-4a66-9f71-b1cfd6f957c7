/*
 * @Author: your name
 * @Date: 2021-10-19 18:56:15
 * @LastEditTime: 2021-10-19 18:59:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\apis\modules\common.js
 */
import moment from 'moment'
import { API } from '@mtech-common/http'

export const NAME = 'common'
export const Dateformat = (date) => {
  if (typeof date === 'string') {
    date = Number(date)
  }
  return moment(date).format('YYYY-MM-DD HH:mm:ss')
}

export const getAllCountryList = (params) => {
  return API.post('/masterDataManagement/common/country/queryAll', params)
}

export const getAreaListByParent = (params) => {
  return API.post('/masterDataManagement/common/area/selectByParentCode', params)
}

export const getDictItemTree = (params) => {
  return API.post('/masterDataManagement/common/dict-item/item-tree', params)
}

export const getCapitalCurrency = (params) => {
  return API.get('/masterDataManagement/common/currency/queryActiveCurrency', params)
}

export const getAreaListByCriteria = (params) => {
  return API.get('/masterDataManagement/common/area/criteria-query', params)
}
