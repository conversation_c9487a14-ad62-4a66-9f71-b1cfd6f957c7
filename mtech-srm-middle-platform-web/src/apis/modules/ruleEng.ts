import { API } from '@mtech-common/http'
const ruleEng = ($axios: any) => ({
  // 获取规则
  getRule: (params: string) => {
    return $axios.get(`/ruleConfig/admin/rule/data/${params}`)
  },
  // 更新规则
  updateRule: (params: any) => {
    return $axios.put('/ruleConfig/admin/rule/data', params, {
      useNotify: true
    })
  },
  // 增加规则
  addRule: (params: any, status: number) => {
    return $axios.post(`/ruleConfig/admin/rule/data/${status}`, params, {
      useNotify: true
    })
  },
  // 删除规则
  deleteRule: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/data/ids', params, {
      useNotify: true
    })
  },
  // 获取所有的规则列表
  getAllRule: () => {
    return $axios.get('/ruleConfig/admin/rule/data/list')
  },
  // 查询规则分页列表
  getRuleList: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/page', params)
  },

  // 查询对象分页列表
  getRuleModelList: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/model/page', params)
  },
  // 增加对象
  addRuleModelList: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/model/data', params, {
      useNotify: true
    })
  },
  // 删除对象
  deleteRuleModelList: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/model/data/ids', params, {
      useNotify: true
    })
  },
  // 查询对象字段
  getObjectField: (modelId: string) => {
    return $axios.get(`/ruleConfig/admin/model/field/data/${modelId}`)
  },
  // 增加对象字段
  addObjectField: (params: any) => {
    return $axios.post('/ruleConfig/admin/model/field/data', params, {
      useNotify: true
    })
  },
  // 获取字段枚举类型
  getObjectFieldType: () => {
    return $axios.get('/ruleConfig/admin/model/field/type')
  },

  // 查询所有项目列表
  getProjectList: () => {
    return $axios.get('/ruleConfig/admin/project/list')
  },
  // 查询所有项目分页列表
  getProjectPage: (params: any) => {
    return $axios.post('/ruleConfig/admin/project/page', params)
  },
  // 增加项目
  addProject: (params: any) => {
    return $axios.post('/ruleConfig/admin/project/data', params, {
      useNotify: true
    })
  },
  // 删除项目
  deleteProject: (params: any) => {
    return $axios.post('/ruleConfig/admin/project/data/ids', params, {
      useNotify: true
    })
  },
  // 编辑项目
  updataProject: (params: any) => {
    return $axios.put('/ruleConfig/admin/project/data', params, {
      useNotify: true
    })
  },
  // 查询所有方法列表
  getMethodList: () => {
    return $axios.get('/ruleConfig/admin/method/list')
  },
  // 查询分页方法列表
  getMethodPage: (params: any) => {
    return $axios.post('/ruleConfig/admin/method/page', params)
  },
  // 增加方法
  addMethod: (params: any) => {
    return $axios.post('/ruleConfig/admin/method/data', params, {
      useNotify: true
    })
  },
  // 删除方法
  deleteMethod: (params: any) => {
    return $axios.post('/ruleConfig/admin/method/data/ids', params, {
      useNotify: true
    })
  },
  // 修改方法
  updataMethod: (params: any) => {
    return $axios.put('/ruleConfig/admin/method/data', params, {
      useNotify: true
    })
  },
  // 获取规则绑定列表
  getBindList: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/method/page', params)
  },
  // 增加绑定关系
  addRuleBind: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/method/data', params, {
      useNotify: true
    })
  },
  // 删除绑定关系
  deleteRuleBind: (params: any) => {
    return $axios.post('/ruleConfig/admin/rule/method/data/ids', params, {
      useNotify: true
    })
  },
  // 修改绑定关系
  updataRuleBind: (params: any) => {
    return $axios.put('/ruleConfig/admin/rule/method/data', params, {
      useNotify: true
    })
  },

  // 查询对象树
  getObjectTree: (name: string) => {
    if (name === '') {
      return $axios.get('/ruleConfig/admin/rule/model/tree')
    } else {
      return $axios.get(`/ruleConfig/admin/rule/model/tree?modelName=${name}`)
    }
  }
})

export default ruleEng(API)
