import { API } from '@mtech-common/http'
const dataManage = ($axios: any) => ({
  // 查询表列表
  getAppTableList: (params: any) => {
    return $axios.post('/platform/tenant/es/field/appTableList', params)
  },
  // 查询表字段清单
  getFieldList: (params: any) => {
    return $axios.post('/platform/tenant/es/field/fieldList', params, {
      useNotify: true
    })
  },
  // 查询表字段配置列表
  queryFieldList: (params: any) => {
    return $axios.post('/platform/tenant/es/field/queryBuilder', params)
  },
  // 保存表字段配置
  saveField: (params: any) => {
    return $axios.post('/platform/tenant/es/field/save', params, {
      useNotify: true
    })
  },
  // 导出数据
  exportData: (params: any, url) => {
    return $axios.post(url, params, {
      responseType: 'blob'
    })
  },
  // 增量数据查询
  searchAfter: (params: any, url) => {
    return $axios.post(url, params)
  },
  // 保存任务配置
  saveTaskLog: (params: any) => {
    return $axios.post('/platform/tenant/es/taskLog/save', params, {
      useNotify: true
    })
  },
  // 刷新数据
  refreshData: (params: any, table: any, urlRoute: any) => {
    return $axios.post(`/${urlRoute}/tenant/data/es/refresh?table=${table}`, params)
  },
  // 删除数据
  deleteData: (params: any, table: any, urlRoute: any) => {
    return $axios.post(`/${urlRoute}/tenant/data/es/delete?table=${table}`, params)
  },
  // 回滚数据
  rollbackData: (params: any, table: any, urlRoute: any) => {
    return $axios.post(`/${urlRoute}/tenant/data/es/rollback?table=${table}`, params)
  },
  // 归档数据
  archiveData: (params: any, table: any, urlRoute: any) => {
    return $axios.post(`/${urlRoute}/tenant/data/es/archiveData?table=${table}`, params)
  }
})

export default dataManage(API)
