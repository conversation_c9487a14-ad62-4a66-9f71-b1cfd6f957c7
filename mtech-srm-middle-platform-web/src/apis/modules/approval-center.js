/*
 * @Author: your name
 * @Date: 2021-10-12 18:52:41
 * @LastEditTime: 2021-11-01 20:27:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mtech-srm-middle-platform-web\src\apis\modules\approval-center.js
 */
import { API } from '@mtech-common/http'

const PROXY_BASE = '/approveCenter/approve/center' // 请求接口的前缀

export const NAME = 'approvalCenter'

export const approvalCenterWatingApi = `${PROXY_BASE}/queryWaitTaskPage`
export const approvalCenterWatiedApi = `${PROXY_BASE}/queryAlreadyPage`
export const otherWatingApi = '/approveCenter/tenant/approve/other/queryPendingProcessPage'
export const otherWatiedApi = '/approveCenter/tenant/approve/other/queryFinishProcessPage'
export const reject = (params) => {
  return API.post(`${PROXY_BASE}/approvalTask`, params)
}
export const getInfoData = (params) => {
  return API.get(`${PROXY_BASE}/statistics`, params)
}
export const getHtmlData = (params) => {
  return API.get(`${PROXY_BASE}/getApproveBillInfos`, params)
}
export const getUserInfo = (params) => {
  return API.get(`${PROXY_BASE}/getApproveUserInfo`, params)
}
export const getOtherUserInfo = (params) => {
  return API.get('/approveCenter/tenant/approve/other/statistics', params)
}
export const queryWaitProcessUserInfo = (params) => {
  return API.get(`${PROXY_BASE}/queryWaitProcessUserInfo`, params)
}

export const queryAlreadyProcessUserInfo = (params) => {
  return API.get(`${PROXY_BASE}/queryAlreadyProcessUserInfo`, params)
}
