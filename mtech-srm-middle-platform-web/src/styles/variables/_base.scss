$white: rgb(255, 255, 255);
// background-colors
$background-color: rgb(244, 244, 244); // body background color
$head-bg-color: rgb(255, 255, 255); // head background color
$left-bg-color: rgb(22, 27, 31);

// color
$light-blue: #31AFC8;
$dark-gray: rgb(88, 94, 108);
$green: rgb(51, 181, 147);
$pink: rgb(248, 118, 120);
$gray: rgb(209, 209, 209);


//scrollBar-color
$scrollBar-track-color: rgb(235,235,235);
$scrollBar-thumb-color: rgb(210,210,210);
$scrollBar-thumb-hover-color: rgb(200,200,200);
$scrollBar-thumb-active-color: rgb(190,190,190);
//left-background


// fonts
$primary-font-family: 'Microsoft YaHei','DINAlternate', 'Arial', serif; // SimHei  // SimSun // Microsoft YaHei      // "Helvetica Neue", Helvetica, Arial, sans-serif; font-family
$primary-font-color: rgb(88, 94, 107); //primary text color //基本文字颜色
$main-font-color: rgb(66, 66, 66); // 主要文字颜色 lighter
$link-font-color: rgb(12, 168, 242); // clickable text color
$nav-font-color: rgb(178, 202, 216); //nav font color
$active-font-color: rgb(101, 157, 237); //selected font color

//width,height
$main-width: 1280px;
$menu-width: 200px;
$head-height: 60px;
$nav-height: 45px;
$foot-height: 35px;

//border
$border-color: rgb(216,221,227);
$border: 1px solid $border-color;

//border-radius
$border-radius-default: 2px;
$border-radius-small: 1px;
$border-radius-medium: 3px;
$border-radius-large: 4px;

// padding-top,padding-bottom
$padding-default-vertical: 3px;
$padding-small-vertical: 2px;
$padding-medium-vertical: 5px;
$padding-large-vertical: 8px;

// padding-left,padding-right
$padding-default-horizontal: 8px;
$padding-small-horizontal: 10px;
$padding-medium-horizontal: 10px;
$padding-large-horizontal: 30px;

//font-size
$font-size-default: 14px;
$font-size-small: 14px;
$font-size-medium: 15px;
$font-size-large: 16px;

// 禁止点击
$cursor-disabled: not-allowed;
//total-money color
$total-money-color: rgb(217, 86, 82);

:root {
  --common-tp-bg-fa: #fafafa;
  --common-tp-bg-ff: #ffffff;
  --common-tp-border-color: #e8e8e8;
  --common-tp-title-color: #292929;
  --common-tp-span-color: #4f5b6d;
  --common-tp-span-hover-color: #707b8b;
  --common-tp-handle-before-color: #ffffff;
  --common-tp-column-handler-bg: #9a9a9a;
  --common-tp-column-remove-color: #9baac1;
  --common-tp-column-handler-hover-bg: #6386c1;
  --common-tp-column-color: #00469c;
  --common-tp-column-shadow: rgba(0, 0, 0, 0.06);
  --common-tp-loading:rgba(255, 255, 255, 0.6);
  --common-tp-column-selction-box-shadow: rgba(0, 0, 0, 0.12);
  --common-tp-column-footer-label-color: rgba(237, 86, 51, 1);
  --common-tp-column-footer-btns-color: rgba(0, 70, 156, 1);
  --common-tp-result-item-box-shadow: rgba(0, 0, 0, 0.06);
  --common-tp-add-rule-label-color: rgba(99, 134, 193, 1);
  --common-tp-footer-disable-color: #dedede;
  --scroll-bar-track-color: #ffffff;
  --scroll-bar-thumb-color: #d8d8d8;
  --scroll-bar-thumb-hover-color: rgb(200, 200, 200);
  --scroll-bar-thumb-active-color: rgb(190, 190, 190);
  --plugin-ct-bg-fa: #fafafa;
  --plugin-ct-content-color: #2783FE;
  --plugin-ct-cell-icon-color: #2783FE;
  --plugin-ct-cell-icon-disbaled-color: #9daabf;
  --plugin-dg-bg-fa: #fafafa;
  --plugin-dg-bg-ff: #ffffff;
  --plugin-dg-border-color: #e8e8e8;
  --plugin-dg-shadow-color: #e8e8e8;
  --plugin-tb-bg-fa: #fafafa;
  --plugin-tb-bg-ff: #ffffff;
  --plugin-tb-tool-item-color: #4D5B6F;
  --plugin-tb-tool-item-hover-color: #707b8b;
  --plugin-tb-tool-item-disable-color: #9baac1;
  --plugin-tb-tool-item-tip-color: #9BAAC1;
  --plugin-tb-tool-item-marker-bg: #ED5633;
  --plugin-tb-tool-item-marker-color: #ffffff;
  --plugin-tb-border-color:#e8e8e8;
  --plugin-tb-tool-custom-btn-color: #4E5A70;
  --plugin-tb-tool-custom-btn-hover-color: #ffffff;
  --plugin-tb-tool-custom-btn-disable-color: #e8e8e8;
  --plugin-tb-tool-container-shadow-color:rgb(0 0 0 / 20%);
  --common-pcs-bg-fa: #fafafa;
  --common-pcs-border-color: #e8e8e8;
  --common-pcs-disable-color: #9a9a9a;
  --common-pcs-search-container-color: #9baac1;
  --common-pcs-column-title-color: #292929;
  --common-pcs-item-handler-color:#6386c1;
  --common-pcs-item-remove-color:#98aac3;
  --common-pcs-item-remove-hover-color:#6386c1;
  --common-pcs-list-item-disabled-color:#ffffff;
  --common-pcs-sticky-item-color: #9a9a9a;
  --scroll-bar-track-color: #ffffff;
  --scroll-bar-thumb-color: #d8d8d8;
  --scroll-bar-thumb-hover-color: rgb(200, 200, 200);
  --scroll-bar-thumb-active-color: rgb(190, 190, 190);
  --plugin-cs-bg-fa: #fafafa;
  --plugin-cs-icon-color: #4f5b6d;
  --plugin-cs-icon-active-color: #707b8b;
  --plugin-cs-border-color: #e8e8e8;
  --plugin-cs-title-color: #292929;
  --plugin-cs-column-handler-line-color: #ffffff;
  --plugin-cs-column-handler-bg: #9a9a9a;
  --plugin-cs-column-handler-hover-bg: #6386c1;
  --plugin-cs-column-color: #00469c;
  --plugin-cs-column-shadow-color:rgba(0, 0, 0, 0.06);
  --plugin-tg-bg-fa: #fafafa;
  --plugin-tg-bg-ff: #ffffff;
  --plugin-tg-border-color: #e8e8e8;
  --plugin-tg-shadow-color: #e8e8e8;
  --common-loading-bg: #fafafa;
  --common-tw-bg: #fafafa;
  --common-tw-bg-ff: #fff;
  --common-tw-level-color: #6386c1;
  --common-tw-level-active-color: #f5f6f9;
  --common-tw-collapsible-color: #6386c1;
  --common-tw-action-boxs-border-color:#fff;
  --common-tw-action-boxs-shadow-color:rgba(0, 0, 0, 0.1);
  --common-tw-action-boxs-operation-color:rgba(41, 41, 41, 1);
  --common-tw-action-boxs-minus-color: rgba(99, 134, 193, 1);
  --common-tw-span-color: #292929;
  --plugin-qs-bg-fa: #fafafa;
  --plugin-qs-btn-color: rgba(0, 70, 156, 1);
}
