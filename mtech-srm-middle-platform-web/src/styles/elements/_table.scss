//table外层div  用于table列数过多显示水平滚动条
.table-container {
  max-width: 100%;
  overflow-x: auto;
  max-height: calc(100vh - 250px);
}

table {
  width: 100%;
  max-width: 100%;
  //table-layout: fixed;
  th, td {
    border: 1px solid  $table-border-color;
    text-align: center;
    vertical-align: middle !important;
    white-space: nowrap;
    overflow: hidden;
  }
}

.table {
  th, td {
    padding: 10px 8px;
    line-height: 28px;
  }
  thead tr {
    background-color: $table-head-bg-color;
  }
  tbody tr {
    //display: table-row;
    &.active {
      background-color: $table-bg-selected-color !important;
    }
  }
}

//行颜色间隔
.table-stripe {
  > tbody > tr {
    &:nth-child(even) {
      background-color: $table-bg-color;
    }
    &:nth-child(odd) {
      background-color: $white;
    }
  }
}

//hover effect
.table-hover {
  > tbody > tr {
    &:hover {
      background-color: $table-bg-selected-color !important;
    }
  }
}

//内容太多加省略号，鼠标经过显示全部文字
.table-showAllInfo {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  a {
    display: block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
}
//表头固定样式
.table-body {
  position: relative;
}

.table-fixed-header {
  @include position(sticky, 0 null null 0);
  z-index: 2;
  tr {
    background-color: $table-bg-color;
    th {
      padding: 10px 8px;
      line-height: 28px;
    }
  }
}
