.mr20 {
  margin-right: 20px;
}
.detail-top-info {
  height: auto;
  overflow: hidden;
  background: #fff;
  padding: 0 0 20px 20px;
  transition: all 0.5s ease-in-out;

  &-small {
    height: 50px;
  }
  .header-box {
    height: 50px;
    padding-right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px dashed #e6e9ed;
    margin-bottom: 24px;

    .middle-blank {
      flex: 1;
    }
    .status-highlight {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(99, 134, 193, 1);
      padding: 4px;
      background: rgba(238, 242, 249, 1);
      border-radius: 2px;
    }
    .status-disable {
      font-size: 12px;
      font-family: PingFangSC;
      font-weight: 500;
      color: rgba(154, 154, 154, 1);
      padding: 4px;
      background: #f4f4f4;
      border-radius: 2px;
    }

    .infos {
      font-size: 14px;
      font-family: PingFangSC;
      font-weight: normal;
      color: rgba(154, 154, 154, 1);
    }

    .sort-box {
      height: 36px;
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 20px;
      .mt-icons {
        font-size: 12px;
        margin-left: 2px;
      }
      .expendIcon {
        transform: rotate(180deg);
        margin-top: -4px;
      }
      .unExpendIcon {
        margin-top: 4px;
      }
    }

    .header-box-btn {
      margin-left: 10px;
      button {
        display: block;
        padding: 6px 16px;
        border-radius: 4px;
        font-weight: bold;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        border: 1px solid #4a556b;
        background: #fff;
        color: #4a556b;
        box-shadow: unset;
      }
    }
    .primary {
      button {
        background: #4a556b;
        color: #fff;
      }
    }
  }

  .main-bottom {
    .mt-form-item {
      width: calc(20% - 20px);
      min-width: 200px;
      display: inline-flex;
      margin-right: 20px;

      .full-width {
        width: calc(100% - 20px) !important;
      }
    }
  }
}
