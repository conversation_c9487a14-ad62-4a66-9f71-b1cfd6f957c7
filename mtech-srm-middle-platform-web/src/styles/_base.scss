.mt-flex {
    display: flex;
    position: relative;
}

.mt-flex-direction-column {
    display: flex;
    flex-direction: column;
    position: relative;
}

//active状态，左侧线条
.list-item-active {
    border-left: none;
    position: relative;
    &:before {
        content: "";
        height: 100%;
        width: 0;
        position: absolute;
        border-left: 2px solid #00469c;
        left: 0;
        top: 0;
        animation: list-item-active-animation 0.2s ease;
    }
    @keyframes list-item-active-animation {
        0% {
            top: 50%;
            height: 0;
        }
        100% {
            top: 0;
            height: 100%;
        }
    }
}

//左上角角标
.top-left-arrow-tag {
    padding-left: 5px;
    position: relative;
    &:before {
        content: "";
        height: 0;
        width: 0;
        border-right: 6px solid transparent;
        border-top: 6px solid #eda133;
        position: absolute;
        left: 0;
        top: 0;
    }
}

.svg-option-item {
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    position: relative;
    color: #4f5b6d;
    .mt-icons{
      margin-right: 6px;
    }
    span {
      word-break: keep-all;
      font-size: 14px;
    }
}

//侧拉框-分组规则、分配策略
.slider-panel-container {
  background: rgba(0, 0, 0, 0.2);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;

  .slider-modal {
    width: 800px;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    background: #fafafa;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px 0 0 8px;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);

    // &:before {
    //   content: "";
    //   cursor: pointer;
    //   width: 12px;
    //   height: 60px;
    //   background: #f3f3f3;
    //   border-radius: 8px 0 0 8px;
    //   position: absolute;
    //   left: -12px;
    //   top: calc(50% - 30px);
    // }

    // &:after {
    //   content: "";
    //   width: 0;
    //   height: 0;
    //   cursor: pointer;
    //   border-width: 4px 0px 4px 4px;
    //   border-style: solid;
    //   border-color: transparent transparent transparent #6c7a8f;
    //   position: absolute;
    //   left: -8px;
    //   top: calc(50% - 4px);
    // }

    .slider-header {
      height: 60px;
      background: #f3f3f3;
      padding: 10px 20px 10px 30px;
      justify-content: space-between;
      align-items: center;

      .slider-title {
        font-size: 16px;
        font-weight: 500;
        color: #292929;
      }

      .slider-close {
        cursor: pointer;
        font-size: 30px;
        color: #4d5b6f;
        transform: rotate(45deg);
      }
    }

    .slider-content {
      flex: 1;
      padding: 20px;
      .rule-group {
        margin-top: 30px;

        &:first-of-type {
          margin-top: 0;
        }

        .group-header {
          font-size: 14px;
          color: #292929;
          display: inline-block;
          padding-left: 10px;
          position: relative;

          &:before {
            content: "";
            position: absolute;
            width: 3px;
            height: 12px;
            background: #eda133;
            border-radius: 2px 0 0 2px;
            left: 0;
            top: 1px;
          }
        }

        .group-description {
          padding-left: 10px;
          font-size: 12px;
          display: block;
          color: #9a9a9a;
          margin-top: 6px;
        }

        .group-content {
          padding: 20px 10px 0 10px;
          display: flex;
          .mt-form{
            margin: 0 10px;
            flex: 1;

            &:last-of-type{
              margin-right: 0;
            }
            &:first-of-type{
              margin-left: 0;
            }
          }
        }
      }
    }

    .slider-footer {
      height: 60px;
      background: #ffffff;
      border-radius: 0 0 0 8px;
      box-shadow: inset 0 1px 0 0 #e8e8e8;
      align-items: center;
      flex-direction: row-reverse;

      span {
        cursor: pointer;
        margin: 0 30px;
        font-size: 14px;
        color: #0043a8;
        font-weight: 500;
        padding: 5px 10px;
        &:hover{
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

.page-grid-container{
  overflow-y: auto !important;
  .grid-container{
    flex: 1;
    height: auto;
    .mt-data-grid{
      display: flex;
      flex-direction: column;
      position: relative;
      .e-grid{
        flex: 1;
      }
    }
  }
}


.full-height {
  height: 100%;
}
.full-width {
  width: 100%!important;
}
.pt20 {
  padding-top: 20px;
}

.col-active {
  color: #6386C1;
  font-size: 12px;
  padding: 2px 6px;
  line-height: 1;
  border-radius: 2px;
  overflow: hidden;
  background: rgba(238,242,249,1);
}
.col-inactive {
  color: #9BAAC1;
  font-size: 12px;
  padding: 2px 6px;
  line-height: 1;
  border-radius: 2px;
  overflow: hidden;
  background: rgba(155,170,193,0.1);
}
