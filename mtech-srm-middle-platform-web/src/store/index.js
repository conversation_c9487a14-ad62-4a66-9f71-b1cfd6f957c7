import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
const store = new Vuex.Store({
  state: {
    loading: false,
    user: {}
  },
  mutations: {
    startLoading(state) {
      state.loading = true
    },
    endLoading(state) {
      state.loading = false
    },
    setUser(state, data) {
      state.user = data
    }
  },
  getters: {},
  actions: {
    endLoading({ commit, dispatch }) {
      console.log('dispatch', dispatch)
      setTimeout(() => {
        commit('endLoading')
      }, parseInt(Math.random() * 300 + 300))
    }
  }
})

export default store
