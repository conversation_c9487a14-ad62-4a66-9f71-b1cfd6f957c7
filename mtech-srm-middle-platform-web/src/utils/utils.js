import Vue from 'vue'

export const isEmpty = (obj) => {
  /* eslint-disable */
  if (obj == null) return true
  if (Array.isArray(obj) || isString(obj)) return obj.length === 0
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) return false
  }
  return true
}

export const isNumber = (num) => {
  return parseFloat(num).toString() !== 'NaN'
}

export const isString = (obj) => Object.prototype.toString.call(obj) === '[object String]'

// 通过 a 标签下载文件
export const download = (data) => {
  const { fileName, blob } = data

  if (!blob) {
    return
  }

  if (blob?.type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(blob, 'utf-8')
    reader.onload = function () {
      const readerRes = reader.result
      const resObj = JSON.parse(readerRes)
      Vue.prototype.$toast({
        content: resObj.msg,
        type: 'error'
      })
    }

    return
  }

  const a = document.createElement('a')
  a.href = URL.createObjectURL(blob)
  a.style.display = 'none'
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

/**
 * 获取请求头中的文件名称
 * @param data data: { headers: content-disposition: "<文件名信息>" }
 * @returns String
 */
export const getHeadersFileName = (data) => {
  const contentDisposition = data?.headers['content-disposition'] || ''
  const prefix = 'attachment;filename='
  const prefix1 = 'attachment; filename='
  const prefix2 = 'attachment;filename*='
  const prefix3 = "attachment;filename*=utf-8'zh_cn'"
  let fileName = contentDisposition
  if (contentDisposition.indexOf(prefix) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix.length))
  }
  // 兼容不同格式
  if (contentDisposition.indexOf(prefix1) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix1.length))
  }
  if (contentDisposition.indexOf(prefix2) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix2.length))
  }
  if (contentDisposition.indexOf(prefix3) === 0) {
    fileName = decodeURI(contentDisposition.slice(prefix3.length))
  }

  return fileName
}

// 防抖
let timeid = null
export const debounce = (func, wait = 0) => {
  if (typeof func !== 'function') {
    throw new TypeError('need a function arguments')
  }
  let result

  return function () {
    let context = this
    let args = arguments

    if (timeid) {
      clearTimeout(timeid)
    }
    timeid = setTimeout(function () {
      result = func.apply(context, args)
    }, wait)

    return result
  }
}

const newDate = (val) => (isString(val) ? new Date(val.replace(/-/g, '/')) : new Date(val))
export const formateTime = (date = new Date(), format = 'yyyy-MM-dd') => {
  if (isString(date) || isNumber(date)) {
    date = newDate(date)
  }

  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds()
  }
  let w = [
    ['日', '一', '二', '三', '四', '五', '六'],
    ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  ]
  let now = new Date()
  let today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  let start = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  let diff = (start - today) / 86400000
  let text

  switch (diff) {
    case 0:
      text = '今天'
      break
    // case 1:
    //     text = '明天'
    //     break
    // case 2:
    //     text = '后天'
    //     break
    default:
      text = ''
  }

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }

  if (/(w+)/.test(format)) {
    if (text) {
      format = format.replace(RegExp.$1, text)
    } else {
      format = format.replace(RegExp.$1, w[RegExp.$1.length - 1][date.getDay()])
    }
  }

  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }

  return format
}
