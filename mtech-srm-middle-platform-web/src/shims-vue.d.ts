import VueI18n from 'vue-i18n'
import VueRouter, { Route } from 'vue-router'
import { Store } from 'vuex'

declare module 'vue/types/vue' {
  interface Vue {
    $router: VueRouter
    $route: Route
    $store: Store<any>
    // 以下是在main.ts中挂载到Vue.prototype上的变量
    $api: any
    $tips: any
    $load: any
    $preview: any
    $confirm: any
    $toast: any
    $dialog: any
    $t: {
      (key: string, values?: VueI18n.Values | undefined): VueI18n.TranslateResult
      (key: string, locale: string, values?: VueI18n.Values | undefined): VueI18n.TranslateResult
    }
  }
}
